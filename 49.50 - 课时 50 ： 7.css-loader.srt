1
00:00:00,000 --> 00:00:03,200
本节呢我们来接着写这个css loader

2
00:00:03,200 --> 00:00:05,200
这个css loader呢稍微有些复杂

3
00:00:05,200 --> 00:00:08,460
那我们来看看这css loader呢主要处理什么问题的

4
00:00:08,460 --> 00:00:11,120
我们往下找找到我们的这个index less

5
00:00:11,120 --> 00:00:13,620
比如这里呢我稍微把这个颜色改一下

6
00:00:13,620 --> 00:00:16,000
background我来一个url吧

7
00:00:16,000 --> 00:00:16,680
一个图片

8
00:00:16,680 --> 00:00:19,820
这里呢我就直接去引.gpublic勾笔记

9
00:00:19,820 --> 00:00:22,540
这时候你会想到我这样引的话呀

10
00:00:22,540 --> 00:00:23,700
其实引的是一个字符串

11
00:00:23,700 --> 00:00:25,360
在我们less解析的时候啊

12
00:00:25,360 --> 00:00:27,860
他也不会去帮你怎么样去requare这个文件

13
00:00:27,860 --> 00:00:29,660
那肯定最后打包出来的呀

14
00:00:29,660 --> 00:00:31,800
还叫这个第二杠狗PG

15
00:00:31,800 --> 00:00:32,340
对吧

16
00:00:32,340 --> 00:00:33,020
那这时候呢

17
00:00:33,020 --> 00:00:33,920
我们来运行一下

18
00:00:33,920 --> 00:00:34,600
看看是不是这样

19
00:00:34,600 --> 00:00:35,560
哎执行

20
00:00:35,560 --> 00:00:36,880
他应该是自动执行了

21
00:00:36,880 --> 00:00:37,960
我在这刷新一下

22
00:00:37,960 --> 00:00:39,020
走你

23
00:00:39,020 --> 00:00:40,400
你看这时候报了一个错

24
00:00:40,400 --> 00:00:40,700
是吧

25
00:00:40,700 --> 00:00:41,700
说当前呢

26
00:00:41,700 --> 00:00:42,720
我们这个文件怎么样

27
00:00:42,720 --> 00:00:43,580
是没找到啊

28
00:00:43,580 --> 00:00:44,840
因为当前我们引的

29
00:00:44,840 --> 00:00:46,120
还是这个public点狗PG

30
00:00:46,120 --> 00:00:48,140
但是我们这个DIS的目录下呀

31
00:00:48,140 --> 00:00:49,320
肯定没有这个public

32
00:00:49,320 --> 00:00:50,660
那怎么办呢

33
00:00:50,660 --> 00:00:51,780
那这时候我们就想到啊

34
00:00:51,780 --> 00:00:52,820
需要把这个东西怎么样

35
00:00:52,820 --> 00:00:53,660
进行转化

36
00:00:53,660 --> 00:00:54,100
那好

37
00:00:54,100 --> 00:00:55,020
我揭个图是吧

38
00:00:55,020 --> 00:00:56,040
揭个图

39
00:00:56,040 --> 00:00:57,680
我在这里呢

40
00:00:57,680 --> 00:00:58,620
我在这里呢

41
00:00:58,620 --> 00:00:59,040
揭个图

42
00:00:59,040 --> 00:01:00,640
比如说snapface是吧

43
00:01:00,640 --> 00:01:02,100
我来来个文件

44
00:01:02,100 --> 00:01:05,280
这里面我把它拿过来

45
00:01:05,280 --> 00:01:05,960
完了呢

46
00:01:05,960 --> 00:01:07,660
放到我们的这个保存一下是吧

47
00:01:07,660 --> 00:01:08,500
复制一下

48
00:01:08,500 --> 00:01:09,280
完了里面呢

49
00:01:09,280 --> 00:01:12,100
我就放到我们的这个评设工具里是吧

50
00:01:12,100 --> 00:01:13,120
好

51
00:01:13,120 --> 00:01:14,060
我站过来以后

52
00:01:14,060 --> 00:01:15,400
你会发现这样一个问题啊

53
00:01:15,400 --> 00:01:16,560
我们需要干嘛呢

54
00:01:16,560 --> 00:01:18,660
是不是希望把这个文件分成几部分

55
00:01:18,660 --> 00:01:20,100
比如说刚开始啊

56
00:01:20,100 --> 00:01:21,360
这个整个的这个地方

57
00:01:21,360 --> 00:01:21,540
对吧

58
00:01:21,540 --> 00:01:23,420
从这儿到这儿是一部分

59
00:01:23,420 --> 00:01:24,180
对吧

60
00:01:24,180 --> 00:01:25,300
完了我们需要干嘛呢

61
00:01:25,300 --> 00:01:26,500
把这后面算成一部分

62
00:01:26,500 --> 00:01:28,460
完了把中间这个字段怎么样

63
00:01:28,460 --> 00:01:29,980
改成一个写法叫require

64
00:01:29,980 --> 00:01:31,100
因为我们都知道

65
00:01:31,100 --> 00:01:32,420
通过require的写法

66
00:01:32,420 --> 00:01:33,980
这个东西就可以被怎么样

67
00:01:33,980 --> 00:01:34,520
被打包

68
00:01:34,520 --> 00:01:35,260
好了

69
00:01:35,260 --> 00:01:37,440
我们在这就来处理一下逻辑

70
00:01:37,440 --> 00:01:39,220
怎么能把这样一个路径匹配到

71
00:01:39,220 --> 00:01:41,520
变成什么require的路径名

72
00:01:41,520 --> 00:01:42,420
是不是就可以了

73
00:01:42,420 --> 00:01:42,980
好

74
00:01:42,980 --> 00:01:44,200
我在这里就来写一下

75
00:01:44,200 --> 00:01:47,360
找到我们这样一个csl loader

76
00:01:47,360 --> 00:01:49,840
这里我们需要怎么样的

77
00:01:49,840 --> 00:01:50,380
来个正责

78
00:01:50,380 --> 00:01:52,080
这正责非常简单

79
00:01:52,080 --> 00:01:54,680
它的作用主要就是用来匹配我们的什么

80
00:01:54,680 --> 00:01:56,280
是匹配我们的URL

81
00:01:56,280 --> 00:01:57,640
然后URL括号

82
00:01:57,640 --> 00:01:59,680
完了这括号肯定需要转移一下

83
00:01:59,680 --> 00:02:01,440
完了中间可能是任意字符

84
00:02:01,440 --> 00:02:02,820
点加吧

85
00:02:02,820 --> 00:02:03,320
来个问号

86
00:02:03,320 --> 00:02:04,420
可以给这多个

87
00:02:04,420 --> 00:02:05,740
完了我们需要全局

88
00:02:05,740 --> 00:02:07,860
这时候我们需要怎么做

89
00:02:07,860 --> 00:02:09,900
可能这里面不单单有一个

90
00:02:09,900 --> 00:02:10,900
肯定有N个

91
00:02:10,900 --> 00:02:11,760
我们就需要怎么样

92
00:02:11,760 --> 00:02:12,460
是不是拼多次

93
00:02:12,460 --> 00:02:13,160
这取出来

94
00:02:13,160 --> 00:02:13,860
这取出来

95
00:02:13,860 --> 00:02:15,200
这地方怎么样

96
00:02:15,200 --> 00:02:16,120
加上这样一个requare

97
00:02:16,120 --> 00:02:17,060
好了

98
00:02:17,060 --> 00:02:18,080
我要怎么做

99
00:02:18,080 --> 00:02:18,880
非常简单了

100
00:02:18,880 --> 00:02:21,080
我在这里需要来一个位置

101
00:02:21,080 --> 00:02:22,260
第一次查找

102
00:02:22,260 --> 00:02:23,600
肯定是从零开始找

103
00:02:23,600 --> 00:02:24,540
下次查找

104
00:02:24,540 --> 00:02:25,580
是不是应该从这个地方

105
00:02:25,580 --> 00:02:26,280
接着往下找

106
00:02:26,280 --> 00:02:27,360
接着往下不停的去找

107
00:02:27,360 --> 00:02:28,160
那好了

108
00:02:28,160 --> 00:02:30,360
那我们的写法就是这样的

109
00:02:30,360 --> 00:02:31,780
我们希望写个外国循环

110
00:02:31,780 --> 00:02:33,560
完了呢去怎么样

111
00:02:33,560 --> 00:02:35,720
拿我们当前的这个原代码啊

112
00:02:35,720 --> 00:02:36,300
一次去匹配

113
00:02:36,300 --> 00:02:38,300
那比如说我就用这个sauce

114
00:02:38,300 --> 00:02:38,860
对吧

115
00:02:38,860 --> 00:02:39,260
第二

116
00:02:39,260 --> 00:02:40,600
那是正泽是吧

117
00:02:40,600 --> 00:02:41,200
正泽

118
00:02:41,200 --> 00:02:42,680
第二esec匹配

119
00:02:42,680 --> 00:02:44,080
完了匹配谁呢

120
00:02:44,080 --> 00:02:45,800
匹配我们这个原代码

121
00:02:45,800 --> 00:02:46,840
完了并且呢

122
00:02:46,840 --> 00:02:49,000
它整个返回的就是什么东西

123
00:02:49,000 --> 00:02:51,180
就是我们当前找到这样一个结果

124
00:02:51,180 --> 00:02:51,820
叫current

125
00:02:51,820 --> 00:02:53,740
完了我默认给个空吧

126
00:02:53,740 --> 00:02:54,660
完了有了以后呢

127
00:02:54,660 --> 00:02:55,580
就把这直付给它

128
00:02:55,580 --> 00:02:57,320
那我们都知道啊

129
00:02:57,320 --> 00:02:58,860
这current肯定有两个值

130
00:02:58,860 --> 00:03:01,560
第一个是什么是匹配到的路径

131
00:03:01,560 --> 00:03:02,820
match的url

132
00:03:02,820 --> 00:03:07,260
完了第二个肯定是我们所谓的叫匹配的分组

133
00:03:07,260 --> 00:03:07,520
是吧

134
00:03:07,520 --> 00:03:08,220
就是分组

135
00:03:08,220 --> 00:03:08,980
比如叫group

136
00:03:08,980 --> 00:03:10,560
好了

137
00:03:10,560 --> 00:03:12,180
我在这里就这样来写

138
00:03:12,180 --> 00:03:13,380
我把这个数字结构一下

139
00:03:13,380 --> 00:03:15,260
等于这个current

140
00:03:15,260 --> 00:03:17,460
好了

141
00:03:17,460 --> 00:03:19,980
我们来看看当前匹配的字符串

142
00:03:19,980 --> 00:03:23,080
我们是不是就相当于第一次匹配到字符串肯定就是

143
00:03:23,080 --> 00:03:23,960
这么长

144
00:03:23,960 --> 00:03:25,980
就整个的url

145
00:03:25,980 --> 00:03:27,980
就这个画个红的

146
00:03:27,980 --> 00:03:28,980
拼起来

147
00:03:28,980 --> 00:03:30,320
这个东西是整个的

148
00:03:30,320 --> 00:03:31,180
网站的分组呢

149
00:03:31,180 --> 00:03:32,280
肯定指的就是哪啊

150
00:03:32,280 --> 00:03:33,720
指的就是这个圈

151
00:03:33,720 --> 00:03:34,440
这里面

152
00:03:34,440 --> 00:03:35,380
那咱来试试啊

153
00:03:35,380 --> 00:03:36,440
为了看着方便

154
00:03:36,440 --> 00:03:37,080
我就在这里呢

155
00:03:37,080 --> 00:03:38,080
先打出来

156
00:03:38,080 --> 00:03:38,540
cotal log

157
00:03:38,540 --> 00:03:41,280
来个什么呢

158
00:03:41,280 --> 00:03:41,880
match

159
00:03:41,880 --> 00:03:42,420
对吧

160
00:03:42,420 --> 00:03:43,820
ur还有这个g

161
00:03:43,820 --> 00:03:46,080
看看是不是我想要的结果

162
00:03:46,080 --> 00:03:46,580
我在这里

163
00:03:46,580 --> 00:03:48,080
每一步都这样来写啊

164
00:03:48,080 --> 00:03:51,320
那这里面是ur ok

165
00:03:51,320 --> 00:03:52,180
那拿到以后啊

166
00:03:52,180 --> 00:03:53,140
我需要干嘛呢

167
00:03:53,140 --> 00:03:54,740
我是不是需要先把url

168
00:03:54,740 --> 00:03:56,340
前面这一部分都拿到啊

169
00:03:56,340 --> 00:03:58,740
那现在啊我们都知道使用exec啊

170
00:03:58,740 --> 00:03:59,980
他这证则有个功能

171
00:03:59,980 --> 00:04:01,840
就是他会有个叫last index

172
00:04:01,840 --> 00:04:02,940
但他匹配完以后啊

173
00:04:02,940 --> 00:04:04,140
这个所以就会指向这

174
00:04:04,140 --> 00:04:05,640
那我需要怎么样呢

175
00:04:05,640 --> 00:04:07,980
是不是从0就是0就开始的位置

176
00:04:07,980 --> 00:04:10,540
对吧是不是到这个地方我需要怎么样

177
00:04:10,540 --> 00:04:12,540
先变成一段存起来啊

178
00:04:12,540 --> 00:04:13,240
完了之后怎么样

179
00:04:13,240 --> 00:04:14,540
再把后面这一段存起来

180
00:04:14,540 --> 00:04:15,940
我把中间怎么样听完叫

181
00:04:15,940 --> 00:04:18,140
所以说啊我需要用这个last

182
00:04:18,140 --> 00:04:19,040
就是这个位置

183
00:04:19,040 --> 00:04:19,940
这个最后的位置

184
00:04:19,940 --> 00:04:20,840
往来解决什么

185
00:04:20,840 --> 00:04:22,340
这个当前匹配到ur

186
00:04:22,340 --> 00:04:24,540
这个长度就是我们当前前面这个

187
00:04:24,540 --> 00:04:25,840
所以那好了

188
00:04:25,840 --> 00:04:26,740
我就需要怎么样

189
00:04:26,740 --> 00:04:28,380
拿到这样一个所以

190
00:04:28,380 --> 00:04:29,200
last let

191
00:04:29,200 --> 00:04:30,600
比如说就叫last

192
00:04:30,600 --> 00:04:31,500
等于多少呢

193
00:04:31,500 --> 00:04:34,240
等于当前我们匹配到的这个整个的叫什么

194
00:04:34,240 --> 00:04:36,500
叫reg的last index

195
00:04:36,500 --> 00:04:37,300
减去什么

196
00:04:37,300 --> 00:04:40,500
是不是减去刚才我们看到的这个整个字幕串的长度

197
00:04:40,500 --> 00:04:42,800
那匹配到字幕串是哪个是不是match ul

198
00:04:42,800 --> 00:04:43,780
.lns

199
00:04:43,780 --> 00:04:45,880
那拿到以后的话

200
00:04:45,880 --> 00:04:47,000
那非常好办了

201
00:04:47,000 --> 00:04:47,880
那我希望怎么样

202
00:04:47,880 --> 00:04:48,980
是不是把它存起来

203
00:04:48,980 --> 00:04:51,700
那同样这里面我稍微写个怪异的写法

204
00:04:51,700 --> 00:04:52,800
等会给大家解释一下

205
00:04:52,800 --> 00:04:54,080
我先来个书组

206
00:04:54,080 --> 00:04:56,580
这个书组里面我放个这样的书组

207
00:04:56,580 --> 00:04:58,080
放一个这样的书组吧

208
00:04:58,080 --> 00:04:59,580
我料里面呢我就这样写

209
00:04:59,580 --> 00:05:01,280
light一个什么的叫啊

210
00:05:01,280 --> 00:05:04,180
哎呀或者叫利斯他列表等一个书组

211
00:05:04,180 --> 00:05:05,480
哎干嘛呢

212
00:05:05,480 --> 00:05:07,980
因为我要拼出来一个字符串的结果

213
00:05:07,980 --> 00:05:09,080
所以我这样来写

214
00:05:09,080 --> 00:05:10,680
你看看完就懂了啊

215
00:05:10,680 --> 00:05:11,180
这里呢

216
00:05:11,180 --> 00:05:13,680
我把这个压怎么样点铺事

217
00:05:13,680 --> 00:05:15,680
哎第二铺事

218
00:05:15,680 --> 00:05:17,180
那好了里面呢

219
00:05:17,180 --> 00:05:18,620
我就放上这样一个对吧

220
00:05:18,620 --> 00:05:19,580
放上一个这样的东西啊

221
00:05:19,580 --> 00:05:21,080
反映号吧就赖他

222
00:05:21,080 --> 00:05:23,880
比如说叫啊点铺事叫利斯他

223
00:05:23,880 --> 00:05:27,480
类似的点不是这个里面的变量和这里面的变量是丁上的

224
00:05:27,480 --> 00:05:28,980
我来里面的我放上什么呢

225
00:05:28,980 --> 00:05:31,680
是放上我们当前这个圆码要截取啊

226
00:05:31,680 --> 00:05:32,980
那圆码指的是哪呢

227
00:05:32,980 --> 00:05:35,580
是不是就说我们的这个整个的圆码叫sauce

228
00:05:35,580 --> 00:05:38,280
完了那这里面我需要这样来写的是吧

229
00:05:38,280 --> 00:05:39,780
sauce.slice

230
00:05:39,780 --> 00:05:40,980
从哪开始截呢

231
00:05:40,980 --> 00:05:42,680
需要通过我们这个position

232
00:05:42,680 --> 00:05:43,680
也从零的位置

233
00:05:43,680 --> 00:05:44,780
那零的位置截到哪呢

234
00:05:44,780 --> 00:05:45,980
是不是截到我们这个last

235
00:05:45,980 --> 00:05:47,080
他指的就是什么

236
00:05:47,080 --> 00:05:48,580
就是我们前面这一段

237
00:05:48,580 --> 00:05:49,480
哎就这一段

238
00:05:49,480 --> 00:05:52,180
看到这个颜色啊有点丑是吧

239
00:05:52,180 --> 00:05:52,880
就这一段

240
00:05:53,180 --> 00:05:54,380
没有报告URL

241
00:05:54,380 --> 00:05:56,380
拿到这一段以后非常方便了

242
00:05:56,380 --> 00:05:58,680
但这样的写的话肯定会有换行回车

243
00:05:58,680 --> 00:06:00,380
我们都说过怎么解决这个问题来的

244
00:06:00,380 --> 00:06:01,580
说有个技巧叫什么

245
00:06:01,580 --> 00:06:03,080
叫json.stringify

246
00:06:03,080 --> 00:06:04,880
这东西我就直接搞定

247
00:06:04,880 --> 00:06:06,980
json.stringify

248
00:06:06,980 --> 00:06:09,280
完了这样写完以后就OK了

249
00:06:09,280 --> 00:06:10,880
写完以后我们再干嘛

250
00:06:10,880 --> 00:06:11,580
再往下看

251
00:06:11,580 --> 00:06:14,980
这时候我们还需要替换URL什么

252
00:06:14,980 --> 00:06:15,980
这个中间的部分

253
00:06:15,980 --> 00:06:17,980
这中间的部分怎么拿到

254
00:06:17,980 --> 00:06:19,280
其实就是我们所谓的G

255
00:06:19,280 --> 00:06:20,980
我在这描述一下对吧

256
00:06:20,980 --> 00:06:22,580
就是把这个G

257
00:06:22,580 --> 00:06:23,140
对吧

258
00:06:23,140 --> 00:06:24,900
把G替换成什么呢

259
00:06:24,900 --> 00:06:27,780
是不是替换成什么require的写法

260
00:06:27,780 --> 00:06:29,840
require的写法

261
00:06:29,840 --> 00:06:30,600
那好了

262
00:06:30,600 --> 00:06:32,340
我就再在这个速度里接着扑示

263
00:06:32,340 --> 00:06:33,500
接着扑示

264
00:06:33,500 --> 00:06:34,240
完了里面呢

265
00:06:34,240 --> 00:06:34,960
我放个翻译号

266
00:06:34,960 --> 00:06:36,720
写个list的push

267
00:06:36,720 --> 00:06:38,320
完了里面怎么办呢

268
00:06:38,320 --> 00:06:38,820
还是一样

269
00:06:38,820 --> 00:06:39,680
我们需要怎么样

270
00:06:39,680 --> 00:06:41,180
是不是放的肯定是个字符串

271
00:06:41,180 --> 00:06:42,860
字符串里面有unil括号

272
00:06:42,860 --> 00:06:43,720
往这里呢

273
00:06:43,720 --> 00:06:44,560
我拼个字符串

274
00:06:44,560 --> 00:06:45,660
你看这拼的挺恶心的

275
00:06:45,660 --> 00:06:47,060
里面放上一个什么呢

276
00:06:47,060 --> 00:06:47,680
这是require

277
00:06:47,680 --> 00:06:48,620
完了里面呢

278
00:06:48,620 --> 00:06:50,040
加上我们当前这样一个G

279
00:06:50,040 --> 00:06:50,600
那G呢

280
00:06:50,600 --> 00:06:51,100
就这样写

281
00:06:51,100 --> 00:06:51,880
到了辅大括号

282
00:06:51,880 --> 00:06:53,780
应该能看懂

283
00:06:53,780 --> 00:06:55,120
其实就是这样一个东西

284
00:06:55,120 --> 00:06:55,320
对吧

285
00:06:55,320 --> 00:06:57,380
就URL里面放一个require

286
00:06:57,380 --> 00:06:58,580
我让里面放了一个

287
00:06:58,580 --> 00:06:59,840
当前的这样一个路径

288
00:06:59,840 --> 00:07:00,080
对吧

289
00:07:00,080 --> 00:07:01,040
XX对吧

290
00:07:01,040 --> 00:07:01,760
那好了

291
00:07:01,760 --> 00:07:02,920
那这样写完以后

292
00:07:02,920 --> 00:07:04,120
完了我们还要干嘛

293
00:07:04,120 --> 00:07:06,120
是不是还要把这后一段拼上

294
00:07:06,120 --> 00:07:07,660
那这后一段怎么来的

295
00:07:07,660 --> 00:07:08,500
那非常简单

296
00:07:08,500 --> 00:07:10,580
我需要让我们当前这个位置

297
00:07:10,580 --> 00:07:10,900
怎么样

298
00:07:10,900 --> 00:07:11,420
里加

299
00:07:11,420 --> 00:07:12,300
里加以后

300
00:07:12,300 --> 00:07:14,320
是不是把最后这一段取出来就OK了

301
00:07:14,320 --> 00:07:15,540
所以说到这

302
00:07:15,540 --> 00:07:17,420
完了我们放完以后

303
00:07:17,420 --> 00:07:18,080
这是少了个

304
00:07:18,080 --> 00:07:18,920
没问题是吧

305
00:07:18,920 --> 00:07:19,460
少了个括号

306
00:07:19,460 --> 00:07:20,600
来一个

307
00:07:20,600 --> 00:07:21,720
来个用括号

308
00:07:21,720 --> 00:07:24,160
最后循环完以后

309
00:07:24,160 --> 00:07:25,800
我们是不是要把最后段追上

310
00:07:25,800 --> 00:07:27,880
最后一段的所以我们要往上涨

311
00:07:27,880 --> 00:07:28,200
是吧

312
00:07:28,200 --> 00:07:29,380
有position要涨

313
00:07:29,380 --> 00:07:30,360
position等于多少

314
00:07:30,360 --> 00:07:32,500
等于我们当前的last

315
00:07:32,500 --> 00:07:34,420
reg的last index

316
00:07:34,420 --> 00:07:36,020
完了每次涨完以后

317
00:07:36,020 --> 00:07:37,000
我最后怎么样

318
00:07:37,000 --> 00:07:39,040
是不是把原码最后的部分

319
00:07:39,040 --> 00:07:42,580
大括号和后面的后面货汇车都放进去

320
00:07:42,580 --> 00:07:43,340
怎么放

321
00:07:43,340 --> 00:07:46,160
反正list

322
00:07:46,160 --> 00:07:49,900
里面我就直接还是这样一个操作

323
00:07:49,900 --> 00:07:51,920
但是这时候我们放的参数

324
00:07:51,920 --> 00:07:52,940
就要设备改造一下

325
00:07:52,940 --> 00:07:53,900
到这里

326
00:07:53,900 --> 00:07:57,180
里面直接放什么

327
00:07:57,180 --> 00:07:58,600
是不是就放我们的position就好了

328
00:07:58,600 --> 00:07:59,860
从最后开始接

329
00:07:59,860 --> 00:08:00,660
接到最后

330
00:08:00,660 --> 00:08:01,720
完了最后我干嘛

331
00:08:01,720 --> 00:08:02,920
我再往里放一个东西

332
00:08:02,920 --> 00:08:03,820
你就看得懂了

333
00:08:03,820 --> 00:08:04,900
AR.push

334
00:08:04,900 --> 00:08:05,640
我放个什么

335
00:08:05,640 --> 00:08:07,100
来一个module

336
00:08:07,100 --> 00:08:09,180
等于什么

337
00:08:09,180 --> 00:08:11,280
等于当前我们这样一个结果

338
00:08:11,280 --> 00:08:12,520
这个结果长什么样

339
00:08:12,520 --> 00:08:13,220
非常简单

340
00:08:13,220 --> 00:08:14,260
我可以在这来个叫什么

341
00:08:14,260 --> 00:08:14,780
叫AR

342
00:08:14,780 --> 00:08:15,980
叫list

343
00:08:15,980 --> 00:08:17,140
list.jour

344
00:08:17,140 --> 00:08:20,080
list.draw

345
00:08:20,080 --> 00:08:21,780
OK完了

346
00:08:21,780 --> 00:08:22,480
拼个什么呢

347
00:08:22,480 --> 00:08:23,440
我就拼它一个空

348
00:08:23,440 --> 00:08:25,180
这样就可以了

349
00:08:25,180 --> 00:08:25,620
那好了

350
00:08:25,620 --> 00:08:26,680
你看这什么意思呢

351
00:08:26,680 --> 00:08:27,220
最后啊

352
00:08:27,220 --> 00:08:28,720
我们是不是现在有个数组

353
00:08:28,720 --> 00:08:29,920
那出来的长什么样呢

354
00:08:29,920 --> 00:08:30,160
对吧

355
00:08:30,160 --> 00:08:30,980
我把这数组啊

356
00:08:30,980 --> 00:08:31,780
给你打印一下

357
00:08:31,780 --> 00:08:33,380
Costlog.AR.draw

358
00:08:33,380 --> 00:08:34,360
来个什么呢

359
00:08:34,360 --> 00:08:34,800
来个空

360
00:08:34,800 --> 00:08:36,520
来个欢迎回车吧

361
00:08:36,520 --> 00:08:36,980
欢迎回车

362
00:08:36,980 --> 00:08:37,900
看着清晰一点

363
00:08:37,900 --> 00:08:39,320
比如说我们打印出来

364
00:08:39,320 --> 00:08:39,960
它是不是数组

365
00:08:39,960 --> 00:08:40,960
我们把这数组怎么样

366
00:08:40,960 --> 00:08:41,900
是不是整合到一起了

367
00:08:41,900 --> 00:08:43,260
那这个一起组成的

368
00:08:43,260 --> 00:08:43,720
就是一个什么

369
00:08:43,720 --> 00:08:45,100
就是一个代码片段

370
00:08:45,100 --> 00:08:46,440
代码片段就是生命的数组

371
00:08:46,440 --> 00:08:47,800
往数字里不停的放结果

372
00:08:47,800 --> 00:08:48,900
放到结果以后怎么样

373
00:08:48,900 --> 00:08:50,000
通过猫对的炮怎么样

374
00:08:50,000 --> 00:08:50,840
要导出

375
00:08:50,840 --> 00:08:51,680
试啥

376
00:08:51,680 --> 00:08:53,020
玩一下是吧

377
00:08:53,020 --> 00:08:54,760
这是考验自物串拼接

378
00:08:54,760 --> 00:08:56,300
在这走过来

379
00:08:56,300 --> 00:08:56,500
你看

380
00:08:56,500 --> 00:08:57,700
是不是这样一个结果

381
00:08:57,700 --> 00:08:59,380
就是let生命数组

382
00:08:59,380 --> 00:09:00,620
数组里面是不是放了一个

383
00:09:00,620 --> 00:09:01,160
barground

384
00:09:01,160 --> 00:09:02,660
barground的冒号

385
00:09:02,660 --> 00:09:03,740
等住了是吧

386
00:09:03,740 --> 00:09:05,060
说这里面又放了个数组

387
00:09:05,060 --> 00:09:06,000
这是个自物串吧

388
00:09:06,000 --> 00:09:06,940
这是个变量吧

389
00:09:06,940 --> 00:09:08,140
你看变量是不是requare

390
00:09:08,140 --> 00:09:09,260
加上这样一个东西又加

391
00:09:09,260 --> 00:09:09,620
ok

392
00:09:09,620 --> 00:09:10,580
最后呢

393
00:09:10,580 --> 00:09:11,460
是不是又放了一个什么

394
00:09:11,460 --> 00:09:12,220
是不是-n

395
00:09:12,220 --> 00:09:12,960
结束

396
00:09:12,960 --> 00:09:13,700
最后呢

397
00:09:13,700 --> 00:09:15,360
我是不是把这个数组怎么拼完以后

398
00:09:15,360 --> 00:09:16,700
那他再去招案的话

399
00:09:16,700 --> 00:09:18,020
是不是出来的就是什么

400
00:09:18,020 --> 00:09:19,880
是不是就是这里面的所有代码

401
00:09:19,880 --> 00:09:20,460
字符串啊

402
00:09:20,460 --> 00:09:22,000
我把这字符串付给了谁啊

403
00:09:22,000 --> 00:09:22,840
毛丢一次抛次

404
00:09:22,840 --> 00:09:24,100
那最后好了

405
00:09:24,100 --> 00:09:25,240
那也就是说这里面

406
00:09:25,240 --> 00:09:26,620
我最后返回的就是啥呀

407
00:09:26,620 --> 00:09:27,700
是不是就应该是这东西啊

408
00:09:27,700 --> 00:09:29,300
那这个东西就是什么

409
00:09:29,300 --> 00:09:30,480
就是刚才我们看到的

410
00:09:30,480 --> 00:09:31,340
组成的什么

411
00:09:31,340 --> 00:09:33,020
这样的一个字符串

412
00:09:33,020 --> 00:09:35,300
那组成了这样一个字符串

413
00:09:35,300 --> 00:09:35,800
那OK了

414
00:09:35,800 --> 00:09:37,480
那这字符串给了以后怎么样

415
00:09:37,480 --> 00:09:38,960
我是不是可以拿到

416
00:09:38,960 --> 00:09:40,380
拿到他导出的结果

417
00:09:40,380 --> 00:09:41,600
直接Require进来

418
00:09:41,600 --> 00:09:42,440
是不是就可以执行了

419
00:09:42,440 --> 00:09:43,520
执行完以后怎么样

420
00:09:43,520 --> 00:09:44,740
是不是就可以在页面上

421
00:09:44,740 --> 00:09:46,240
哎 显示我们就看个结果了

422
00:09:46,240 --> 00:09:48,580
那好 那我们这个写完以后啊

423
00:09:48,580 --> 00:09:49,700
那还需要改造谁呢

424
00:09:49,700 --> 00:09:50,940
改造这个style loader

425
00:09:50,940 --> 00:09:53,500
我们说了咱一直都没有用到那个方法啊

426
00:09:53,500 --> 00:09:55,700
其实这个loader上呢是有一个东西叫什么来着

427
00:09:55,700 --> 00:09:56,780
叫p 出来的

428
00:09:56,780 --> 00:09:58,440
哎 它是个函数

429
00:09:58,440 --> 00:10:01,740
我们说了只要我在这个style loader上对吧

430
00:10:01,740 --> 00:10:03,500
就是在style

431
00:10:03,500 --> 00:10:07,040
loader上对吧 style loader上

432
00:10:07,040 --> 00:10:10,540
style写个 e 是吧 style loader上

433
00:10:10,540 --> 00:10:11,600
完了写了什么呢

434
00:10:11,600 --> 00:10:13,980
写了p 写了p 会有什么效果啊

435
00:10:13,980 --> 00:10:15,740
是不是相当于后面的怎么样

436
00:10:15,740 --> 00:10:16,580
是不是都不走了

437
00:10:16,580 --> 00:10:17,380
P是loader

438
00:10:17,380 --> 00:10:18,860
都不走了怎么样

439
00:10:18,860 --> 00:10:19,740
是不是自己的也不

440
00:10:19,740 --> 00:10:20,580
自己loader也不走了

441
00:10:20,580 --> 00:10:21,580
这时候怎么样

442
00:10:21,580 --> 00:10:23,460
它其实里面有个参数叫什么

443
00:10:23,460 --> 00:10:24,340
叫re money

444
00:10:24,340 --> 00:10:25,300
对吧

445
00:10:25,300 --> 00:10:25,880
request

446
00:10:25,880 --> 00:10:26,960
这什么意思

447
00:10:26,960 --> 00:10:28,740
叫剩余的请求

448
00:10:28,740 --> 00:10:29,120
对吧

449
00:10:29,120 --> 00:10:30,860
剩余的请求

450
00:10:30,860 --> 00:10:32,340
什么意思

451
00:10:32,340 --> 00:10:34,060
就是我们现在有几个loader来的

452
00:10:34,060 --> 00:10:34,560
是不是有三个

453
00:10:34,560 --> 00:10:35,500
第一个叫什么

454
00:10:35,500 --> 00:10:36,780
叫styleloader

455
00:10:36,780 --> 00:10:39,180
第二个叫我们的lessloader

456
00:10:39,180 --> 00:10:40,400
最后叫什么

457
00:10:40,400 --> 00:10:41,680
叫csiloader

458
00:10:41,680 --> 00:10:42,580
什么叫剩余的

459
00:10:42,580 --> 00:10:43,960
你说现在已经走到哪了

460
00:10:43,960 --> 00:10:46,680
是不是我们说了先走的是styleloader的peach

461
00:10:46,680 --> 00:10:47,740
那现在还剩几个

462
00:10:47,740 --> 00:10:48,620
是不是剩两个

463
00:10:48,620 --> 00:10:49,200
一个叫less

464
00:10:49,200 --> 00:10:49,740
一个叫css

465
00:10:49,740 --> 00:10:50,560
那好了

466
00:10:50,560 --> 00:10:51,340
他会怎么样

467
00:10:51,340 --> 00:10:52,260
用这个loader

468
00:10:52,260 --> 00:10:53,580
完了和这个loader怎么样

469
00:10:53,580 --> 00:10:54,140
拼在一起

470
00:10:54,140 --> 00:10:55,360
完了再加上什么呢

471
00:10:55,360 --> 00:10:57,100
加上我们当前的这个文件

472
00:10:57,100 --> 00:10:57,640
也就这样

473
00:10:57,640 --> 00:10:59,580
in best.less

474
00:10:59,580 --> 00:11:00,720
这个呀

475
00:11:00,720 --> 00:11:02,900
就是我们所谓的叫剩余的请求

476
00:11:02,900 --> 00:11:04,620
那咱来看看是不是这样一个东西

477
00:11:04,620 --> 00:11:05,380
conalog

478
00:11:05,380 --> 00:11:06,300
完了里面呢

479
00:11:06,300 --> 00:11:07,460
我就把它打出来

480
00:11:07,460 --> 00:11:08,960
叫remending request

481
00:11:08,960 --> 00:11:11,960
那同样在这来一下

482
00:11:11,960 --> 00:11:14,800
你看是不是这样

483
00:11:14,800 --> 00:11:16,220
这是我们的绝对路径

484
00:11:16,220 --> 00:11:16,600
是吧

485
00:11:16,600 --> 00:11:17,420
css loader

486
00:11:17,420 --> 00:11:18,860
css loader完了之后

487
00:11:18,860 --> 00:11:19,760
是不是less loader

488
00:11:19,760 --> 00:11:21,360
之后是不是就当前文件的less

489
00:11:21,360 --> 00:11:22,920
那说明就ok了

490
00:11:22,920 --> 00:11:24,720
那是不是有剩余的结果怎么样呢

491
00:11:24,720 --> 00:11:25,920
我现在需要干嘛呢

492
00:11:25,920 --> 00:11:26,520
就是哦

493
00:11:26,520 --> 00:11:27,260
我可以这样

494
00:11:27,260 --> 00:11:28,640
让什么呢

495
00:11:28,640 --> 00:11:30,920
让这个style loader

496
00:11:30,920 --> 00:11:31,520
对不对

497
00:11:31,520 --> 00:11:32,280
去怎么样呢

498
00:11:32,280 --> 00:11:32,520
去

499
00:11:32,520 --> 00:11:34,140
去处理

500
00:11:34,140 --> 00:11:35,460
去处理

501
00:11:35,460 --> 00:11:37,700
这样一个loader

502
00:11:37,700 --> 00:11:37,940
是吧

503
00:11:37,940 --> 00:11:38,740
处理这样一个结果

504
00:11:38,740 --> 00:11:39,880
那怎么去做呢

505
00:11:39,880 --> 00:11:40,780
非常简单啊

506
00:11:40,780 --> 00:11:41,880
其实代码核心的

507
00:11:41,880 --> 00:11:42,740
还是不变

508
00:11:42,740 --> 00:11:43,500
差在哪呢

509
00:11:43,500 --> 00:11:44,260
差在这呢

510
00:11:44,260 --> 00:11:46,040
这里面我不再这样写了

511
00:11:46,040 --> 00:11:46,820
我怎么去写呢

512
00:11:46,820 --> 00:11:47,400
我这样去写

513
00:11:47,400 --> 00:11:48,500
我需要怎么样

514
00:11:48,500 --> 00:11:50,340
是不是让拿这个loader去加载

515
00:11:50,340 --> 00:11:51,400
那怎么做呢

516
00:11:51,400 --> 00:11:51,860
我说可以这样

517
00:11:51,860 --> 00:11:52,580
来个叫什么呢

518
00:11:52,580 --> 00:11:53,460
叫requare

519
00:11:53,460 --> 00:11:55,420
requare谁呢

520
00:11:55,420 --> 00:11:56,120
到了辅大括号

521
00:11:56,120 --> 00:11:57,880
完了把它怎么样放在里面

522
00:11:57,880 --> 00:11:59,140
但这样写的意思是什么意思

523
00:11:59,140 --> 00:12:01,840
是不是相当于又去引用了我们这样一个loader

524
00:12:01,840 --> 00:12:02,580
是不是行内loader

525
00:12:02,580 --> 00:12:03,680
但是我们说了

526
00:12:03,680 --> 00:12:04,760
引行内loader的话

527
00:12:04,760 --> 00:12:06,560
是不是他一看又是来死文件

528
00:12:06,560 --> 00:12:08,800
是不是他又会走到什么styleloader

529
00:12:08,800 --> 00:12:09,940
什么cssloader这些

530
00:12:09,940 --> 00:12:10,940
是不是就死循环了

531
00:12:10,940 --> 00:12:13,200
你每次怎么样都不停的去这样一个文件

532
00:12:13,200 --> 00:12:14,600
那这肯定不对吧

533
00:12:14,600 --> 00:12:15,720
那我怎么做呢

534
00:12:15,720 --> 00:12:16,580
那非常简单了

535
00:12:16,580 --> 00:12:17,240
我需要干什么

536
00:12:17,240 --> 00:12:18,800
我需要干什么事来着

537
00:12:18,800 --> 00:12:20,260
还记不记得我这里面有什么

538
00:12:20,260 --> 00:12:21,700
两个碳号

539
00:12:21,700 --> 00:12:23,320
就这样来写的

540
00:12:23,320 --> 00:12:24,480
两碳号加上

541
00:12:24,480 --> 00:12:26,360
但是这样写还是不行

542
00:12:26,360 --> 00:12:27,000
为啥呢

543
00:12:27,000 --> 00:12:28,060
刚才我们看到了

544
00:12:28,060 --> 00:12:30,680
我们引的这什么C盘下的肯定有问题

545
00:12:30,680 --> 00:12:32,420
因为我要去怎么做呢

546
00:12:32,420 --> 00:12:33,240
我是不是需要这样

547
00:12:33,240 --> 00:12:34,420
Let's loader碳号

548
00:12:34,420 --> 00:12:35,020
我要下一个

549
00:12:35,020 --> 00:12:35,680
我要再怎么样

550
00:12:35,680 --> 00:12:37,000
这再碳号少个碳号

551
00:12:37,000 --> 00:12:37,580
打错了

552
00:12:37,580 --> 00:12:38,920
是这样啊

553
00:12:38,920 --> 00:12:39,560
应该用的什么

554
00:12:39,560 --> 00:12:40,320
是相对路径

555
00:12:40,320 --> 00:12:43,900
因为这东西是个绝对路径加了谈号肯定不是我想要的

556
00:12:43,900 --> 00:12:44,960
这时候怎么办呢

557
00:12:44,960 --> 00:12:45,720
我非常简单了

558
00:12:45,720 --> 00:12:46,300
我可以怎么样

559
00:12:46,300 --> 00:12:51,200
我还可以用刚才在一直没有用到库叫loader utils

560
00:12:51,200 --> 00:12:52,800
loader utils

561
00:12:52,800 --> 00:12:57,140
等于requare我们就直接引了叫loader utils

562
00:12:57,140 --> 00:12:58,440
完了我可以怎么做

563
00:12:58,440 --> 00:12:59,540
使用这样一个方法

564
00:12:59,540 --> 00:13:01,060
你把它稍微折一下

565
00:13:01,060 --> 00:13:03,120
这是我们大括号别折错了

566
00:13:03,120 --> 00:13:04,380
到这叫什么呢

567
00:13:04,380 --> 00:13:05,720
叫loader utils

568
00:13:05,720 --> 00:13:07,460
它里面有个方法叫什么

569
00:13:07,460 --> 00:13:08,880
叫string file request

570
00:13:08,880 --> 00:13:11,620
就是它可以把我们这样一个方法怎么样

571
00:13:11,620 --> 00:13:13,580
转这个路径转成相对路径

572
00:13:13,580 --> 00:13:15,580
这里面this就是它的一个参数

573
00:13:15,580 --> 00:13:17,020
后面就是一个绝对路径

574
00:13:17,020 --> 00:13:18,340
它会把这个绝对路径怎么样

575
00:13:18,340 --> 00:13:19,220
转成相对路径

576
00:13:19,220 --> 00:13:20,020
那好了

577
00:13:20,020 --> 00:13:21,040
那你想假如说

578
00:13:21,040 --> 00:13:22,440
我在这里面再去require

579
00:13:22,440 --> 00:13:23,800
是不是又会去走怎么样

580
00:13:23,800 --> 00:13:25,040
走我们剩下这loader

581
00:13:25,040 --> 00:13:26,060
有什么CSloader

582
00:13:26,060 --> 00:13:27,380
还有这个lessloader处理

583
00:13:27,380 --> 00:13:28,660
那处理完以后怎么样

584
00:13:28,660 --> 00:13:29,940
是把处理的结果怎么样

585
00:13:29,940 --> 00:13:31,120
插到音面上吧

586
00:13:31,120 --> 00:13:32,720
这样的话相当于怎么样

587
00:13:32,720 --> 00:13:34,940
是不是就不会按照以前的loader流程来走

588
00:13:34,940 --> 00:13:37,240
以前是style走完走less

589
00:13:37,240 --> 00:13:38,520
走这个css

590
00:13:38,520 --> 00:13:42,120
再走less 相当于这样的是吧 画个图

591
00:13:42,120 --> 00:13:44,520
这是我们的style

592
00:13:44,520 --> 00:13:47,420
这些画上是吧 这是style loader

593
00:13:47,420 --> 00:13:48,520
style loader

594
00:13:48,520 --> 00:13:50,520
style loader

595
00:13:50,520 --> 00:13:55,020
你可以认为它是什么是pitch是吧 这是pitch p-i-t-c-h

596
00:13:55,020 --> 00:13:59,620
同样的里面还有一个什么呢 这底下往下写是吧 这个叫我们的css loader

597
00:13:59,620 --> 00:14:03,120
我要这个叫我们的less loader

598
00:14:03,120 --> 00:14:06,520
完了里面我要怎么走啊 是不是底下还有一个normal

599
00:14:06,520 --> 00:14:07,760
这他对应的normal是吧

600
00:14:07,760 --> 00:14:09,360
那走的时候怎么走

601
00:14:09,360 --> 00:14:10,340
是不是只要一看

602
00:14:10,340 --> 00:14:12,020
有style loader的peach方法

603
00:14:12,020 --> 00:14:12,700
是不是就不走了

604
00:14:12,700 --> 00:14:13,640
那直接走哪去了

605
00:14:13,640 --> 00:14:14,860
是不是就在这里面开始执行

606
00:14:14,860 --> 00:14:16,340
那执行的时候怎么样

607
00:14:16,340 --> 00:14:17,680
是不是我在这里发现

608
00:14:17,680 --> 00:14:19,000
他又引用了什么

609
00:14:19,000 --> 00:14:20,440
是不是引用了less loader

610
00:14:20,440 --> 00:14:21,020
set loader

611
00:14:21,020 --> 00:14:21,600
他会怎么办

612
00:14:21,600 --> 00:14:22,280
他会怎么样

613
00:14:22,280 --> 00:14:23,960
跳过这所有的loader

614
00:14:23,960 --> 00:14:24,740
然后去执行什么

615
00:14:24,740 --> 00:14:26,100
是不是获取资源

616
00:14:26,100 --> 00:14:27,120
把这资源怎么样

617
00:14:27,120 --> 00:14:28,260
是不是通过他来处理

618
00:14:28,260 --> 00:14:29,560
通过他来处理

619
00:14:29,560 --> 00:14:30,580
我要把他怎么样

620
00:14:30,580 --> 00:14:32,280
set loader返回的结果

621
00:14:32,280 --> 00:14:34,460
是不是插到我inner team要里去

622
00:14:34,460 --> 00:14:35,500
那我一require

623
00:14:35,500 --> 00:14:37,700
require回来的是不是就是那个字不串啊

624
00:14:37,700 --> 00:14:38,600
我在写个主事是吧

625
00:14:38,600 --> 00:14:41,300
就是这里面require路径

626
00:14:41,300 --> 00:14:42,100
对吧

627
00:14:42,100 --> 00:14:42,900
返回的

628
00:14:42,900 --> 00:14:44,540
返回的对吧

629
00:14:44,540 --> 00:14:48,020
返回的就是cssloader

630
00:14:48,020 --> 00:14:50,160
处理好的结果

631
00:14:50,160 --> 00:14:53,140
处理好的结果

632
00:14:53,140 --> 00:14:54,060
那也就是什么

633
00:14:54,060 --> 00:14:54,960
也就是我们这样

634
00:14:54,960 --> 00:14:56,760
require是不是引了那个什么

635
00:14:56,760 --> 00:14:57,820
比如说某个

636
00:14:57,820 --> 00:14:59,600
就index ls是吧

637
00:14:59,600 --> 00:15:00,680
那引完以后怎么样

638
00:15:00,680 --> 00:15:02,400
是不是他前面会加上什么

639
00:15:02,400 --> 00:15:04,000
我这就在那写下了

640
00:15:04,000 --> 00:15:05,620
里面有cssloader

641
00:15:05,620 --> 00:15:07,120
完了还有我们碳号对吧

642
00:15:07,120 --> 00:15:08,620
这个叫lessloader

643
00:15:08,620 --> 00:15:10,760
完了这里面一用的时候还不要走以前的是吧

644
00:15:10,760 --> 00:15:11,420
是不是加了分号

645
00:15:11,420 --> 00:15:12,180
加了碳号啊

646
00:15:12,180 --> 00:15:14,820
那这样的话是不是最后处理完以后

647
00:15:14,820 --> 00:15:15,680
哦写法栏

648
00:15:15,680 --> 00:15:16,320
这是less

649
00:15:16,320 --> 00:15:17,100
这是css

650
00:15:17,100 --> 00:15:18,360
那最后处理完以后

651
00:15:18,360 --> 00:15:22,580
这个cssloader返回的是不是一个module.exepost等一个字不串

652
00:15:22,580 --> 00:15:24,100
那我在这里面一require

653
00:15:24,100 --> 00:15:26,640
是不是就把那个module拿到的结果返回来了

654
00:15:26,640 --> 00:15:28,740
所以就把我刚才拼的这个

655
00:15:28,740 --> 00:15:29,760
就这个字不串

656
00:15:29,760 --> 00:15:31,020
哪去了

657
00:15:31,020 --> 00:15:31,580
找不到了

658
00:15:31,580 --> 00:15:34,200
应该就是这个字符串是吧

659
00:15:34,200 --> 00:15:35,060
把这字符拿到

660
00:15:35,060 --> 00:15:36,220
那拿到以后呢

661
00:15:36,220 --> 00:15:38,080
是不是就是background的什么什么都拿到了

662
00:15:38,080 --> 00:15:40,380
拿到以后是不是就塞到我们的这个itml里去了

663
00:15:40,380 --> 00:15:41,300
最后怎么样

664
00:15:41,300 --> 00:15:42,340
把它插入到头部

665
00:15:42,340 --> 00:15:43,100
那这样的话

666
00:15:43,100 --> 00:15:44,560
我们的功能就实现了

667
00:15:44,560 --> 00:15:45,840
那再来试试啊

668
00:15:45,840 --> 00:15:47,420
看看能不能无愿以偿

669
00:15:47,420 --> 00:15:47,780
是吧

670
00:15:47,780 --> 00:15:48,540
跑一下

671
00:15:48,540 --> 00:15:51,180
OK

672
00:15:51,180 --> 00:15:52,540
完了我刷新一下是吧

673
00:15:52,540 --> 00:15:53,000
走你

674
00:15:53,000 --> 00:15:55,120
看看现在这个背景图是不是也出来了

675
00:15:55,120 --> 00:15:55,940
而且怎么样

676
00:15:55,940 --> 00:15:57,760
确实已经变成我想要的那个结果了

677
00:15:57,760 --> 00:15:58,560
我来看看吧

678
00:15:58,560 --> 00:15:59,560
是不是这样啊

679
00:15:59,560 --> 00:16:00,560
我再证明一点

680
00:16:00,560 --> 00:16:01,020
在这里

681
00:16:01,020 --> 00:16:01,940
是不是你看

682
00:16:01,940 --> 00:16:03,220
是不是我叔祖放在这了

683
00:16:03,220 --> 00:16:04,660
是把这猫的炮子放在这了

684
00:16:04,660 --> 00:16:06,940
那最后是不是就把这个东西直接拿过来了

685
00:16:06,940 --> 00:16:07,600
好

686
00:16:07,600 --> 00:16:08,580
那这样呢

687
00:16:08,580 --> 00:16:11,460
我们这样一个完整的CSLoader就实现了

