1
00:00:00,000 --> 00:00:02,180
本节呢

2
00:00:02,180 --> 00:00:05,400
我们来讲一下我们的这个YPAC中的一个热更新操作

3
00:00:05,400 --> 00:00:06,600
什么叫热更新呢

4
00:00:06,600 --> 00:00:08,360
在我们以往的开发中啊

5
00:00:08,360 --> 00:00:10,140
每次更改页面都会怎么样

6
00:00:10,140 --> 00:00:11,240
导致页面重新刷新

7
00:00:11,240 --> 00:00:12,500
那我希望怎么样

8
00:00:12,500 --> 00:00:13,840
是不是只更新某一部分

9
00:00:13,840 --> 00:00:15,460
比如当这个组件更新了

10
00:00:15,460 --> 00:00:16,480
我只更新这个组件

11
00:00:16,480 --> 00:00:17,520
那这时候呢

12
00:00:17,520 --> 00:00:19,780
我们就可以用我们的这样一个叫热更新操作

13
00:00:19,780 --> 00:00:20,680
我们可以啊

14
00:00:20,680 --> 00:00:22,480
现在我们这样一个开发服务上啊

15
00:00:22,480 --> 00:00:23,740
配上一个耗的处

16
00:00:23,740 --> 00:00:24,560
表示哎

17
00:00:24,560 --> 00:00:25,360
启用什么

18
00:00:25,360 --> 00:00:26,000
启用

19
00:00:26,000 --> 00:00:27,180
热更新

20
00:00:27,180 --> 00:00:29,000
那同样啊

21
00:00:29,000 --> 00:00:29,900
我们可以在上上找

22
00:00:29,900 --> 00:00:33,240
找找找这里面我可以再加上这样一个插件来做支持

23
00:00:33,240 --> 00:00:33,960
比如说new

24
00:00:33,960 --> 00:00:36,240
这是webpack的一个叫热更新插件

25
00:00:36,240 --> 00:00:38,860
叫hot module replacements plugin

26
00:00:38,860 --> 00:00:42,700
并且如果你想跟他说哪个模块更新了

27
00:00:42,700 --> 00:00:42,900
对吧

28
00:00:42,900 --> 00:00:43,940
哪个模块热更新

29
00:00:43,940 --> 00:00:45,880
我还可以用一个叫name的

30
00:00:45,880 --> 00:00:46,400
对吧

31
00:00:46,400 --> 00:00:47,360
module plugin

32
00:00:47,360 --> 00:00:47,920
就这个吧

33
00:00:47,920 --> 00:00:49,420
叫name modules plugin

34
00:00:49,420 --> 00:00:53,140
他可以帮我们告诉我哪个模块更新了

35
00:00:53,140 --> 00:00:53,400
是吧

36
00:00:53,400 --> 00:00:53,920
好

37
00:00:53,920 --> 00:00:54,900
这是两个插件

38
00:00:54,900 --> 00:00:57,560
这个叫打印

39
00:00:57,560 --> 00:01:00,140
更新的模块路径

40
00:01:00,140 --> 00:01:02,620
完了下面叫什么

41
00:01:02,620 --> 00:01:04,040
叫热更新对吧

42
00:01:04,040 --> 00:01:05,100
热更新插件

43
00:01:05,100 --> 00:01:07,540
好了

44
00:01:07,540 --> 00:01:08,820
我在下面的同样

45
00:01:08,820 --> 00:01:10,860
把我们的webpack配置一下

46
00:01:10,860 --> 00:01:12,920
回到我们的index里面来

47
00:01:12,920 --> 00:01:14,680
把代码注视一下

48
00:01:14,680 --> 00:01:16,340
我们现在不用拦加载了

49
00:01:16,340 --> 00:01:21,600
这里面我们就去直接引入str from第二个sauce

50
00:01:21,600 --> 00:01:23,820
这里面我们可以去看一件事

51
00:01:23,820 --> 00:01:24,080
对吧

52
00:01:24,080 --> 00:01:24,900
console log str

53
00:01:24,900 --> 00:01:25,680
你发现

54
00:01:25,680 --> 00:01:27,980
其实这时候的热更新还不会起作用

55
00:01:27,980 --> 00:01:29,620
我们来启动一下

56
00:01:29,620 --> 00:01:34,440
在这里应该叫npm run dv

57
00:01:34,440 --> 00:01:36,100
打包

58
00:01:36,100 --> 00:01:37,080
稍等一下

59
00:01:37,080 --> 00:01:40,400
这样的话就会帮我们启动这样一个服务3000

60
00:01:40,400 --> 00:01:42,840
完了里面我们看看结果日志

61
00:01:42,840 --> 00:01:45,040
log console

62
00:01:45,040 --> 00:01:48,020
我每次改代码

63
00:01:48,020 --> 00:01:49,180
你会发现它还是怎么样

64
00:01:49,180 --> 00:01:50,000
在频繁的刷新

65
00:01:50,000 --> 00:01:51,300
你看我把它删掉

66
00:01:51,300 --> 00:01:52,340
这删掉是吧

67
00:01:52,340 --> 00:01:54,580
完了里面我就去改一下文件

68
00:01:54,580 --> 00:01:55,840
比如说改sauce了

69
00:01:55,840 --> 00:01:57,320
然后改个1保存

70
00:01:57,320 --> 00:01:59,260
你发现这边会重新编译

71
00:01:59,260 --> 00:02:00,520
然后这边也会怎么样

72
00:02:00,520 --> 00:02:01,220
刷新页面

73
00:02:01,220 --> 00:02:03,120
好像怪怪的

74
00:02:03,120 --> 00:02:04,260
你看是不是在刷新页面

75
00:02:04,260 --> 00:02:05,720
在这之后再改一下2

76
00:02:05,720 --> 00:02:07,340
这时候是吧

77
00:02:07,340 --> 00:02:08,880
这个地方会转一下

78
00:02:08,880 --> 00:02:12,040
是不是傻了一下

79
00:02:12,040 --> 00:02:13,780
说明真的是强制更新

80
00:02:13,780 --> 00:02:15,220
那我希望是怎么样

81
00:02:15,220 --> 00:02:15,940
是不是热更新

82
00:02:15,940 --> 00:02:17,000
什么叫热更新呢

83
00:02:17,000 --> 00:02:18,380
就是说我们

84
00:02:18,380 --> 00:02:19,500
比如说改这个代码

85
00:02:19,500 --> 00:02:20,460
同样的

86
00:02:20,460 --> 00:02:21,780
我可以这样写

87
00:02:21,780 --> 00:02:24,520
如果当前这样一个模块

88
00:02:24,520 --> 00:02:25,040
对吧

89
00:02:25,040 --> 00:02:25,880
支持要更新

90
00:02:25,880 --> 00:02:26,280
那好

91
00:02:26,280 --> 00:02:27,100
我可以在这怎么样

92
00:02:27,100 --> 00:02:27,960
叫accept

93
00:02:27,960 --> 00:02:30,080
叫module.hot

94
00:02:30,080 --> 00:02:31,200
叫accept

95
00:02:31,200 --> 00:02:32,480
比如这里呢

96
00:02:32,480 --> 00:02:33,320
我可以放上这个路径

97
00:02:33,320 --> 00:02:34,840
比如说当这个路径

98
00:02:34,840 --> 00:02:35,180
对吧

99
00:02:35,180 --> 00:02:36,580
或者这个模块更新了

100
00:02:36,580 --> 00:02:37,960
我要执行某个方法

101
00:02:37,960 --> 00:02:38,620
来告诉他

102
00:02:38,620 --> 00:02:39,740
比如说coslog

103
00:02:39,740 --> 00:02:41,820
比如说文件更新

104
00:02:41,820 --> 00:02:44,600
文件对吧

105
00:02:44,600 --> 00:02:45,480
更新了

106
00:02:45,480 --> 00:02:46,960
更新了

107
00:02:46,960 --> 00:02:47,680
看看效果

108
00:02:47,680 --> 00:02:48,220
这时候呢

109
00:02:48,220 --> 00:02:49,260
我再来放在这

110
00:02:49,260 --> 00:02:50,180
那这里面呢

111
00:02:50,180 --> 00:02:50,800
我刷新一下

112
00:02:50,800 --> 00:02:53,600
他说module.hot.accept

113
00:02:53,600 --> 00:02:54,360
拼错了

114
00:02:54,360 --> 00:02:56,560
ACCEPT是吧刷新

115
00:02:56,560 --> 00:02:59,460
这里面先注掉啊

116
00:02:59,460 --> 00:03:01,080
我现在呢去改我们的代码

117
00:03:01,080 --> 00:03:03,880
比如说把sauce的一二改成一二三保存

118
00:03:03,880 --> 00:03:06,180
这时候我改完了以后对吧

119
00:03:06,180 --> 00:03:07,340
会告诉我更新成功

120
00:03:07,340 --> 00:03:08,720
并且呢这页面会不会刷新呢

121
00:03:08,720 --> 00:03:09,360
你看没有刷新

122
00:03:09,360 --> 00:03:11,360
而且呢告诉我是不是文件更新了

123
00:03:11,360 --> 00:03:13,540
这就是我们那个内容猫就的作用

124
00:03:13,540 --> 00:03:15,380
对吧告诉我A是这个路径更新了

125
00:03:15,380 --> 00:03:17,480
那同样吧这个路径更新以后呢

126
00:03:17,480 --> 00:03:18,760
我们可以怎么样干我想干的事

127
00:03:18,760 --> 00:03:20,180
比如说在这里面怎么样

128
00:03:20,180 --> 00:03:22,560
我再重新引用一下这个最新的代码

129
00:03:22,560 --> 00:03:23,160
这块儿

130
00:03:23,160 --> 00:03:24,100
又快点谁呢

131
00:03:24,100 --> 00:03:25,740
比如就这个src下的sauce

132
00:03:25,740 --> 00:03:27,820
那为什么不能用import

133
00:03:27,820 --> 00:03:29,800
因为import只能写在页面的顶端

134
00:03:29,800 --> 00:03:32,520
那这里面我就可以拿到这样一个资源

135
00:03:32,520 --> 00:03:34,240
往上再去打印这个资源

136
00:03:34,240 --> 00:03:35,440
那这样的话

137
00:03:35,440 --> 00:03:36,460
是我每次更新的话

138
00:03:36,460 --> 00:03:38,440
就可以重新启用这样一个模块

139
00:03:38,440 --> 00:03:39,800
并且怎么样实时更新

140
00:03:39,800 --> 00:03:40,520
来看看效果

141
00:03:40,520 --> 00:03:41,980
我这里面我一样

142
00:03:41,980 --> 00:03:43,320
是不是现在是猪培训加3

143
00:03:43,320 --> 00:03:44,880
给我来个4保存

144
00:03:44,880 --> 00:03:45,920
那这时候呢

145
00:03:45,920 --> 00:03:47,060
是不是马上就会实时更新

146
00:03:47,060 --> 00:03:48,300
告诉我这4怎么样

147
00:03:48,300 --> 00:03:49,460
是不是应该更新了

148
00:03:49,460 --> 00:03:50,940
哦好像有点问题

149
00:03:50,940 --> 00:03:51,500
等着是吧

150
00:03:51,500 --> 00:03:52,300
你看是4来了

151
00:03:52,300 --> 00:03:53,220
再来个5是吧

152
00:03:53,220 --> 00:03:53,820
保存

153
00:03:53,820 --> 00:03:55,480
这时候我的页面其实怎么样

154
00:03:55,480 --> 00:03:56,440
并没有刷新

155
00:03:56,440 --> 00:03:56,980
而是怎么样

156
00:03:56,980 --> 00:03:57,180
是不是

157
00:03:57,180 --> 00:03:58,700
增量再增量更新

158
00:03:58,700 --> 00:03:59,740
如果增了一个

159
00:03:59,740 --> 00:04:00,440
是不是就自己更新

160
00:04:00,440 --> 00:04:01,520
你看是不是就可以了

161
00:04:01,520 --> 00:04:03,460
现在我们就知道了

162
00:04:03,460 --> 00:04:05,020
热更新的配置方式

163
00:04:05,020 --> 00:04:05,520
好

