1
00:00:00,000 --> 00:00:00,880
好

2
00:00:00,880 --> 00:00:02,860
那我们接下来在讲上最后一个

3
00:00:02,860 --> 00:00:04,640
这个就像我们的什么叫

4
00:00:04,640 --> 00:00:06,000
一步串行

5
00:00:06,000 --> 00:00:07,800
完了瀑布钩子函数

6
00:00:07,800 --> 00:00:08,680
什么意思呢

7
00:00:08,680 --> 00:00:10,720
就是说我们一步之间有关系了

8
00:00:10,720 --> 00:00:12,160
那这时候还是和以前一样

9
00:00:12,160 --> 00:00:13,860
我们需要先取出第一步

10
00:00:13,860 --> 00:00:14,520
那再执行

11
00:00:14,520 --> 00:00:16,000
把执行的结果怎么样

12
00:00:16,000 --> 00:00:17,340
串给下一个一步的回调

13
00:00:17,340 --> 00:00:19,240
那这里我们就来写一下

14
00:00:19,240 --> 00:00:21,240
同样把代码稍微改造一下

15
00:00:21,240 --> 00:00:23,300
这里名字也不要了

16
00:00:23,300 --> 00:00:23,540
对吧

17
00:00:23,540 --> 00:00:25,240
叫a sink series

18
00:00:25,240 --> 00:00:28,500
a sink series waterfall hook

19
00:00:28,500 --> 00:00:30,140
同样我们new的时候

20
00:00:30,140 --> 00:00:31,340
new它就可以了

21
00:00:31,340 --> 00:00:32,460
往来这里我们说了

22
00:00:32,460 --> 00:00:33,400
它不叫promise

23
00:00:33,400 --> 00:00:35,280
我们先写简单的叫个think

24
00:00:35,280 --> 00:00:37,100
好 这也一样type of think

25
00:00:37,100 --> 00:00:38,400
同样这也是

26
00:00:38,400 --> 00:00:41,580
这应该叫call of think 是吧

27
00:00:41,580 --> 00:00:44,900
往来这里我们也可以直接不用去send

28
00:00:44,900 --> 00:00:46,280
拿到回调中的结果

29
00:00:46,280 --> 00:00:49,080
当然了第一个人成功以后

30
00:00:49,080 --> 00:00:50,220
可能他会调什么呢

31
00:00:50,220 --> 00:00:50,780
调这个cb

32
00:00:50,780 --> 00:00:51,800
这稍微改造一下

33
00:00:51,800 --> 00:00:53,100
没有promise了

34
00:00:53,100 --> 00:00:53,540
就删掉

35
00:00:53,540 --> 00:00:55,440
来一个叫cb

36
00:00:55,440 --> 00:00:57,360
我们这里会调cb

37
00:00:57,360 --> 00:00:59,060
这里面顺便说一下

38
00:00:59,060 --> 00:01:00,720
还记得刚才我们说的那个bellhook

39
00:01:00,720 --> 00:01:03,360
就是说他如果想中断的话

40
00:01:03,360 --> 00:01:04,220
这里可以传参

41
00:01:04,220 --> 00:01:06,000
第一个参数就是错误

42
00:01:06,000 --> 00:01:07,720
比如说这叫error出错了

43
00:01:07,720 --> 00:01:11,020
同样第二个就是他要传给下一个人的结果

44
00:01:11,020 --> 00:01:12,460
比如说result

45
00:01:12,460 --> 00:01:15,760
但是同样如果这里面已经error了

46
00:01:15,760 --> 00:01:16,300
那就怎么样

47
00:01:16,300 --> 00:01:17,980
就不会把这个结果出给下一个了

48
00:01:17,980 --> 00:01:18,480
你报错了

49
00:01:18,480 --> 00:01:20,120
当然了什么事会传

50
00:01:20,120 --> 00:01:21,360
比如这里面我写个no

51
00:01:21,360 --> 00:01:22,680
没错误

52
00:01:22,680 --> 00:01:23,680
这个结果会怎么样

53
00:01:23,680 --> 00:01:25,960
传给下一个人的什么数据

54
00:01:25,960 --> 00:01:27,960
这里同样我可以拿到这个data

55
00:01:27,960 --> 00:01:28,960
那new promise

56
00:01:28,960 --> 00:01:30,400
我有标了是吧

57
00:01:30,400 --> 00:01:32,800
完成功以后应该掉我们的cb

58
00:01:32,800 --> 00:01:35,400
看看效果是不是这样的

59
00:01:35,400 --> 00:01:36,200
又坚乱

60
00:01:36,200 --> 00:01:38,640
这里我们可以拿到最终的结果

61
00:01:38,640 --> 00:01:41,280
你看我好像这个时间很快

62
00:01:41,280 --> 00:01:42,280
应该叫type think

63
00:01:42,280 --> 00:01:43,320
没错是不是

64
00:01:43,320 --> 00:01:44,920
我没有写时间

65
00:01:44,920 --> 00:01:46,000
来个一秒

66
00:01:46,000 --> 00:01:46,880
这来个两秒

67
00:01:46,880 --> 00:01:47,960
来个一秒吧

68
00:01:47,960 --> 00:01:50,360
上来以后应该是第一个

69
00:01:50,360 --> 00:01:51,720
第二个第三个是吧

70
00:01:51,720 --> 00:01:52,320
ok

71
00:01:52,320 --> 00:01:54,680
是不是上个人的result

72
00:01:54,680 --> 00:01:55,960
是下一个人的什么

73
00:01:55,960 --> 00:01:56,880
输入

74
00:01:56,880 --> 00:01:57,640
那这时候啊

75
00:01:57,640 --> 00:01:58,840
比如说我在这里面呢

76
00:01:58,840 --> 00:02:00,080
放了一个irror

77
00:02:00,080 --> 00:02:00,720
那说明怎么样

78
00:02:00,720 --> 00:02:01,720
是不是报错了

79
00:02:01,720 --> 00:02:02,720
那报错的话呀

80
00:02:02,720 --> 00:02:03,960
那好了直接走哪去

81
00:02:03,960 --> 00:02:05,520
是不是会跳过这个钩子

82
00:02:05,520 --> 00:02:06,360
再去怎么样

83
00:02:06,360 --> 00:02:07,640
执行最后这个钩子

84
00:02:07,640 --> 00:02:08,600
我在这乱一下

85
00:02:08,600 --> 00:02:10,720
还是不是这样

86
00:02:10,720 --> 00:02:11,960
你看是不是上来以后

87
00:02:11,960 --> 00:02:13,000
直接走到第一个

88
00:02:13,000 --> 00:02:13,120
哎

89
00:02:13,120 --> 00:02:13,640
报错了

90
00:02:13,640 --> 00:02:14,720
直接走哪去了

91
00:02:14,720 --> 00:02:15,840
那好了

92
00:02:15,840 --> 00:02:16,840
那咱来看看吧

93
00:02:16,840 --> 00:02:17,480
这个方法呢

94
00:02:17,480 --> 00:02:18,400
其实名字挺长

95
00:02:18,400 --> 00:02:19,680
但是也比较简单

96
00:02:19,680 --> 00:02:21,760
也是基于我们刚才写过那个圆嘛

97
00:02:21,760 --> 00:02:22,240
这里呢

98
00:02:22,240 --> 00:02:23,520
把它拿过来

99
00:02:23,520 --> 00:02:24,840
kiss

100
00:02:24,840 --> 00:02:25,680
名字改了

101
00:02:25,680 --> 00:02:26,580
这里也改了

102
00:02:26,580 --> 00:02:27,140
那这里呢

103
00:02:27,140 --> 00:02:28,980
我就直接先改成type和sync

104
00:02:28,980 --> 00:02:33,160
这里一样叫promise

105
00:02:33,160 --> 00:02:34,420
叫不是叫promise

106
00:02:34,420 --> 00:02:35,820
叫call和sync

107
00:02:35,820 --> 00:02:38,040
这里呢把它删掉

108
00:02:38,040 --> 00:02:40,460
同样这里呢

109
00:02:40,460 --> 00:02:41,960
我也把这个promise就干掉了

110
00:02:41,960 --> 00:02:43,480
我要来个cb

111
00:02:43,480 --> 00:02:44,800
cb呢哪来的呢

112
00:02:44,800 --> 00:02:46,460
是穿三传过来的

113
00:02:46,460 --> 00:02:47,260
参数呢

114
00:02:47,260 --> 00:02:47,920
第一个是错误

115
00:02:47,920 --> 00:02:48,800
我们可以写个no

116
00:02:48,800 --> 00:02:50,440
第二个呢是我们的结果

117
00:02:50,440 --> 00:02:51,040
对吧

118
00:02:51,040 --> 00:02:51,440
结果

119
00:02:51,440 --> 00:02:53,360
那这个结果呢

120
00:02:53,360 --> 00:02:54,800
会传给下一个的date

121
00:02:54,800 --> 00:02:56,280
同样他也有CB

122
00:02:56,280 --> 00:02:59,200
并且呢我们这个new promise也不要了

123
00:02:59,200 --> 00:03:00,360
完了成功后呢一样

124
00:03:00,360 --> 00:03:02,240
我们会去调我们当前的CB

125
00:03:02,240 --> 00:03:03,860
完了如果没有错误的话

126
00:03:03,860 --> 00:03:04,840
好你也得传个闹

127
00:03:04,840 --> 00:03:07,180
那这个最终呢就会走向这个方法

128
00:03:07,180 --> 00:03:08,860
那咱来看看吧

129
00:03:08,860 --> 00:03:10,140
这不叫promise也改一下

130
00:03:10,140 --> 00:03:11,900
叫call or sync是吧

131
00:03:11,900 --> 00:03:14,540
这个呢叫我们的type or sync

132
00:03:14,540 --> 00:03:16,580
type or sync

133
00:03:16,580 --> 00:03:19,500
完了成功以后呢我们可以来个回调

134
00:03:19,500 --> 00:03:22,660
OK那咱来看看吧

135
00:03:22,660 --> 00:03:24,340
那这个方法怎么显得一样

136
00:03:24,340 --> 00:03:26,940
我们需要先去迭代我们的一部

137
00:03:26,940 --> 00:03:28,100
那一部迭代的话

138
00:03:28,100 --> 00:03:29,380
肯定需要一个中间函数

139
00:03:29,380 --> 00:03:30,360
叫NES函数

140
00:03:30,360 --> 00:03:32,260
这时候现在见到函数了

141
00:03:32,260 --> 00:03:34,240
完了并且我们需要干嘛

142
00:03:34,240 --> 00:03:37,540
是不是拿到第一个人的什么执行的结果

143
00:03:37,540 --> 00:03:38,560
那他执行的时候

144
00:03:38,560 --> 00:03:39,640
我们是不是需要怎么样

145
00:03:39,640 --> 00:03:40,860
先拿出第一个执行

146
00:03:40,860 --> 00:03:42,820
传给他一个自定义的函数

147
00:03:42,820 --> 00:03:43,120
好了

148
00:03:43,120 --> 00:03:44,140
那自定义函数就是谁

149
00:03:44,140 --> 00:03:44,700
就是这个NES

150
00:03:44,700 --> 00:03:46,620
那NES参数分别是什么

151
00:03:46,620 --> 00:03:47,500
第一个是Error

152
00:03:47,500 --> 00:03:49,200
第二个就是我们这个Date

153
00:03:49,200 --> 00:03:51,460
那现在我们就可以怎么样

154
00:03:51,460 --> 00:03:52,200
拿到第一个

155
00:03:52,200 --> 00:03:53,580
第一个的话Index是0

156
00:03:53,580 --> 00:03:54,980
我们可以在这里呢

157
00:03:54,980 --> 00:03:57,520
去取this.tasks

158
00:03:57,520 --> 00:03:58,800
完了翻过号什么呢

159
00:03:58,800 --> 00:03:59,460
index

160
00:03:59,460 --> 00:04:01,520
相当于就是取到了哪个

161
00:04:01,520 --> 00:04:02,160
这个函数

162
00:04:02,160 --> 00:04:03,520
那这个函数里面

163
00:04:03,520 --> 00:04:05,240
我给它起个名字叫task

164
00:04:05,240 --> 00:04:06,920
那当然了

165
00:04:06,920 --> 00:04:08,300
有没有可能我去取不到

166
00:04:08,300 --> 00:04:09,920
相当于没有注册

167
00:04:09,920 --> 00:04:11,240
那没有注册的话怎么样

168
00:04:11,240 --> 00:04:12,260
是不是直接走错就好了

169
00:04:12,260 --> 00:04:13,840
所以这里面我就判断一下

170
00:04:13,840 --> 00:04:15,620
如果task对吧

171
00:04:15,620 --> 00:04:16,140
没有

172
00:04:16,140 --> 00:04:17,120
那就怎么样

173
00:04:17,120 --> 00:04:18,000
就告诉了return

174
00:04:18,000 --> 00:04:18,820
走哪个呢

175
00:04:18,820 --> 00:04:21,420
是不是走当前args最后的

176
00:04:21,420 --> 00:04:22,560
那个final callback

177
00:04:22,560 --> 00:04:26,140
然后我同样把这个final callback拿出来

178
00:04:26,140 --> 00:04:29,400
等于args的什么pop

179
00:04:29,400 --> 00:04:31,800
完了这时候呢

180
00:04:31,800 --> 00:04:34,160
我们可以直接用final callback执行

181
00:04:34,160 --> 00:04:36,480
完了把我们当前的这个传进去

182
00:04:36,480 --> 00:04:36,760
是吧

183
00:04:36,760 --> 00:04:38,060
不用传三了

184
00:04:38,060 --> 00:04:39,380
这也就都做最后一个了

185
00:04:39,380 --> 00:04:40,480
那同样啊

186
00:04:40,480 --> 00:04:41,620
如果要是取出来的呢

187
00:04:41,620 --> 00:04:42,300
那说明怎么样

188
00:04:42,300 --> 00:04:42,960
是不是有啊

189
00:04:42,960 --> 00:04:44,300
那有的话我要干嘛呢

190
00:04:44,300 --> 00:04:45,320
是不是干这件事

191
00:04:45,320 --> 00:04:46,300
看看他当前啊

192
00:04:46,300 --> 00:04:47,740
是第一个的话我怎么样

193
00:04:47,740 --> 00:04:49,400
是不是需要先传一个什么

194
00:04:49,400 --> 00:04:52,060
内幕是我们当前这个剩下的参数

195
00:04:52,060 --> 00:04:53,960
后面可能就是data了

196
00:04:53,960 --> 00:04:55,680
所以这我要区分一下

197
00:04:55,680 --> 00:04:56,760
怎么区分

198
00:04:56,760 --> 00:04:58,080
我就可以这样做判断

199
00:04:58,080 --> 00:04:59,920
如果index等等几

200
00:04:59,920 --> 00:05:00,540
0

201
00:05:00,540 --> 00:05:01,640
说明什么

202
00:05:01,640 --> 00:05:03,640
是不是相当于执行的是

203
00:05:03,640 --> 00:05:05,360
第一个

204
00:05:05,360 --> 00:05:06,780
第一个函数

205
00:05:06,780 --> 00:05:08,340
我们希望让tass执行

206
00:05:08,340 --> 00:05:09,140
执行第一个

207
00:05:09,140 --> 00:05:09,900
传的是name

208
00:05:09,900 --> 00:05:12,000
name指的就是我们刚才的参数

209
00:05:12,000 --> 00:05:13,760
叫dll.args

210
00:05:13,760 --> 00:05:16,660
dll.args

211
00:05:16,660 --> 00:05:20,220
后面的cb指的就是我们所谓的index函数

212
00:05:20,220 --> 00:05:21,260
好了

213
00:05:21,260 --> 00:05:22,840
那如果它不是第一次呢

214
00:05:22,840 --> 00:05:24,240
那不是第一次就更方便了

215
00:05:24,240 --> 00:05:25,240
那我们需要干什么事

216
00:05:25,240 --> 00:05:27,320
是不是应该走的是下面这个

217
00:05:27,320 --> 00:05:28,080
那这个呢

218
00:05:28,080 --> 00:05:29,400
就应该是上面这个人的什么

219
00:05:29,400 --> 00:05:30,800
是返回结果了

220
00:05:30,800 --> 00:05:32,000
那这个结果怎么来的呢

221
00:05:32,000 --> 00:05:32,980
是不是跑到这个data里去了

222
00:05:32,980 --> 00:05:34,900
那我下次传的就应该是data

223
00:05:34,900 --> 00:05:35,220
对吧

224
00:05:35,220 --> 00:05:35,880
task

225
00:05:35,880 --> 00:05:38,400
完了就要传的是data

226
00:05:38,400 --> 00:05:39,900
后面有一个nest

227
00:05:39,900 --> 00:05:42,840
那每次迭代完以后

228
00:05:42,840 --> 00:05:44,100
我需要的所以你干嘛呀

229
00:05:44,100 --> 00:05:45,180
是不是加价

230
00:05:45,180 --> 00:05:46,680
当然了上来以后

231
00:05:46,680 --> 00:05:47,620
我肯定要先怎么样

232
00:05:47,620 --> 00:05:48,520
掉一次

233
00:05:48,520 --> 00:05:49,320
OK

234
00:05:49,320 --> 00:05:50,380
来看看这个效果

235
00:05:50,380 --> 00:05:50,920
哦不OK啊

236
00:05:50,920 --> 00:05:51,300
运行

237
00:05:51,300 --> 00:05:52,720
这时候呢

238
00:05:52,720 --> 00:05:52,960
哦

239
00:05:52,960 --> 00:05:54,480
第一个right对吧

240
00:05:54,480 --> 00:05:54,640
哦

241
00:05:54,640 --> 00:05:55,340
爆错了是吧

242
00:05:55,340 --> 00:05:56,440
内幕没找到

243
00:05:56,440 --> 00:05:58,120
因为这个名字可能没改啊

244
00:05:58,120 --> 00:05:59,040
找一下这里

245
00:05:59,040 --> 00:06:00,880
修改一下

246
00:06:00,880 --> 00:06:02,780
运行一下是吧

247
00:06:02,780 --> 00:06:03,380
看看效果

248
00:06:03,380 --> 00:06:05,240
一个

249
00:06:05,240 --> 00:06:06,180
两个

250
00:06:06,180 --> 00:06:07,180
是不是结束了

251
00:06:07,180 --> 00:06:08,220
那这样的话呢

252
00:06:08,220 --> 00:06:09,200
我们就实现了一个

253
00:06:09,200 --> 00:06:09,980
一步

254
00:06:09,980 --> 00:06:10,500
完了

255
00:06:10,500 --> 00:06:11,300
串行

256
00:06:11,300 --> 00:06:11,900
并且呢

257
00:06:11,900 --> 00:06:13,700
是一个瀑布形式的一个构参数

258
00:06:13,700 --> 00:06:14,700
完这样呢

259
00:06:14,700 --> 00:06:15,640
大家就来想一想

260
00:06:15,640 --> 00:06:17,040
怎么去实现另一个

261
00:06:17,040 --> 00:06:17,340
对吧

262
00:06:17,340 --> 00:06:18,300
就是我们所谓的叫

263
00:06:18,300 --> 00:06:18,820
对吧

264
00:06:18,820 --> 00:06:19,820
type promise

265
00:06:19,820 --> 00:06:21,500
我来加上我们这样一个

266
00:06:21,500 --> 00:06:25,020
我们的叫promise的一个调用方法

267
00:06:25,020 --> 00:06:26,420
其实上面也讲过了

268
00:06:26,420 --> 00:06:27,880
这样的话给大家思考时间

269
00:06:27,880 --> 00:06:29,740
大家可以看看它怎么去实现

270
00:06:29,740 --> 00:06:32,000
咱们这个typeball就先讲到这里

