1
00:00:00,000 --> 00:00:06,240
温小杰呢我们来说一下我们webpack呢如何实现多线程打包

2
00:00:06,240 --> 00:00:09,560
那这里面呢他主要靠的一个模块呢叫happypack

3
00:00:09,560 --> 00:00:12,880
一听名字对吧就很开心那个名字叫快乐打包

4
00:00:12,880 --> 00:00:18,180
他呢可以对吧可以使用对吧多线程来打包

5
00:00:18,180 --> 00:00:22,740
当然了你要不明白什么线程啊进程啊可以参加我们的node课程

6
00:00:22,740 --> 00:00:26,320
我们node里面呢主要也讲了一下什么是多线程什么来说是单线程

7
00:00:26,320 --> 00:00:28,160
多线程和线程之间的关系对吧

8
00:00:28,160 --> 00:00:29,420
那这里面呢

9
00:00:29,420 --> 00:00:31,620
我就先不说这个现成和进程的关系了

10
00:00:31,620 --> 00:00:33,180
我们就直接来说一下

11
00:00:33,180 --> 00:00:33,860
如何呢

12
00:00:33,860 --> 00:00:35,060
使用这样一个模块来打包

13
00:00:35,060 --> 00:00:36,040
那里面呢

14
00:00:36,040 --> 00:00:36,660
非常简单

15
00:00:36,660 --> 00:00:39,320
我们需要用一下我们的这个happypack

16
00:00:39,320 --> 00:00:40,440
等于require

17
00:00:40,440 --> 00:00:42,700
我们就需要引用一下这个happypack

18
00:00:42,700 --> 00:00:45,540
这个用法非常简单

19
00:00:45,540 --> 00:00:46,340
那它呢

20
00:00:46,340 --> 00:00:47,280
是一个第三方模块

21
00:00:47,280 --> 00:00:48,680
所以在用之前呢

22
00:00:48,680 --> 00:00:49,960
我们需要先安装一下

23
00:00:49,960 --> 00:00:51,340
yarr的happypack

24
00:00:51,340 --> 00:00:52,660
安装

25
00:00:52,660 --> 00:00:54,200
那这样的一个东西

26
00:00:54,200 --> 00:00:55,120
我怎么使用呢

27
00:00:55,120 --> 00:00:56,140
非常简单啊

28
00:00:56,140 --> 00:00:56,860
就是以前呀

29
00:00:56,860 --> 00:00:58,380
我们在匹配到gs的时候

30
00:00:58,380 --> 00:00:59,640
我们要用bibbleloader

31
00:00:59,640 --> 00:01:02,340
现在我们要用happypack里面这样一个loader

32
00:01:02,340 --> 00:01:06,420
我们用bibble用happypack

33
00:01:06,420 --> 00:01:08,840
-他下的loader

34
00:01:08,840 --> 00:01:10,680
完了问号你给他们标识一下

35
00:01:10,680 --> 00:01:13,240
比如说这个东西我是打包gs的

36
00:01:13,240 --> 00:01:14,160
为什么呢

37
00:01:14,160 --> 00:01:16,200
因为可能不光是gs要多现成

38
00:01:16,200 --> 00:01:17,900
可能css也要多现成

39
00:01:17,900 --> 00:01:19,420
这时候我们配好以后

40
00:01:19,420 --> 00:01:22,900
我们在插件里面可以new一个叫happypack

41
00:01:22,900 --> 00:01:25,240
这样一个happypack

42
00:01:25,240 --> 00:01:26,740
完了里面呢

43
00:01:26,740 --> 00:01:28,600
我就可以直接这个函数

44
00:01:28,600 --> 00:01:29,580
里面呢

45
00:01:29,580 --> 00:01:30,780
我就可以给它来个什么id

46
00:01:30,780 --> 00:01:32,160
这个id叫什么呢

47
00:01:32,160 --> 00:01:33,460
id就是我们的这个gs

48
00:01:33,460 --> 00:01:35,380
当然这个名字最好大写啊

49
00:01:35,380 --> 00:01:36,840
改成h大写的

50
00:01:36,840 --> 00:01:38,400
这里一样happypack

51
00:01:38,400 --> 00:01:39,560
同样呢

52
00:01:39,560 --> 00:01:39,940
下面呢

53
00:01:39,940 --> 00:01:41,300
我也改成happypack

54
00:01:41,300 --> 00:01:43,700
那我用的这个gs就是这个这个

55
00:01:43,700 --> 00:01:44,920
 plugin材件

56
00:01:44,920 --> 00:01:45,920
并且呢

57
00:01:45,920 --> 00:01:46,620
我用的时候呢

58
00:01:46,620 --> 00:01:47,040
还是它

59
00:01:47,040 --> 00:01:48,040
那这里面啊

60
00:01:48,040 --> 00:01:49,080
我们要描述一下

61
00:01:49,080 --> 00:01:49,960
它的u字里面呢

62
00:01:49,960 --> 00:01:50,720
必须得是个数组

63
00:01:50,720 --> 00:01:52,060
只是它规定好啊

64
00:01:52,060 --> 00:01:52,440
这里呢

65
00:01:52,440 --> 00:01:53,280
我把它放过来

66
00:01:53,280 --> 00:01:54,620
个说一下

67
00:01:54,620 --> 00:01:56,880
相当于我们用loader打包的时候

68
00:01:56,880 --> 00:01:58,760
我们用的是hapypack loader

69
00:01:58,760 --> 00:02:00,400
这个loader它给它标示一下

70
00:02:00,400 --> 00:02:01,420
用的是idgs

71
00:02:01,420 --> 00:02:03,540
它就会电用相关的插件

72
00:02:03,540 --> 00:02:05,660
使用这样一个loader来进行打包

73
00:02:05,660 --> 00:02:07,520
好了我们来看看效果

74
00:02:07,520 --> 00:02:09,360
来用一下是吧

75
00:02:09,360 --> 00:02:11,460
叫npxwebpack

76
00:02:11,460 --> 00:02:13,860
运行一下是吧

77
00:02:13,860 --> 00:02:15,380
你看它默认会怎么样

78
00:02:15,380 --> 00:02:18,000
启动我们的这样一个三个线程来进行打包

79
00:02:18,000 --> 00:02:20,740
当然了这个打包速度是1.375秒

80
00:02:20,740 --> 00:02:21,800
毫秒是吧

81
00:02:21,800 --> 00:02:24,460
因为我们默认情况下这个文件很小

82
00:02:24,460 --> 00:02:27,580
我使用多线程打包的方式可能反而会慢

83
00:02:27,580 --> 00:02:28,500
什么意思呢

84
00:02:28,500 --> 00:02:30,560
就是说我这里面如果把它改回去

85
00:02:30,560 --> 00:02:31,480
刚才那样一个写法

86
00:02:31,480 --> 00:02:33,400
可能速度还会比多线程怎么样

87
00:02:33,400 --> 00:02:34,140
更快一些

88
00:02:34,140 --> 00:02:35,860
除非我这个项目比较大

89
00:02:35,860 --> 00:02:37,120
需要多线程的时候

90
00:02:37,120 --> 00:02:38,640
可能会增快

91
00:02:38,640 --> 00:02:40,400
这里面我再来试一下

92
00:02:40,400 --> 00:02:41,640
npx外拍

93
00:02:41,640 --> 00:02:43,560
稍等一下

94
00:02:43,560 --> 00:02:45,240
他告诉我多了一个方括号

95
00:02:45,240 --> 00:02:46,560
这里更说话一下

96
00:02:46,560 --> 00:02:49,020
我看看哪粘错了

97
00:02:49,020 --> 00:02:50,680
我把这个地方再粘一下

98
00:02:50,680 --> 00:02:52,040
再放到这

99
00:02:52,040 --> 00:02:54,260
看看刚才是

100
00:02:54,460 --> 00:02:55,760
13几几是吧

101
00:02:55,760 --> 00:02:58,140
看这回可能会比刚才更快一些

102
00:02:58,140 --> 00:02:59,880
当然这里面你看

103
00:02:59,880 --> 00:03:01,060
是不是我并没有

104
00:03:01,060 --> 00:03:03,000
这个插件好像有点效果

105
00:03:03,000 --> 00:03:04,120
1536

106
00:03:04,120 --> 00:03:08,180
这里我把插件也住掉

107
00:03:08,180 --> 00:03:09,840
因为它要分配多线程

108
00:03:09,840 --> 00:03:11,020
这里一样

109
00:03:11,020 --> 00:03:12,060
我再执行一下

110
00:03:12,060 --> 00:03:15,840
你看这个时间是什么

111
00:03:15,840 --> 00:03:16,700
是不是1045

112
00:03:16,700 --> 00:03:19,400
也说在我们分配线程的过程中

113
00:03:19,400 --> 00:03:21,260
其实也会浪费一些性能的

114
00:03:21,260 --> 00:03:24,060
同样比如说我们还有css

115
00:03:24,060 --> 00:03:25,980
那好我可以怎么样的再来一个对吧

116
00:03:25,980 --> 00:03:29,140
比如说匹配以这个css结尾的

117
00:03:29,140 --> 00:03:32,340
那同样啊也可以怎么样去用一下我们这样一个

118
00:03:32,340 --> 00:03:33,980
比如说叫以前是不是这样写

119
00:03:33,980 --> 00:03:35,100
是不是叫cssloader

120
00:03:35,100 --> 00:03:37,440
完了后面呢我们要把它插到style标签里

121
00:03:37,440 --> 00:03:41,300
是可能还需要一个叫styleloader

122
00:03:41,300 --> 00:03:43,240
那好了那这样用完以后呢

123
00:03:43,240 --> 00:03:44,560
我们是不是可以在这里面同样

124
00:03:44,560 --> 00:03:46,860
那这时候是不是该写的刷也是这样写啊

125
00:03:46,860 --> 00:03:50,020
叫hype对排克排克对

126
00:03:50,020 --> 00:03:51,420
完了告loader

127
00:03:51,420 --> 00:03:53,160
完了后面呢给了一个问号id

128
00:03:53,160 --> 00:03:54,420
比如他叫css

129
00:03:54,420 --> 00:03:55,520
那下面的话

130
00:03:55,520 --> 00:03:58,340
是不是我应该再去new这样一个happypack插件

131
00:03:58,340 --> 00:04:00,140
我希望他也可以怎么样

132
00:04:00,140 --> 00:04:01,300
实现多线上打包

133
00:04:01,300 --> 00:04:02,800
happypack

134
00:04:02,800 --> 00:04:04,580
那这里面我要怎么写呢

135
00:04:04,580 --> 00:04:05,720
是不是同样来个id

136
00:04:05,720 --> 00:04:06,720
他就叫css

137
00:04:06,720 --> 00:04:08,620
那同样用字的模块呢

138
00:04:08,620 --> 00:04:09,060
就是他

139
00:04:09,060 --> 00:04:10,040
那这里面

140
00:04:10,040 --> 00:04:11,900
我是不是就要安装这两个模块了

141
00:04:11,900 --> 00:04:14,780
一个叫style122的对吧

142
00:04:14,780 --> 00:04:16,520
好了styleloader

143
00:04:16,520 --> 00:04:20,060
还有我们所谓的这个叫cssloader

144
00:04:20,060 --> 00:04:21,180
那好了

145
00:04:21,180 --> 00:04:21,920
稍等一下

146
00:04:21,920 --> 00:04:23,860
那现在就可以怎么样

147
00:04:23,860 --> 00:04:27,020
是不是实现CSS和我们的这个GS可以怎么样

148
00:04:27,020 --> 00:04:28,720
采用多线程打包的方式

149
00:04:28,720 --> 00:04:30,080
就是用法非常简单

150
00:04:30,080 --> 00:04:30,960
比如说这里面呢

151
00:04:30,960 --> 00:04:32,500
我刚才说用的是GS

152
00:04:32,500 --> 00:04:33,200
这是CSS

153
00:04:33,200 --> 00:04:34,220
它会调用怎么样

154
00:04:34,220 --> 00:04:35,560
对应的这样的loader呢

155
00:04:35,560 --> 00:04:36,260
来实现打包

156
00:04:36,260 --> 00:04:36,880
好

157
00:04:36,880 --> 00:04:38,040
稍等是吧

158
00:04:38,040 --> 00:04:38,940
这里面的一样

159
00:04:38,940 --> 00:04:39,520
NPX

160
00:04:39,520 --> 00:04:40,060
Mapack

161
00:04:40,060 --> 00:04:41,300
但是我说了

162
00:04:41,300 --> 00:04:42,400
如果项目很小的话

163
00:04:42,400 --> 00:04:44,140
怎么看是不是开起了三个三个

164
00:04:44,140 --> 00:04:45,780
并且时间是不是比以前更多了

165
00:04:45,780 --> 00:04:47,160
所以说这里面呢

166
00:04:47,160 --> 00:04:48,280
我们根本看不到效果

167
00:04:48,280 --> 00:04:49,060
那后面的话

168
00:04:49,060 --> 00:04:50,640
我们比如写的项目比较大的时候

169
00:04:50,640 --> 00:04:51,900
会采用这种方式呢

170
00:04:51,900 --> 00:04:53,420
来实现多线程大炮

