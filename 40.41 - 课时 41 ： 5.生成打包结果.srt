1
00:00:00,000 --> 00:00:02,160
那我们呢

2
00:00:02,160 --> 00:00:03,300
接下来就往下接着写

3
00:00:03,300 --> 00:00:04,780
我们来写什么功能呢

4
00:00:04,780 --> 00:00:06,280
主要就是用这个对象啊

5
00:00:06,280 --> 00:00:07,640
去渲染我们这样一个模板

6
00:00:07,640 --> 00:00:08,540
那我们呢

7
00:00:08,540 --> 00:00:10,480
就先把这个模板拿过来啊

8
00:00:10,480 --> 00:00:11,320
这是我们的一个模板

9
00:00:11,320 --> 00:00:12,620
但是我们要稍微改造一下

10
00:00:12,620 --> 00:00:13,340
不在这里

11
00:00:13,340 --> 00:00:14,780
我们为了方便啊

12
00:00:14,780 --> 00:00:15,720
就不拼字不串了

13
00:00:15,720 --> 00:00:16,500
我就直接呢

14
00:00:16,500 --> 00:00:17,260
用这个慢

15
00:00:17,260 --> 00:00:18,820
用个DGS模板吧

16
00:00:18,820 --> 00:00:19,960
放过来

17
00:00:19,960 --> 00:00:21,500
那这个对象啊

18
00:00:21,500 --> 00:00:22,320
我们就要改一改了

19
00:00:22,320 --> 00:00:23,620
这个地方肯定是个变量

20
00:00:23,620 --> 00:00:24,520
那变量呢

21
00:00:24,520 --> 00:00:25,100
我们刚才说了

22
00:00:25,100 --> 00:00:26,240
其实就是一个按钮ID

23
00:00:26,240 --> 00:00:27,580
尖叫号对吧

24
00:00:27,580 --> 00:00:29,000
比如说百分号高

25
00:00:29,000 --> 00:00:30,780
就起个名字

26
00:00:30,780 --> 00:00:31,820
叫entryid

27
00:00:31,820 --> 00:00:34,760
这是我们的名字

28
00:00:34,760 --> 00:00:35,420
那下面呢

29
00:00:35,420 --> 00:00:36,440
是不是应该写个循环

30
00:00:36,440 --> 00:00:37,660
把它一丝便利出来

31
00:00:37,660 --> 00:00:38,320
OK

32
00:00:38,320 --> 00:00:39,500
那就来个什么呢

33
00:00:39,500 --> 00:00:39,920
来个full

34
00:00:39,920 --> 00:00:41,200
那尖角号百分号

35
00:00:41,200 --> 00:00:41,820
full

36
00:00:41,820 --> 00:00:42,720
完了

37
00:00:42,720 --> 00:00:43,780
light一个i

38
00:00:43,780 --> 00:00:44,560
那就key吧

39
00:00:44,560 --> 00:00:45,280
循环是吧

40
00:00:45,280 --> 00:00:46,720
in我们的这个modus

41
00:00:46,720 --> 00:00:47,620
它是个对象

42
00:00:47,620 --> 00:00:50,140
当然了这里面我也一样

43
00:00:50,140 --> 00:00:51,140
来个封口是吧

44
00:00:51,140 --> 00:00:52,720
把它放到里面来

45
00:00:52,720 --> 00:00:53,460
完了这底下呢

46
00:00:53,460 --> 00:00:55,360
一样叫尖角号百分号

47
00:00:55,360 --> 00:00:56,940
完了百分号尖角号

48
00:00:56,940 --> 00:00:57,740
完了里面呢

49
00:00:57,740 --> 00:00:58,440
写这样一个东西

50
00:00:58,440 --> 00:01:01,540
那把这个代码怎么样粘过来

51
00:01:01,540 --> 00:01:04,200
那key肯定就是我们当前的这样一个东西

52
00:01:04,200 --> 00:01:04,380
是吧

53
00:01:04,380 --> 00:01:04,840
就这个key

54
00:01:04,840 --> 00:01:07,220
那好好把它直接扔过来

55
00:01:07,220 --> 00:01:07,780
肩脚号

56
00:01:07,780 --> 00:01:09,800
这里面我就用非转移的叫杠

57
00:01:09,800 --> 00:01:11,360
我把key拿过来

58
00:01:11,360 --> 00:01:13,200
这是egs的用法

59
00:01:13,200 --> 00:01:15,560
就是可以把我们这样一个循环的变量放在这里

60
00:01:15,560 --> 00:01:16,580
当做gs输出

61
00:01:16,580 --> 00:01:19,220
然后这里面就不在Evil这里面东西了

62
00:01:19,220 --> 00:01:21,380
那我们肯定需要拿到modus的key

63
00:01:21,380 --> 00:01:23,160
就是它里面原代码是塞到这里

64
00:01:23,160 --> 00:01:25,360
但这个原代码你要注意一下

65
00:01:25,360 --> 00:01:26,700
它里面可能会有换行

66
00:01:26,700 --> 00:01:28,380
所以这里面为了方便

67
00:01:28,380 --> 00:01:29,700
我就直接使用木板组创

68
00:01:29,700 --> 00:01:33,480
完了里面直接取值

69
00:01:33,480 --> 00:01:34,740
尖角号百分号杠

70
00:01:34,740 --> 00:01:36,780
比如说就叫它modus

71
00:01:36,780 --> 00:01:38,680
放过号key

72
00:01:38,680 --> 00:01:42,640
OK

73
00:01:42,640 --> 00:01:44,660
那现在我们就实现了一个

74
00:01:44,660 --> 00:01:45,960
我们所谓的这个egs

75
00:01:45,960 --> 00:01:46,360
是吧

76
00:01:46,360 --> 00:01:47,420
那现在就可以怎么样

77
00:01:47,420 --> 00:01:48,740
把这个东西进行渲染

78
00:01:48,740 --> 00:01:50,040
那木板有了

79
00:01:50,040 --> 00:01:51,940
我们是不是把这数据一丢进来

80
00:01:51,940 --> 00:01:52,480
就可以了

81
00:01:52,480 --> 00:01:54,220
那回到我们的compile

82
00:01:54,220 --> 00:01:56,100
这里呢要做些什么事呢

83
00:01:56,100 --> 00:01:57,300
第一步我需要怎么样

84
00:01:57,300 --> 00:01:58,280
是不是拿到

85
00:01:58,280 --> 00:02:00,280
输出到

86
00:02:00,280 --> 00:02:01,500
那就是拿到

87
00:02:01,500 --> 00:02:03,840
对输出到哪个目录下

88
00:02:03,840 --> 00:02:06,360
输出到哪个目录下

89
00:02:06,360 --> 00:02:07,340
那这个目录啊

90
00:02:07,340 --> 00:02:07,900
哪来的呢

91
00:02:07,900 --> 00:02:08,120
是吧

92
00:02:08,120 --> 00:02:08,960
那非常简单

93
00:02:08,960 --> 00:02:09,560
我可以怎么样

94
00:02:09,560 --> 00:02:10,180
是不是通过

95
00:02:10,180 --> 00:02:11,860
this.刚才那个config

96
00:02:11,860 --> 00:02:13,180
它里面是不是有个叫

97
00:02:13,180 --> 00:02:16,000
output.pass

98
00:02:16,000 --> 00:02:16,700
这是路径

99
00:02:16,700 --> 00:02:18,940
那同样我们还需要它的文件名吧

100
00:02:18,940 --> 00:02:19,680
那又是拼

101
00:02:19,680 --> 00:02:20,100
对吧

102
00:02:20,100 --> 00:02:21,740
那pass.什么.转

103
00:02:21,740 --> 00:02:24,280
这是文件名

104
00:02:24,280 --> 00:02:25,140
路径

105
00:02:25,140 --> 00:02:25,600
对吧

106
00:02:25,600 --> 00:02:26,280
路径

107
00:02:26,280 --> 00:02:28,760
完了还有我们当前的config

108
00:02:28,760 --> 00:02:29,700
完了

109
00:02:29,700 --> 00:02:32,060
dr我们的output.fill name

110
00:02:32,060 --> 00:02:35,120
那这个都拿到以后

111
00:02:35,120 --> 00:02:36,420
我们就可以拿到一个什么呢

112
00:02:36,420 --> 00:02:37,960
是不是一个总的路径

113
00:02:37,960 --> 00:02:39,340
然后在这里可以写个变量

114
00:02:39,340 --> 00:02:41,000
比如就是这样一个pass

115
00:02:41,000 --> 00:02:41,340
对吧

116
00:02:41,340 --> 00:02:42,440
比如说就是一个man

117
00:02:42,440 --> 00:02:44,200
这就是我们主入口的文件

118
00:02:44,200 --> 00:02:45,360
或者叫一个资源

119
00:02:45,360 --> 00:02:45,720
对吧

120
00:02:45,720 --> 00:02:48,140
那我们为了把这资源存放起来

121
00:02:48,140 --> 00:02:49,000
我们可以弄个变量

122
00:02:49,000 --> 00:02:49,800
叫个size

123
00:02:49,800 --> 00:02:51,920
比如说到时候我们可能会打包多个

124
00:02:51,920 --> 00:02:52,660
那这时候呢

125
00:02:52,660 --> 00:02:54,000
我们这个资源里面可能就N个

126
00:02:54,000 --> 00:02:55,200
那我可以在这里呢

127
00:02:55,200 --> 00:02:55,920
直接放上一个

128
00:02:55,920 --> 00:02:57,400
叫this叫个size

129
00:02:57,400 --> 00:02:58,580
把这个man拿往里c

130
00:02:58,580 --> 00:03:02,800
但是这个man对应的结果是什么样的

131
00:03:02,800 --> 00:03:04,600
是不是应该通过我们那个数据

132
00:03:04,600 --> 00:03:06,840
加刚才我们看到这个模板来渲染

133
00:03:06,840 --> 00:03:08,640
那这里面非常简单了

134
00:03:08,640 --> 00:03:10,340
那我们需要这样一个egs模块

135
00:03:10,340 --> 00:03:12,480
那egs模块我就先下载一下

136
00:03:12,480 --> 00:03:13,080
在这里下

137
00:03:13,080 --> 00:03:14,780
这是我们自己的包啊

138
00:03:14,780 --> 00:03:16,300
addegs

139
00:03:16,300 --> 00:03:19,960
那同样我们呢就把这个egs啊

140
00:03:19,960 --> 00:03:20,660
引进来是吧

141
00:03:20,660 --> 00:03:21,120
在这里

142
00:03:21,120 --> 00:03:24,440
light一个egs等于require对吧

143
00:03:24,440 --> 00:03:25,160
egs

144
00:03:25,160 --> 00:03:29,960
完了 这时候我们可以通过egs来直接去先把这文件读出来

145
00:03:29,960 --> 00:03:32,920
读的话肯定先用template

146
00:03:32,920 --> 00:03:38,500
这里面改个名字

147
00:03:38,500 --> 00:03:43,680
template-sg等于fs.reader.file.sync

148
00:03:43,680 --> 00:03:45,620
完了读谁呢

149
00:03:45,620 --> 00:03:47,120
读的话是不是有那个方法

150
00:03:47,120 --> 00:03:49,340
在封装了一个叫this.get source

151
00:03:49,340 --> 00:03:51,500
我把谁放进去呢

152
00:03:51,500 --> 00:03:52,940
是不是把这个路径放进去就可以了

153
00:03:52,940 --> 00:03:54,620
OK 这是个绝对路径

154
00:03:54,620 --> 00:03:55,780
那我就放在这

155
00:03:55,780 --> 00:03:57,800
放在这里

156
00:03:57,800 --> 00:03:59,640
完了并且我们还需要干嘛

157
00:03:59,640 --> 00:04:01,260
是不是把路径放进去

158
00:04:01,260 --> 00:04:03,000
完这时候出来就是一个模板字幕串

159
00:04:03,000 --> 00:04:04,140
那字幕串怎么办

160
00:04:04,140 --> 00:04:07,180
我是不是应该去用EGS来渲染

161
00:04:07,180 --> 00:04:08,880
好 EGS.Render

162
00:04:08,880 --> 00:04:10,300
那就渲染什么

163
00:04:10,300 --> 00:04:11,280
这样一个字幕串

164
00:04:11,280 --> 00:04:12,780
就渲染当下这个模板

165
00:04:12,780 --> 00:04:14,660
模板里面需要两个变量

166
00:04:14,660 --> 00:04:15,620
一个叫Entry ID

167
00:04:15,620 --> 00:04:17,020
那就传吧

168
00:04:17,020 --> 00:04:18,640
第一个叫Entry ID

169
00:04:18,640 --> 00:04:20,960
它取的就是this.Entry ID

170
00:04:20,960 --> 00:04:22,840
完了还有个东西叫Modules

171
00:04:22,840 --> 00:04:24,280
他取的就应该是

172
00:04:24,280 --> 00:04:25,600
this.modus

173
00:04:25,600 --> 00:04:27,960
OK

174
00:04:27,960 --> 00:04:30,180
那现在我们渲染完以后

175
00:04:30,180 --> 00:04:32,180
他返回的就应该是个什么了

176
00:04:32,180 --> 00:04:32,980
是不是就是一个

177
00:04:32,980 --> 00:04:35,060
我们所谓的渲染后的一个结果了

178
00:04:35,060 --> 00:04:35,840
那我们可以认为

179
00:04:35,840 --> 00:04:37,140
他就是个code代码块

180
00:04:37,140 --> 00:04:39,300
那我就可以把这code放在这来

181
00:04:39,300 --> 00:04:40,980
现在就相当于什么

182
00:04:40,980 --> 00:04:41,960
是不是资源里

183
00:04:41,960 --> 00:04:43,000
资源中

184
00:04:43,000 --> 00:04:45,540
完了这是路径对应的代码

185
00:04:45,540 --> 00:04:45,860
对吧

186
00:04:45,860 --> 00:04:47,800
路径对应的代码

187
00:04:47,800 --> 00:04:50,300
到时候我们可以打包多个的话

188
00:04:50,300 --> 00:04:51,280
可以在这里面怎么样

189
00:04:51,280 --> 00:04:52,180
再加些路径

190
00:04:52,180 --> 00:04:53,300
我到时候循环这个路径

191
00:04:53,300 --> 00:04:53,980
循环这个对象

192
00:04:53,980 --> 00:04:54,780
我意思渲染

193
00:04:54,780 --> 00:04:56,000
那这里面为了方便

194
00:04:56,000 --> 00:04:59,120
我就直接先用这个FS叫WriteFill

195
00:04:59,120 --> 00:05:00,900
就往里面怎么样去写

196
00:05:00,900 --> 00:05:02,180
WriteFillSync

197
00:05:02,180 --> 00:05:04,000
那写到哪个文件里呢

198
00:05:04,000 --> 00:05:04,540
非常简单

199
00:05:04,540 --> 00:05:06,400
那肯定写到这个man里面去

200
00:05:06,400 --> 00:05:07,800
那写什么内容呢

201
00:05:07,800 --> 00:05:10,740
那写的肯定是当前这样一个代码

202
00:05:10,740 --> 00:05:11,640
那写进完以后

203
00:05:11,640 --> 00:05:13,140
是不是这个文件相当于怎么样

204
00:05:13,140 --> 00:05:14,500
是不是被发射出去了

205
00:05:14,500 --> 00:05:15,420
那这时候呢

206
00:05:15,420 --> 00:05:16,240
我们来一行一下

207
00:05:16,240 --> 00:05:17,680
把它跑一下

208
00:05:17,680 --> 00:05:18,520
不是这里啊

209
00:05:18,520 --> 00:05:18,780
这里

210
00:05:18,780 --> 00:05:20,940
我们来试试啊

211
00:05:20,940 --> 00:05:23,020
看看现在能不能达到我的愿望

212
00:05:23,020 --> 00:05:23,780
能把它怎么样

213
00:05:23,780 --> 00:05:24,360
已经打包

214
00:05:24,360 --> 00:05:26,040
他说这里面报了个错

215
00:05:26,040 --> 00:05:28,520
说没有这样一个文件或者目录

216
00:05:28,520 --> 00:05:29,860
说dist没找到

217
00:05:29,860 --> 00:05:31,540
说bundle.js

218
00:05:31,540 --> 00:05:31,900
是吧

219
00:05:31,900 --> 00:05:32,760
那好

220
00:05:32,760 --> 00:05:34,180
我来看看什么原因

221
00:05:34,180 --> 00:05:35,660
切过来

222
00:05:35,660 --> 00:05:37,500
我们在这里先打一下这个man

223
00:05:37,500 --> 00:05:38,000
是吧

224
00:05:38,000 --> 00:05:39,380
主要是一个man的问题应该

225
00:05:39,380 --> 00:05:40,860
我们再来运行一下

226
00:05:40,860 --> 00:05:41,800
走你

227
00:05:41,800 --> 00:05:45,580
他说找不到这样一个文件

228
00:05:45,580 --> 00:05:47,520
c牌下的啦啦啦

229
00:05:47,520 --> 00:05:48,120
这么长

230
00:05:48,120 --> 00:05:50,600
说distbundle.js

231
00:05:50,600 --> 00:05:52,160
这是Redfield

232
00:05:52,160 --> 00:05:53,500
完了读的话

233
00:05:53,500 --> 00:05:55,220
读取的话有点问题

234
00:05:55,220 --> 00:05:57,200
你读的时候不能读这个路径

235
00:05:57,200 --> 00:05:58,800
因为这个路径是一个什么

236
00:05:58,800 --> 00:06:00,740
是一个输出的路径

237
00:06:00,740 --> 00:06:01,780
我要往这里去写

238
00:06:01,780 --> 00:06:03,180
这里得稍微改改

239
00:06:03,180 --> 00:06:05,260
这里面这个路径就不太对了

240
00:06:05,260 --> 00:06:06,540
这里面应该读的是哪个

241
00:06:06,540 --> 00:06:09,660
是不是当前我们所谓的模板的路径

242
00:06:09,660 --> 00:06:12,980
模板是不是当前慢点EGS

243
00:06:12,980 --> 00:06:14,900
所以这里面我就写死了

244
00:06:14,900 --> 00:06:15,620
叫passer join

245
00:06:15,620 --> 00:06:16,400
来个什么

246
00:06:16,400 --> 00:06:17,680
来一个杠杠

247
00:06:17,680 --> 00:06:18,320
第二例

248
00:06:18,320 --> 00:06:19,560
他下的什么

249
00:06:19,560 --> 00:06:21,980
是不是当前的这个慢点egs

250
00:06:21,980 --> 00:06:23,640
这里面要分清啊

251
00:06:23,640 --> 00:06:24,900
上面这个是什么呢

252
00:06:24,900 --> 00:06:25,580
写上对吧

253
00:06:25,580 --> 00:06:26,820
这是一个输出路径

254
00:06:26,820 --> 00:06:27,940
这是输出路径

255
00:06:27,940 --> 00:06:31,500
输出路径对吧

256
00:06:31,500 --> 00:06:32,600
输出路径

257
00:06:32,600 --> 00:06:34,420
完了下面这个代表的什么呢

258
00:06:34,420 --> 00:06:35,280
就是对吧

259
00:06:35,280 --> 00:06:36,880
就是通模板的路径

260
00:06:36,880 --> 00:06:37,460
对吧

261
00:06:37,460 --> 00:06:38,480
模板的路径

262
00:06:38,480 --> 00:06:40,380
完了用什么呢

263
00:06:40,380 --> 00:06:42,000
用这个get source

264
00:06:42,000 --> 00:06:43,260
把这模板中的内容怎么样

265
00:06:43,260 --> 00:06:43,940
读取出来

266
00:06:43,940 --> 00:06:44,740
那好了

267
00:06:44,740 --> 00:06:46,560
我们再来看一下效果啊

268
00:06:46,560 --> 00:06:47,160
在这里

269
00:06:47,160 --> 00:06:49,540
哦点错了啊

270
00:06:49,540 --> 00:06:51,720
这里面我再跑一下

271
00:06:51,720 --> 00:06:54,540
看现在起码没有报错

272
00:06:54,540 --> 00:06:55,720
而且呢也OK了

273
00:06:55,720 --> 00:06:57,820
那我把这东西稍微改一改

274
00:06:57,820 --> 00:06:59,000
把这个console先删掉

275
00:06:59,000 --> 00:07:01,000
我看看我们的代码行不行

276
00:07:01,000 --> 00:07:01,640
bundle

277
00:07:01,640 --> 00:07:04,480
这个代码应该就是刚才我们怎么样

278
00:07:04,480 --> 00:07:06,840
自己编写出来的打包出来的结果

279
00:07:06,840 --> 00:07:08,780
你看是不是三个在自己循环出来的

280
00:07:08,780 --> 00:07:10,640
中间多号都一样是吧

281
00:07:10,640 --> 00:07:11,920
那这时候我们来看看

282
00:07:11,920 --> 00:07:13,020
而且它是木板子不串

283
00:07:13,020 --> 00:07:13,520
看到了吧

284
00:07:13,520 --> 00:07:15,480
那现在我们来在这运行一下

285
00:07:15,480 --> 00:07:16,320
运行

286
00:07:16,320 --> 00:07:18,060
在这运行一下

287
00:07:18,060 --> 00:07:20,020
看看AB是不是也有

288
00:07:20,020 --> 00:07:21,700
那同样我们在6.7里面

289
00:07:21,700 --> 00:07:22,380
引这个bundle

290
00:07:22,380 --> 00:07:23,300
看看能不能跑

291
00:07:23,300 --> 00:07:25,020
能跑的话说明怎么样

292
00:07:25,020 --> 00:07:26,660
是不是我就实现了这样的

293
00:07:26,660 --> 00:07:27,780
一个自己的打包工具

294
00:07:27,780 --> 00:07:28,660
那好

295
00:07:28,660 --> 00:07:30,300
我把它翻过来

296
00:07:30,300 --> 00:07:30,980
来运行一下

297
00:07:30,980 --> 00:07:32,400
看看效果

298
00:07:32,400 --> 00:07:32,780
console

299
00:07:32,780 --> 00:07:36,040
是不是也可以

300
00:07:36,040 --> 00:07:37,740
那现在我们就怎么样

301
00:07:37,740 --> 00:07:39,780
自己实现了这样一个webpack

302
00:07:39,780 --> 00:07:41,500
但这个webpack稍微点弱

303
00:07:41,500 --> 00:07:42,520
它只能怎么样

304
00:07:42,520 --> 00:07:43,540
打包模块

305
00:07:43,540 --> 00:07:44,760
我们还希望怎么样

306
00:07:44,760 --> 00:07:46,780
是不是可以在我们的webpack中

307
00:07:46,780 --> 00:07:48,300
引入loader和插件

308
00:07:48,300 --> 00:07:50,340
接下来我们就开始想想

309
00:07:50,340 --> 00:07:52,640
怎么去写这样一个loader的功能

