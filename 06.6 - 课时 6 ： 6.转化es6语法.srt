1
00:00:00,000 --> 00:00:05,400
本节呢我们就来看一下如何在webpack中呢来处理我们的GS模块

2
00:00:05,400 --> 00:00:08,560
那同样啊我们先把这个配置文件的备份一份

3
00:00:08,560 --> 00:00:09,960
我来稍微改一下是吧

4
00:00:09,960 --> 00:00:13,040
那这里呢我们把应该没有什么注视啊

5
00:00:13,040 --> 00:00:13,740
我闪闪控

6
00:00:13,740 --> 00:00:15,800
那这里呢我们需要怎么样呢

7
00:00:15,800 --> 00:00:19,120
把ES6转成ES5或者更高级的语法呢转成ES5

8
00:00:19,120 --> 00:00:21,500
那这里呢我们可能就需要用到这个babel了

9
00:00:21,500 --> 00:00:23,680
那babel的作用呢就是用来转化的

10
00:00:23,680 --> 00:00:24,840
转化我们的GS的

11
00:00:24,840 --> 00:00:26,580
比如说要我们希望怎么样呢

12
00:00:26,580 --> 00:00:28,180
来一个代码吧

13
00:00:28,180 --> 00:00:29,440
来来一个FN

14
00:00:29,440 --> 00:00:31,900
fn 等于一个间断权数

15
00:00:31,900 --> 00:00:33,740
这里呢

16
00:00:33,740 --> 00:00:34,980
依旧我们可以在这里呢

17
00:00:34,980 --> 00:00:36,480
去调一下这个 fn 之行

18
00:00:36,480 --> 00:00:37,620
fn 之行

19
00:00:37,620 --> 00:00:38,980
完了里面呢

20
00:00:38,980 --> 00:00:40,120
我们就随便写一句

21
00:00:40,120 --> 00:00:40,840
console log吧

22
00:00:40,840 --> 00:00:42,040
我们都知道吧

23
00:00:42,040 --> 00:00:42,760
这样的代码呢

24
00:00:42,760 --> 00:00:44,440
可能默认情况下会有一些问题

25
00:00:44,440 --> 00:00:44,860
是吧

26
00:00:44,860 --> 00:00:46,580
我在这 mpx webpack

27
00:00:46,580 --> 00:00:47,500
运行一下

28
00:00:47,500 --> 00:00:49,040
它打包吹的结果呀

29
00:00:49,040 --> 00:00:50,080
依旧是 es6

30
00:00:50,080 --> 00:00:52,180
并没有把这个高版本语法呢

31
00:00:52,180 --> 00:00:52,800
进行转化

32
00:00:52,800 --> 00:00:53,740
稍等

33
00:00:53,740 --> 00:00:55,600
这里面还是那个错误啊

34
00:00:55,600 --> 00:00:56,700
我把它先住掉

35
00:00:56,700 --> 00:00:57,660
住掉

36
00:00:57,660 --> 00:00:58,920
我再来一次是吧

37
00:00:58,920 --> 00:01:01,480
进行

38
00:01:01,480 --> 00:01:04,200
完了里面就会告诉我

39
00:01:04,200 --> 00:01:05,500
成功了是吧

40
00:01:05,500 --> 00:01:07,040
当然当然还是语法错误

41
00:01:07,040 --> 00:01:08,600
但是有没有打包出来

42
00:01:08,600 --> 00:01:09,780
我们来看一下

43
00:01:09,780 --> 00:01:11,200
但是它已经被压缩了

44
00:01:11,200 --> 00:01:12,660
这里面我们为了方便

45
00:01:12,660 --> 00:01:14,220
我先把模式改回来

46
00:01:14,220 --> 00:01:15,180
我把这个东西

47
00:01:15,180 --> 00:01:18,800
用把mode改成DVEOPMNT

48
00:01:18,800 --> 00:01:20,140
为了不报错

49
00:01:20,140 --> 00:01:21,800
我先把优化项先住掉好吧

50
00:01:21,800 --> 00:01:22,560
这样的话

51
00:01:22,560 --> 00:01:23,860
其实就不会走优化项了

52
00:01:23,860 --> 00:01:24,900
因为现在是开发环境

53
00:01:24,900 --> 00:01:25,600
好

54
00:01:25,600 --> 00:01:26,260
再来一下

55
00:01:26,260 --> 00:01:28,200
这里依旧

56
00:01:28,200 --> 00:01:29,400
看看效果

57
00:01:29,400 --> 00:01:30,940
大包出来了

58
00:01:30,940 --> 00:01:31,480
没有包错

59
00:01:31,480 --> 00:01:32,660
因为是开发环境

60
00:01:32,660 --> 00:01:34,700
这里我们去看一下bundle

61
00:01:34,700 --> 00:01:36,100
完了里面的结果

62
00:01:36,100 --> 00:01:37,800
其实还是这样一个见头函数

63
00:01:37,800 --> 00:01:38,740
折一下

64
00:01:38,740 --> 00:01:39,740
完了里面

65
00:01:39,740 --> 00:01:40,840
你看看是不是见头函数

66
00:01:40,840 --> 00:01:43,800
这时候我们希望把ES6转成ES5

67
00:01:43,800 --> 00:01:44,920
非常方便了

68
00:01:44,920 --> 00:01:46,080
我们就需要用babel

69
00:01:46,080 --> 00:01:47,960
babel的话我们怎么用

70
00:01:47,960 --> 00:01:49,480
首先第一步还是一样

71
00:01:49,480 --> 00:01:51,080
我们需要处理这个模块

72
00:01:51,080 --> 00:01:52,200
肯定需要怎么样

73
00:01:52,200 --> 00:01:53,520
把原文件经转换

74
00:01:53,520 --> 00:01:54,700
转换成另一种输出

75
00:01:54,700 --> 00:01:56,300
这时候我们肯定需要用到

76
00:01:56,300 --> 00:01:57,540
这样一个babel Loader

77
00:01:57,540 --> 00:01:58,720
他可以怎么样

78
00:01:58,720 --> 00:01:59,940
进行转化的一个下载器

79
00:01:59,940 --> 00:02:03,440
转化的时候肯定需要用了我们的babel去转化

80
00:02:03,440 --> 00:02:05,920
这时候我们还需要一个叫babel core

81
00:02:05,920 --> 00:02:07,340
这个模块

82
00:02:07,340 --> 00:02:08,940
这就是babel的核心模块

83
00:02:08,940 --> 00:02:11,580
它可以调我们的一个穿风方法

84
00:02:11,580 --> 00:02:12,780
进行转化我们的源代码

85
00:02:12,780 --> 00:02:14,420
同样怎么转化

86
00:02:14,420 --> 00:02:15,660
还需要告诉人家

87
00:02:15,660 --> 00:02:17,960
我希望把es6转成es5

88
00:02:17,960 --> 00:02:19,460
有一个这样的模块

89
00:02:19,460 --> 00:02:21,020
它是一个转化模块

90
00:02:21,020 --> 00:02:21,740
也叫env

91
00:02:21,740 --> 00:02:23,440
它可以把一些标准的语法

92
00:02:23,440 --> 00:02:24,760
转成D级的语法

93
00:02:24,760 --> 00:02:25,360
好了

94
00:02:25,360 --> 00:02:26,560
这里我安装一下

95
00:02:26,560 --> 00:02:27,740
钢地

96
00:02:27,740 --> 00:02:29,040
完了里面呢

97
00:02:29,040 --> 00:02:31,560
我就直接安装了

98
00:02:31,560 --> 00:02:32,340
先用着

99
00:02:32,340 --> 00:02:34,000
我们在webpack里面配置一下

100
00:02:34,000 --> 00:02:35,760
找到我们的webpack配置

101
00:02:35,760 --> 00:02:36,720
完了里面呢

102
00:02:36,720 --> 00:02:37,940
我们需要配上这样一个

103
00:02:37,940 --> 00:02:40,000
接着再配个规则是吧

104
00:02:40,000 --> 00:02:40,760
这里呢

105
00:02:40,760 --> 00:02:41,600
我需要匹配

106
00:02:41,600 --> 00:02:42,640
以什么呢

107
00:02:42,640 --> 00:02:44,160
以这个gs结尾的

108
00:02:44,160 --> 00:02:45,820
完了我们需要use对吧

109
00:02:45,820 --> 00:02:46,520
跟人说一下

110
00:02:46,520 --> 00:02:48,800
use我们的这样一个babel loader

111
00:02:48,800 --> 00:02:49,620
但是为了方便

112
00:02:49,620 --> 00:02:50,260
我还说了

113
00:02:50,260 --> 00:02:51,800
如果你要加参数的话

114
00:02:51,800 --> 00:02:52,900
最好这样配是吧

115
00:02:52,900 --> 00:02:54,460
叫babel loader

116
00:02:54,460 --> 00:02:55,360
完了里面呢

117
00:02:55,360 --> 00:02:56,040
我们同样啊

118
00:02:56,040 --> 00:02:57,040
需要配个options

119
00:02:57,040 --> 00:02:59,440
这个options我们可以写到外面去

120
00:02:59,440 --> 00:03:00,240
你可以写到里面

121
00:03:00,240 --> 00:03:01,340
写到里面怎么写

122
00:03:01,340 --> 00:03:03,840
我们可以跟他说用这个是吧

123
00:03:03,840 --> 00:03:04,260
用

124
00:03:04,260 --> 00:03:05,940
babel loader

125
00:03:05,940 --> 00:03:06,840
babel loader

126
00:03:06,840 --> 00:03:07,940
完了需要怎么样

127
00:03:07,940 --> 00:03:11,340
需要把es6转换成es5

128
00:03:11,340 --> 00:03:13,640
刚才我们下了一个大包

129
00:03:13,640 --> 00:03:15,040
那个包叫什么

130
00:03:15,040 --> 00:03:17,040
叫有点慢是吧

131
00:03:17,040 --> 00:03:18,740
叫babel preset因为

132
00:03:18,740 --> 00:03:20,540
他的功能就是用来怎么样

133
00:03:20,540 --> 00:03:22,640
转化我们所有的gs语法的

134
00:03:22,640 --> 00:03:25,340
所以这里面我可以给他添一个大的插件库

135
00:03:25,340 --> 00:03:25,840
对吧

136
00:03:25,840 --> 00:03:26,320
叫什么呢

137
00:03:26,320 --> 00:03:27,840
叫预设precess

138
00:03:27,840 --> 00:03:28,860
网站里面呢

139
00:03:28,860 --> 00:03:31,060
我就可以直接把我们的这个babel

140
00:03:31,060 --> 00:03:31,600
杠

141
00:03:31,600 --> 00:03:32,840
爱他写全名

142
00:03:32,840 --> 00:03:33,940
爱的babel

143
00:03:33,940 --> 00:03:35,000
完了杠

144
00:03:35,000 --> 00:03:35,640
precess

145
00:03:35,640 --> 00:03:36,480
因为放在这

146
00:03:36,480 --> 00:03:37,440
那他里面呢

147
00:03:37,440 --> 00:03:39,720
包含着比如说把esu转成es5的模块

148
00:03:39,720 --> 00:03:41,360
他会去调用这样一个模块的

149
00:03:41,360 --> 00:03:42,500
去处理我们的阶段文件

150
00:03:42,500 --> 00:03:43,500
哎那好了

151
00:03:43,500 --> 00:03:44,900
我们来看一下

152
00:03:44,900 --> 00:03:45,980
好像网不太好

153
00:03:45,980 --> 00:03:46,520
这里呢

154
00:03:46,520 --> 00:03:47,480
我去安装一下

155
00:03:47,480 --> 00:03:48,260
在啊

156
00:03:48,260 --> 00:03:48,960
走你

157
00:03:48,960 --> 00:03:50,900
看看啊

158
00:03:50,900 --> 00:03:51,280
什么原因

159
00:03:51,280 --> 00:03:52,600
我点一下网络

160
00:03:52,600 --> 00:03:57,080
好

161
00:03:57,080 --> 00:03:57,840
我们这回呢

162
00:03:57,840 --> 00:03:58,600
安装成功了

163
00:03:58,600 --> 00:03:59,760
那现在呢

164
00:03:59,760 --> 00:04:01,040
我们来看一下啊

165
00:04:01,040 --> 00:04:01,640
那这时候呢

166
00:04:01,640 --> 00:04:03,140
我们再去运行npx

167
00:04:03,140 --> 00:04:03,720
webpack

168
00:04:03,720 --> 00:04:04,840
看看是否呢

169
00:04:04,840 --> 00:04:05,840
能把我们的gs呢

170
00:04:05,840 --> 00:04:06,440
进行转化

171
00:04:06,440 --> 00:04:07,380
ok

172
00:04:07,380 --> 00:04:08,580
稍等

173
00:04:08,580 --> 00:04:11,460
你发现是可以的啊

174
00:04:11,460 --> 00:04:12,000
并且呢

175
00:04:12,000 --> 00:04:14,200
我这时候也用了我们那个啊

176
00:04:14,200 --> 00:04:14,720
刚才那个

177
00:04:14,720 --> 00:04:15,880
aglifygs

178
00:04:15,880 --> 00:04:16,660
你看这里

179
00:04:16,660 --> 00:04:18,560
我们也用再用这个gs啊

180
00:04:18,560 --> 00:04:19,260
把它打开

181
00:04:19,260 --> 00:04:21,240
看看会不会有这个问题了啊

182
00:04:21,240 --> 00:04:22,280
把它拿过来

183
00:04:22,280 --> 00:04:26,760
因为刚才我们并没有配着解析GIS模块的

184
00:04:26,760 --> 00:04:27,500
所以说它有异常

185
00:04:27,500 --> 00:04:28,960
现在也是ok的

186
00:04:28,960 --> 00:04:32,260
这时候我们在目录下来跑一下文件

187
00:04:32,260 --> 00:04:34,880
我为了方便就执行npm run dv

188
00:04:34,880 --> 00:04:36,720
npm run dv

189
00:04:36,720 --> 00:04:42,060
ok 完这里我们就访问一下

190
00:04:42,060 --> 00:04:42,460
稍等

191
00:04:42,460 --> 00:04:44,360
里面localhouse8080

192
00:04:44,360 --> 00:04:45,480
我们看看效果

193
00:04:45,480 --> 00:04:47,320
这里刷新

194
00:04:47,320 --> 00:04:48,720
发现是可以的

195
00:04:48,720 --> 00:04:51,160
并且我们的结果应该也是打印出来了

196
00:04:51,160 --> 00:04:53,540
现在我们的gs解析了

197
00:04:53,540 --> 00:04:55,020
并且我们可以看一下

198
00:04:55,020 --> 00:04:56,980
现在的打包后的代码

199
00:04:56,980 --> 00:04:58,220
刚才我们打包后一次

200
00:04:58,220 --> 00:05:00,080
我们看看函数没有变

201
00:05:00,080 --> 00:05:01,460
刚才我们叫fn函数

202
00:05:01,460 --> 00:05:04,140
现在是变成了哇这种模式了

203
00:05:04,140 --> 00:05:06,600
现在我们已经把es6怎么样

204
00:05:06,600 --> 00:05:08,260
装翻成我们所谓的es5了

205
00:05:08,260 --> 00:05:10,440
同样可能es6里面

206
00:05:10,440 --> 00:05:11,700
还有一些比较高级的语法

207
00:05:11,700 --> 00:05:13,900
像这个我们写一个

208
00:05:13,900 --> 00:05:15,060
来个class

209
00:05:15,060 --> 00:05:16,820
a函数

210
00:05:16,820 --> 00:05:19,320
这时候我们可以在这写个a0e

211
00:05:19,320 --> 00:05:20,540
其实我们都知道

212
00:05:20,540 --> 00:05:22,600
这是一种es6的变种的写法

213
00:05:22,600 --> 00:05:25,260
相当于给new的a上面怎么样

214
00:05:25,260 --> 00:05:26,540
是不是加了个属性

215
00:05:26,540 --> 00:05:28,420
完了叫什么叫a等于1

216
00:05:28,420 --> 00:05:31,240
相当于给实例上添加了个a的属性等于1

217
00:05:31,240 --> 00:05:32,500
但这种语法

218
00:05:32,500 --> 00:05:35,040
是e7的语法

219
00:05:35,040 --> 00:05:36,100
它并不会支持

220
00:05:36,100 --> 00:05:37,320
你看人家直接就报错了

221
00:05:37,320 --> 00:05:39,300
说你需要加上这样一个插件

222
00:05:39,300 --> 00:05:39,860
叫什么呢

223
00:05:39,860 --> 00:05:41,100
叫plugin process

224
00:05:41,100 --> 00:05:43,060
就是一个插件是提案里的

225
00:05:43,060 --> 00:05:43,280
对吧

226
00:05:43,280 --> 00:05:44,620
一个类的属性的插件

227
00:05:44,620 --> 00:05:45,280
那好了

228
00:05:45,280 --> 00:05:46,140
那你在这也怎么样

229
00:05:46,140 --> 00:05:47,360
可以这样去用了

230
00:05:47,360 --> 00:05:47,960
比如来这

231
00:05:47,960 --> 00:05:49,700
加R的OK

232
00:05:49,700 --> 00:05:51,560
当地是吧

233
00:05:51,560 --> 00:05:52,560
那这里面呢

234
00:05:52,560 --> 00:05:53,880
我们就可以跑下来

235
00:05:53,880 --> 00:05:54,760
应该就OK了

236
00:05:54,760 --> 00:05:56,600
按好是吧

237
00:05:56,600 --> 00:05:57,120
稍等

238
00:05:57,120 --> 00:05:59,260
那OK了以后呢

239
00:05:59,260 --> 00:05:59,920
我们就可以怎么样

240
00:05:59,920 --> 00:06:01,720
别忘了还需要配置这样一个插件

241
00:06:01,720 --> 00:06:02,540
在这里呢

242
00:06:02,540 --> 00:06:03,300
我们需要在这里

243
00:06:03,300 --> 00:06:05,420
这是一个大插件的集合

244
00:06:05,420 --> 00:06:07,400
我们还需要配一个个的小插件

245
00:06:07,400 --> 00:06:08,420
所以这里面呢

246
00:06:08,420 --> 00:06:10,880
我们需要一个叫plugins插件

247
00:06:10,880 --> 00:06:11,980
那好了

248
00:06:11,980 --> 00:06:12,960
我再来跑一下

249
00:06:12,960 --> 00:06:14,080
run dv

250
00:06:14,080 --> 00:06:15,640
这个名字啊

251
00:06:15,640 --> 00:06:16,480
不用去记啊

252
00:06:16,480 --> 00:06:17,160
它很长

253
00:06:17,160 --> 00:06:18,540
我们也不需要去记

254
00:06:18,540 --> 00:06:20,580
这时候看效果

255
00:06:20,580 --> 00:06:22,240
应该是没有问题的

256
00:06:22,240 --> 00:06:23,660
我把代码跑一下

257
00:06:23,660 --> 00:06:27,000
在这里直接let个a等于new一个a

258
00:06:27,000 --> 00:06:28,580
完了我打印一下

259
00:06:28,580 --> 00:06:30,640
看看小a上有没有a属性

260
00:06:30,640 --> 00:06:32,160
我打印出来

261
00:06:32,160 --> 00:06:33,340
说明是不是可以了

262
00:06:33,340 --> 00:06:35,800
同样除了这样的一个写法

263
00:06:35,800 --> 00:06:37,020
可能我们一般常用的

264
00:06:37,020 --> 00:06:37,860
除了类属性

265
00:06:37,860 --> 00:06:39,540
还有我们的类的装饰器

266
00:06:39,540 --> 00:06:40,420
比如说叫log

267
00:06:40,420 --> 00:06:42,360
我们都知道它可以装饰什么

268
00:06:42,360 --> 00:06:44,060
我们所谓的类或者属性

269
00:06:44,060 --> 00:06:44,540
放在上面

270
00:06:44,540 --> 00:06:46,180
我就装饰这样的类

271
00:06:46,180 --> 00:06:48,140
但语法它也是不认的

272
00:06:48,140 --> 00:06:49,680
他又告诉我这网上报错了

273
00:06:49,680 --> 00:06:52,120
这个错误他告诉我其实是没有装饰器

274
00:06:52,120 --> 00:06:53,360
你可以直接告诉你

275
00:06:53,360 --> 00:06:55,540
他需要安装这样一个叫decreator

276
00:06:55,540 --> 00:06:56,580
来的西是吧

277
00:06:56,580 --> 00:06:57,820
这里面一样

278
00:06:57,820 --> 00:06:59,680
我们可以去babel官网上去找

279
00:06:59,680 --> 00:07:00,160
是吧

280
00:07:00,160 --> 00:07:00,860
我babel

281
00:07:00,860 --> 00:07:02,960
你比如说想用decreator装饰器

282
00:07:02,960 --> 00:07:05,120
好你就搜decreator是吧

283
00:07:05,120 --> 00:07:05,860
这里面就有了

284
00:07:05,860 --> 00:07:09,000
你看是不是也是提案中的一个装饰器语法

285
00:07:09,000 --> 00:07:11,320
但是慢慢我估计会变成正式版了

286
00:07:11,320 --> 00:07:14,040
这里我们就找一下配置直接粘过去好了

287
00:07:14,040 --> 00:07:17,000
他就说了这地方需要怎么样按顺序来写

288
00:07:17,000 --> 00:07:19,740
先去加我们的的decreate的装饰器

289
00:07:19,740 --> 00:07:21,800
这个属性装饰器对吧

290
00:07:21,800 --> 00:07:24,400
完之后再去用我们的类属性是吧

291
00:07:24,400 --> 00:07:26,100
而且要标上这是legacy true

292
00:07:26,100 --> 00:07:27,300
这是一个宽松模式

293
00:07:27,300 --> 00:07:27,800
ok

294
00:07:27,800 --> 00:07:29,800
这就是一个默认配置

295
00:07:29,800 --> 00:07:32,500
然后把它加到我们的配置里面来

296
00:07:32,500 --> 00:07:33,000
是吧

297
00:07:33,000 --> 00:07:35,200
看看能不能达到我的预期

298
00:07:35,200 --> 00:07:37,900
这里我再去跑一下run dv

299
00:07:37,900 --> 00:07:40,200
或者就run build也好无所谓

300
00:07:40,200 --> 00:07:42,700
完了这里面运行的时候

301
00:07:42,700 --> 00:07:44,200
他告诉我这玩意报错了

302
00:07:44,200 --> 00:07:44,800
咋说的呢

303
00:07:44,800 --> 00:07:48,000
说这个webpack好像这个插件包错了

304
00:07:48,000 --> 00:07:48,740
包了好多

305
00:07:48,740 --> 00:07:49,600
在网上找一找

306
00:07:49,600 --> 00:07:51,100
看来是不是哪写错了

307
00:07:51,100 --> 00:07:53,680
说intel.js没有找到这样一个模块

308
00:07:53,680 --> 00:07:55,340
我模块没安装是吧

309
00:07:55,340 --> 00:07:57,140
其实他挺对的

310
00:07:57,140 --> 00:07:57,840
在这里

311
00:07:57,840 --> 00:07:59,080
一按对吧

312
00:07:59,080 --> 00:08:00,040
一按二的

313
00:08:00,040 --> 00:08:01,620
ok-d是吧

314
00:08:01,620 --> 00:08:03,980
这名字都是很很很有特点的

315
00:08:03,980 --> 00:08:05,860
叫@babel-plugin

316
00:08:05,860 --> 00:08:07,420
propso这叫decreator

317
00:08:07,420 --> 00:08:10,120
那个叫propsoclass properties是吧

318
00:08:10,120 --> 00:08:11,300
这样两个东西

319
00:08:11,300 --> 00:08:13,460
好当他安装好

320
00:08:13,460 --> 00:08:14,300
安装好以后

321
00:08:14,300 --> 00:08:16,100
我们就应该可以跑了

322
00:08:16,100 --> 00:08:18,200
然后在这里面再去转DV

323
00:08:18,200 --> 00:08:19,180
你可以看到

324
00:08:19,180 --> 00:08:20,680
这样一个装饰器

325
00:08:20,680 --> 00:08:21,620
我们都知道怎么用

326
00:08:21,620 --> 00:08:24,740
我们在Rate中其实也都讲过了

327
00:08:24,740 --> 00:08:25,820
我们回到这一来

328
00:08:25,820 --> 00:08:28,240
我们可以在这里直接去function

329
00:08:28,240 --> 00:08:29,920
我说装饰器其实就是个函数

330
00:08:29,920 --> 00:08:31,680
完了如果装饰类的话

331
00:08:31,680 --> 00:08:33,760
它的第一个参数就是类

332
00:08:33,760 --> 00:08:35,000
我们可以在这打印一下

333
00:08:35,000 --> 00:08:36,880
看看是不是这样一个打类

334
00:08:36,880 --> 00:08:38,720
完了运行

335
00:08:38,720 --> 00:08:40,420
看看有没有刷新

336
00:08:40,420 --> 00:08:42,740
这里面不能打A

337
00:08:42,740 --> 00:08:43,780
应该打target

338
00:08:43,780 --> 00:08:45,540
这里为了看得到

339
00:08:45,540 --> 00:08:46,660
我就来个23行

340
00:08:46,660 --> 00:08:47,860
我刷新一下

341
00:08:47,860 --> 00:08:50,060
你看这时候是不是就可以了

342
00:08:50,060 --> 00:08:51,320
你看是不是我们的

343
00:08:51,320 --> 00:08:52,320
点过来了

344
00:08:52,320 --> 00:08:53,160
再点回去

345
00:08:53,160 --> 00:08:54,200
console

346
00:08:54,200 --> 00:08:56,260
看是不是就这一个A

347
00:08:56,260 --> 00:08:57,040
是23号

348
00:08:57,040 --> 00:08:58,840
现在我们就实现了个什么

349
00:08:58,840 --> 00:09:00,780
是不是把一些高级预法

350
00:09:00,780 --> 00:09:02,540
转成了我们的ES5预法

