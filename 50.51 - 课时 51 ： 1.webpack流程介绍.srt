1
00:00:00,000 --> 00:00:02,640
本章节呢

2
00:00:02,640 --> 00:00:04,900
我们来讲一下这个YPAC中的plugin

3
00:00:04,900 --> 00:00:06,100
我们都知道啊

4
00:00:06,100 --> 00:00:09,160
其实YPAC呢通过plugin呢来实现了我们的各种功能

5
00:00:09,160 --> 00:00:11,920
比如说我们希望呢引入一些自己的逻辑

6
00:00:11,920 --> 00:00:13,900
往来加入到YPAC的构建流程中

7
00:00:13,900 --> 00:00:16,820
那这时候呢我们就需要通过自己来编写一些插件

8
00:00:16,820 --> 00:00:21,320
但是啊如果我们要学习这个YPACplugin的编写呢

9
00:00:21,320 --> 00:00:24,400
我们是需要掌握一下这个YPAC底层内部特性的

10
00:00:24,400 --> 00:00:26,060
因为在我们之前也看到了

11
00:00:26,060 --> 00:00:28,300
我们呢自己手写了一把这个简易的YPAC

12
00:00:28,300 --> 00:00:29,260
它里面呢

13
00:00:29,260 --> 00:00:31,380
其实靠的都是我们的所谓的勾参数

14
00:00:31,380 --> 00:00:33,420
就是我们可以通过这个typeable实践流

15
00:00:33,420 --> 00:00:34,260
往来来呢

16
00:00:34,260 --> 00:00:35,040
绑定一些世界

17
00:00:35,040 --> 00:00:35,860
触发一些世界

18
00:00:35,860 --> 00:00:37,000
那这样的话呢

19
00:00:37,000 --> 00:00:38,880
我们可以自己编写插件的

20
00:00:38,880 --> 00:00:40,160
来注册这些实践

21
00:00:40,160 --> 00:00:40,780
这样呢

22
00:00:40,780 --> 00:00:42,460
我们的webpack在构建的时候呢

23
00:00:42,460 --> 00:00:44,520
就会帮我们去调用我们绑定的方法

24
00:00:44,520 --> 00:00:45,340
那好了

25
00:00:45,340 --> 00:00:46,820
那我们来看一下吧

26
00:00:46,820 --> 00:00:49,020
先来了解一下这个webpack的大致流程

27
00:00:49,020 --> 00:00:49,820
这样的话呢

28
00:00:49,820 --> 00:00:50,960
等会我们编写的时候呢

29
00:00:50,960 --> 00:00:52,060
也会更加容易一些

30
00:00:52,060 --> 00:00:53,280
我们在这里呢

31
00:00:53,280 --> 00:00:54,300
大家看一下啊

32
00:00:54,300 --> 00:00:55,560
把这个图片打开

33
00:00:55,560 --> 00:00:56,820
找一张大的地址

34
00:00:56,820 --> 00:00:59,080
这打开了

35
00:00:59,080 --> 00:01:00,200
把它粘过来

36
00:01:00,200 --> 00:01:01,820
OK

37
00:01:01,820 --> 00:01:03,560
这就是一个WIPAC勾建的影片

38
00:01:03,560 --> 00:01:05,300
这里面刚开始

39
00:01:05,300 --> 00:01:06,900
我们WIPAC勾建的时候

40
00:01:06,900 --> 00:01:09,280
肯定需要通过一些命令行参数

41
00:01:09,280 --> 00:01:11,100
还有我们的WIPAC Confirm GS

42
00:01:11,100 --> 00:01:12,740
完了合并出来一个什么

43
00:01:12,740 --> 00:01:14,260
是去整个的一个对象

44
00:01:14,260 --> 00:01:15,300
也就是我们的参数

45
00:01:15,300 --> 00:01:16,220
参数列表

46
00:01:16,220 --> 00:01:18,020
当然咱也写过这个结极的WIPAC了

47
00:01:18,020 --> 00:01:20,100
那么拿到这样一个列表以后

48
00:01:20,100 --> 00:01:22,180
需要传给我们这样一个WIPAC

49
00:01:22,180 --> 00:01:23,980
WIPAC它内部

50
00:01:23,980 --> 00:01:25,980
主要就是创建了一个compiler

51
00:01:25,980 --> 00:01:28,680
当然你也看到可以去new一个compiler对象

52
00:01:28,680 --> 00:01:30,860
完了这里面可以干什么事

53
00:01:30,860 --> 00:01:32,900
当我们创建这样一个对象以后

54
00:01:32,900 --> 00:01:33,900
其实就开始怎么样

55
00:01:33,900 --> 00:01:35,740
执行我们的run方法开始编译了

56
00:01:35,740 --> 00:01:36,920
编译的话

57
00:01:36,920 --> 00:01:38,420
其实主要我们说了

58
00:01:38,420 --> 00:01:41,280
这compiler我们每一个webpack运行的时候

59
00:01:41,280 --> 00:01:42,260
只会产生一次

60
00:01:42,260 --> 00:01:44,020
每次编译的时候

61
00:01:44,020 --> 00:01:46,480
还会再产生个对象叫complication

62
00:01:46,480 --> 00:01:48,700
它俩的区别是什么

63
00:01:48,700 --> 00:01:49,800
是一个只有一份

64
00:01:49,800 --> 00:01:51,220
一个是每次编译

65
00:01:51,220 --> 00:01:52,240
比如代码有更改

66
00:01:52,240 --> 00:01:54,460
就会创建这样一个complex对象

67
00:01:54,460 --> 00:01:57,000
它代表的就是当前的变异

68
00:01:57,000 --> 00:01:59,140
这里可能会包含了一些属性

69
00:01:59,140 --> 00:02:00,240
像什么modus

70
00:02:00,240 --> 00:02:01,240
装着所有的模块

71
00:02:01,240 --> 00:02:02,260
还有我们的trunks

72
00:02:02,260 --> 00:02:03,080
一些代码块

73
00:02:03,080 --> 00:02:04,760
当然了我们这里要注意

74
00:02:04,760 --> 00:02:06,740
complication上有非常重要的属性

75
00:02:06,740 --> 00:02:07,540
叫assess

76
00:02:07,540 --> 00:02:11,180
它代表我们当前变异的所有的资源

77
00:02:11,180 --> 00:02:12,280
比如说产生的atml

78
00:02:12,280 --> 00:02:13,320
gs css

79
00:02:13,320 --> 00:02:14,460
都在这个对象里

80
00:02:14,460 --> 00:02:16,640
同样里面还有一些

81
00:02:16,640 --> 00:02:17,660
它自己所谓的模板

82
00:02:17,660 --> 00:02:18,500
我们都知道

83
00:02:18,500 --> 00:02:20,140
需要拿到我们的modus

84
00:02:20,140 --> 00:02:21,060
还有我们的入口

85
00:02:21,060 --> 00:02:21,800
完了怎么样

86
00:02:21,800 --> 00:02:22,820
配合使用

87
00:02:22,820 --> 00:02:23,940
完了通过我们的模板

88
00:02:23,940 --> 00:02:24,940
选出来是吧

89
00:02:24,940 --> 00:02:26,260
这里面我们就知道

90
00:02:26,260 --> 00:02:27,460
模板大约有这么几种

91
00:02:27,460 --> 00:02:29,280
有什么主模板代码模块

92
00:02:29,280 --> 00:02:30,580
当然了还有什么热加载

93
00:02:30,580 --> 00:02:31,380
热更新

94
00:02:31,380 --> 00:02:32,740
网友里面还有我们的

95
00:02:32,740 --> 00:02:34,060
预行预行时代码模块

96
00:02:34,060 --> 00:02:34,380
对吧

97
00:02:34,380 --> 00:02:35,660
还有我们的模块的模板

98
00:02:35,660 --> 00:02:36,980
这里我们先不介绍

99
00:02:36,980 --> 00:02:38,500
那我们接着往下看

100
00:02:38,500 --> 00:02:39,880
那编译完以后

101
00:02:39,880 --> 00:02:41,420
可能我们还需要怎么样

102
00:02:41,420 --> 00:02:42,200
是不是开始就是

103
00:02:42,200 --> 00:02:43,400
构建我们的模块了

104
00:02:43,400 --> 00:02:45,400
开始就会调一个make方法

105
00:02:45,400 --> 00:02:46,780
这个方法里面会干些事

106
00:02:46,780 --> 00:02:47,080
对吧

107
00:02:47,080 --> 00:02:48,240
比如说他会怎么样

108
00:02:48,240 --> 00:02:49,660
去对我们的模块进行编译

109
00:02:49,660 --> 00:02:51,120
当然咱也写过这样一个方法

110
00:02:51,120 --> 00:02:51,900
叫build module

111
00:02:51,900 --> 00:02:54,120
他会去创建这样一个主模块

112
00:02:54,120 --> 00:02:57,340
通过这个主模块去分析什么依赖

113
00:02:57,340 --> 00:02:58,300
往来来怎么样

114
00:02:58,300 --> 00:03:02,100
对应的把我们所有的模块都放到一个modus最象上

115
00:03:02,100 --> 00:03:03,060
当然咱也看见过

116
00:03:03,060 --> 00:03:06,020
那处理完这样一个我们的低规依赖的模块以后

117
00:03:06,020 --> 00:03:07,740
那我们所有的模块里面怎么样

118
00:03:07,740 --> 00:03:08,620
是不是就放进去了

119
00:03:08,620 --> 00:03:10,420
当然了这里面放进去以后

120
00:03:10,420 --> 00:03:12,500
我们是不是还要去解析文件的内容

121
00:03:12,500 --> 00:03:14,640
通过我们的体育法术

122
00:03:14,640 --> 00:03:16,140
往来解析你看人写了对吧

123
00:03:16,140 --> 00:03:18,680
里面需要通过这个normalmodule来解析

124
00:03:18,680 --> 00:03:19,840
解析完以后怎么样

125
00:03:19,840 --> 00:03:21,700
碰去看看有没有require依赖

126
00:03:21,700 --> 00:03:22,680
有依赖的话怎么样

127
00:03:22,680 --> 00:03:23,880
再去递规加载

128
00:03:23,880 --> 00:03:25,900
那这样一个流程

129
00:03:25,900 --> 00:03:26,680
加载完以后

130
00:03:26,680 --> 00:03:27,480
我们可以怎么样

131
00:03:27,480 --> 00:03:28,500
比如说我们要打包

132
00:03:28,500 --> 00:03:30,280
肯定需要它里面有个内部方法

133
00:03:30,280 --> 00:03:30,740
叫seal

134
00:03:30,740 --> 00:03:31,280
封装

135
00:03:31,280 --> 00:03:32,960
我们需要把这个模块

136
00:03:32,960 --> 00:03:34,040
代码进行整理

137
00:03:34,040 --> 00:03:35,580
最后生生编译

138
00:03:35,580 --> 00:03:37,200
完了最后我们需要怎么样

139
00:03:37,200 --> 00:03:38,940
是不是开始去优化我们的代码

140
00:03:38,940 --> 00:03:39,740
完了去怎么样

141
00:03:39,740 --> 00:03:40,200
打包

142
00:03:40,200 --> 00:03:41,420
打包的时候

143
00:03:41,420 --> 00:03:43,420
我们需要通过我们的模板

144
00:03:43,420 --> 00:03:44,460
加上我们的数据

145
00:03:44,460 --> 00:03:45,820
最后产生一个什么

146
00:03:45,820 --> 00:03:47,420
完整的一个输出文件

147
00:03:47,420 --> 00:03:49,720
完了最终我们需要怎么样

148
00:03:49,720 --> 00:03:51,500
把这个文件进行发射

149
00:03:51,500 --> 00:03:53,320
通过这个emate和sess对吧

150
00:03:53,320 --> 00:03:55,440
来发射出到一个我们的输出目录下

151
00:03:55,440 --> 00:03:57,500
这就是一个webpack大致流程

152
00:03:57,500 --> 00:03:59,360
后面当时写的那个减版的webpack

153
00:03:59,360 --> 00:04:00,900
其实流程是一样的

154
00:04:00,900 --> 00:04:03,340
我们就是通过这样一个流程来抽一出来的

155
00:04:03,340 --> 00:04:04,860
这里面我们要说什么

156
00:04:04,860 --> 00:04:07,540
说这里面其实有很多的我们所谓的叫什么

157
00:04:07,540 --> 00:04:09,060
这么多都叫构图函数

158
00:04:09,060 --> 00:04:10,840
我们大致的来看一看

159
00:04:10,840 --> 00:04:12,360
但这些函数我们有很多

160
00:04:12,360 --> 00:04:14,080
大约有六七十个构子

161
00:04:14,080 --> 00:04:16,060
我们就介绍一些重要的构子

162
00:04:16,060 --> 00:04:18,700
先来找

163
00:04:18,700 --> 00:04:21,140
这里我写了一个文档

164
00:04:21,500 --> 00:04:22,520
刚开始我们说了

165
00:04:22,520 --> 00:04:23,600
有两个重要的对象

166
00:04:23,600 --> 00:04:24,100
我们要知道

167
00:04:24,100 --> 00:04:25,020
一个叫compiler

168
00:04:25,020 --> 00:04:26,240
一个叫complication

169
00:04:26,240 --> 00:04:27,480
这compiler呢

170
00:04:27,480 --> 00:04:28,980
它代表的是我们的编译对象

171
00:04:28,980 --> 00:04:30,980
我说了圈局下只有一份

172
00:04:30,980 --> 00:04:32,300
那complication呢

173
00:04:32,300 --> 00:04:33,760
代表每次构建的时候呢

174
00:04:33,760 --> 00:04:34,840
都会产生这样一个对象

175
00:04:34,840 --> 00:04:36,700
那编译的时候非常简单

176
00:04:36,700 --> 00:04:37,820
可以开始运行

177
00:04:37,820 --> 00:04:39,160
往来开始怎么样编译

178
00:04:39,160 --> 00:04:40,320
那编译的时候呢

179
00:04:40,320 --> 00:04:42,060
会创建这样一个complication对象

180
00:04:42,060 --> 00:04:43,440
往来开始创建模块

181
00:04:43,440 --> 00:04:44,260
往来发射

182
00:04:44,260 --> 00:04:44,720
还有档

183
00:04:44,720 --> 00:04:46,260
这些都是它所谓的什么

184
00:04:46,260 --> 00:04:47,200
自身的钩子

185
00:04:47,200 --> 00:04:50,180
那我们编译这个complication资源的时候呢

186
00:04:50,180 --> 00:04:50,560
也一样

187
00:04:50,560 --> 00:04:51,520
我们可能会怎么样

188
00:04:51,520 --> 00:04:53,200
是不是开始构建我们的模块

189
00:04:53,200 --> 00:04:53,860
完了怎么样

190
00:04:53,860 --> 00:04:54,640
构建模块以后

191
00:04:54,640 --> 00:04:55,880
开始去加载这个模块

192
00:04:55,880 --> 00:04:56,860
完加载模块

193
00:04:56,860 --> 00:04:58,460
当前模块加载完

194
00:04:58,460 --> 00:05:00,060
会触发一个叫Scceed Module

195
00:05:00,060 --> 00:05:02,180
当然这只是一个模块

196
00:05:02,180 --> 00:05:03,260
我们还需要怎么样

197
00:05:03,260 --> 00:05:04,460
递规加载依赖

198
00:05:04,460 --> 00:05:06,420
这时候我们可能有个叫Finish Module

199
00:05:06,420 --> 00:05:08,360
完成我们的所有模块的加载

200
00:05:08,360 --> 00:05:10,780
最终我们需要干嘛

201
00:05:10,780 --> 00:05:12,400
是不是封装代码整理

202
00:05:12,400 --> 00:05:14,700
完之后调用我们的什么Automize优化项

203
00:05:14,700 --> 00:05:16,380
去优化我们的构建出来的代码

204
00:05:16,380 --> 00:05:19,020
完最后会触发AfterSeal

205
00:05:19,020 --> 00:05:19,600
封装后

206
00:05:19,600 --> 00:05:21,760
那都封装完以后干嘛了

207
00:05:21,760 --> 00:05:22,360
那很简单

208
00:05:22,360 --> 00:05:23,600
我们是不是就要发射文件了

209
00:05:23,600 --> 00:05:24,780
但是在这个之中

210
00:05:24,780 --> 00:05:26,800
我们说了构建模块的时候

211
00:05:26,800 --> 00:05:27,580
还有一些特点

212
00:05:27,580 --> 00:05:29,440
比如说有这个解析我们模块

213
00:05:29,440 --> 00:05:31,680
解析前解析后还有解析

214
00:05:31,680 --> 00:05:34,360
这时候我们解析模块的时候

215
00:05:34,360 --> 00:05:36,420
肯定需要用到ST语法术

216
00:05:36,420 --> 00:05:38,020
这里面有个passer解析

217
00:05:38,020 --> 00:05:40,180
这个module上并没有挂任何的实践

218
00:05:40,180 --> 00:05:41,920
passer解析的时候

219
00:05:41,920 --> 00:05:43,800
我们要递归每一行语法

220
00:05:43,800 --> 00:05:45,320
当然包括语句

221
00:05:45,320 --> 00:05:46,540
还有我们的call

222
00:05:46,540 --> 00:05:48,380
call就指的就是我们的require语法

223
00:05:48,380 --> 00:05:49,840
就比如说我们requare了

224
00:05:49,840 --> 00:05:51,420
包括文件就是靠调用

225
00:05:51,420 --> 00:05:53,200
当然还有一些所谓的表达式

226
00:05:53,200 --> 00:05:54,160
也需要处理一下

227
00:05:54,160 --> 00:05:56,920
最后我们解析完以后

228
00:05:56,920 --> 00:05:57,840
我们需要干嘛

229
00:05:57,840 --> 00:06:01,320
是不是通过模板和我们的数据来进行处理

230
00:06:01,320 --> 00:06:03,140
比如编译出真正的文件

231
00:06:03,140 --> 00:06:04,600
这里我们需要怎么样

232
00:06:04,600 --> 00:06:06,040
是不是可能会给我们的模板

233
00:06:06,040 --> 00:06:06,720
添上哈记戳

234
00:06:06,720 --> 00:06:08,100
当然还有可能怎么样

235
00:06:08,100 --> 00:06:08,780
有启动

236
00:06:08,780 --> 00:06:10,180
比如说启动编译

237
00:06:10,180 --> 00:06:12,080
这里面还有一些变量

238
00:06:12,080 --> 00:06:13,100
还有最后我们怎么样

239
00:06:13,100 --> 00:06:14,040
是不是要渲染完成

240
00:06:14,040 --> 00:06:15,600
有Runder方法

241
00:06:15,600 --> 00:06:16,820
这里面我们可以看到

242
00:06:16,820 --> 00:06:19,340
大致上就是我们这样一个webpack流程

243
00:06:19,340 --> 00:06:20,980
它里面有很多重要的钩子

244
00:06:20,980 --> 00:06:23,640
我们可以在对应的钩子上绑进一些世界

245
00:06:23,640 --> 00:06:24,660
来干我们想干的事

246
00:06:24,660 --> 00:06:26,160
这里面我们说了

247
00:06:26,160 --> 00:06:27,560
唯一两个重要的对象

248
00:06:27,560 --> 00:06:28,500
一个叫compiler

249
00:06:28,500 --> 00:06:29,800
还有叫complication

250
00:06:29,800 --> 00:06:31,440
这是我们要掌握的两个对象

251
00:06:31,440 --> 00:06:33,400
这两个对象上可能有很多方法

252
00:06:33,400 --> 00:06:34,820
比如说发射文件

253
00:06:34,820 --> 00:06:35,920
可能还有一些

254
00:06:35,920 --> 00:06:37,460
比如说我们创建模画的一些方法

255
00:06:37,460 --> 00:06:39,540
这里面我们就先不过多介绍

256
00:06:39,540 --> 00:06:44,580
我们之后就开始来写我们这样一个webpack插件

