1
00:00:00,000 --> 00:00:06,960
你到安心里边来了是不是开始新的编译了好咱们开始走了咱们看爱单水怎么实现的是不是看原码看原码啊

2
00:00:06,960 --> 00:00:09,500
原码到哪了原码到

3
00:00:09,500 --> 00:00:18,700
嗯这你看是不是到这了你看是不是先创建了一个依赖啊咱们不管依赖的这个什么啊你看是不是走的completion的爱单水啊好进去

4
00:00:18,700 --> 00:00:29,600
你看先来出发了什么出发了一下爱单水这个时间是不是传入了一个安排限定再出发一下是不是啊好这个不甘示弱啊咱们也出发一下

5
00:00:29,600 --> 00:00:31,140
啊就是嘛就是一个

6
00:00:31,140 --> 00:00:33,440
zdrhooks 点什么

7
00:00:33,440 --> 00:00:35,240
dl add entry

8
00:00:35,240 --> 00:00:36,760
变什么 点靠

9
00:00:36,760 --> 00:00:40,600
传进我们的这个 entry和我们这个 name 对吧

10
00:00:40,600 --> 00:00:41,640
啊

11
00:00:41,640 --> 00:00:42,400
就是

12
00:00:42,400 --> 00:00:44,700
把这个 entry和 name 传给你去

13
00:00:44,700 --> 00:00:47,520
触发什么样安退时间 你看我个勾的我们上面是不是

14
00:00:47,520 --> 00:00:49,060
这是什么建好了

15
00:00:49,060 --> 00:00:51,360
是个同步勾的吧 两个参数一个是 entry

16
00:00:51,360 --> 00:00:54,680
入口的名字和我们代码块的名字是不是都有了啊 都有了

17
00:00:54,680 --> 00:00:57,500
分别是什么分别是什么.src

18
00:00:57,500 --> 00:00:58,780
interngs

19
00:00:59,300 --> 00:01:01,300
喝什么喝我们的妹是吧

20
00:01:01,300 --> 00:01:02,160
就给这两个

21
00:01:02,160 --> 00:01:04,360
好我们再来看

22
00:01:04,360 --> 00:01:08,660
好走走走

23
00:01:08,660 --> 00:01:10,300
不重要的我略掉了啊

24
00:01:10,300 --> 00:01:11,400
不重要的我就干掉了

25
00:01:11,400 --> 00:01:14,200
是在干嘛

26
00:01:14,200 --> 00:01:15,540
是在去搞了一个入口点

27
00:01:15,540 --> 00:01:17,900
叫prepared undepoint的入口点是吧

28
00:01:17,900 --> 00:01:18,700
嗯

29
00:01:18,700 --> 00:01:20,960
好然后在走什么走

30
00:01:20,960 --> 00:01:22,560
叫Villia add model tree

31
00:01:22,560 --> 00:01:23,240
什么意思

32
00:01:23,240 --> 00:01:24,940
开始编译模画链

33
00:01:24,940 --> 00:01:26,660
为啥模画链呀

34
00:01:26,660 --> 00:01:28,460
因为这个模式可能一再别的模画

35
00:01:28,460 --> 00:01:29,200
会生一个链条

36
00:01:29,200 --> 00:01:29,860
对不对

37
00:01:29,860 --> 00:01:30,700
做什么块链

38
00:01:30,700 --> 00:01:31,220
对吧

39
00:01:31,220 --> 00:01:33,400
叫爱的猫就吹是不是

40
00:01:33,400 --> 00:01:36,020
然后他会生一个毛牛是不是啊

41
00:01:36,020 --> 00:01:36,560
然后呢

42
00:01:36,560 --> 00:01:39,660
你看他会走什么走这个色塞斯安锤

43
00:01:39,660 --> 00:01:41,060
然后走callback是不是

44
00:01:41,060 --> 00:01:41,360
好

45
00:01:41,360 --> 00:01:42,700
我们来写这个代码逻辑啊

46
00:01:42,700 --> 00:01:46,800
他会怎么样

47
00:01:46,800 --> 00:01:48,700
他会先去走什么走

48
00:01:48,700 --> 00:01:50,320
Z的点下滑线

49
00:01:50,320 --> 00:01:52,720
爱的猫丢啊

50
00:01:52,720 --> 00:01:53,920
吹

51
00:01:53,920 --> 00:01:54,600
对吧

52
00:01:54,600 --> 00:01:58,120
爱的猫的吹

53
00:01:58,120 --> 00:02:00,020
然后里边呢传了一个context

54
00:02:00,020 --> 00:02:04,160
还有什么entry和name对吧

55
00:02:04,160 --> 00:02:05,280
啊entr name

56
00:02:05,280 --> 00:02:09,460
然后完事之后呢就调我们调我们这个final copy uk了啊

57
00:02:09,460 --> 00:02:10,280
直接回调 uk了

58
00:02:10,280 --> 00:02:12,280
为了减换我把这个

59
00:02:12,280 --> 00:02:14,460
这里边的代码呀都变成同步的了啊

60
00:02:14,460 --> 00:02:15,820
比较简略一些是不是

61
00:02:15,820 --> 00:02:18,160
好我们写这个add mode train方法

62
00:02:18,160 --> 00:02:23,480
好里边传说我们这个context entry和name是不是传过来

63
00:02:23,480 --> 00:02:24,580
放到这是不是啊

64
00:02:24,580 --> 00:02:25,960
是不是开始编译了

65
00:02:25,960 --> 00:02:27,460
那怎么编译啊

66
00:02:27,460 --> 00:02:27,960
首先啊

67
00:02:27,960 --> 00:02:29,160
我们要创业一个模块

68
00:02:29,160 --> 00:02:30,560
那么怎么创业模块呢

69
00:02:30,560 --> 00:02:31,960
我们需要有模块的工厂

70
00:02:31,960 --> 00:02:32,860
对不对啊

71
00:02:32,860 --> 00:02:34,060
所以我们建个模块工厂啊

72
00:02:34,060 --> 00:02:36,360
你觉得马里面这样干的啊

73
00:02:36,360 --> 00:02:37,260
你看他也会怎么样

74
00:02:37,260 --> 00:02:37,700
会会

75
00:02:37,700 --> 00:02:39,160
你看他会搞一个模块的工厂

76
00:02:39,160 --> 00:02:41,320
你看这个进去看看

77
00:02:41,320 --> 00:02:42,060
你看怎么做的

78
00:02:42,060 --> 00:02:43,760
他里边啊

79
00:02:43,760 --> 00:02:44,660
你看他会

80
00:02:44,660 --> 00:02:47,760
啊

81
00:02:47,760 --> 00:02:48,860
他会通过什么

82
00:02:48,860 --> 00:02:50,760
通过一个模块的依赖工厂

83
00:02:50,760 --> 00:02:52,860
拿到一个什么module factory

84
00:02:52,860 --> 00:02:54,960
然后通过module factory的什么呀

85
00:02:54,960 --> 00:02:56,660
他的可以方法创建一个模块

86
00:02:57,260 --> 00:02:58,220
他这样干的

87
00:02:58,220 --> 00:02:59,020
对吧

88
00:02:59,020 --> 00:02:59,940
咱们也这样干啊

89
00:02:59,940 --> 00:03:00,660
也这样干

90
00:03:00,660 --> 00:03:01,500
分两部曲啊

91
00:03:01,500 --> 00:03:03,180
咱们建一个模式工厂啊

92
00:03:03,180 --> 00:03:04,260
叫什么叫建一个文件

93
00:03:04,260 --> 00:03:07,220
叫什么

94
00:03:07,220 --> 00:03:07,860
叫这个

95
00:03:07,860 --> 00:03:08,420
Normal

96
00:03:08,420 --> 00:03:13,700
Normal什么呀

97
00:03:13,700 --> 00:03:15,700
module factory

98
00:03:15,700 --> 00:03:16,780
叫JS啊

99
00:03:16,780 --> 00:03:19,060
叫普通模式工厂是不是啊

100
00:03:19,060 --> 00:03:19,500
好

101
00:03:19,500 --> 00:03:20,660
那普通模式工厂也是什么

102
00:03:20,660 --> 00:03:22,300
它是不是也是一个类啊

103
00:03:22,300 --> 00:03:22,860
对吧

104
00:03:22,860 --> 00:03:23,140
好

105
00:03:23,140 --> 00:03:24,580
我们写个pass

106
00:03:24,580 --> 00:03:25,540
东西require什么呀

107
00:03:25,540 --> 00:03:27,140
pass

108
00:03:27,260 --> 00:03:28,020
对吧

109
00:03:28,020 --> 00:03:31,860
然后呢我们去class什么叫normal

110
00:03:31,860 --> 00:03:33,660
module factory

111
00:03:33,660 --> 00:03:35,700
一个tans什么呀

112
00:03:35,700 --> 00:03:38,180
我们这个叫不用介绍啊

113
00:03:38,180 --> 00:03:39,300
直接完事就可以了

114
00:03:39,300 --> 00:03:41,080
然后他有create方法

115
00:03:41,080 --> 00:03:44,420
然后他呢参入呢是个date是一个

116
00:03:44,420 --> 00:03:45,940
数据吧

117
00:03:45,940 --> 00:03:47,740
然后返回有模块的一个实例

118
00:03:47,740 --> 00:03:48,500
return

119
00:03:48,500 --> 00:03:50,820
一个什么new一个normal module

120
00:03:50,820 --> 00:03:52,860
这样写的是吧

121
00:03:52,860 --> 00:03:53,880
好倒出吧

122
00:03:53,880 --> 00:03:56,960
咱们呀干脆直接怎么直接把这个

123
00:03:56,960 --> 00:03:57,460
module

124
00:03:57,460 --> 00:04:00,380
dr

125
00:04:00,380 --> 00:04:05,360
adpost等于什么等于一个new一个normal modifactory的实力有点是吧

126
00:04:05,360 --> 00:04:11,120
你看这是一个normal modifactory是吧里面有什么有亏的方法对吧

127
00:04:11,120 --> 00:04:15,120
然后可以创建通过一个对象创建什么创建模块的实力是吧

128
00:04:15,120 --> 00:04:18,000
然后可以把这个对的传给这个他是不是

129
00:04:18,000 --> 00:04:20,440
那他是啥是不是也是个类啊

130
00:04:24,760 --> 00:04:30,400
他也是个类啊 也是个类 我们要见下这个类吧 叫normalmodule 就是正常的模块

131
00:04:30,400 --> 00:04:32,440
 是不是啊 好 我们来见一下这个文件

132
00:04:32,440 --> 00:04:35,000
好 点s吧

133
00:04:35,000 --> 00:04:40,120
好 那他是个类 要写class吧 好class

134
00:04:40,120 --> 00:04:45,760
normalmodule 然后里边呢 我们写个难数contractor

135
00:04:45,760 --> 00:04:50,880
然后呢 导出个类 是吧 module

136
00:04:53,680 --> 00:04:56,260
adport等于什么一个normalmodule是不是

137
00:04:56,260 --> 00:04:57,640
倒不烫就赔了

138
00:04:57,640 --> 00:04:59,680
好

139
00:04:59,680 --> 00:05:01,800
那我们看一下他有什么参数吗

140
00:05:01,800 --> 00:05:03,100
他是不是有有参数啊

141
00:05:03,100 --> 00:05:05,300
你看我是不是把这个data传过来了

142
00:05:05,300 --> 00:05:06,260
那data啥东西

143
00:05:06,260 --> 00:05:09,600
你看你看我你看我这的话

144
00:05:09,600 --> 00:05:11,620
admodule什么train

145
00:05:11,620 --> 00:05:12,360
对吧

146
00:05:12,360 --> 00:05:13,080
admodule

147
00:05:13,080 --> 00:05:14,680
我这要干嘛

148
00:05:14,680 --> 00:05:17,120
是不是去先创建模块呀

149
00:05:17,120 --> 00:05:18,800
我首先要看你要创建模块吧

150
00:05:18,800 --> 00:05:19,920
对不对啊

151
00:05:19,920 --> 00:05:20,400
怎么创建呢

152
00:05:20,400 --> 00:05:21,600
你看我会这么写啊

153
00:05:21,600 --> 00:05:23,080
我会这引入这个模块工厂

154
00:05:23,080 --> 00:05:26,380
是吧 看 let 叫 normal

155
00:05:26,380 --> 00:05:29,320
module factory

156
00:05:29,320 --> 00:05:31,840
等于require 谁呀

157
00:05:31,840 --> 00:05:33,940
我们的第二缸是什么呀

158
00:05:33,940 --> 00:05:35,440
normal factory

159
00:05:35,440 --> 00:05:37,980
拿到了某工厂吧

160
00:05:37,980 --> 00:05:39,320
拿到之后怎么样

161
00:05:39,320 --> 00:05:41,140
是不是来去掉工厂呀

162
00:05:41,140 --> 00:05:41,780
第二什么

163
00:05:41,780 --> 00:05:43,580
掉 quit 创建

164
00:05:43,580 --> 00:05:46,720
对吧 创建工要创建一个模块吧

165
00:05:46,720 --> 00:05:47,340
对不对

166
00:05:47,340 --> 00:05:49,340
然后传去一个对象

167
00:05:49,340 --> 00:05:51,640
传谁就传他们三哥们就可以了

168
00:05:52,420 --> 00:05:55,820
contest 配合 name 把它程序

169
00:05:55,820 --> 00:06:01,000
那么它会返回什么 是不是返回一个模块呀

170
00:06:01,000 --> 00:06:04,540
lite 什么module 发挥一个模块呀 对吧

171
00:06:04,540 --> 00:06:10,540
我这参数传啥比较合适 你看啊 我们来写一下啊

172
00:06:10,540 --> 00:06:12,800
它第一个会传一个name过来

173
00:06:12,800 --> 00:06:19,300
name呢 是什么意思 就是这个代码块的名字 是吧 就是所属的代码块的名字

174
00:06:19,300 --> 00:06:21,120
叫啥

175
00:06:23,380 --> 00:06:23,720
叫啥

176
00:06:23,720 --> 00:06:26,280
想想叫啥

177
00:06:26,280 --> 00:06:29,260
我们这个项目里边只有一个代码块

178
00:06:29,260 --> 00:06:30,460
那个他名字叫啥

179
00:06:30,460 --> 00:06:32,860
妹是不是啊

180
00:06:32,860 --> 00:06:33,960
妹啊妹

181
00:06:33,960 --> 00:06:37,120
然后另外呢还会有contest上海门吧

182
00:06:37,120 --> 00:06:40,180
上海门的话我们给他一个z的点contest

183
00:06:40,180 --> 00:06:41,080
对吧

184
00:06:41,080 --> 00:06:41,620
contest

185
00:06:41,620 --> 00:06:44,680
因为你看z的contest是谁是不是就是

186
00:06:44,680 --> 00:06:46,160
就是他呀

187
00:06:46,160 --> 00:06:47,080
就是这个上海门啊

188
00:06:47,080 --> 00:06:48,220
都是一个啊

189
00:06:48,220 --> 00:06:48,860
就是那个

190
00:06:48,860 --> 00:06:50,180
第四的目录呗

191
00:06:50,180 --> 00:06:50,760
对吧

192
00:06:50,760 --> 00:06:52,280
还有什么

193
00:06:52,420 --> 00:06:54,380
这还需要一个路径啊叫request

194
00:06:54,380 --> 00:06:56,420
就是一个请路径

195
00:06:56,420 --> 00:06:58,580
就说这个模画的绝对路径

196
00:06:58,580 --> 00:06:59,940
对吧绝对

197
00:06:59,940 --> 00:07:01,600
那怎么拿呢这样的

198
00:07:01,600 --> 00:07:02,360
他只要什么

199
00:07:02,360 --> 00:07:04,360
prostX的点什么点叫

200
00:07:04,360 --> 00:07:07,740
把我们的contest跟什么

201
00:07:07,740 --> 00:07:09,020
跟安准拼在一起

202
00:07:09,020 --> 00:07:10,680
不就是一个模式的路径吗

203
00:07:10,680 --> 00:07:11,820
你看他什么

204
00:07:11,820 --> 00:07:13,560
是个根目的一个路径

205
00:07:13,560 --> 00:07:15,540
他呢是相对根目的一个

206
00:07:15,540 --> 00:07:16,180
相对路径

207
00:07:16,180 --> 00:07:17,120
他们拼在一起

208
00:07:17,120 --> 00:07:18,480
就是这个模画的绝对路径

209
00:07:18,480 --> 00:07:19,680
是不是啊

210
00:07:19,680 --> 00:07:20,400
做request了吗

211
00:07:20,400 --> 00:07:20,880
request了就是

212
00:07:20,880 --> 00:07:22,320
此模画的

213
00:07:22,320 --> 00:07:26,960
就这此模画的什么呀

214
00:07:26,960 --> 00:07:32,160
绝对入境

215
00:07:32,160 --> 00:07:33,040
对吧

216
00:07:33,040 --> 00:07:34,200
就入境就可以了

217
00:07:34,200 --> 00:07:37,860
那么context和我们的request

218
00:07:37,860 --> 00:07:39,220
是不是有块的搞定了啊

219
00:07:39,220 --> 00:07:39,580
搞定了

220
00:07:39,580 --> 00:07:40,580
你看是不是创业好了

221
00:07:40,580 --> 00:07:41,980
返回应模画啊

222
00:07:41,980 --> 00:07:42,360
对不对

223
00:07:42,360 --> 00:07:45,220
然后呢模画创业之后干嘛

224
00:07:45,220 --> 00:07:46,100
该编译了啊

225
00:07:46,100 --> 00:07:47,480
你看看原码里边怎么做的

226
00:07:47,480 --> 00:07:47,920
你看啊

227
00:07:47,920 --> 00:07:48,760
看啊

228
00:07:48,760 --> 00:07:49,360
看他怎么做的

229
00:07:49,360 --> 00:07:50,480
你看先拿到工厂

230
00:07:50,480 --> 00:07:52,500
然后怎么样

231
00:07:52,500 --> 00:07:53,620
是不是开始创建模画

232
00:07:53,620 --> 00:07:54,680
啊

233
00:07:54,680 --> 00:07:55,200
创建模画

234
00:07:55,200 --> 00:07:56,640
创建完之后怎么样

235
00:07:56,640 --> 00:07:58,120
走到这来了吧

236
00:07:58,120 --> 00:07:58,880
进来

237
00:07:58,880 --> 00:08:00,800
干干干嘛了

238
00:08:00,800 --> 00:08:01,060
你看

239
00:08:01,060 --> 00:08:02,480
他会干嘛

240
00:08:02,480 --> 00:08:05,780
他会走什么

241
00:08:05,780 --> 00:08:08,100
是不是走这个编译啊

242
00:08:08,100 --> 00:08:11,180
看

243
00:08:11,180 --> 00:08:12,380
是不是走了

244
00:08:12,380 --> 00:08:13,420
走什么走

245
00:08:13,420 --> 00:08:14,700
Z的Build Module啊

246
00:08:14,700 --> 00:08:15,180
是不是啊

247
00:08:15,180 --> 00:08:15,820
Build Module

248
00:08:15,820 --> 00:08:17,820
这编译模画啊

249
00:08:17,820 --> 00:08:18,740
我们也一样吧

250
00:08:18,740 --> 00:08:20,040
先拿到这个模画

251
00:08:20,040 --> 00:08:21,120
来往里面开始编译

252
00:08:21,120 --> 00:08:21,680
怎么编译呢

253
00:08:21,680 --> 00:08:23,920
就是叫我们这个module.build的方法

254
00:08:23,920 --> 00:08:26,520
build的方法

255
00:08:26,520 --> 00:08:27,080
就是编译

256
00:08:27,080 --> 00:08:27,480
对吧

257
00:08:27,480 --> 00:08:27,800
编译

258
00:08:27,800 --> 00:08:31,280
其实你看啊

259
00:08:31,280 --> 00:08:31,840
它怎么编译呢

260
00:08:31,840 --> 00:08:31,980
你看

261
00:08:31,980 --> 00:08:32,540
你看

262
00:08:32,540 --> 00:08:34,840
completion的buildmodule

263
00:08:34,840 --> 00:08:35,800
其实它是怎么编译呢

264
00:08:35,800 --> 00:08:36,660
我们看一下啊

265
00:08:36,660 --> 00:08:37,940
它其实掉的是谁

266
00:08:37,940 --> 00:08:38,780
掉的是

267
00:08:38,780 --> 00:08:41,100
它其实啊

268
00:08:41,100 --> 00:08:41,840
掉的是什么

269
00:08:41,840 --> 00:08:43,160
掉的是module.build

270
00:08:43,160 --> 00:08:45,820
掉的是模块自己的编译方法

271
00:08:45,820 --> 00:08:46,500
是不是啊

272
00:08:46,500 --> 00:08:48,020
所以我们又二合一了啊

273
00:08:48,020 --> 00:08:48,740
我们又没有封装

274
00:08:48,740 --> 00:08:49,920
我们直接掉模块编译方法

275
00:08:49,920 --> 00:08:50,880
对吧

276
00:08:50,880 --> 00:08:52,320
就直接给他二合一了

277
00:08:52,320 --> 00:08:53,860
直接给他用module build

278
00:08:53,860 --> 00:08:54,600
可以编译是吧

279
00:08:54,600 --> 00:08:55,260
就费了

280
00:08:55,260 --> 00:08:56,800
好

281
00:08:56,800 --> 00:08:58,160
然后呢最后怎么样

282
00:08:58,160 --> 00:08:59,080
编译完之后怎么样

283
00:08:59,080 --> 00:09:00,180
把它填入口里面去吧

284
00:09:00,180 --> 00:09:01,900
就我们需要加个属性啊

285
00:09:01,900 --> 00:09:02,380
叫什么叫

286
00:09:02,380 --> 00:09:04,500
对吧

287
00:09:04,500 --> 00:09:05,020
好

288
00:09:05,020 --> 00:09:06,220
我们写这样写

289
00:09:06,220 --> 00:09:07,460
vd.entries

290
00:09:07,460 --> 00:09:08,500
变什么

291
00:09:08,500 --> 00:09:09,660
push一个怎么样

292
00:09:09,660 --> 00:09:10,260
module

293
00:09:10,260 --> 00:09:11,500
是不是

294
00:09:11,500 --> 00:09:12,660
这什么入口模块

295
00:09:12,660 --> 00:09:13,640
就是把什么

296
00:09:13,640 --> 00:09:15,600
把入口模块

297
00:09:15,600 --> 00:09:17,520
或者什么

298
00:09:17,520 --> 00:09:19,540
或者编译后的入口模块

299
00:09:19,540 --> 00:09:21,260
添加到什么呀

300
00:09:21,260 --> 00:09:22,280
咱们的入口里

301
00:09:22,280 --> 00:09:24,800
是不是就可以了

302
00:09:24,800 --> 00:09:25,900
入口速度里边就可以了吧

303
00:09:25,900 --> 00:09:27,940
还记得吗

304
00:09:27,940 --> 00:09:29,120
当时给大家看过一个

305
00:09:29,120 --> 00:09:30,460
看过一个数据结构

306
00:09:30,460 --> 00:09:31,820
是不是这个啊

307
00:09:31,820 --> 00:09:33,960
看那里边是不是有entries啊

308
00:09:33,960 --> 00:09:34,880
里边是不是有个node

309
00:09:34,880 --> 00:09:36,760
有个normal module字啊

310
00:09:36,760 --> 00:09:37,780
是不是啊

311
00:09:37,780 --> 00:09:39,480
就是入口这个速度里边

312
00:09:39,480 --> 00:09:40,420
有一个什么入口模式

313
00:09:40,420 --> 00:09:41,000
对不对

314
00:09:41,000 --> 00:09:41,460
对象了吧

315
00:09:41,460 --> 00:09:43,380
是不是对象一步搞定啊

316
00:09:43,380 --> 00:09:43,780
对不对

317
00:09:43,780 --> 00:09:44,380
好

318
00:09:44,380 --> 00:09:47,260
好我们接着写啊

319
00:09:47,260 --> 00:09:48,860
你看下面该写哪个了

320
00:09:48,860 --> 00:09:50,540
是不是写这个create方法了

321
00:09:50,540 --> 00:09:51,060
对吧

322
00:09:51,060 --> 00:09:53,640
好找到我们的normal module factory的create方法

323
00:09:53,640 --> 00:09:56,280
然后这呢传个date过来

324
00:09:56,280 --> 00:09:57,200
传给了normal module

325
00:09:57,200 --> 00:09:58,940
那module呢节奏之后呢

326
00:09:58,940 --> 00:10:00,040
是不是要解构啊

327
00:10:00,040 --> 00:10:01,060
解构什么呀

328
00:10:01,060 --> 00:10:02,240
三属性吧

329
00:10:02,240 --> 00:10:03,100
那个属性name

330
00:10:03,100 --> 00:10:04,580
还有什么呀

331
00:10:04,580 --> 00:10:07,460
还有这个name

332
00:10:07,460 --> 00:10:09,960
还有什么呀

333
00:10:09,960 --> 00:10:11,380
contest和request吧

334
00:10:11,380 --> 00:10:13,500
contest和request

335
00:10:13,500 --> 00:10:14,180
对吧

336
00:10:14,180 --> 00:10:15,600
有三属性啊

337
00:10:15,600 --> 00:10:16,120
三属性

338
00:10:16,120 --> 00:10:19,320
那么我们都拿过来吧

339
00:10:19,320 --> 00:10:19,780
比如说

340
00:10:19,780 --> 00:10:21,920
z.name等于name

341
00:10:21,920 --> 00:10:25,180
z.contest等于contest

342
00:10:25,180 --> 00:10:29,460
z.request等于request

343
00:10:29,460 --> 00:10:32,060
把name拿过来放到这

344
00:10:32,060 --> 00:10:36,120
把它全部付给z的属性

345
00:10:36,120 --> 00:10:37,720
然后呢

346
00:10:37,720 --> 00:10:39,080
它是不是还有什么

347
00:10:39,080 --> 00:10:40,500
是有个build的方法

348
00:10:40,500 --> 00:10:40,960
好

349
00:10:40,960 --> 00:10:41,720
来个build方法

350
00:10:41,720 --> 00:10:42,100
开始编译

351
00:10:42,100 --> 00:10:43,020
编译自己吧

352
00:10:43,020 --> 00:10:44,380
build就可以了

353
00:10:44,380 --> 00:10:45,100
编译自己

354
00:10:45,100 --> 00:10:46,560
然后参注呢

355
00:10:46,560 --> 00:10:47,140
就是那么的

356
00:10:47,140 --> 00:10:47,980
什么completion

357
00:10:47,980 --> 00:10:49,520
你看completion的

358
00:10:49,520 --> 00:10:50,700
是这次是不是

359
00:10:50,700 --> 00:10:51,600
对吧

360
00:10:51,600 --> 00:10:52,860
completion

361
00:10:52,860 --> 00:10:59,340
然后completion是吧

362
00:10:59,340 --> 00:11:00,680
completion对吧

363
00:11:00,680 --> 00:11:01,340
就可以了

364
00:11:01,340 --> 00:11:03,940
好咱们来打印一下啊

365
00:11:03,940 --> 00:11:04,500
打印什么呢

366
00:11:04,500 --> 00:11:04,900
打印说

367
00:11:04,900 --> 00:11:06,540
现在开始什么

368
00:11:06,540 --> 00:11:08,520
编译入口模块了

369
00:11:08,520 --> 00:11:14,120
对不对

370
00:11:14,120 --> 00:11:14,680
是不是就可以了

371
00:11:14,680 --> 00:11:18,360
咱们来试试啊

372
00:11:44,680 --> 00:11:45,040
tectory

373
00:11:45,040 --> 00:11:48,100
require

374
00:11:48,100 --> 00:11:51,680
req ure 是吧

375
00:11:51,680 --> 00:12:01,860
呃 这第几行 这是那个

376
00:12:01,860 --> 00:12:03,980
convention的三十二行

377
00:12:03,980 --> 00:12:06,280
completion 32行

378
00:12:06,280 --> 00:12:07,760
这吧

379
00:12:07,760 --> 00:12:10,060
刚 past 叫

380
00:12:10,060 --> 00:12:12,060
他的教育方法

381
00:12:13,260 --> 00:12:14,880
教育方法是把他和他连在一起

382
00:12:14,880 --> 00:12:17,880
那么他呢应该是Z2contest 是吧

383
00:12:17,880 --> 00:12:20,640
Z2contest 诶 这是不是传过来了

384
00:12:20,640 --> 00:12:22,740
add module train 是吧

385
00:12:22,740 --> 00:12:28,500
这包错了啊 你看这add module train也没有传吧 你看contest传过来了

386
00:12:28,500 --> 00:12:32,500
add entry传给他了 他传给他了

387
00:12:32,500 --> 00:12:35,460
add entry也传过来 是吧 往下传

388
00:12:35,460 --> 00:12:38,640
怎么看的三个参数拿到了没有啊

389
00:12:43,260 --> 00:12:55,260
看一下啊 这个哎 这两啊 你饭的是吧 没有值 两个人饭的为啥呀 你看说明啊

390
00:12:55,260 --> 00:12:55,740
 这个

391
00:12:55,740 --> 00:13:00,420
爱的安稠的时候contact 安稠也没有值啊 对不对

392
00:13:00,420 --> 00:13:08,660
他传给了他 他又传给了他是不是 好 那看看爱的安稠方法的时候为啥没传过来啊

393
00:13:09,180 --> 00:13:13,780
按按锤上调的是不是在那个在那个拆下人调的呀

394
00:13:13,780 --> 00:13:16,580
他里面里边的那个什么新关键他硬吧

395
00:13:16,580 --> 00:13:19,780
主战啊你看考虑的艾丹锤

396
00:13:19,780 --> 00:13:22,700
康配逊的艾丹锤

397
00:13:22,700 --> 00:13:25,580
这已经结构了

398
00:13:25,580 --> 00:13:27,120
是啊

399
00:13:27,120 --> 00:13:30,100
康这已经解禁他这传过来了是吧

400
00:13:30,100 --> 00:13:34,580
啊

401
00:13:34,880 --> 00:13:37,880
context entry和name对吧

402
00:13:37,880 --> 00:13:40,640
那new这个single plugin的时候 你看他哪 new的呀

403
00:13:40,640 --> 00:13:42,540
这 new的呀

404
00:13:42,540 --> 00:13:45,640
这 new的 你看啊 这 new的

405
00:13:45,640 --> 00:13:48,940
context和他是不是

406
00:13:48,940 --> 00:13:51,340
和他

407
00:13:51,340 --> 00:13:56,320
那么相当于我这个entry plugin的时候 之前的时候传过来了吗 看一下

408
00:13:56,320 --> 00:13:58,020
看一下我这个

409
00:13:58,020 --> 00:14:00,520
看一下我这啊 看一下我这个地方

410
00:14:04,880 --> 00:14:09,440
应该在这 应该在这啊

411
00:14:09,440 --> 00:14:13,560
wire tag options 这 这 这 你看啊 我这个地方

412
00:14:13,560 --> 00:14:17,220
process 这 是吧

413
00:14:17,220 --> 00:14:20,960
那 options on test options entry 是吧

414
00:14:20,960 --> 00:14:25,540
那我这 叫他传过来了吗 看看我这个compiler

415
00:14:25,540 --> 00:14:30,220
看这啊 看这传过来了没有啊 看这

416
00:14:35,840 --> 00:14:37,940
嗯对不在这在在那个wipag那

417
00:14:37,940 --> 00:14:39,580
wipag在这

418
00:14:39,580 --> 00:14:40,920
inite节目里面这啊

419
00:14:40,920 --> 00:14:44,320
这传过来一个options是吧

420
00:14:44,320 --> 00:14:45,880
process options options

421
00:14:45,880 --> 00:14:46,920
这个地方有值吗

422
00:14:46,920 --> 00:14:48,080
你看啊这个地方

423
00:14:48,080 --> 00:14:51,780
这样我给大家debug一下啊

424
00:14:51,780 --> 00:14:52,420
给大家debug一下

425
00:14:52,420 --> 00:14:53,080
看一下啊

426
00:14:53,080 --> 00:14:53,940
咱们读一下流程

427
00:14:53,940 --> 00:14:54,820
debug

428
00:14:54,820 --> 00:14:58,440
看他为啥对这两只没传过来

429
00:14:58,440 --> 00:14:59,320
debug是吧

430
00:14:59,320 --> 00:15:01,880
好回到咱们这个代码里边

431
00:15:01,880 --> 00:15:02,920
谁入口是吧

432
00:15:02,920 --> 00:15:03,740
CY吧

433
00:15:04,240 --> 00:15:07,920
好咱们的这这我们来一个调试对吧 走

434
00:15:07,920 --> 00:15:13,820
好走啊 往下走

435
00:15:13,820 --> 00:15:18,980
你看这一步的话他有些参数 你看contest entry都有值是吧

436
00:15:18,980 --> 00:15:25,060
这 entry怎么是对象呢 你看我应该把这个不应该是对象啊 我这个地方配置文件我改错了

437
00:15:25,060 --> 00:15:26,980
 我这个把它删掉是吧 ok了啊

438
00:15:26,980 --> 00:15:29,820
好 重新走

439
00:15:29,820 --> 00:15:33,960
好 进来了啊 咱们再看

440
00:15:34,900 --> 00:15:37,300
他的context是对的 是不是 是第4

441
00:15:37,300 --> 00:15:46,540
他的这个option呢 他的entry是不是应该就是对了啊 刚才我给他模拟的时候给他改把那个配置文件给改了啊

442
00:15:46,540 --> 00:15:48,320
然后你看

443
00:15:48,320 --> 00:15:51,000
是不是进入了process了 进去

444
00:15:51,000 --> 00:15:56,200
诶 啥情况

445
00:16:02,540 --> 00:16:04,860
啥情况 进来了啊 进来

446
00:16:04,860 --> 00:16:07,500
你看 我在这儿 你看我走

447
00:16:07,500 --> 00:16:11,540
看这有拿招职了吗 康泰子安锤是不是有啊 有

448
00:16:11,540 --> 00:16:16,100
看 然后到哪儿是不是到安锤奥姓的这个监定喊中里边去了

449
00:16:16,100 --> 00:16:18,860
谁是不是这个插件安锤奥姓带你在哪儿

450
00:16:18,860 --> 00:16:21,220
是不是在这儿安锤奥姓带你

451
00:16:21,220 --> 00:16:25,500
安锤奥姓带你在这儿啊 对吧 到这儿来了

452
00:16:25,500 --> 00:16:28,780
是不是到这儿来了 进来了 看看有没有啊

453
00:16:29,860 --> 00:16:32,420
kontest antifinal

454
00:16:32,420 --> 00:16:34,220
entree呢

455
00:16:34,220 --> 00:16:37,020
antifinal没有值了

456
00:16:37,020 --> 00:16:40,100
为什么呀是不是这没传餐呀

457
00:16:40,100 --> 00:16:42,620
kontest antifinal没有传餐

458
00:16:42,620 --> 00:16:44,760
就这的问题啊

459
00:16:44,760 --> 00:16:46,820
你看这就这就这断断档了是吧

460
00:16:46,820 --> 00:16:48,400
看这为啥呀

461
00:16:48,400 --> 00:16:52,620
entree option对吧

462
00:16:52,620 --> 00:16:54,420
看一下啊

463
00:16:59,860 --> 00:17:01,700
看在这你看啊

464
00:17:01,700 --> 00:17:04,260
在这触发的时候

465
00:17:04,260 --> 00:17:05,520
你看在触发的时候

466
00:17:05,520 --> 00:17:06,420
我已经明明给了

467
00:17:06,420 --> 00:17:09,780
你看compiler,hook,entry,option

468
00:17:09,780 --> 00:17:11,200
我不知道是不是给了这两个参数了

469
00:17:11,200 --> 00:17:13,040
contest和entry给了

470
00:17:13,040 --> 00:17:15,120
对吧

471
00:17:15,120 --> 00:17:15,660
给了

472
00:17:15,660 --> 00:17:17,820
给了在它的接定函数里面

473
00:17:17,820 --> 00:17:18,680
应该能收到啊

474
00:17:18,680 --> 00:17:19,620
看entry,option

475
00:17:19,620 --> 00:17:21,700
entry,option

476
00:17:21,700 --> 00:17:23,120
应该能收到啊

477
00:17:23,120 --> 00:17:24,860
这应该能收到啊

478
00:17:24,860 --> 00:17:26,660
你看这两应该能收到啊

479
00:17:26,660 --> 00:17:28,060
但是它没收到

480
00:17:28,060 --> 00:17:28,720
对吧

481
00:17:28,720 --> 00:17:31,220
他监听了这个

482
00:17:31,220 --> 00:17:32,240
all-in-option实键

483
00:17:32,240 --> 00:17:34,440
tap监听

484
00:17:34,440 --> 00:17:35,460
对吧

485
00:17:35,460 --> 00:17:39,440
anifin

486
00:17:39,440 --> 00:17:41,700
那说明他没有收到啊

487
00:17:41,700 --> 00:17:43,620
apply

488
00:17:43,620 --> 00:17:45,200
监听这个实键

489
00:17:45,200 --> 00:17:48,040
我们看这个勾子什么勾子呀

490
00:17:48,040 --> 00:17:49,460
这肯定是个同步勾子呀

491
00:17:49,460 --> 00:17:52,160
好咱们看一下这个compiler

492
00:17:52,160 --> 00:17:53,140
它的勾子什么勾子

493
00:17:53,140 --> 00:17:56,400
compiler

494
00:17:56,400 --> 00:17:58,060
应该这个是吧

495
00:17:58,060 --> 00:18:03,180
sync bell hook是不是就是通过的勾子呗

496
00:18:03,180 --> 00:18:05,940
有两个参数contest和ntry是不是

497
00:18:05,940 --> 00:18:08,900
我们是不是建勾的时候玫瑰的两个参数啊

498
00:18:08,900 --> 00:18:10,320
看那勾子啊

499
00:18:10,320 --> 00:18:11,300
看我们这个sync

500
00:18:11,300 --> 00:18:12,860
看我们这两个勾子

501
00:18:12,860 --> 00:18:15,760
这个compiler是吧

502
00:18:15,760 --> 00:18:19,800
看找到他这个勾子

503
00:18:19,800 --> 00:18:21,600
果真你看是没有啊

504
00:18:21,600 --> 00:18:22,160
注意啊

505
00:18:22,160 --> 00:18:23,420
如果这没有给的话

506
00:18:23,420 --> 00:18:24,740
你给了他也收不到

507
00:18:24,740 --> 00:18:25,840
明白吗

508
00:18:25,840 --> 00:18:26,680
给了他也收不到

509
00:18:26,680 --> 00:18:27,260
为啥

510
00:18:27,260 --> 00:18:28,780
因为他都没有接收啊

511
00:18:28,780 --> 00:18:29,740
是不是啊

512
00:18:29,740 --> 00:18:30,920
坑爹了啊

513
00:18:30,920 --> 00:18:31,740
好

514
00:18:31,740 --> 00:18:32,260
我们再来一次

515
00:18:32,260 --> 00:18:33,780
这一定要给啊

516
00:18:33,780 --> 00:18:34,560
一定要给

517
00:18:34,560 --> 00:18:37,100
第四你看

518
00:18:37,100 --> 00:18:38,580
是不是有了

519
00:18:38,580 --> 00:18:39,340
就对对对

520
00:18:39,340 --> 00:18:40,020
这就对了啊

521
00:18:40,020 --> 00:18:40,300
对了

522
00:18:40,300 --> 00:18:40,480
好

523
00:18:40,480 --> 00:18:41,440
往下走

524
00:18:41,440 --> 00:18:44,140
好

525
00:18:44,140 --> 00:18:46,320
咱们来测试一下啊

526
00:18:46,320 --> 00:18:47,600
这个这个很坑啊

527
00:18:47,600 --> 00:18:49,100
好

528
00:18:49,100 --> 00:18:50,400
我们来走

529
00:18:50,400 --> 00:18:51,640
你看

530
00:18:51,640 --> 00:18:53,920
make完成了

531
00:18:53,920 --> 00:18:54,600
你看啊

532
00:18:54,600 --> 00:18:55,640
是不是就进来了

533
00:18:55,640 --> 00:18:57,240
看我们这个什么啊

534
00:18:57,240 --> 00:18:59,640
contest和我们这个may是不是都拿到了

535
00:18:59,640 --> 00:19:01,300
开始编译入口模块了对吧

536
00:19:01,300 --> 00:19:02,480
就完成了

537
00:19:02,480 --> 00:19:03,200
好

538
00:19:03,200 --> 00:19:04,440
刚才写到哪呢

539
00:19:04,440 --> 00:19:04,820
我们看一下

540
00:19:04,820 --> 00:19:06,260
刚才写到了这个

541
00:19:06,260 --> 00:19:08,160
admode train

542
00:19:08,160 --> 00:19:11,400
应该是会开始编译啊

543
00:19:11,400 --> 00:19:12,700
是不是应该走到这了呀

544
00:19:12,700 --> 00:19:14,040
开始编译入口模块了呀

545
00:19:14,040 --> 00:19:14,740
是不是答应出来了

546
00:19:14,740 --> 00:19:16,240
说明是不是到这了

547
00:19:16,240 --> 00:19:17,440
是不是到这了

548
00:19:17,440 --> 00:19:17,700
是不是

549
00:19:17,700 --> 00:19:18,340
好

550
00:19:18,340 --> 00:19:19,680
那我们就接着往下写了

551
00:19:19,680 --> 00:19:19,880
是不是

552
00:19:19,880 --> 00:19:20,920
往下写了

553
00:19:20,920 --> 00:19:22,260
很大问题啊

554
00:19:22,260 --> 00:19:31,660
画一个种冲刺啊对

555
00:19:31,660 --> 00:19:34,260
哼给不给机会

556
00:19:34,260 --> 00:19:36,300
不给也得也得这个也得这个

557
00:19:36,300 --> 00:19:37,900
不给也得早机会啊

558
00:19:37,900 --> 00:19:39,620
翻车了是吧啊

559
00:19:39,620 --> 00:19:40,800
这个有少给个参数吧

560
00:19:40,800 --> 00:19:41,320
对不对

561
00:19:41,320 --> 00:19:43,540
结构的时候这个是啥个

562
00:19:43,540 --> 00:19:46,480
那是就是当前的实例吗

563
00:19:46,480 --> 00:19:47,320
少吃点吧

564
00:19:47,320 --> 00:19:47,940
这个这个

565
00:19:47,940 --> 00:19:50,380
这个跟写不出来功利啊

566
00:19:50,380 --> 00:19:51,600
这不都很弱的东西吗

567
00:19:51,600 --> 00:19:51,920
对不对

568
00:19:51,920 --> 00:19:54,600
还得一个小时吧

569
00:19:54,600 --> 00:19:55,600
一个小时就搞定了

570
00:19:55,600 --> 00:19:56,340
好

571
00:19:56,340 --> 00:19:57,960
是不是开始编译了

572
00:19:57,960 --> 00:19:58,580
对吧

573
00:19:58,580 --> 00:19:59,920
我尽量三分钟搞定

574
00:19:59,920 --> 00:20:02,020
刚是不是到这开始编译了

575
00:20:02,020 --> 00:20:02,680
对不对

576
00:20:02,680 --> 00:20:05,700
下一步呢

577
00:20:05,700 --> 00:20:06,700
我们开始编译我们的代码

578
00:20:06,700 --> 00:20:06,920
是不是

579
00:20:06,920 --> 00:20:08,040
编译的时候

580
00:20:08,040 --> 00:20:08,560
大家注意

581
00:20:08,560 --> 00:20:09,540
编译的时候大家注意

582
00:20:09,540 --> 00:20:10,960
就是说要想实现编译

583
00:20:10,960 --> 00:20:12,060
你看回到咱们考虑去

584
00:20:12,060 --> 00:20:12,940
我们要记录很多东西

585
00:20:12,940 --> 00:20:13,760
比如说

586
00:20:13,760 --> 00:20:15,480
这地方加写属性

587
00:20:15,480 --> 00:20:16,640
什么属性

588
00:20:16,640 --> 00:20:17,180
你看啊

589
00:20:17,180 --> 00:20:18,540
看这里边是不是还有什么

590
00:20:18,540 --> 00:20:19,480
trunks

591
00:20:19,480 --> 00:20:20,540
modules

592
00:20:20,540 --> 00:20:21,720
下方线modules

593
00:20:21,720 --> 00:20:22,600
是不是assess

594
00:20:22,600 --> 00:20:24,340
我们看了几个都建上是不是

595
00:20:24,340 --> 00:20:24,960
好吧

596
00:20:24,960 --> 00:20:26,720
我们建上都有用啊

597
00:20:26,720 --> 00:20:27,100
你看啊

598
00:20:27,100 --> 00:20:27,580
比如说有什么

599
00:20:27,580 --> 00:20:28,060
有这个

600
00:20:28,060 --> 00:20:29,240
Z点

601
00:20:29,240 --> 00:20:30,660
Entrance

602
00:20:30,660 --> 00:20:30,980
还有什么

603
00:20:30,980 --> 00:20:32,440
还有这个modules

604
00:20:32,440 --> 00:20:34,140
对吧

605
00:20:34,140 --> 00:20:35,240
还有什么呀

606
00:20:35,240 --> 00:20:36,620
下滑线modules

607
00:20:36,620 --> 00:20:40,700
就是这是一个什么

608
00:20:40,700 --> 00:20:41,320
对象

609
00:20:41,320 --> 00:20:43,660
这是一个什么

610
00:20:43,660 --> 00:20:44,700
一个数组

611
00:20:44,700 --> 00:20:46,740
是一个模块的数组

612
00:20:46,740 --> 00:20:49,100
这是一个对象

613
00:20:49,100 --> 00:20:51,360
就这里面放了很多什么呀

614
00:20:51,360 --> 00:20:52,540
NormalModule的实力

615
00:20:52,540 --> 00:20:53,720
这里边放的什么

616
00:20:53,720 --> 00:20:54,560
放的很多kvalue

617
00:20:54,560 --> 00:20:55,660
kvalue

618
00:20:55,660 --> 00:20:56,520
介绍最后

619
00:20:56,520 --> 00:20:57,660
我给你帮我邀了路费

620
00:20:57,660 --> 00:20:58,960
帮我邀了打跌费

621
00:20:58,960 --> 00:21:00,660
然后这是一个对象

622
00:21:00,660 --> 00:21:01,420
k有kvalue

623
00:21:01,420 --> 00:21:03,140
k是模块的什么

624
00:21:03,140 --> 00:21:04,180
模块的绝对路径

625
00:21:04,180 --> 00:21:10,060
k是模块的绝对路径

626
00:21:10,060 --> 00:21:11,980
直是什么

627
00:21:11,980 --> 00:21:12,840
直是这个

628
00:21:12,840 --> 00:21:13,820
直是什么

629
00:21:13,820 --> 00:21:16,100
直是模块的实力

630
00:21:16,100 --> 00:21:18,580
那么这个是模块数组

631
00:21:18,580 --> 00:21:20,300
它的里边里边都是什么

632
00:21:20,300 --> 00:21:21,080
都是模块实力

633
00:21:21,080 --> 00:21:23,120
有什么用呢

634
00:21:23,120 --> 00:21:24,020
一会再说对吧

635
00:21:24,020 --> 00:21:24,740
对吧再说啊

636
00:21:24,740 --> 00:21:25,680
总而言之

637
00:21:25,680 --> 00:21:26,900
我加两个属性

638
00:21:26,900 --> 00:21:28,100
用来收集什么

639
00:21:28,100 --> 00:21:29,540
收集咱们这个module是不是

640
00:21:29,540 --> 00:21:30,700
收集module对吧

641
00:21:30,700 --> 00:21:32,940
好

642
00:21:32,940 --> 00:21:33,900
我们下边干什么

643
00:21:33,900 --> 00:21:35,020
开始开始编译了啊

644
00:21:35,020 --> 00:21:36,440
你看刚才我在编译上

645
00:21:36,440 --> 00:21:38,040
编译咱们这个代码是不是

646
00:21:38,040 --> 00:21:39,220
编译代码啊

647
00:21:39,220 --> 00:21:40,540
好

648
00:21:40,540 --> 00:21:42,080
那回到咱们这个

649
00:21:42,080 --> 00:21:43,560
no module 这边来

650
00:21:43,560 --> 00:21:44,660
对吧

651
00:21:44,660 --> 00:21:46,160
我要给他加一个属性啊

652
00:21:46,160 --> 00:21:46,580
加一个属性

653
00:21:46,580 --> 00:21:49,060
比如说加上加上这个Z的点什么呀

654
00:21:49,060 --> 00:21:49,860
B fantasy

655
00:21:49,860 --> 00:21:52,800
是不是依赖啊

656
00:21:52,800 --> 00:21:53,320
对吧

657
00:21:53,320 --> 00:21:54,560
就说一个模块

658
00:21:54,560 --> 00:21:55,600
可能会依赖别的模块

659
00:21:55,600 --> 00:21:55,860
是不是

660
00:21:55,860 --> 00:21:57,280
这里放的什么呀

661
00:21:57,280 --> 00:22:00,400
这里放的是依赖的数组

662
00:22:00,400 --> 00:22:03,200
模块数组

663
00:22:03,200 --> 00:22:03,600
是吧

664
00:22:03,600 --> 00:22:05,480
就说我怎么知道

665
00:22:05,480 --> 00:22:06,660
一个模块依赖的模块呢

666
00:22:06,660 --> 00:22:07,160
靠的谁

667
00:22:07,160 --> 00:22:08,680
就是这个就是这个变量

668
00:22:08,680 --> 00:22:11,300
模块数组

669
00:22:11,300 --> 00:22:14,100
好

670
00:22:14,100 --> 00:22:14,740
还有什么

671
00:22:14,740 --> 00:22:15,580
还有这一点什么

672
00:22:15,580 --> 00:22:16,260
modid

673
00:22:16,260 --> 00:22:17,980
有每个模块会有一个id

674
00:22:17,980 --> 00:22:18,720
对吧

675
00:22:18,720 --> 00:22:19,660
有模块id

676
00:22:19,660 --> 00:22:24,020
模块地怎么来呢

677
00:22:24,020 --> 00:22:24,520
一会再说

678
00:22:24,520 --> 00:22:25,000
对吧

679
00:22:25,000 --> 00:22:26,380
其实模块地有个特点

680
00:22:26,380 --> 00:22:27,020
什么特点呀

681
00:22:27,020 --> 00:22:28,240
模块地都是什么

682
00:22:28,240 --> 00:22:29,240
看

683
00:22:29,240 --> 00:22:30,980
它都是一个相对路径

684
00:22:30,980 --> 00:22:32,120
相对于谁呀

685
00:22:32,120 --> 00:22:33,320
相对于我们的这个

686
00:22:33,320 --> 00:22:34,140
根目录

687
00:22:34,140 --> 00:22:34,840
对吧

688
00:22:34,840 --> 00:22:35,580
这个讲过吧

689
00:22:35,580 --> 00:22:37,420
是不是都是相对根目录的一个路径啊

690
00:22:37,420 --> 00:22:37,660
对不对

691
00:22:37,660 --> 00:22:38,720
这模块地是吧

692
00:22:38,720 --> 00:22:39,460
模块地

693
00:22:39,460 --> 00:22:40,440
还有什么

694
00:22:40,440 --> 00:22:41,140
还有咱们这个

695
00:22:41,140 --> 00:22:42,040
Z这点什么呀

696
00:22:42,040 --> 00:22:42,820
下文IT

697
00:22:42,820 --> 00:22:44,040
就是语法术

698
00:22:44,040 --> 00:22:44,560
对吧

699
00:22:44,560 --> 00:22:46,120
还有什么Z这点什么呀

700
00:22:46,120 --> 00:22:47,300
这个下文IN source

701
00:22:47,300 --> 00:22:49,260
是不是原代代码啊

702
00:22:49,260 --> 00:22:49,440
是不是

703
00:22:49,440 --> 00:22:50,880
就是我们这个圆码

704
00:22:50,880 --> 00:22:54,280
这是什么

705
00:22:54,280 --> 00:22:54,900
这是这个

706
00:22:54,900 --> 00:22:57,900
本模块的抽象语法术

707
00:22:57,900 --> 00:23:01,340
这个如果再不回这个的话

708
00:23:01,340 --> 00:23:02,140
大家那个

709
00:23:02,140 --> 00:23:03,480
下一个补一补啊

710
00:23:03,480 --> 00:23:04,780
随便要一剔是吧

711
00:23:04,780 --> 00:23:05,200
读一补

712
00:23:05,200 --> 00:23:06,480
好

713
00:23:06,480 --> 00:23:06,880
然后呢

714
00:23:06,880 --> 00:23:07,980
咱们开始编译了吧

715
00:23:07,980 --> 00:23:09,100
是不是该走build的了

716
00:23:09,100 --> 00:23:09,940
走编译了啊

717
00:23:09,940 --> 00:23:10,400
把它删掉

718
00:23:10,400 --> 00:23:10,980
开始编译了

719
00:23:10,980 --> 00:23:12,140
怎么编译啊

720
00:23:12,140 --> 00:23:12,680
第一步怎么样

721
00:23:12,680 --> 00:23:13,680
我们要读取什么呀

722
00:23:13,680 --> 00:23:14,680
文件内容是不是啊

723
00:23:14,680 --> 00:23:15,080
对吧

724
00:23:15,080 --> 00:23:16,480
叫ordinal source

725
00:23:16,480 --> 00:23:18,200
等于什么

726
00:23:18,200 --> 00:23:18,900
等于这个

727
00:23:18,900 --> 00:23:19,340
complete

728
00:23:19,440 --> 00:23:22,920
他是不是有什么

729
00:23:22,920 --> 00:23:23,480
我还记得吗

730
00:23:23,480 --> 00:23:26,740
是不是有个叫叫input file system

731
00:23:26,740 --> 00:23:29,560
其实就是FI的模块

732
00:23:29,560 --> 00:23:32,260
叫什么read file sync

733
00:23:32,260 --> 00:23:34,580
叫这个读取文件的方法

734
00:23:34,580 --> 00:23:36,120
传递于谁啊

735
00:23:36,120 --> 00:23:38,800
传递于我们的这个什么request

736
00:23:38,800 --> 00:23:40,560
这个request是不是

737
00:23:40,560 --> 00:23:42,460
这个request是不是就是什么

738
00:23:42,460 --> 00:23:44,220
我们这个就是什么

739
00:23:44,220 --> 00:23:47,960
就是这个模块的学读书境

740
00:23:47,960 --> 00:23:50,760
因为时间不多了

741
00:23:50,760 --> 00:23:52,160
我就不再给大家调试了

742
00:23:52,160 --> 00:23:52,940
直接给大家写了

743
00:23:52,940 --> 00:23:53,820
直接写实现了

744
00:23:53,820 --> 00:23:56,500
但是这些方法呀

745
00:23:56,500 --> 00:23:56,960
原理呀

746
00:23:56,960 --> 00:23:58,860
不断跟语言码是一模一样的

747
00:23:58,860 --> 00:23:59,400
对吧

748
00:23:59,400 --> 00:24:00,060
好

749
00:24:00,060 --> 00:24:01,300
这咱们也把这个

750
00:24:01,300 --> 00:24:02,540
就快传进去吧

751
00:24:02,540 --> 00:24:03,660
好传到这来

752
00:24:03,660 --> 00:24:04,100
是不是啊

753
00:24:04,100 --> 00:24:04,480
传到这来

754
00:24:04,480 --> 00:24:07,300
就是读取模块的内容

755
00:24:07,300 --> 00:24:08,460
是不是啊

756
00:24:08,460 --> 00:24:09,400
指定编码得到一个

757
00:24:09,400 --> 00:24:09,860
得到一个什么

758
00:24:09,860 --> 00:24:10,280
得到一个

759
00:24:10,280 --> 00:24:11,500
约帖吧

760
00:24:11,500 --> 00:24:12,720
得到一个文本

761
00:24:12,720 --> 00:24:13,820
是不是啊

762
00:24:13,820 --> 00:24:14,960
写注释啊

763
00:24:14,960 --> 00:24:16,800
就是读取什么呀

764
00:24:16,800 --> 00:24:19,020
读取模块的内容

765
00:24:19,020 --> 00:24:21,080
对吧

766
00:24:21,080 --> 00:24:21,580
模块内容

767
00:24:21,580 --> 00:24:22,760
好

768
00:24:22,760 --> 00:24:23,820
然后呢

769
00:24:23,820 --> 00:24:25,440
是不是棍语法术啊

770
00:24:25,440 --> 00:24:25,840
AIT

771
00:24:25,840 --> 00:24:27,380
等于什么

772
00:24:27,380 --> 00:24:27,880
等于一个

773
00:24:27,880 --> 00:24:28,580
Bibulum

774
00:24:28,580 --> 00:24:29,420
叫什么

775
00:24:29,420 --> 00:24:30,000
点Path

776
00:24:30,000 --> 00:24:32,160
把原码呀

777
00:24:32,160 --> 00:24:32,760
转成语法术

778
00:24:32,760 --> 00:24:36,740
那怎么转呢

779
00:24:36,740 --> 00:24:37,880
靠这个模块啊

780
00:24:37,880 --> 00:24:38,460
这个这个方

781
00:24:38,460 --> 00:24:39,360
我们因为我们

782
00:24:39,360 --> 00:24:40,640
这个没有讲这个

783
00:24:40,640 --> 00:24:42,260
没有讲这个

784
00:24:42,260 --> 00:24:42,700
这儿啊

785
00:24:42,700 --> 00:24:44,320
这其实准备了一些这个

786
00:24:44,320 --> 00:24:45,560
准备了一个支点

787
00:24:45,560 --> 00:24:46,780
只不过来不及了啊

788
00:24:46,780 --> 00:24:48,020
就这个有几个模块

789
00:24:48,020 --> 00:24:50,960
babelon是一个解析器

790
00:24:50,960 --> 00:24:53,180
可以把一个代码转成一个语法术

791
00:24:53,180 --> 00:24:54,980
那么babel types呢

792
00:24:54,980 --> 00:24:55,720
它是什么

793
00:24:55,720 --> 00:24:57,560
是一个类型

794
00:24:57,560 --> 00:24:57,960
用来什么

795
00:24:57,960 --> 00:24:59,640
用来创建我们的AIT节点的

796
00:24:59,640 --> 00:25:01,000
和判断节点类型的

797
00:25:01,000 --> 00:25:01,180
是不是

798
00:25:01,180 --> 00:25:03,840
还有babel generator来生成什么代码的

799
00:25:03,840 --> 00:25:04,840
把语法术变成代码的

800
00:25:04,840 --> 00:25:05,020
是不是

801
00:25:05,020 --> 00:25:06,320
还有我们这个transfer

802
00:25:06,320 --> 00:25:07,020
用来选什么

803
00:25:07,020 --> 00:25:09,140
替换呀移除和增加数的节点

804
00:25:09,140 --> 00:25:10,060
这四个模块

805
00:25:10,060 --> 00:25:11,520
我们要我们其实要装一下

806
00:25:11,520 --> 00:25:11,800
是吧

807
00:25:11,800 --> 00:25:12,280
装一下

808
00:25:12,280 --> 00:25:18,280
装一下这几个模块

809
00:25:18,280 --> 00:25:21,820
就是用来什么

810
00:25:21,820 --> 00:25:23,200
把圆满的一把数

811
00:25:23,200 --> 00:25:24,700
然后呢找到里边的依赖

812
00:25:24,700 --> 00:25:25,420
然后怎么样

813
00:25:25,420 --> 00:25:27,840
然后再去这个生成代码

814
00:25:27,840 --> 00:25:29,040
生成短矿的代码是不是

815
00:25:29,040 --> 00:25:31,080
装一下它们

816
00:25:31,080 --> 00:25:33,560
还有EGS我们都装一下是吧

817
00:25:33,560 --> 00:25:34,760
还有什么这个make

818
00:25:34,760 --> 00:25:37,420
makedrp是吧

819
00:25:37,420 --> 00:25:38,080
都装一下

820
00:25:38,080 --> 00:25:45,920
大家坚持一下啊

821
00:25:45,920 --> 00:25:46,340
我尽快

822
00:25:46,340 --> 00:25:50,600
去DAC装吧

823
00:25:50,600 --> 00:26:02,140
考不出来吗

824
00:26:08,080 --> 00:26:24,120
好 可以了啊 可以了 好那么直接用啊 因为时间有限 我就不再去给大家一解释了啊

825
00:26:24,120 --> 00:26:25,080
 咱们就直接写了 是吧

826
00:26:25,080 --> 00:26:28,360
咱们先引入这几个模块 是吧 引入几个模块

827
00:26:28,360 --> 00:26:37,040
好 来 好 赖的什么呀 这个fs等于个requirefs要读文件用的 是不是啊

828
00:26:37,720 --> 00:26:40,520
还有pass 用来去拼路径是吧

829
00:26:40,520 --> 00:26:41,000
pass

830
00:26:41,000 --> 00:26:45,900
require

831
00:26:45,900 --> 00:26:47,660
还有我们的几个模块

832
00:26:47,660 --> 00:26:50,760
这个egs 用来去渲染我们这个原代码

833
00:26:50,760 --> 00:26:51,760
是不是

834
00:26:51,760 --> 00:26:52,700
egs 模板引擎

835
00:26:52,700 --> 00:26:56,380
还有什么

836
00:26:56,380 --> 00:26:57,280
我们这个 babylon

837
00:26:57,280 --> 00:27:02,280
等于require什么呀

838
00:27:02,280 --> 00:27:03,720
a b y l 1

839
00:27:05,920 --> 00:27:07,720
他们这个叫types

840
00:27:07,720 --> 00:27:11,660
等于require什么呀

841
00:27:11,660 --> 00:27:15,780
咱们这个叫什么叫这个bable-types

842
00:27:15,780 --> 00:27:20,460
还有叫genitor

843
00:27:20,460 --> 00:27:22,460
等于require

844
00:27:22,460 --> 00:27:25,380
那么叫bable-genitor

845
00:27:25,380 --> 00:27:26,020
叫default

846
00:27:26,020 --> 00:27:31,920
他叫traverse

847
00:27:31,920 --> 00:27:33,380
是一个便利的酷

848
00:27:33,380 --> 00:27:37,620
require bibel-traverse

849
00:27:37,620 --> 00:27:40,820
先把这个加点进来啊

850
00:27:40,820 --> 00:27:41,700
然后开始用对吧

851
00:27:41,700 --> 00:27:42,720
用

852
00:27:42,720 --> 00:27:43,520
用到第一步怎么样

853
00:27:43,520 --> 00:27:46,020
我们先通过它转成语法术是吧

854
00:27:46,020 --> 00:27:46,820
然后第二步呢

855
00:27:46,820 --> 00:27:47,240
便利语法

856
00:27:47,240 --> 00:27:49,540
便利语法术是吧

857
00:27:49,540 --> 00:27:50,140
怎么便利呢

858
00:27:50,140 --> 00:27:51,020
你看啊

859
00:27:51,020 --> 00:27:53,080
我一定要把这个代码考过来

860
00:27:53,080 --> 00:27:55,640
这个srcinner.js是吧

861
00:27:55,640 --> 00:27:56,520
考过来

862
00:27:56,520 --> 00:27:59,420
咱们打开这个网站

863
00:27:59,420 --> 00:28:01,140
打开这个网站啊

864
00:28:01,140 --> 00:28:02,920
看它的一个节点的样子是吧

865
00:28:03,380 --> 00:28:06,320
嗯 这几个都没

866
00:28:06,320 --> 00:28:11,320
拿过来咱们把它烤进去

867
00:28:11,320 --> 00:28:15,420
啊 叫什么叫贝博龙是吧

868
00:28:15,420 --> 00:28:19,000
好 这是他的语法书的样子是吧 你看啊

869
00:28:19,000 --> 00:28:23,100
至少你看第一号怎么样是不是有一个lite变量声明啊

870
00:28:23,100 --> 00:28:25,660
variable declaration变量声明对吧

871
00:28:25,660 --> 00:28:27,440
另的是一个变量声明

872
00:28:27,440 --> 00:28:32,300
这个是一个标识符呢是一个pattle

873
00:28:33,080 --> 00:28:36,720
啊 初始 它初始化的只能是一个call impression 是个方法雕用啊

874
00:28:36,720 --> 00:28:42,260
它的这个callin呢 是一个标识符 名字叫 require 是不是一个 require 方法呀

875
00:28:42,260 --> 00:28:47,890
element呢 是一个string literal 是一个字符刷的一个字面量 只能是一个点杠title啊

876
00:28:47,890 --> 00:28:49,180
 是一个这个文本 是吧

877
00:28:49,180 --> 00:28:50,820
啊 是他的名额

878
00:28:50,820 --> 00:28:53,640
啊 那么我们要找谁 是不是找 require啊

879
00:28:53,640 --> 00:28:58,570
是不是 要找到这个语法术 对吧 找到里边 require 节点呀 谁呀 就是那些

880
00:28:58,570 --> 00:29:01,760
 就那些什么 name 等于 require 的call expression

881
00:29:02,460 --> 00:29:03,660
是不是 找到这些东西

882
00:29:03,660 --> 00:29:06,220
然后把找到他怎么样 他依赖的模块

883
00:29:06,220 --> 00:29:08,700
是不是 这都在依赖的代码对不对

884
00:29:08,700 --> 00:29:10,200
好 让他来写啊

885
00:29:10,200 --> 00:29:14,100
好 我们现在来写他是不是

886
00:29:14,100 --> 00:29:15,320
先得到语法术

887
00:29:15,320 --> 00:29:17,360
然后干嘛是变利了对不对

888
00:29:17,360 --> 00:29:20,000
我们先来个变量啊 来个变量 声明一下

889
00:29:20,000 --> 00:29:21,900
就是一个 light 什么呀

890
00:29:21,900 --> 00:29:24,300
Dependency 等于一个空速 是不是啊

891
00:29:24,300 --> 00:29:25,860
然后呢 我们去

892
00:29:25,860 --> 00:29:29,220
traverse 变利 谁呀 语法术 A i t

893
00:29:29,220 --> 00:29:31,700
然后呢 这来什么 来个这个

894
00:29:32,060 --> 00:29:32,660
对象

895
00:29:32,660 --> 00:29:35,560
监控谁监控的call expression

896
00:29:35,560 --> 00:29:37,420
啊

897
00:29:37,420 --> 00:29:42,820
然后呢你什么意思

898
00:29:42,820 --> 00:29:44,780
就是说如果在辨理预法书的过程中

899
00:29:44,780 --> 00:29:46,460
如果发现它是一个call expression

900
00:29:46,460 --> 00:29:47,660
是个方法就用对吧

901
00:29:47,660 --> 00:29:49,400
我会怎么样进到这里边是吧

902
00:29:49,400 --> 00:29:52,020
好这会拿到把拿到一个node什么样

903
00:29:52,020 --> 00:29:53,020
pass啊

904
00:29:53,020 --> 00:29:54,740
拿到一个他会传过一个路径过来啊

905
00:29:54,740 --> 00:29:56,300
node pass 路径啊

906
00:29:56,300 --> 00:29:59,240
然后可以通过路径拿到什么

907
00:29:59,240 --> 00:30:02,060
拿到我当前的节点node pass点什么node

908
00:30:02,060 --> 00:30:02,860
可以拿到节点

909
00:30:02,860 --> 00:30:04,000
什么叫node

910
00:30:04,000 --> 00:30:04,980
就是什么

911
00:30:04,980 --> 00:30:05,760
其实就是什么

912
00:30:05,760 --> 00:30:07,640
就是这个对象

913
00:30:07,640 --> 00:30:08,020
看到了吧

914
00:30:08,020 --> 00:30:08,640
就是对象

915
00:30:08,640 --> 00:30:09,580
看到吧

916
00:30:09,580 --> 00:30:10,020
就是对象

917
00:30:10,020 --> 00:30:11,420
这是对象

918
00:30:11,420 --> 00:30:12,760
有什么属性

919
00:30:12,760 --> 00:30:13,500
piff呀

920
00:30:13,500 --> 00:30:14,200
coy呀

921
00:30:14,200 --> 00:30:14,660
arguments

922
00:30:14,660 --> 00:30:15,140
是不是

923
00:30:15,140 --> 00:30:16,760
有点属性啊

924
00:30:16,760 --> 00:30:17,820
好

925
00:30:17,820 --> 00:30:18,440
我们来吧

926
00:30:18,440 --> 00:30:19,900
现在拿到什么

927
00:30:19,900 --> 00:30:21,480
拿到这个coy吧

928
00:30:21,480 --> 00:30:22,380
什么叫coy呀

929
00:30:22,380 --> 00:30:22,900
是交通者

930
00:30:22,900 --> 00:30:24,300
是交通方啊

931
00:30:24,300 --> 00:30:25,780
它的name是不是就是一块啊

932
00:30:25,780 --> 00:30:26,000
是不是

933
00:30:26,000 --> 00:30:26,900
好

934
00:30:26,900 --> 00:30:27,860
来判断

935
00:30:27,860 --> 00:30:30,220
这个node

936
00:30:30,220 --> 00:30:30,900
dr

937
00:30:30,900 --> 00:30:33,640
要判断

938
00:30:33,640 --> 00:30:38,860
如果说咱们node path

939
00:30:38,860 --> 00:30:40,060
它的什么呀

940
00:30:40,060 --> 00:30:40,500
node

941
00:30:40,500 --> 00:30:43,220
它的call e.name

942
00:30:43,220 --> 00:30:44,820
等于什么require的话

943
00:30:44,820 --> 00:30:46,280
说明就是我们要找的方法是不是

944
00:30:46,280 --> 00:30:48,880
其他人不管

945
00:30:48,880 --> 00:30:50,240
我们只要只剪到一块

946
00:30:50,240 --> 00:30:53,300
好把它放进去

947
00:30:53,300 --> 00:30:53,960
拿到它的节点

948
00:30:53,960 --> 00:30:55,100
拿到节点

949
00:30:55,100 --> 00:31:00,500
拿到节点之后怎么办呢

950
00:31:00,500 --> 00:31:01,120
我们怎么样

951
00:31:01,120 --> 00:31:02,820
我们去写注释啊

952
00:31:02,820 --> 00:31:03,960
获取当线节点

953
00:31:03,960 --> 00:31:06,360
的节点对象

954
00:31:06,360 --> 00:31:07,900
对象

955
00:31:07,900 --> 00:31:09,660
好

956
00:31:09,660 --> 00:31:11,120
拿到之后怎么办

957
00:31:11,120 --> 00:31:13,020
我是不是可以这样写啊

958
00:31:13,020 --> 00:31:14,320
node点什么

959
00:31:14,320 --> 00:31:15,980
call一点

960
00:31:15,980 --> 00:31:17,240
怎么name

961
00:31:17,240 --> 00:31:19,020
等于谁啊

962
00:31:19,020 --> 00:31:19,840
还记得吗

963
00:31:19,840 --> 00:31:21,480
咱们这个require话要变掉

964
00:31:21,480 --> 00:31:21,920
变成谁

965
00:31:21,920 --> 00:31:22,820
变成我们的这个

966
00:31:22,820 --> 00:31:24,340
叫wiprequire啊

967
00:31:24,340 --> 00:31:25,240
还记得吧

968
00:31:25,240 --> 00:31:26,400
是变成谁

969
00:31:26,400 --> 00:31:27,100
变成他呀

970
00:31:27,100 --> 00:31:28,340
你看知道吗

971
00:31:28,340 --> 00:31:30,480
是不是变成他呀

972
00:31:30,480 --> 00:31:36,620
你看 猪羊怎么变的 把require变成wipyrequire 是不是 是把它变成他 改掉就可以了

973
00:31:36,620 --> 00:31:37,240
 是不是啊

974
00:31:37,240 --> 00:31:39,240
好 然后呢

975
00:31:39,240 --> 00:31:43,320
他拿着模型的名字吧 let module name

976
00:31:43,320 --> 00:31:50,840
等于谁 等于这个node.arguments 0 对吧 拿到nodearguments 0

977
00:31:50,840 --> 00:31:57,130
arment0是谁 你看 arment是不是数组啊 零是不是这个signVitual呀 它是不是有value属性啊

978
00:31:57,130 --> 00:31:57,920
 就是咱们字不刷呀

979
00:31:58,320 --> 00:31:59,460
对啊

980
00:31:59,460 --> 00:32:02,160
你看应该取什么取这个nodeiPhone的零

981
00:32:02,160 --> 00:32:03,260
点什么

982
00:32:03,260 --> 00:32:05,760
点伴侣啊

983
00:32:05,760 --> 00:32:09,080
是谁是不是拿到那个什么就点个src

984
00:32:09,080 --> 00:32:10,220
杠牌头啊

985
00:32:10,220 --> 00:32:12,420
应该是点棒太多吧

986
00:32:12,420 --> 00:32:14,720
是拿到他了

987
00:32:14,720 --> 00:32:16,760
是不是你看

988
00:32:16,760 --> 00:32:18,480
是不是这个点太多啊

989
00:32:18,480 --> 00:32:20,920
就这个图片啊就他吧拿到他了吧

990
00:32:20,920 --> 00:32:23,580
啊然后怎么办呢

991
00:32:23,580 --> 00:32:25,260
你想想然后怎么办

992
00:32:25,260 --> 00:32:27,420
让我第一步啊我需要怎么样

993
00:32:27,620 --> 00:32:29,360
我需要这个把它进行补权

994
00:32:29,360 --> 00:32:30,200
因为你会发现

995
00:32:30,200 --> 00:32:32,560
你代码里面写的时候是没有加的后缀啊

996
00:32:32,560 --> 00:32:34,000
但是你看打不出来代码里面

997
00:32:34,000 --> 00:32:35,320
这是不是都有后缀啊

998
00:32:35,320 --> 00:32:36,440
交接的后缀啊

999
00:32:36,440 --> 00:32:38,000
加上啊

1000
00:32:38,000 --> 00:32:38,980
好我们先声明

1001
00:32:38,980 --> 00:32:40,600
声明了一个ET

1002
00:32:40,600 --> 00:32:41,920
name对吧

1003
00:32:41,920 --> 00:32:42,580
就是扩展名

1004
00:32:42,580 --> 00:32:43,960
那等于什么呢

1005
00:32:43,960 --> 00:32:45,660
咱们可以这样啊

1006
00:32:45,660 --> 00:32:46,220
咱们可以这样

1007
00:32:46,220 --> 00:32:48,180
先把这个module name怎么样

1008
00:32:48,180 --> 00:32:48,940
先怎么样

1009
00:32:48,940 --> 00:32:49,840
先于slead一下

1010
00:32:49,840 --> 00:32:51,100
分割一下

1011
00:32:51,100 --> 00:32:53,000
拿分割服分割一下

1012
00:32:53,000 --> 00:32:53,500
对吧

1013
00:32:57,620 --> 00:32:59,000
分割一下

1014
00:32:59,000 --> 00:33:00,300
你看什么意思

1015
00:33:00,300 --> 00:33:02,360
你看把他已经拿这个杠已经分割

1016
00:33:02,360 --> 00:33:05,120
这个IDP就是分割符的意思啊

1017
00:33:05,120 --> 00:33:06,240
就是录音分割符就是杠

1018
00:33:06,240 --> 00:33:07,660
那时候分成两段了

1019
00:33:07,660 --> 00:33:08,080
一个是点

1020
00:33:08,080 --> 00:33:09,100
一个是Title

1021
00:33:09,100 --> 00:33:10,760
然后我要判断

1022
00:33:10,760 --> 00:33:11,880
对吧

1023
00:33:11,880 --> 00:33:12,560
要分割吧

1024
00:33:12,560 --> 00:33:13,180
然后呢

1025
00:33:13,180 --> 00:33:14,360
他会反回速度是不是

1026
00:33:14,360 --> 00:33:15,620
然后pop

1027
00:33:15,620 --> 00:33:16,540
pop的意思

1028
00:33:16,540 --> 00:33:18,800
这个pop的话

1029
00:33:18,800 --> 00:33:19,720
反回是反回什么

1030
00:33:19,720 --> 00:33:21,220
是这个Title

1031
00:33:21,220 --> 00:33:22,640
是不是反回Title

1032
00:33:22,640 --> 00:33:23,500
对吧

1033
00:33:23,500 --> 00:33:23,960
反回Title

1034
00:33:23,960 --> 00:33:29,080
你看这个地方

1035
00:33:29,080 --> 00:33:30,020
咱们干脆啊

1036
00:33:30,020 --> 00:33:31,940
咱干脆可以一气呵成是吧

1037
00:33:31,940 --> 00:33:32,740
一气呵成

1038
00:33:32,740 --> 00:33:35,880
好

1039
00:33:35,880 --> 00:33:36,660
一气呵成是吧

1040
00:33:36,660 --> 00:33:37,340
判断一下

1041
00:33:37,340 --> 00:33:38,420
那你看

1042
00:33:38,420 --> 00:33:39,680
那我怎么判断

1043
00:33:39,680 --> 00:33:41,120
它扩展平是啥呀

1044
00:33:41,120 --> 00:33:41,680
对不对

1045
00:33:41,680 --> 00:33:43,860
怎么可以这样吧

1046
00:33:43,860 --> 00:33:44,300
就说你看

1047
00:33:44,300 --> 00:33:45,380
POW出来怎么样

1048
00:33:45,380 --> 00:33:46,600
我们点怎么样

1049
00:33:46,600 --> 00:33:47,700
可以这样

1050
00:33:47,700 --> 00:33:48,380
INDEXO

1051
00:33:48,380 --> 00:33:51,920
看它有没有点

1052
00:33:51,920 --> 00:33:52,520
对吧

1053
00:33:52,520 --> 00:33:53,780
如果有的话

1054
00:33:53,780 --> 00:33:56,740
说明说明他是可能是是不是点接四啊

1055
00:33:56,740 --> 00:33:57,780
加了后对了

1056
00:33:57,780 --> 00:33:59,840
那这样的话扩展面我就不用加好了

1057
00:33:59,840 --> 00:34:01,740
我们不用加后对是不是啊

1058
00:34:01,740 --> 00:34:02,480
那如果他怎么样

1059
00:34:02,480 --> 00:34:03,840
如果是呃

1060
00:34:03,840 --> 00:34:05,280
比如说不等于服役

1061
00:34:05,280 --> 00:34:06,340
我能等于服役吧

1062
00:34:06,340 --> 00:34:07,140
那等于服役的话

1063
00:34:07,140 --> 00:34:09,020
是不是就是没有这个点啊

1064
00:34:09,020 --> 00:34:11,220
那么给什么后对点接的后对

1065
00:34:11,220 --> 00:34:13,280
我写死了啊写死了

1066
00:34:13,280 --> 00:34:16,520
其实那应该去独配这文件去动态去替换

1067
00:34:16,520 --> 00:34:16,740
是不是

1068
00:34:16,740 --> 00:34:19,740
我们就不去动独配这文件我写死了

1069
00:34:19,740 --> 00:34:20,780
所以就空吧

1070
00:34:20,780 --> 00:34:23,280
就说如果没有点加点就要加后对

1071
00:34:23,280 --> 00:34:24,700
如果有点就拉倒了

1072
00:34:24,700 --> 00:34:25,580
就不用压后对了

1073
00:34:25,580 --> 00:34:25,800
对吧

1074
00:34:25,800 --> 00:34:27,240
因为他写权就不用压后对了呗

1075
00:34:27,240 --> 00:34:28,620
不然就重复了啊

1076
00:34:28,620 --> 00:34:30,660
啊

1077
00:34:30,660 --> 00:34:31,680
困难民友取到的就是什么

1078
00:34:31,680 --> 00:34:33,320
就是空图话或者什么

1079
00:34:33,320 --> 00:34:34,700
或者点解释是不是啊

1080
00:34:34,700 --> 00:34:35,700
对吧

1081
00:34:35,700 --> 00:34:35,940
好

1082
00:34:35,940 --> 00:34:36,320
然后呢

1083
00:34:36,320 --> 00:34:36,900
我们去什么

1084
00:34:36,900 --> 00:34:39,160
来到一个dependency

1085
00:34:39,160 --> 00:34:42,740
来到一个dependency

1086
00:34:42,740 --> 00:34:43,200
对吧

1087
00:34:43,200 --> 00:34:45,700
request

1088
00:34:45,700 --> 00:34:47,400
这啥意思啊

1089
00:34:47,400 --> 00:34:48,120
这什么意思

1090
00:34:48,120 --> 00:34:48,400
注意啊

1091
00:34:48,400 --> 00:34:49,680
这个地方是难点啊

1092
00:34:49,680 --> 00:34:51,240
要听清楚是吧

1093
00:34:51,240 --> 00:34:51,900
这是怎么样

1094
00:34:51,900 --> 00:34:54,900
获取依赖模块的什么呀

1095
00:34:54,900 --> 00:34:55,500
绝对路径

1096
00:34:55,500 --> 00:34:59,640
这个怎么取啊

1097
00:34:59,640 --> 00:35:01,140
那怎么拿到什么

1098
00:35:01,140 --> 00:35:03,560
拿到咱们这个谁呀

1099
00:35:03,560 --> 00:35:04,700
是不是抬头啊

1100
00:35:04,700 --> 00:35:07,500
拿到抬头模块的绝对路径呢

1101
00:35:07,500 --> 00:35:08,900
你想想怎么拿

1102
00:35:08,900 --> 00:35:11,060
拿当前拿着一类杰斯

1103
00:35:11,060 --> 00:35:13,240
他的所在的绝对路径

1104
00:35:13,240 --> 00:35:14,540
所在的目录的路径

1105
00:35:14,540 --> 00:35:16,360
加上这样一个性路径

1106
00:35:16,360 --> 00:35:18,100
比如说这个模块绝对路径吗

1107
00:35:18,100 --> 00:35:18,500
对不对

1108
00:35:18,500 --> 00:35:19,760
因为比如说一类杰斯在这

1109
00:35:19,760 --> 00:35:21,400
这不在sd 下面啊

1110
00:35:21,400 --> 00:35:26,520
那我呢 ac 这个目录加上这个文件路径是不是就可以了对吧怎么做呢你看啊这样写

1111
00:35:26,520 --> 00:35:31,640
好这点写啊你看像加加吧

1112
00:35:31,640 --> 00:35:33,680
像加 pass 点什么呀

1113
00:35:33,680 --> 00:35:39,320
paw6 点什么点就啊好这呢 pass 点什么paw6

1114
00:35:39,320 --> 00:35:42,760
点什么点这个点呢谁呀

1115
00:35:42,760 --> 00:35:43,420
z 这一块

1116
00:35:43,420 --> 00:35:47,840
z 这一块的是不是当节模块的就路径啊拿到他所在的目录

1117
00:35:47,840 --> 00:35:49,040
然后呢

1118
00:35:49,300 --> 00:35:49,880
加上谁啊

1119
00:35:49,880 --> 00:35:50,860
加上这个什么呀

1120
00:35:50,860 --> 00:35:52,760
这个module name

1121
00:35:52,760 --> 00:35:53,680
是不是文化的名字啊

1122
00:35:53,680 --> 00:35:54,900
这相对路径吧

1123
00:35:54,900 --> 00:35:55,840
名字

1124
00:35:55,840 --> 00:35:56,420
加上什么呀

1125
00:35:56,420 --> 00:35:58,100
加上咱们这个yet name

1126
00:35:58,100 --> 00:35:59,260
是不是后对啊

1127
00:35:59,260 --> 00:36:00,760
就可以了是吧

1128
00:36:00,760 --> 00:36:01,440
这样的话拿到了

1129
00:36:01,440 --> 00:36:02,840
拿到他的绝路径了吧

1130
00:36:02,840 --> 00:36:03,760
嗯

1131
00:36:03,760 --> 00:36:04,200
其实就是谁

1132
00:36:04,200 --> 00:36:07,300
就是这个tattle

1133
00:36:07,300 --> 00:36:08,420
第二tattle yes吧

1134
00:36:08,420 --> 00:36:10,900
第二tattle yes

1135
00:36:10,900 --> 00:36:11,560
是不是

1136
00:36:11,560 --> 00:36:13,740
好

1137
00:36:13,740 --> 00:36:13,960
好

1138
00:36:13,960 --> 00:36:15,240
然后拿到之后怎么样

1139
00:36:15,240 --> 00:36:15,940
我们要怎么样

1140
00:36:15,940 --> 00:36:18,000
是不是拿到这个

1141
00:36:18,000 --> 00:36:18,340
获取

1142
00:36:18,340 --> 00:36:19,800
在获取什么获取这个

1143
00:36:19,800 --> 00:36:24,000
在获取什么依赖模块的什么样

1144
00:36:24,000 --> 00:36:24,540
模块的

1145
00:36:24,540 --> 00:36:26,640
是不是

1146
00:36:26,640 --> 00:36:27,940
id怎么拿

1147
00:36:27,940 --> 00:36:30,700
id你想想刚才说怎么拿的呀

1148
00:36:30,700 --> 00:36:31,340
是不是就是

1149
00:36:31,340 --> 00:36:35,040
这个路径这个文件相对于我们这个什么呀

1150
00:36:35,040 --> 00:36:37,040
谁的路径谁的子路径啊

1151
00:36:37,040 --> 00:36:38,240
相对于我们这个

1152
00:36:38,240 --> 00:36:40,240
contest

1153
00:36:40,240 --> 00:36:42,940
就是当年根本有子路径就是他的模块id啊

1154
00:36:42,940 --> 00:36:44,100
是不是啊

1155
00:36:44,100 --> 00:36:45,500
那怎么拿

1156
00:36:45,500 --> 00:36:46,780
赖的什么呀

1157
00:36:48,200 --> 00:36:50,700
definance是什么 叫modelid

1158
00:36:50,700 --> 00:36:53,960
等于谁

1159
00:36:53,960 --> 00:36:57,360
是不是应该是一个.杠加上一个什么

1160
00:36:57,360 --> 00:37:00,740
pass.pass.relative

1161
00:37:00,740 --> 00:37:03,960
或许相处于谁相对于谁啊

1162
00:37:03,960 --> 00:37:06,240
就是我们这个request

1163
00:37:06,240 --> 00:37:07,660
对吧

1164
00:37:07,660 --> 00:37:10,640
应该是z.connect

1165
00:37:10,640 --> 00:37:11,860
对吧

1166
00:37:11,860 --> 00:37:16,860
z.connect什么就是当前的根目录吧

1167
00:37:18,000 --> 00:37:21,040
是不是就是这个路径相对于根目录的像路径

1168
00:37:21,040 --> 00:37:22,900
但是他这不带点杠呢

1169
00:37:22,900 --> 00:37:24,360
把点杠加上又贴了是不是

1170
00:37:24,360 --> 00:37:25,600
加上又贴了啊

1171
00:37:25,600 --> 00:37:28,360
魔化ID其实就是活到就是我们的点杠怎么样

1172
00:37:28,360 --> 00:37:30,760
sd杠一杠杠他多少结词呗

1173
00:37:30,760 --> 00:37:32,840
就他对不对就他啊

1174
00:37:32,840 --> 00:37:36,560
好电脑魔化ID一辆的魔化ID拿到了对吧

1175
00:37:36,560 --> 00:37:37,140
然后呢

1176
00:37:37,140 --> 00:37:40,700
然后的话我们要把它放进去吧

1177
00:37:40,700 --> 00:37:43,740
叫着把它放到我们的数字里边去收集起来了吧

1178
00:37:43,740 --> 00:37:46,140
dependence 点什么点push

1179
00:37:47,200 --> 00:37:48,760
拨设一个对象啊

1180
00:37:48,760 --> 00:37:50,640
有什么有 name 对吧

1181
00:37:50,640 --> 00:37:52,600
取我们litz点什么呀 name

1182
00:37:52,600 --> 00:37:54,360
这是什么啊

1183
00:37:54,360 --> 00:37:55,440
这是代码块的名字啊

1184
00:37:55,440 --> 00:37:57,240
就是所属的

1185
00:37:57,240 --> 00:38:00,240
就是此模块

1186
00:38:00,240 --> 00:38:03,960
所属的

1187
00:38:03,960 --> 00:38:06,960
代码块的名字

1188
00:38:06,960 --> 00:38:09,960
还有什么呀

1189
00:38:09,960 --> 00:38:13,440
他们的contest吧

1190
00:38:13,440 --> 00:38:14,640
这不就是litz contest啊

1191
00:38:14,640 --> 00:38:15,560
当地上海门吧

1192
00:38:15,560 --> 00:38:16,760
对不对啊

1193
00:38:16,760 --> 00:38:17,120
还有什么

1194
00:38:17,120 --> 00:38:18,280
还有我们这个叫request

1195
00:38:18,280 --> 00:38:20,300
就谁不就是这个

1196
00:38:20,300 --> 00:38:21,820
地盘的request吗

1197
00:38:21,820 --> 00:38:22,580
就是绝路径嘛

1198
00:38:22,580 --> 00:38:23,040
对不对

1199
00:38:23,040 --> 00:38:24,600
就是东西

1200
00:38:24,600 --> 00:38:27,380
这个东西大家感觉很熟悉吗

1201
00:38:27,380 --> 00:38:28,020
很熟悉

1202
00:38:28,020 --> 00:38:30,920
三属性name,contest,request

1203
00:38:30,920 --> 00:38:31,620
他们俩干嘛

1204
00:38:31,620 --> 00:38:33,000
要创建模块的

1205
00:38:33,000 --> 00:38:34,140
不近你看

1206
00:38:34,140 --> 00:38:35,840
我们你看我们在这个

1207
00:38:35,840 --> 00:38:36,920
创建模块的时候

1208
00:38:36,920 --> 00:38:37,960
我们是不是有三属性啊

1209
00:38:37,960 --> 00:38:39,020
name,contest,request

1210
00:38:39,020 --> 00:38:39,840
对不对

1211
00:38:39,840 --> 00:38:40,880
创建模块的

1212
00:38:40,880 --> 00:38:42,160
先把它放进来啊

1213
00:38:42,160 --> 00:38:42,960
放进来

1214
00:38:42,960 --> 00:38:44,120
好

1215
00:38:44,120 --> 00:38:44,940
到了这一步啊

1216
00:38:44,940 --> 00:38:46,160
咱们这个Eline已经收集完了

1217
00:38:46,160 --> 00:38:46,400
是不是

1218
00:38:46,400 --> 00:38:47,300
收取完了

1219
00:38:47,300 --> 00:38:48,780
然后还插最后一步

1220
00:38:48,780 --> 00:38:50,060
怎么做呀

1221
00:38:50,060 --> 00:38:51,180
node.augments

1222
00:38:51,180 --> 00:38:55,120
等于什么一个数组

1223
00:38:55,120 --> 00:38:58,220
types.string.liter

1224
00:38:58,220 --> 00:39:01,700
里边放上一个modid

1225
00:39:01,700 --> 00:39:02,920
就可以了是吧

1226
00:39:02,920 --> 00:39:04,460
就这样

1227
00:39:04,460 --> 00:39:05,840
我要把参数改掉

1228
00:39:05,840 --> 00:39:06,620
原来啥

1229
00:39:06,620 --> 00:39:08,140
原来是不是一个点杠type.js

1230
00:39:08,140 --> 00:39:09,100
现在改成什么

1231
00:39:09,100 --> 00:39:10,300
改成一个点杠什么样

1232
00:39:10,300 --> 00:39:11,260
sd杠type.js

1233
00:39:11,260 --> 00:39:11,720
是不是

1234
00:39:11,720 --> 00:39:13,180
就是说你看

1235
00:39:13,180 --> 00:39:14,420
把这个参数

1236
00:39:14,420 --> 00:39:17,080
从什么呀

1237
00:39:17,080 --> 00:39:17,680
第二杠

1238
00:39:17,680 --> 00:39:18,600
tidal.js

1239
00:39:18,600 --> 00:39:20,320
改为什么呀

1240
00:39:20,320 --> 00:39:20,780
第二杠

1241
00:39:20,780 --> 00:39:21,900
slc

1242
00:39:21,900 --> 00:39:22,360
杠

1243
00:39:22,360 --> 00:39:23,120
tidal.js

1244
00:39:23,120 --> 00:39:23,880
是不是

1245
00:39:23,880 --> 00:39:25,500
那就这样可以了啊

1246
00:39:25,500 --> 00:39:26,500
好

1247
00:39:26,500 --> 00:39:27,800
那么便利完成了

1248
00:39:27,800 --> 00:39:29,380
那么完成之后干嘛

1249
00:39:29,380 --> 00:39:30,640
是不是该去重新生存代码了

1250
00:39:30,640 --> 00:39:31,480
好

1251
00:39:31,480 --> 00:39:32,360
我们把这个循环

1252
00:39:32,360 --> 00:39:33,580
把这个transverse写完了

1253
00:39:33,580 --> 00:39:35,140
我天

1254
00:39:35,140 --> 00:39:35,680
transverse

1255
00:39:35,680 --> 00:39:37,340
这么长吗

1256
00:39:37,340 --> 00:39:38,260
好吧

1257
00:39:38,260 --> 00:39:38,820
就这么长

1258
00:39:38,820 --> 00:39:41,740
好

1259
00:39:41,740 --> 00:39:43,040
把这transverse结束了啊

1260
00:39:43,040 --> 00:39:43,500
结束了

1261
00:39:43,500 --> 00:39:44,140
结束之后呢

1262
00:39:44,140 --> 00:39:45,580
我们就往下写对吧

1263
00:39:45,580 --> 00:39:48,540
怎么先ledcode重新生成代码吧

1264
00:39:48,540 --> 00:39:50,760
等于什么等于这个generate

1265
00:39:50,760 --> 00:39:52,220
然后呢

1266
00:39:52,220 --> 00:39:53,020
我们的it

1267
00:39:53,020 --> 00:39:54,220
就是把

1268
00:39:54,220 --> 00:39:56,380
把什么呀

1269
00:39:56,380 --> 00:40:00,560
把转换后的抽象语法术

1270
00:40:00,560 --> 00:40:04,520
对吧语法术

1271
00:40:04,520 --> 00:40:06,760
重新什么呀

1272
00:40:06,760 --> 00:40:07,660
生成代码

1273
00:40:07,660 --> 00:40:12,720
看他的代码结构长得跟我们这个什么呀

1274
00:40:12,720 --> 00:40:14,020
跟我们这边这个

1275
00:40:14,020 --> 00:40:15,460
代码就

1276
00:40:15,460 --> 00:40:17,560
是不是就整成这个样子了

1277
00:40:17,560 --> 00:40:19,780
名字变成wiprequire了

1278
00:40:19,780 --> 00:40:22,420
这个模块id变成这个模块id了

1279
00:40:22,420 --> 00:40:22,580
是不是

1280
00:40:22,580 --> 00:40:23,860
变了啊

1281
00:40:23,860 --> 00:40:26,280
给你打印一下给他看看

1282
00:40:26,280 --> 00:40:31,800
好

1283
00:40:31,800 --> 00:40:33,820
好拿过来吧

1284
00:40:33,820 --> 00:40:34,720
就可以了

1285
00:40:34,720 --> 00:40:35,320
好

1286
00:40:35,320 --> 00:40:35,820
然后呢

1287
00:40:35,820 --> 00:40:37,160
然后注意啊

1288
00:40:37,160 --> 00:40:38,220
咱们先混定起来吧

1289
00:40:38,220 --> 00:40:39,140
Z这点什么呀

1290
00:40:39,140 --> 00:40:40,960
下完IT等于什么

1291
00:40:40,960 --> 00:40:41,340
IT

1292
00:40:41,340 --> 00:40:42,680
把预发数保证起来是吧

1293
00:40:42,680 --> 00:40:43,960
给别人用了吧

1294
00:40:43,960 --> 00:40:45,840
谁要想用给谁用是吧

1295
00:40:45,840 --> 00:40:46,560
那张什么

1296
00:40:46,560 --> 00:40:47,540
那张咱们的source

1297
00:40:47,540 --> 00:40:50,840
是不是当前模块原代码

1298
00:40:50,840 --> 00:40:51,900
是什么

1299
00:40:51,900 --> 00:40:53,440
就是我们的code是不是啊

1300
00:40:53,440 --> 00:40:53,740
对吧

1301
00:40:53,740 --> 00:40:56,500
是当前模块对应的原码

1302
00:40:56,500 --> 00:41:01,660
当前模块对应的原码

1303
00:41:01,660 --> 00:41:04,640
就是我们的code是不是啊

1304
00:41:04,640 --> 00:41:05,900
然后呢还有什么呀

1305
00:41:05,900 --> 00:41:08,580
那么到这其实编译编译完成了吧

1306
00:41:08,580 --> 00:41:10,520
已经拿到了我们的这个编户原码了

1307
00:41:10,520 --> 00:41:11,900
已经拿到它的依赖的这个

1308
00:41:11,900 --> 00:41:13,340
是不是依赖的这个

1309
00:41:13,340 --> 00:41:14,680
魔花速度了

1310
00:41:14,680 --> 00:41:16,220
编译完成了

1311
00:41:16,220 --> 00:41:17,480
编译怎么样

1312
00:41:17,480 --> 00:41:18,280
放吧

1313
00:41:18,280 --> 00:41:18,720
放到哪

1314
00:41:18,720 --> 00:41:20,040
放到咱们convade里面去是吧

1315
00:41:20,040 --> 00:41:21,620
放到它的什么样

1316
00:41:21,620 --> 00:41:22,120
比如说DR

1317
00:41:22,120 --> 00:41:23,540
modules

1318
00:41:23,540 --> 00:41:24,860
DR push

1319
00:41:24,860 --> 00:41:26,460
把自己啊

1320
00:41:26,460 --> 00:41:27,040
放到什么

1321
00:41:27,040 --> 00:41:28,220
魔花速度里面去

1322
00:41:28,220 --> 00:41:28,880
对吧

1323
00:41:28,880 --> 00:41:31,200
还有什么呀

1324
00:41:31,200 --> 00:41:32,000
放到什么

1325
00:41:32,000 --> 00:41:33,720
DR这个下关键module里面去

1326
00:41:33,720 --> 00:41:34,760
它呀是个对象

1327
00:41:34,760 --> 00:41:35,400
是不是

1328
00:41:35,400 --> 00:41:36,300
K呢

1329
00:41:36,300 --> 00:41:37,540
是魔花的什么呀

1330
00:41:37,540 --> 00:41:38,120
这个

1331
00:41:38,120 --> 00:41:39,560
角度硬

1332
00:41:39,560 --> 00:41:40,540
直呢

1333
00:41:40,540 --> 00:41:41,860
是魔花本身吧

1334
00:41:41,860 --> 00:41:42,340
module

1335
00:41:42,340 --> 00:41:43,180
是不是

1336
00:41:43,180 --> 00:41:44,900
还记得吗

1337
00:41:44,900 --> 00:41:45,080
你看

1338
00:41:45,080 --> 00:41:46,300
看这个

1339
00:41:46,300 --> 00:41:47,020
看张图

1340
00:41:47,020 --> 00:41:48,380
当时我们是原码里面搞出来的

1341
00:41:48,380 --> 00:41:48,600
你看

1342
00:41:48,600 --> 00:41:49,920
module是不是一个

1343
00:41:49,920 --> 00:41:51,460
node module数组啊

1344
00:41:51,460 --> 00:41:52,840
这个相关于module

1345
00:41:52,840 --> 00:41:53,680
是map呀

1346
00:41:53,680 --> 00:41:55,280
k那是模画的id啊

1347
00:41:55,280 --> 00:41:56,480
这是不是就是模画的那个

1348
00:41:56,480 --> 00:41:57,880
node module啊

1349
00:41:57,880 --> 00:41:58,300
对不对

1350
00:41:58,300 --> 00:41:59,740
就是它啊

1351
00:41:59,740 --> 00:42:01,440
好

1352
00:42:01,440 --> 00:42:02,520
到了这之后呢

1353
00:42:02,520 --> 00:42:03,400
我们就最后怎么样

1354
00:42:03,400 --> 00:42:04,240
你看是不是要

1355
00:42:04,240 --> 00:42:05,680
你看啊

1356
00:42:05,680 --> 00:42:07,420
添加本模画

1357
00:42:07,420 --> 00:42:10,200
然后最后

1358
00:42:10,200 --> 00:42:11,340
要干嘛了

1359
00:42:11,340 --> 00:42:14,920
说在于dv解析dv编辑它依赖的模块啊

1360
00:42:14,920 --> 00:42:16,620
现在是不是在编辑什么入口模块

1361
00:42:16,620 --> 00:42:17,680
依赖解死啊

1362
00:42:17,680 --> 00:42:18,820
现在还要编辑谁

1363
00:42:18,820 --> 00:42:19,820
tanto解死是不是

1364
00:42:19,820 --> 00:42:21,740
怎么做dv怎么dv呢

1365
00:42:21,740 --> 00:42:25,340
就是用调我们调我们的convelation

1366
00:42:25,340 --> 00:42:28,960
调用convelation那个方法

1367
00:42:28,960 --> 00:42:30,340
它的什么叫build

1368
00:42:30,340 --> 00:42:32,000
debandancy

1369
00:42:32,000 --> 00:42:34,820
放吧

1370
00:42:34,820 --> 00:42:35,780
然后把什么

1371
00:42:35,780 --> 00:42:37,720
把这个this就是当线模块

1372
00:42:37,720 --> 00:42:39,400
和什么和咱们这个

1373
00:42:39,400 --> 00:42:41,320
这个是this啊

1374
00:42:41,320 --> 00:42:45,100
把当前模块和什么呀

1375
00:42:45,100 --> 00:42:48,300
和这个依赖像法谁

1376
00:42:48,300 --> 00:42:50,560
是不是第一次的传进去啊

1377
00:42:50,560 --> 00:42:51,560
叫低规编译啊

1378
00:42:51,560 --> 00:42:53,120
就可以了

1379
00:42:53,120 --> 00:42:55,260
那低规编译怎么编译呢

1380
00:42:55,260 --> 00:42:56,100
很简单啊

1381
00:42:56,100 --> 00:42:58,900
我们把它回到咱们这个completion里边来

1382
00:42:58,900 --> 00:43:00,860
这是吧

1383
00:43:00,860 --> 00:43:02,060
咱们再写个方法叫什么

1384
00:43:02,060 --> 00:43:03,060
叫低规编译

1385
00:43:03,060 --> 00:43:06,060
两个参数

1386
00:43:06,060 --> 00:43:07,060
第一个呢是module

1387
00:43:07,060 --> 00:43:10,160
是不是是不是复模块啊

1388
00:43:10,160 --> 00:43:11,000
第二个呢

1389
00:43:11,000 --> 00:43:12,520
是不是他依赖了模画啊

1390
00:43:12,520 --> 00:43:14,080
就dependency的对不对

1391
00:43:14,080 --> 00:43:14,840
放在这

1392
00:43:14,840 --> 00:43:17,400
但是我这尽量把这个模画返回啊

1393
00:43:17,400 --> 00:43:18,300
把this返回

1394
00:43:18,300 --> 00:43:21,220
就是我的模画的build方法

1395
00:43:21,220 --> 00:43:22,280
它会返回什么

1396
00:43:22,280 --> 00:43:23,540
自己对吧

1397
00:43:23,540 --> 00:43:24,800
会返回自己的模画

1398
00:43:24,800 --> 00:43:25,240
对不对

1399
00:43:25,240 --> 00:43:26,100
自己的模画

1400
00:43:26,100 --> 00:43:27,100
好

1401
00:43:27,100 --> 00:43:28,140
咱们来写什么

1402
00:43:28,140 --> 00:43:30,200
咱们写这个build的dependency的这个方法

1403
00:43:30,200 --> 00:43:30,380
是不是

1404
00:43:30,380 --> 00:43:32,240
它是怎么写的呀

1405
00:43:32,240 --> 00:43:33,580
好

1406
00:43:33,580 --> 00:43:34,380
咱们这样写啊

1407
00:43:34,380 --> 00:43:35,060
就是module

1408
00:43:35,060 --> 00:43:35,660
第2什么

1409
00:43:35,660 --> 00:43:37,100
dependency

1410
00:43:37,100 --> 00:43:37,960
你看

1411
00:43:37,960 --> 00:43:39,400
它原来这个属性啊

1412
00:43:39,400 --> 00:43:40,180
你看原来属性

1413
00:43:40,180 --> 00:43:40,960
你看啊

1414
00:43:40,960 --> 00:43:43,720
我这没负网纸

1415
00:43:43,720 --> 00:43:45,460
你给他负网纸吧

1416
00:43:45,460 --> 00:43:48,220
这应该给他负网纸啊

1417
00:43:48,220 --> 00:43:54,020
原来这个模画的什么

1418
00:43:54,020 --> 00:43:55,320
这个DVD属性

1419
00:43:55,320 --> 00:43:58,360
是不是就是一个这样这样的对象啊

1420
00:43:58,360 --> 00:44:00,400
什么对象就这个对象啊

1421
00:44:00,400 --> 00:44:01,620
对吧这是个模画不是

1422
00:44:01,620 --> 00:44:03,320
但是还可以用来干嘛

1423
00:44:03,320 --> 00:44:04,740
创业模画是不是

1424
00:44:04,740 --> 00:44:05,760
对吧

1425
00:44:05,760 --> 00:44:06,540
所以我干嘛

1426
00:44:06,540 --> 00:44:07,140
来吧

1427
00:44:07,140 --> 00:44:08,000
你看啊

1428
00:44:08,000 --> 00:44:08,900
DVD的调什么

1429
00:44:08,900 --> 00:44:10,060
讲Map音色

1430
00:44:10,060 --> 00:44:11,560
拿到每个date

1431
00:44:11,560 --> 00:44:13,660
然后他会返回什么

1432
00:44:13,660 --> 00:44:15,940
返回一个新的模块

1433
00:44:15,940 --> 00:44:16,880
怎么传用新模块啊

1434
00:44:16,880 --> 00:44:17,280
可以用什么

1435
00:44:17,280 --> 00:44:18,260
也可以用我们的

1436
00:44:18,260 --> 00:44:19,880
moremore factory是吧

1437
00:44:19,880 --> 00:44:20,600
比较crete

1438
00:44:20,600 --> 00:44:23,100
是不是叫crete啊

1439
00:44:23,100 --> 00:44:25,060
把date传进去

1440
00:44:25,060 --> 00:44:26,400
返回一个新模块吧

1441
00:44:26,400 --> 00:44:27,000
好

1442
00:44:27,000 --> 00:44:27,680
叫led什么

1443
00:44:27,680 --> 00:44:29,640
led一个child的module

1444
00:44:29,640 --> 00:44:31,020
这个子模块

1445
00:44:31,020 --> 00:44:32,880
然后反问一下子模块

1446
00:44:32,880 --> 00:44:33,320
也可以了是不是

1447
00:44:33,320 --> 00:44:36,240
当然先编译啊

1448
00:44:36,240 --> 00:44:37,940
我们还会调用什么子模块的编译方法

1449
00:44:37,940 --> 00:44:38,300
已经编译

1450
00:44:38,300 --> 00:44:39,800
讲什么build的

1451
00:44:39,800 --> 00:44:41,800
进行编译

1452
00:44:41,800 --> 00:44:44,240
BULD

1453
00:44:44,240 --> 00:44:46,700
编译之后返回吧

1454
00:44:46,700 --> 00:44:47,740
把自己给返回了

1455
00:44:47,740 --> 00:44:48,120
对不对

1456
00:44:48,120 --> 00:44:48,700
RTUK了

1457
00:44:48,700 --> 00:44:51,920
好这样的话

1458
00:44:51,920 --> 00:44:53,160
你看我们就完成了吧

1459
00:44:53,160 --> 00:44:54,340
你看自己编译好了

1460
00:44:54,340 --> 00:44:55,860
他的儿子们也编好了

1461
00:44:55,860 --> 00:44:56,360
是不是

1462
00:44:56,360 --> 00:44:57,320
儿子编译之后呢

1463
00:44:57,320 --> 00:44:58,060
如果他还有儿子

1464
00:44:58,060 --> 00:44:58,640
还可以去编译

1465
00:44:58,640 --> 00:44:58,860
是不是

1466
00:44:58,860 --> 00:45:00,560
可以无限低位下去

1467
00:45:00,560 --> 00:45:00,800
是不是

1468
00:45:00,800 --> 00:45:02,160
低位编译

1469
00:45:02,160 --> 00:45:03,640
那这一步我们编译完成了

1470
00:45:03,640 --> 00:45:03,860
是吧

1471
00:45:03,860 --> 00:45:05,100
编译完成了

1472
00:45:05,100 --> 00:45:07,740
好

1473
00:45:07,740 --> 00:45:09,320
这个大家有没有问题啊

1474
00:45:09,320 --> 00:45:14,260
其实这一步

1475
00:45:14,260 --> 00:45:16,360
我觉得这一步这可写可不行

1476
00:45:16,360 --> 00:45:17,100
为啥

1477
00:45:17,100 --> 00:45:18,620
因为我在这

1478
00:45:18,620 --> 00:45:20,500
我是不是也写了

1479
00:45:20,500 --> 00:45:24,040
看我这写了

1480
00:45:24,040 --> 00:45:27,820
就是他的依赖项编译完成之后

1481
00:45:27,820 --> 00:45:28,760
您看这样写

1482
00:45:28,760 --> 00:45:29,900
等于谁等于他

1483
00:45:29,900 --> 00:45:32,460
就说这个依赖这个date数组

1484
00:45:32,460 --> 00:45:34,920
他map之后是不是一个新的模画了

1485
00:45:34,920 --> 00:45:35,780
新的模画数组

1486
00:45:35,780 --> 00:45:36,800
付给什么

1487
00:45:36,800 --> 00:45:38,820
当付模画的地方date的属性

1488
00:45:38,820 --> 00:45:39,660
对不对

1489
00:45:39,660 --> 00:45:41,060
就可以了

1490
00:45:41,060 --> 00:45:44,580
好 这样就完事了吧

1491
00:45:44,580 --> 00:45:47,000
咱们来看一下效果啊

1492
00:45:47,000 --> 00:45:47,720
你看啊

1493
00:45:47,720 --> 00:45:49,480
你看这个编义完成之后

1494
00:45:49,480 --> 00:45:51,820
我们我们打印一下吧

1495
00:45:51,820 --> 00:45:53,260
打印谁啊

1496
00:45:53,260 --> 00:45:54,920
打印咱们这个

1497
00:45:54,920 --> 00:45:56,740
他吧

1498
00:45:56,740 --> 00:45:59,320
咱们打印我们的entries是吧

1499
00:45:59,320 --> 00:46:03,720
你看

1500
00:46:03,720 --> 00:46:05,900
现在entries里面有一个模块

1501
00:46:05,900 --> 00:46:09,040
那么它的modules有几个模块

1502
00:46:09,040 --> 00:46:09,760
两个吧

1503
00:46:09,760 --> 00:46:12,020
你看这个modules有两个模块

1504
00:46:12,020 --> 00:46:13,320
应该是这样的

1505
00:46:13,320 --> 00:46:15,240
那么它的下关页modules呢

1506
00:46:15,240 --> 00:46:16,720
是不是两个kvalue啊

1507
00:46:16,720 --> 00:46:17,280
这就对了吧

1508
00:46:17,280 --> 00:46:19,340
下关页modules应该有两kvalue

1509
00:46:19,340 --> 00:46:19,660
是不是

1510
00:46:19,660 --> 00:46:21,380
是吧

1511
00:46:21,380 --> 00:46:26,600
入口的速度

1512
00:46:26,600 --> 00:46:27,820
模式速度

1513
00:46:27,820 --> 00:46:29,040
map的kvalue

1514
00:46:29,040 --> 00:46:30,660
好打印一下

1515
00:46:30,660 --> 00:46:35,280
哎呀又装去了

1516
00:46:35,280 --> 00:46:37,580
好

1517
00:46:37,580 --> 00:46:38,020
node

1518
00:46:38,020 --> 00:46:38,840
cdgs

1519
00:46:38,840 --> 00:46:44,560
generate

1520
00:46:44,560 --> 00:46:45,480
看下啊

1521
00:46:45,480 --> 00:46:45,940
generate

1522
00:46:45,940 --> 00:46:50,020
generate

1523
00:46:50,020 --> 00:46:51,460
generate

1524
00:46:51,460 --> 00:46:51,480
generate

1525
00:46:51,480 --> 00:46:57,700
生成的意思

1526
00:46:57,700 --> 00:46:58,620
生成的意思

1527
00:46:58,620 --> 00:47:00,920
其实

1528
00:47:00,920 --> 00:47:04,760
感觉这个改成generate吧

1529
00:47:04,760 --> 00:47:19,420
哎呀他说completion没有拿到是不是啊看什么也没什么没有传呀completion

1530
00:47:19,420 --> 00:47:20,800
果真

1531
00:47:20,800 --> 00:47:21,760
你看看这build

1532
00:47:21,760 --> 00:47:23,580
这我没有传呀

1533
00:47:23,580 --> 00:47:24,820
你看这不是这没有传这个

1534
00:47:24,820 --> 00:47:26,940
这应该传什么传类似啊

1535
00:47:26,940 --> 00:47:28,180
对吧传自己传进来

1536
00:47:28,180 --> 00:47:30,700
没有传啊

1537
00:47:30,700 --> 00:47:36,320
他说找不到title

1538
00:47:36,320 --> 00:47:37,320
这什么原因啊

1539
00:47:37,320 --> 00:47:38,780
是不是肯定是没有加货罪啊

1540
00:47:38,780 --> 00:47:39,180
对吧

1541
00:47:39,180 --> 00:47:39,740
没有加货罪

1542
00:47:39,740 --> 00:47:40,500
其货罪加上吧

1543
00:47:40,500 --> 00:47:41,160
为啥没加

1544
00:47:41,160 --> 00:47:43,900
看这个地方是没有加货罪啊

1545
00:47:43,900 --> 00:47:44,980
YAT内门没拿到吧

1546
00:47:44,980 --> 00:47:46,620
YAT在这是吧

1547
00:47:46,620 --> 00:47:48,900
他

1548
00:47:48,900 --> 00:47:55,780
它进行分割是吧 分割之后呢 拿到最后一个 应该如果等于-1的话

1549
00:47:55,780 --> 00:48:01,820
等于-1的话就是没有 没有的话就给它加上gs 否则又是空 对吧

1550
00:48:01,820 --> 00:48:04,740
看这应该有问题啊 你看它这应该是点

1551
00:48:04,740 --> 00:48:09,260
咱们打一下这个module 咱们打一下这个值啊

1552
00:48:09,260 --> 00:48:15,100
打一下module内部 打一下这也挺内部是吧

1553
00:48:18,060 --> 00:48:20,300
打一下这个SEP

1554
00:48:20,300 --> 00:48:32,060
你看这个.tattle

1555
00:48:32,060 --> 00:48:34,900
.tattle又拿到了 是吧

1556
00:48:34,900 --> 00:48:37,260
你看.tattle是谁是不是这个model name呀

1557
00:48:37,260 --> 00:48:39,160
这个

1558
00:48:39,160 --> 00:48:41,900
edit name是空的

1559
00:48:41,900 --> 00:48:44,260
这个是没有啊

1560
00:48:44,260 --> 00:48:45,800
pass.edit

1561
00:48:48,060 --> 00:48:52,160
这是indifind 是吧 你看 这属于indifind

1562
00:48:52,160 --> 00:49:07,720
Lad path 等于 require path

1563
00:49:13,520 --> 00:49:16,340
你看它这个separate

1564
00:49:16,340 --> 00:49:18,640
sep

1565
00:49:18,640 --> 00:49:21,960
对吧sep 可不是

1566
00:49:21,960 --> 00:49:24,020
写反了啊

1567
00:49:24,020 --> 00:49:25,560
sequence

1568
00:49:25,560 --> 00:49:28,360
应该写separate

1569
00:49:28,360 --> 00:49:31,180
看还有没有别的

1570
00:49:31,180 --> 00:49:32,200
sep

1571
00:49:32,200 --> 00:49:34,000
seq

1572
00:49:34,000 --> 00:49:35,540
没了是吧

1573
00:49:35,540 --> 00:49:38,600
separate

1574
00:49:38,600 --> 00:49:40,140
sep

1575
00:49:40,140 --> 00:49:42,440
你看这么拿到了

1576
00:49:42,440 --> 00:49:43,220
你看拿到了

1577
00:49:43,220 --> 00:49:44,760
你看看我们结果

1578
00:49:44,760 --> 00:49:45,940
结果你看啊

1579
00:49:45,940 --> 00:49:48,140
这个地方是不是接的会给拿到了

1580
00:49:48,140 --> 00:49:49,120
这都会拿到了

1581
00:49:49,120 --> 00:49:50,580
然后你看这变成功了吧

1582
00:49:50,580 --> 00:49:52,400
然后一打印成功了吧

1583
00:49:52,400 --> 00:49:53,340
好

1584
00:49:53,340 --> 00:49:53,800
然后呢

1585
00:49:53,800 --> 00:49:55,020
你看这是那个什么

1586
00:49:55,020 --> 00:49:55,700
猫有你看

1587
00:49:55,700 --> 00:49:57,740
弄猫猫有有内幕呀

1588
00:49:57,740 --> 00:49:58,780
扛待在去快岁呀

1589
00:49:58,780 --> 00:49:59,220
依赖呀

1590
00:49:59,220 --> 00:49:59,740
语法术啊

1591
00:49:59,740 --> 00:50:00,300
什么都有了

1592
00:50:00,300 --> 00:50:01,180
别那么也有了

1593
00:50:01,180 --> 00:50:01,460
是不是

1594
00:50:01,460 --> 00:50:03,040
好

1595
00:50:03,040 --> 00:50:04,240
那么到了这儿啊

1596
00:50:04,240 --> 00:50:06,180
基本上咱们的模块编译就完成了

1597
00:50:06,180 --> 00:50:06,780
对吧

1598
00:50:06,780 --> 00:50:08,480
编译完成了啊

1599
00:50:08,480 --> 00:50:10,300
那边边在干嘛了呢

1600
00:50:10,300 --> 00:50:11,060
看原码

1601
00:50:11,060 --> 00:50:11,580
对不对

1602
00:50:11,580 --> 00:50:12,480
看原码啊

1603
00:50:12,480 --> 00:50:13,920
看他原码编译完之后要干嘛了

1604
00:50:13,920 --> 00:50:15,380
看啊走

1605
00:50:15,380 --> 00:50:17,260
这编译啊

1606
00:50:17,260 --> 00:50:18,760
这个编译啊

1607
00:50:18,760 --> 00:50:22,160
这个因为这个编译过程会很漫长啊

1608
00:50:22,160 --> 00:50:24,380
我就我也不想一一再等待了

1609
00:50:24,380 --> 00:50:26,480
我直接以为他走完了就得了啊

1610
00:50:26,480 --> 00:50:28,560
走完之后呢

1611
00:50:28,560 --> 00:50:30,300
走完之后还能啊

1612
00:50:30,300 --> 00:50:30,980
我重新来一下

1613
00:50:30,980 --> 00:50:32,580
大家重新来一下啊

1614
00:50:32,580 --> 00:50:35,160
看编译完之后干嘛了呢

1615
00:50:35,160 --> 00:50:35,640
我们看一下

1616
00:50:35,640 --> 00:50:40,780
看走

1617
00:50:40,780 --> 00:50:41,720
走

1618
00:50:41,720 --> 00:50:42,360
run吧

1619
00:50:42,360 --> 00:50:44,440
编译

1620
00:50:44,440 --> 00:50:45,460
然后走回掉

1621
00:50:45,460 --> 00:50:47,640
重一来

1622
00:50:47,640 --> 00:50:48,120
重一来一下

1623
00:50:48,120 --> 00:50:51,860
这个编译之后会走谁呢

1624
00:50:51,860 --> 00:50:52,980
会走我们的seal

1625
00:50:52,980 --> 00:50:53,960
seal什么意思

1626
00:50:53,960 --> 00:50:54,700
叫封装

1627
00:50:54,700 --> 00:50:56,020
你看啊

1628
00:50:56,020 --> 00:50:57,380
它这个编译后啊

1629
00:50:57,380 --> 00:50:57,900
会走到

1630
00:50:57,900 --> 00:50:58,580
你看会走到哪

1631
00:50:58,580 --> 00:50:59,680
会走到这个

1632
00:50:59,680 --> 00:51:03,460
是不是这个compile啊

1633
00:51:03,460 --> 00:51:05,300
会compile完成之后

1634
00:51:05,300 --> 00:51:06,200
你看啊

1635
00:51:06,200 --> 00:51:06,920
这个是

1636
00:51:06,920 --> 00:51:10,700
你看啊

1637
00:51:10,700 --> 00:51:11,700
他会

1638
00:51:11,700 --> 00:51:13,960
你看到完成了

1639
00:51:13,960 --> 00:51:15,900
这里就是完成了啊

1640
00:51:15,900 --> 00:51:18,080
然后这是什么呀

1641
00:51:18,080 --> 00:51:20,240
这是弹出资源是吧

1642
00:51:20,240 --> 00:51:22,860
然后uncompiled

1643
00:51:22,860 --> 00:51:26,300
开始走

1644
00:51:26,300 --> 00:51:27,700
编译

1645
00:51:27,700 --> 00:51:28,840
编译走

1646
00:51:28,840 --> 00:51:31,040
编译之王之后你会走呢

1647
00:51:31,040 --> 00:51:32,600
你看他会走呢

1648
00:51:32,600 --> 00:51:33,400
是不是走finish

1649
00:51:33,400 --> 00:51:35,220
你看这make完成了吧

1650
00:51:35,220 --> 00:51:36,780
你看啊make完成了

1651
00:51:36,780 --> 00:51:38,140
make完成之后会走呢

1652
00:51:38,140 --> 00:51:39,660
你看

1653
00:51:39,660 --> 00:51:40,800
走

1654
00:51:40,800 --> 00:51:41,960
走

1655
00:51:41,960 --> 00:51:43,820
看会走什么

1656
00:51:43,820 --> 00:51:44,780
走非得起完成

1657
00:51:44,780 --> 00:51:46,300
完成之后会走什么

1658
00:51:46,300 --> 00:51:47,240
走这个seal

1659
00:51:47,240 --> 00:51:48,120
seal什么呀

1660
00:51:48,120 --> 00:51:49,620
就是一个封包

1661
00:51:49,620 --> 00:51:51,300
就是seal该干嘛

1662
00:51:51,300 --> 00:51:52,960
通过模块生成代码块

1663
00:51:52,960 --> 00:51:54,600
然后呢

1664
00:51:54,600 --> 00:51:55,540
再会走什么cobank

1665
00:51:55,540 --> 00:51:56,320
cobank呢会什么

1666
00:51:56,320 --> 00:51:57,300
会给代码块生什么

1667
00:51:57,300 --> 00:51:59,480
生成我们的assess资源

1668
00:51:59,480 --> 00:52:01,000
然后会给你资源怎么样

1669
00:52:01,000 --> 00:52:01,800
生成我们的文件

1670
00:52:01,800 --> 00:52:03,980
因为我也不在一看了

1671
00:52:03,980 --> 00:52:04,700
大家也得看吧

1672
00:52:04,700 --> 00:52:05,320
我也得写了

1673
00:52:05,320 --> 00:52:06,760
好

1674
00:52:06,760 --> 00:52:07,620
那下面干嘛

1675
00:52:07,620 --> 00:52:08,660
是不是该走我们的seal

1676
00:52:08,660 --> 00:52:09,120
什么了

1677
00:52:09,120 --> 00:52:09,620
是不是

1678
00:52:09,620 --> 00:52:11,620
不是啊好走 sale sale在哪写的

1679
00:52:11,620 --> 00:52:13,620
你看他在哪啊

1680
00:52:13,620 --> 00:52:15,620
是不是在这个compiler里面啊对不对

1681
00:52:15,620 --> 00:52:19,620
那make回调里边是不是走这个finish和 sale啊对不对

1682
00:52:19,620 --> 00:52:22,620
好下面呢我们找我们找什么找mail的回调

1683
00:52:22,620 --> 00:52:23,620
找mail的回调

1684
00:52:23,620 --> 00:52:25,620
好来吧

1685
00:52:25,620 --> 00:52:27,620
你看找这个mail的回调

1686
00:52:27,620 --> 00:52:33,620
好这个找到我们要compiler是吧

1687
00:52:33,620 --> 00:52:35,620
这吧 make make make

1688
00:52:35,620 --> 00:52:37,620
是不是在这啊

1689
00:52:37,620 --> 00:52:38,620
回到这了吧

1690
00:52:38,620 --> 00:52:40,640
是不是回到make回调里边来了

1691
00:52:40,640 --> 00:52:41,540
对吧

1692
00:52:41,540 --> 00:52:42,960
然后接着往下写啊

1693
00:52:42,960 --> 00:52:43,340
写什么

1694
00:52:43,340 --> 00:52:44,200
写一个叫

1695
00:52:44,200 --> 00:52:46,240
compiler

1696
00:52:46,240 --> 00:52:48,080
completion

1697
00:52:48,080 --> 00:52:51,120
调什么

1698
00:52:51,120 --> 00:52:51,640
调sive

1699
00:52:51,640 --> 00:52:53,400
然后也传回调

1700
00:52:53,400 --> 00:52:54,620
error是吧

1701
00:52:54,620 --> 00:52:56,060
然后呢

1702
00:52:56,060 --> 00:52:56,820
触发什么

1703
00:52:56,820 --> 00:52:58,540
v.hook

1704
00:52:58,540 --> 00:52:59,500
次调什么

1705
00:52:59,500 --> 00:53:00,780
调after

1706
00:53:00,780 --> 00:53:01,380
compile

1707
00:53:01,380 --> 00:53:03,100
调什么

1708
00:53:03,100 --> 00:53:04,060
调call

1709
00:53:04,060 --> 00:53:04,500
think

1710
00:53:04,500 --> 00:53:06,920
要传回调吧

1711
00:53:06,920 --> 00:53:07,820
completion

1712
00:53:07,820 --> 00:53:09,880
和回调

1713
00:53:09,880 --> 00:53:13,340
然后呢

1714
00:53:13,340 --> 00:53:15,780
回调我们的

1715
00:53:15,780 --> 00:53:16,520
是不是调整个

1716
00:53:16,520 --> 00:53:19,140
on compare就完事了

1717
00:53:19,140 --> 00:53:21,380
on compare里边呢

1718
00:53:21,380 --> 00:53:23,080
会写住文件是不是会写住文件

1719
00:53:23,080 --> 00:53:25,500
你看啊

1720
00:53:25,500 --> 00:53:27,080
这是一个after compare

1721
00:53:27,080 --> 00:53:28,740
是不是一个变缘成啊

1722
00:53:28,740 --> 00:53:30,500
on compare是写文件了

1723
00:53:30,500 --> 00:53:32,300
这会写住文件系统啊

1724
00:53:32,300 --> 00:53:33,760
写住文件系统

1725
00:53:33,760 --> 00:53:37,400
对吧写住文件系统

1726
00:53:37,400 --> 00:53:39,420
那么你看这个seal怎么干

1727
00:53:39,420 --> 00:53:40,020
怎么办呢

1728
00:53:40,020 --> 00:53:41,060
是不是该写seal方法了

1729
00:53:41,060 --> 00:53:42,940
找到我们的completion吧

1730
00:53:42,940 --> 00:53:43,540
写seal方法

1731
00:53:43,540 --> 00:53:44,280
封包是不是

1732
00:53:44,280 --> 00:53:45,560
什么叫封包呀

1733
00:53:45,560 --> 00:53:48,460
就是通过模块依赖

1734
00:53:48,460 --> 00:53:51,680
生生什么代码块

1735
00:53:51,680 --> 00:53:52,660
对吧

1736
00:53:52,660 --> 00:53:54,000
然后completion里边呢

1737
00:53:54,000 --> 00:53:55,380
会什么会写入文件系统

1738
00:53:55,380 --> 00:53:55,920
好

1739
00:53:55,920 --> 00:53:56,820
那么写seal方法

1740
00:53:56,820 --> 00:53:58,280
回到completion是吧

1741
00:53:58,280 --> 00:53:58,840
写seal

1742
00:53:58,840 --> 00:54:03,260
seal方法对吧

1743
00:54:03,260 --> 00:54:03,920
seal方法

1744
00:54:03,920 --> 00:54:06,360
那么seal里边要干嘛呢

1745
00:54:06,360 --> 00:54:09,160
我们是不是要有tronk的概念啊

1746
00:54:09,160 --> 00:54:09,480
对不对

1747
00:54:09,480 --> 00:54:10,040
好

1748
00:54:10,040 --> 00:54:11,720
所以咱们先写一个类啊

1749
00:54:11,720 --> 00:54:12,640
叫tronk

1750
00:54:12,640 --> 00:54:14,320
叫tronk

1751
00:54:14,320 --> 00:54:15,940
代码块

1752
00:54:15,940 --> 00:54:17,900
都有require什么呀

1753
00:54:17,900 --> 00:54:19,440
我们叫.tronk

1754
00:54:19,440 --> 00:54:20,520
对不对

1755
00:54:20,520 --> 00:54:22,420
然后在里边呢

1756
00:54:22,420 --> 00:54:23,960
我们去写一些勾的吧

1757
00:54:23,960 --> 00:54:25,220
写什么勾的呢

1758
00:54:25,220 --> 00:54:26,500
我们加几个勾的啊

1759
00:54:26,500 --> 00:54:28,600
原来是不是只有一个add entry啊

1760
00:54:28,600 --> 00:54:29,400
我加几个勾的

1761
00:54:29,400 --> 00:54:30,340
加一个吗

1762
00:54:30,340 --> 00:54:30,860
加一个sale

1763
00:54:30,860 --> 00:54:32,440
sale呢

1764
00:54:32,440 --> 00:54:33,780
是一个new一个sinkhook

1765
00:54:33,780 --> 00:54:34,940
啊

1766
00:54:34,940 --> 00:54:37,400
这边是个空速度是吧

1767
00:54:37,400 --> 00:54:37,920
没有参数

1768
00:54:37,920 --> 00:54:39,260
还有什么

1769
00:54:39,260 --> 00:54:40,840
还有这个beforetrunks

1770
00:54:40,840 --> 00:54:42,980
是不是适用代码块之前啊

1771
00:54:42,980 --> 00:54:45,300
它也是个newhooks

1772
00:54:45,300 --> 00:54:49,180
还有什么aftertrunks

1773
00:54:49,180 --> 00:54:50,880
适用代码块之后

1774
00:54:50,880 --> 00:54:51,240
对吧

1775
00:54:51,240 --> 00:54:52,200
newsinkhook

1776
00:54:52,200 --> 00:54:54,420
也是个空速度是吧

1777
00:54:54,420 --> 00:54:55,320
就可以了

1778
00:54:55,320 --> 00:54:57,840
好

1779
00:54:57,840 --> 00:54:59,340
加了三个勾的啊

1780
00:54:59,340 --> 00:54:59,660
勾的

1781
00:54:59,660 --> 00:55:00,820
然后那另外呢

1782
00:55:00,820 --> 00:55:01,560
我这会加什么

1783
00:55:01,560 --> 00:55:02,500
加一个新的一个属性

1784
00:55:02,500 --> 00:55:04,080
叫什么z点什么trunks

1785
00:55:04,080 --> 00:55:05,940
创里边放的啊

1786
00:55:05,940 --> 00:55:06,620
放了个数组

1787
00:55:06,620 --> 00:55:07,440
里边放啥

1788
00:55:07,440 --> 00:55:08,820
是不是应该放咱们这个

1789
00:55:08,820 --> 00:55:10,860
代码块啊

1790
00:55:10,860 --> 00:55:12,320
你看是不是创的代码块啊

1791
00:55:12,320 --> 00:55:13,780
编出来代码块啊

1792
00:55:13,780 --> 00:55:14,280
是不是啊

1793
00:55:14,280 --> 00:55:15,020
好放代码块

1794
00:55:15,020 --> 00:55:15,840
是不是创的啊

1795
00:55:15,840 --> 00:55:16,780
对不对好

1796
00:55:16,780 --> 00:55:17,940
来吧

1797
00:55:17,940 --> 00:55:18,680
那开始什么

1798
00:55:18,680 --> 00:55:19,580
开始写了啊

1799
00:55:19,580 --> 00:55:20,280
开始seal了

1800
00:55:20,280 --> 00:55:21,280
开始封装了是不是

1801
00:55:21,280 --> 00:55:22,380
封装了在哪

1802
00:55:22,380 --> 00:55:24,120
这都不要了啊

1803
00:55:24,120 --> 00:55:24,580
不要了

1804
00:55:24,580 --> 00:55:25,680
不打印了

1805
00:55:25,680 --> 00:55:28,380
在这吧

1806
00:55:28,380 --> 00:55:28,880
在这

1807
00:55:28,880 --> 00:55:31,580
这传一个call bag可以换过来啊

1808
00:55:31,580 --> 00:55:32,880
然后怎么样

1809
00:55:32,880 --> 00:55:33,520
我先怎么样

1810
00:55:33,520 --> 00:55:35,920
先让 huk sale huk 什么样

1811
00:55:35,920 --> 00:55:37,480
靠一下这行吧

1812
00:55:37,480 --> 00:55:39,160
要出发一下是不是

1813
00:55:39,160 --> 00:55:39,580
然后呢

1814
00:55:39,580 --> 00:55:40,420
我会教什么

1815
00:55:40,420 --> 00:55:41,620
电污这叫 huk 字叫什么

1816
00:55:41,620 --> 00:55:43,220
before

1817
00:55:43,220 --> 00:55:43,760
就这个呗

1818
00:55:43,760 --> 00:55:45,020
before trunk 呗

1819
00:55:45,020 --> 00:55:45,420
对吧

1820
00:55:45,420 --> 00:55:46,220
before trunk 字

1821
00:55:46,220 --> 00:55:47,760
点什么点靠

1822
00:55:47,760 --> 00:55:48,860
都是同步的嘛

1823
00:55:48,860 --> 00:55:50,060
很简单啊

1824
00:55:50,060 --> 00:55:51,680
还有什么 after

1825
00:55:51,680 --> 00:55:52,280
after肯定是

1826
00:55:52,280 --> 00:55:53,420
创可之后吧

1827
00:55:53,420 --> 00:55:55,220
after在这

1828
00:55:55,220 --> 00:55:56,180
这个after啊

1829
00:55:56,180 --> 00:55:58,180
在这之后啊

1830
00:55:58,180 --> 00:55:59,220
然后中间呢

1831
00:55:59,220 --> 00:56:00,160
是我们自己的逻辑吧

1832
00:56:00,160 --> 00:56:01,520
什么代表逻辑啊

1833
00:56:01,520 --> 00:56:02,260
那怎么生成呢

1834
00:56:02,260 --> 00:56:03,780
你看我们要怎么样

1835
00:56:03,780 --> 00:56:05,100
循环我这个怎么样

1836
00:56:05,100 --> 00:56:07,060
好

1837
00:56:07,060 --> 00:56:08,500
LightModule

1838
00:56:08,500 --> 00:56:10,300
我叫entry吧

1839
00:56:10,300 --> 00:56:11,420
entry

1840
00:56:11,420 --> 00:56:13,020
off怎么样

1841
00:56:13,020 --> 00:56:14,700
z.entries

1842
00:56:14,700 --> 00:56:16,940
是不是要循环我的所有入口啊

1843
00:56:16,940 --> 00:56:17,940
我说了

1844
00:56:17,940 --> 00:56:19,820
每个入口都会生一个代码块

1845
00:56:19,820 --> 00:56:20,500
是不是啊

1846
00:56:20,500 --> 00:56:21,340
所以你看

1847
00:56:21,340 --> 00:56:22,820
这边有一个entry啊

1848
00:56:22,820 --> 00:56:24,140
入口的一个速度啊

1849
00:56:24,140 --> 00:56:24,540
好

1850
00:56:24,540 --> 00:56:25,380
我要循环什么呀

1851
00:56:25,380 --> 00:56:26,060
这个入口

1852
00:56:26,060 --> 00:56:26,420
对吧

1853
00:56:26,420 --> 00:56:27,820
拿到每个里边的每个入口模块

1854
00:56:27,820 --> 00:56:28,940
叫entryModel吧

1855
00:56:28,940 --> 00:56:31,420
这种按推模

1856
00:56:31,420 --> 00:56:31,700
你看

1857
00:56:31,700 --> 00:56:32,740
按水模特你看

1858
00:56:32,740 --> 00:56:36,640
他你看这个它的入口是不是有个module啊

1859
00:56:36,640 --> 00:56:38,200
入口模块啊好

1860
00:56:38,200 --> 00:56:38,800
然后我走

1861
00:56:38,800 --> 00:56:40,560
然后我怎么样

1862
00:56:40,560 --> 00:56:41,440
我生一个代码块

1863
00:56:41,440 --> 00:56:42,700
tronk等于什么

1864
00:56:42,700 --> 00:56:43,760
6一个tronk

1865
00:56:43,760 --> 00:56:45,700
把module传进去就可以了

1866
00:56:45,700 --> 00:56:47,700
把这个入口模式传进去

1867
00:56:47,700 --> 00:56:49,800
传进去之后呢

1868
00:56:49,800 --> 00:56:52,340
我可以这样放到tronk的tronk里边是吧

1869
00:56:52,340 --> 00:56:54,000
点什么pushtronk

1870
00:56:54,000 --> 00:56:56,040
后边我要根据tronk怎么样

1871
00:56:56,040 --> 00:56:57,060
是生存文件吗

1872
00:56:57,060 --> 00:56:57,400
对不对

1873
00:56:57,400 --> 00:56:59,100
所以要把它捆捆起来吧

1874
00:56:59,100 --> 00:57:00,200
捆起来啊

1875
00:57:01,140 --> 00:57:01,540
然后呢

1876
00:57:01,540 --> 00:57:04,380
那么我还要怎么样

1877
00:57:04,380 --> 00:57:07,540
把这个代码块和他对应的模块关联起来

1878
00:57:07,540 --> 00:57:10,140
我要知道到底哪个代码块要什么呀

1879
00:57:10,140 --> 00:57:10,900
包括哪个模块啊

1880
00:57:10,900 --> 00:57:12,300
对吧

1881
00:57:12,300 --> 00:57:13,340
怎么关联呢

1882
00:57:13,340 --> 00:57:13,980
这样写啊

1883
00:57:13,980 --> 00:57:15,740
就是trunks点什么

1884
00:57:15,740 --> 00:57:17,900
modules等于什么呀

1885
00:57:17,900 --> 00:57:20,140
等于一个z点modules

1886
00:57:20,140 --> 00:57:25,600
modules点什么点filter

1887
00:57:25,600 --> 00:57:27,780
过滤一下拿到每个module

1888
00:57:27,780 --> 00:57:29,700
然后怎么过滤呢

1889
00:57:29,700 --> 00:57:31,420
就是module的name

1890
00:57:31,420 --> 00:57:31,960
对吧

1891
00:57:31,960 --> 00:57:32,700
它的名字

1892
00:57:32,700 --> 00:57:33,500
等于什么

1893
00:57:33,500 --> 00:57:34,620
chunk的name

1894
00:57:34,620 --> 00:57:35,980
是不是也可以了

1895
00:57:35,980 --> 00:57:38,260
chunk的name也可以了

1896
00:57:38,260 --> 00:57:41,820
就是说这个每个代码块啊

1897
00:57:41,820 --> 00:57:43,160
都会有一个名字啊

1898
00:57:43,160 --> 00:57:44,620
都会有一个名字啊

1899
00:57:44,620 --> 00:57:46,820
然后这个只要说的模块的名字

1900
00:57:46,820 --> 00:57:47,820
跟代码块的名字一样的

1901
00:57:47,820 --> 00:57:48,180
说明什么

1902
00:57:48,180 --> 00:57:49,620
这个模块属于这个代码块

1903
00:57:49,620 --> 00:57:50,180
对吧

1904
00:57:50,180 --> 00:57:51,400
那就可以了

1905
00:57:51,400 --> 00:57:52,520
就说只要什么

1906
00:57:52,520 --> 00:57:55,100
只要说模块的名字

1907
00:57:55,100 --> 00:57:59,680
和代码块的名字一样

1908
00:57:59,680 --> 00:58:01,240
就说明什么

1909
00:58:01,240 --> 00:58:04,820
就说明这个模块属于这个代码块

1910
00:58:04,820 --> 00:58:06,600
这为啥呢

1911
00:58:06,600 --> 00:58:08,140
因为这个name是一路上

1912
00:58:08,140 --> 00:58:10,220
就是入口的时候一路传下来的

1913
00:58:10,220 --> 00:58:12,300
传给所有它的模块的

1914
00:58:12,300 --> 00:58:12,480
是不是

1915
00:58:12,480 --> 00:58:15,560
所以它一幕相中传下来的

1916
00:58:15,560 --> 00:58:15,720
是不是

1917
00:58:15,720 --> 00:58:16,640
对吧

1918
00:58:16,640 --> 00:58:18,140
好

1919
00:58:18,140 --> 00:58:18,660
然后呢

1920
00:58:18,660 --> 00:58:20,120
我们就调Covac完事了

1921
00:58:20,120 --> 00:58:20,920
Covac完事

1922
00:58:20,920 --> 00:58:23,760
这叫Covac

1923
00:58:23,760 --> 00:58:27,800
那么什么叫Trunk呢

1924
00:58:27,800 --> 00:58:28,800
我们是不是也得个类啊

1925
00:58:28,800 --> 00:58:29,660
我们见了个类啊

1926
00:58:29,660 --> 00:58:31,660
我们这儿建下这个类

1927
00:58:31,660 --> 00:58:34,620
叫它

1928
00:58:34,620 --> 00:58:35,860
trunk点阶子是吧

1929
00:58:35,860 --> 00:58:36,840
我们建下这个类

1930
00:58:36,840 --> 00:58:39,320
那么trunk也是个类

1931
00:58:39,320 --> 00:58:41,020
它里面有很多的属性

1932
00:58:41,020 --> 00:58:41,880
有很多属性

1933
00:58:41,880 --> 00:58:46,960
你看它是个类的话

1934
00:58:46,960 --> 00:58:48,080
我们要把它拿过来

1935
00:58:48,080 --> 00:58:49,120
这是trunk是吧

1936
00:58:49,120 --> 00:58:50,360
好

1937
00:58:50,360 --> 00:58:51,520
class什么样

1938
00:58:51,520 --> 00:58:52,040
trunk

1939
00:58:52,040 --> 00:58:54,260
也要导出trunk吧

1940
00:58:54,260 --> 00:58:55,000
module

1941
00:58:55,000 --> 00:58:57,880
adpost等于一个什么

1942
00:58:57,880 --> 00:58:58,560
trunk是不是

1943
00:58:58,560 --> 00:58:59,500
代码块

1944
00:58:59,500 --> 00:59:01,100
那么创开啊

1945
00:59:01,100 --> 00:59:02,580
他你看他有哪些

1946
00:59:02,580 --> 00:59:03,860
他不是传过来一个模块啊

1947
00:59:03,860 --> 00:59:05,260
是不是可以接收一下吧

1948
00:59:05,260 --> 00:59:06,040
那么接收

1949
00:59:06,040 --> 00:59:06,720
Constructor

1950
00:59:06,720 --> 00:59:12,560
Constructor

1951
00:59:12,560 --> 00:59:16,200
然后呢传过来一个Module

1952
00:59:16,200 --> 00:59:18,960
你什么就是VDRModule

1953
00:59:18,960 --> 00:59:22,560
等于什么等于一个这个Module

1954
00:59:22,560 --> 00:59:24,600
VDRName

1955
00:59:24,600 --> 00:59:26,640
等于什么等于一个Module的Name

1956
00:59:26,640 --> 00:59:28,660
就是说这个

1957
00:59:28,660 --> 00:59:30,440
代码块的名字啊

1958
00:59:30,440 --> 00:59:32,320
跟这个他的入口模块的名字里一样的

1959
00:59:32,320 --> 00:59:33,180
是不是啊

1960
00:59:33,180 --> 00:59:34,100
是一样的啊

1961
00:59:34,100 --> 00:59:35,820
好

1962
00:59:35,820 --> 00:59:36,640
这代码块

1963
00:59:36,640 --> 00:59:38,760
代码块里可能还有什么啊

1964
00:59:38,760 --> 00:59:39,620
比如说可能会有什么

1965
00:59:39,620 --> 00:59:40,280
会有文件

1966
00:59:40,280 --> 00:59:40,740
对吧

1967
00:59:40,740 --> 00:59:41,620
files是吧

1968
00:59:41,620 --> 00:59:43,060
就他有会有

1969
00:59:43,060 --> 00:59:45,100
他因为代码块可能会生成很多文件

1970
00:59:45,100 --> 00:59:45,340
是不是

1971
00:59:45,340 --> 00:59:45,920
对吧

1972
00:59:45,920 --> 00:59:47,240
会有很多文件

1973
00:59:47,240 --> 00:59:48,040
对吧

1974
00:59:48,040 --> 00:59:48,500
files

1975
00:59:48,500 --> 00:59:53,260
好

1976
00:59:53,260 --> 00:59:54,480
这样的话我们就搞定了啊

1977
00:59:54,480 --> 00:59:55,320
搞定了

1978
00:59:55,320 --> 00:59:56,300
下巴干嘛了

1979
00:59:56,300 --> 00:59:58,640
是不是该去生生文件

1980
00:59:58,640 --> 01:00:01,640
是不是叫发射生命生文件了

1981
01:00:01,640 --> 01:00:03,120
怎么生生文件啊

1982
01:00:03,120 --> 01:00:03,680
你看啊

1983
01:00:03,680 --> 01:00:04,680
看不蛋白啊

1984
01:00:04,680 --> 01:00:06,160
你看我这个地方已经怎么样

1985
01:00:06,160 --> 01:00:07,200
叫callback了

1986
01:00:07,200 --> 01:00:07,740
callback是谁

1987
01:00:07,740 --> 01:00:09,840
callback是不是这里边的

1988
01:00:09,840 --> 01:00:13,240
callback是谁呀

1989
01:00:13,240 --> 01:00:13,700
seal

1990
01:00:13,700 --> 01:00:15,740
你看seal

1991
01:00:15,740 --> 01:00:17,500
然后呢这有个什么

1992
01:00:17,500 --> 01:00:20,440
你看他掉了callback

1993
01:00:20,440 --> 01:00:21,900
是不是让他先回掉了

1994
01:00:21,900 --> 01:00:22,560
就这记住这个

1995
01:00:22,560 --> 01:00:23,920
现在这个after

1996
01:00:23,920 --> 01:00:25,680
他会到这来了

1997
01:00:25,680 --> 01:00:26,140
看到了吧

1998
01:00:26,140 --> 01:00:28,480
你看seal完成之后

1999
01:00:28,480 --> 01:00:30,060
调回调 回调谁就是他呀

2000
01:00:30,060 --> 01:00:31,020
你看这个就是他

2001
01:00:31,020 --> 01:00:31,860
对不对

2002
01:00:31,860 --> 01:00:33,160
是不是到这来关系会走什么

2003
01:00:33,160 --> 01:00:33,920
会有出发

2004
01:00:33,920 --> 01:00:35,100
after compile

2005
01:00:35,100 --> 01:00:36,220
完成之后呢会走什么

2006
01:00:36,220 --> 01:00:37,300
un compile

2007
01:00:37,300 --> 01:00:38,260
到哪了

2008
01:00:38,260 --> 01:00:39,560
是不是到了这儿了

2009
01:00:39,560 --> 01:00:41,600
看到了吧到这儿了

2010
01:00:41,600 --> 01:00:42,860
对吧

2011
01:00:42,860 --> 01:00:43,900
因为un compile

2012
01:00:43,900 --> 01:00:45,320
是不是传到了 compile

2013
01:00:45,320 --> 01:00:47,240
抗议他成 compile啊

2014
01:00:47,240 --> 01:00:48,280
然后他的掉头啊

2015
01:00:48,280 --> 01:00:49,860
是不是掉到这儿了

2016
01:00:49,860 --> 01:00:51,000
是边缘成了回调啊

2017
01:00:51,000 --> 01:00:51,740
好

2018
01:00:51,740 --> 01:00:52,600
那该写什么

2019
01:00:52,600 --> 01:00:54,040
写边缘成了回调

2020
01:00:54,040 --> 01:00:54,740
这写之前啊

2021
01:00:54,740 --> 01:00:58,280
咱们先引入几个类啊

2022
01:00:58,280 --> 01:01:02,080
好 我们也会生成这样一个文件啊 state

2023
01:01:02,080 --> 01:01:03,680
归块 我们叫

2024
01:01:03,680 --> 01:01:05,220
点个 state

2025
01:01:05,220 --> 01:01:09,440
好 然后呢 来了一个什么叫

2026
01:01:09,440 --> 01:01:11,480
conant 叫 make什么呀

2027
01:01:11,480 --> 01:01:14,980
make dlp require dlp

2028
01:01:14,980 --> 01:01:17,680
用来干嘛 用来去这个他是用来干嘛 用来去

2029
01:01:17,680 --> 01:01:20,680
呃 去创建一个文件夹的

2030
01:01:20,680 --> 01:01:23,080
make dlp make dlp

2031
01:01:23,080 --> 01:01:25,380
地规创建文件夹的

2032
01:01:25,380 --> 01:01:28,020
他要怕怎么会是吧

2033
01:01:29,240 --> 01:01:31,040
看着他怕他坚持住啊

2034
01:01:31,040 --> 01:01:32,840
还有大概十分钟一完事了是吧

2035
01:01:32,840 --> 01:01:33,780
坚持一下啊

2036
01:01:33,780 --> 01:01:36,680
归到12点刚好到12点那写完啊

2037
01:01:36,680 --> 01:01:37,680
啊

2038
01:01:37,680 --> 01:01:39,020
这块我怕的模块

2039
01:01:39,020 --> 01:01:40,380
好

2040
01:01:40,380 --> 01:01:41,340
然后我们接待完看啊

2041
01:01:41,340 --> 01:01:42,480
你看有点模块啊

2042
01:01:42,480 --> 01:01:43,380
下面怎么写

2043
01:01:43,380 --> 01:01:43,920
是不是写啊

2044
01:01:43,920 --> 01:01:45,140
on compile啊

2045
01:01:45,140 --> 01:01:45,680
对不对

2046
01:01:45,680 --> 01:01:46,680
on compile

2047
01:01:46,680 --> 01:01:47,520
写之前啊

2048
01:01:47,520 --> 01:01:48,640
咱们现在加勾的

2049
01:01:48,640 --> 01:01:50,040
什么勾的呀

2050
01:01:50,040 --> 01:01:50,680
imit

2051
01:01:50,680 --> 01:01:51,580
啊

2052
01:01:51,580 --> 01:01:52,280
他是内有什么

2053
01:01:52,280 --> 01:01:52,840
内有一个

2054
01:01:52,840 --> 01:01:54,380
async theory

2055
01:01:54,380 --> 01:01:56,040
也是个

2056
01:01:56,040 --> 01:01:57,340
一波的什么呀

2057
01:01:57,340 --> 01:01:58,280
串进勾的啊

2058
01:01:58,280 --> 01:01:59,940
里边是放上一个completion

2059
01:01:59,940 --> 01:02:01,440
就可以了是吧

2060
01:02:01,440 --> 01:02:03,360
好

2061
01:02:03,360 --> 01:02:04,720
发射就可以了是吧

2062
01:02:04,720 --> 01:02:05,900
然后回来吧

2063
01:02:05,900 --> 01:02:08,040
回来写什么写我们的on compile

2064
01:02:08,040 --> 01:02:10,000
on compile里边啊

2065
01:02:10,000 --> 01:02:10,480
我们会什么

2066
01:02:10,480 --> 01:02:11,240
调一个方法叫什么

2067
01:02:11,240 --> 01:02:11,960
叫z的点

2068
01:02:11,960 --> 01:02:13,520
叫什么imit

2069
01:02:13,520 --> 01:02:14,840
assess

2070
01:02:14,840 --> 01:02:17,560
发射资源是不是

2071
01:02:17,560 --> 01:02:19,500
传给我们的on compilation

2072
01:02:19,500 --> 01:02:21,760
然后又回调

2073
01:02:21,760 --> 01:02:24,380
回调里边呢

2074
01:02:24,380 --> 01:02:25,340
有个error错误对象

2075
01:02:25,340 --> 01:02:25,760
对吧

2076
01:02:25,760 --> 01:02:28,100
然后里边呢

2077
01:02:28,100 --> 01:02:30,940
我们会创建一个什么state

2078
01:02:30,940 --> 01:02:33,260
等于什么等于一个new state

2079
01:02:33,260 --> 01:02:35,300
然后里边放上我们这个completion

2080
01:02:35,300 --> 01:02:38,460
然后呢

2081
01:02:38,460 --> 01:02:41,000
我们去调用这点hooks的点什么

2082
01:02:41,000 --> 01:02:42,100
点down

2083
01:02:42,100 --> 01:02:43,100
完事了吧

2084
01:02:43,100 --> 01:02:46,300
点call async

2085
01:02:46,300 --> 01:02:49,900
然后呢写个state和什么error

2086
01:02:49,900 --> 01:02:52,660
然后呢我们去认为一个什么final callback

2087
01:02:52,660 --> 01:02:53,600
是不是他吧

2088
01:02:53,600 --> 01:02:54,600
就完事了

2089
01:02:58,100 --> 01:03:02,940
好拿过来电话也可以了

2090
01:03:02,940 --> 01:03:03,700
你看啊

2091
01:03:03,700 --> 01:03:04,340
先怎么样

2092
01:03:04,340 --> 01:03:06,260
先去发射资源

2093
01:03:06,260 --> 01:03:08,060
有写着文件系统是不是

2094
01:03:08,060 --> 01:03:08,660
然后怎么样

2095
01:03:08,660 --> 01:03:10,860
然后去生了一个这样的一个描述对象

2096
01:03:10,860 --> 01:03:11,300
然后呢

2097
01:03:11,300 --> 01:03:13,400
掉掉方法就是完全结束了

2098
01:03:13,400 --> 01:03:14,960
一切就无关了是不是啊

2099
01:03:14,960 --> 01:03:16,140
好关键要写谁

2100
01:03:16,140 --> 01:03:17,100
Imit Assess吧

2101
01:03:17,100 --> 01:03:18,100
好写他

2102
01:03:18,100 --> 01:03:19,100
Imit Assess

2103
01:03:19,100 --> 01:03:23,700
是要写这样一个方法呀

2104
01:03:23,700 --> 01:03:25,060
太容易就能copulation是不是

2105
01:03:25,060 --> 01:03:26,940
copulation啊

2106
01:03:26,940 --> 01:03:27,940
那么怎么发射

2107
01:03:28,100 --> 01:03:31,340
资源的对吧是我们会这样写啊

2108
01:03:31,340 --> 01:03:32,700
我们会这样写那点什么

2109
01:03:32,700 --> 01:03:36,560
后四点一米特点什么点这个靠

2110
01:03:36,560 --> 01:03:37,000
我 think

2111
01:03:37,000 --> 01:03:44,100
然后里面传过一个传过一个什么传入一个参数啊

2112
01:03:44,100 --> 01:03:45,560
参数就是我们的可不类型是吧

2113
01:03:45,560 --> 01:03:46,200
传过来

2114
01:03:46,200 --> 01:03:47,900
什么意思

2115
01:03:47,900 --> 01:03:48,800
因为什么意思

2116
01:03:48,800 --> 01:03:52,300
就是我要相应盘上显微镜了对吧啊

2117
01:03:52,300 --> 01:03:53,340
可不来去注意啊

2118
01:03:53,340 --> 01:03:55,860
为什么说这个这个这个时间呀

2119
01:03:55,860 --> 01:03:57,160
很多时候用的

2120
01:03:57,160 --> 01:04:00,300
因为当你要想像银码商多写一个文件的时候

2121
01:04:00,300 --> 01:04:01,820
你会你会接那个勾的

2122
01:04:01,820 --> 01:04:04,260
在这改 比如加减文件

2123
01:04:04,260 --> 01:04:05,600
因为他马上要写

2124
01:04:05,600 --> 01:04:08,500
你要在之后去写的话就完就就完了是不是

2125
01:04:08,500 --> 01:04:08,960
对吧

2126
01:04:08,960 --> 01:04:11,700
那这些写的话又不到时机啊

2127
01:04:11,700 --> 01:04:13,160
好那先回调吧

2128
01:04:13,160 --> 01:04:15,100
 error

2129
01:04:15,100 --> 01:04:20,900
对吧 error

2130
01:04:20,900 --> 01:04:22,520
然后呢写 make dlp

2131
01:04:22,520 --> 01:04:23,800
然后传入参数吧

2132
01:04:23,800 --> 01:04:24,500
什么参数

2133
01:04:24,900 --> 01:04:29,100
this.option.output.path

2134
01:04:29,100 --> 01:04:30,600
然后呢

2135
01:04:30,600 --> 01:04:31,340
emitefiles

2136
01:04:31,340 --> 01:04:32,560
什么意思

2137
01:04:32,560 --> 01:04:34,500
我先这边创建一个这样的目录

2138
01:04:34,500 --> 01:04:35,460
什么意思

2139
01:04:35,460 --> 01:04:37,260
就是output.path目录

2140
01:04:37,260 --> 01:04:37,980
谁呀

2141
01:04:37,980 --> 01:04:38,920
是不是就这个目录啊

2142
01:04:38,920 --> 01:04:40,080
把这个目录创建出来呀

2143
01:04:40,080 --> 01:04:41,460
因为这个目录可能不存在

2144
01:04:41,460 --> 01:04:42,140
把这些创建出来

2145
01:04:42,140 --> 01:04:43,240
往里边写个念吧

2146
01:04:43,240 --> 01:04:43,440
对

2147
01:04:43,440 --> 01:04:44,640
先创建出来

2148
01:04:44,640 --> 01:04:45,660
然后成什么

2149
01:04:45,660 --> 01:04:46,180
emitefile

2150
01:04:46,180 --> 01:04:47,200
好我们写emitefile

2151
01:04:47,200 --> 01:04:48,240
emitefile是什么

2152
01:04:48,240 --> 01:04:49,200
是一个函数啊

2153
01:04:49,200 --> 01:04:50,300
我们去生命一下它

2154
01:04:54,900 --> 01:04:55,940
对吧

2155
01:04:55,940 --> 01:04:57,640
他要干嘛是不是要去

2156
01:04:57,640 --> 01:04:59,000
他这是比较写文件啊

2157
01:04:59,000 --> 01:04:59,600
怎么写文件

2158
01:04:59,600 --> 01:05:00,760
led assess

2159
01:05:00,760 --> 01:05:03,140
等于什么

2160
01:05:03,140 --> 01:05:03,560
等于一个

2161
01:05:03,560 --> 01:05:04,880
communation assess

2162
01:05:04,880 --> 01:05:07,100
还记得吗

2163
01:05:07,100 --> 01:05:08,240
如果大家写查假的话

2164
01:05:08,240 --> 01:05:09,460
经常会遇到这样一个变量

2165
01:05:09,460 --> 01:05:10,460
assess

2166
01:05:10,460 --> 01:05:11,540
assess啊

2167
01:05:11,540 --> 01:05:11,880
它什么

2168
01:05:11,880 --> 01:05:13,140
它是一个对象

2169
01:05:13,140 --> 01:05:14,700
它是个对象

2170
01:05:14,700 --> 01:05:16,260
assess是一个对象

2171
01:05:16,260 --> 01:05:19,260
对象上面什么

2172
01:05:19,260 --> 01:05:20,060
对象上

2173
01:05:20,060 --> 01:05:22,140
有什么属性和值

2174
01:05:22,140 --> 01:05:24,760
属性呢

2175
01:05:24,760 --> 01:05:25,300
是什么

2176
01:05:25,300 --> 01:05:28,160
是这个文件的名字

2177
01:05:28,160 --> 01:05:29,300
对吧

2178
01:05:29,300 --> 01:05:29,800
名字

2179
01:05:29,800 --> 01:05:31,320
这个值呢

2180
01:05:31,320 --> 01:05:31,900
是什么

2181
01:05:31,900 --> 01:05:32,920
是这个语言码

2182
01:05:32,920 --> 01:05:34,300
应该是什么

2183
01:05:34,300 --> 01:05:35,280
是这个语言码

2184
01:05:35,280 --> 01:05:37,040
就是原代码呗

2185
01:05:37,040 --> 01:05:37,600
对不对

2186
01:05:37,600 --> 01:05:40,280
那么

2187
01:05:40,280 --> 01:05:42,520
那你说这个对象哪来的

2188
01:05:42,520 --> 01:05:43,960
这个对象哪来的

2189
01:05:43,960 --> 01:05:46,280
这个对象应该什么

2190
01:05:46,280 --> 01:05:47,880
应该是我们这个体验创业好的

2191
01:05:47,880 --> 01:05:48,100
是不是

2192
01:05:48,100 --> 01:05:48,840
对吧

2193
01:05:48,840 --> 01:05:50,020
应该是我们在前面

2194
01:05:50,020 --> 01:05:51,980
我们这个在上一环节

2195
01:05:51,980 --> 01:05:53,180
我们要体验把它创业好

2196
01:05:53,180 --> 01:05:53,600
对吧

2197
01:05:53,600 --> 01:05:54,440
创业好

2198
01:05:54,440 --> 01:05:58,220
然后这可以用了是吧

2199
01:05:58,220 --> 01:06:00,120
那么假如我们创业好了

2200
01:06:00,120 --> 01:06:01,400
我们先写这个

2201
01:06:01,400 --> 01:06:02,960
再写其他的创业创业过程吧

2202
01:06:02,960 --> 01:06:04,660
我们先这个先往下写对吧

2203
01:06:04,660 --> 01:06:05,500
好 怎么写呢

2204
01:06:05,500 --> 01:06:06,040
你看这样写

2205
01:06:06,040 --> 01:06:07,480
我们来的来的这吧

2206
01:06:07,480 --> 01:06:07,820
这个

2207
01:06:07,820 --> 01:06:10,060
嗯 复循环吧

2208
01:06:10,060 --> 01:06:11,400
复循环的什么样

2209
01:06:11,400 --> 01:06:12,700
file 音什么样

2210
01:06:12,700 --> 01:06:13,280
assess

2211
01:06:13,280 --> 01:06:15,640
循环这个对象

2212
01:06:15,640 --> 01:06:15,960
是不是

2213
01:06:15,960 --> 01:06:18,100
音是不是循环对象的意思啊

2214
01:06:18,100 --> 01:06:18,440
对不对

2215
01:06:18,440 --> 01:06:20,160
好 然后呢

2216
01:06:20,160 --> 01:06:20,660
我们怎么做呢

2217
01:06:20,660 --> 01:06:21,200
我们来的什么

2218
01:06:21,200 --> 01:06:22,060
那个source

2219
01:06:22,060 --> 01:06:24,340
等于什么

2220
01:06:24,340 --> 01:06:27,600
等于这个assess一个file

2221
01:06:27,600 --> 01:06:29,520
我说了吗

2222
01:06:29,520 --> 01:06:30,740
他是文件名

2223
01:06:30,740 --> 01:06:31,700
他是什么呀

2224
01:06:31,700 --> 01:06:32,440
文件的源码

2225
01:06:32,440 --> 01:06:32,960
对不对

2226
01:06:32,960 --> 01:06:34,560
然后我要写文件系统

2227
01:06:34,560 --> 01:06:35,080
什么写的

2228
01:06:35,080 --> 01:06:37,060
那你看第一个我要指定什么

2229
01:06:37,060 --> 01:06:37,900
文件名吧

2230
01:06:37,900 --> 01:06:38,440
好

2231
01:06:38,440 --> 01:06:39,280
let什么呀

2232
01:06:39,280 --> 01:06:41,040
叫target pass

2233
01:06:41,040 --> 01:06:42,860
就是要写入的文件的名字

2234
01:06:42,860 --> 01:06:43,220
对吧

2235
01:06:43,220 --> 01:06:44,080
一个角度硬

2236
01:06:44,080 --> 01:06:44,360
是不是

2237
01:06:44,360 --> 01:06:45,700
怎么办呢

2238
01:06:45,700 --> 01:06:46,860
pass点pass点什么

2239
01:06:46,860 --> 01:06:47,700
postics

2240
01:06:47,700 --> 01:06:49,360
点什么

2241
01:06:49,360 --> 01:06:50,240
点这个叫

2242
01:06:50,240 --> 01:06:51,940
什么叫postics

2243
01:06:51,940 --> 01:06:53,260
就是说他会让你什么

2244
01:06:53,260 --> 01:06:54,160
让你在文中下边

2245
01:06:54,160 --> 01:06:55,460
和麦克下边它的这个

2246
01:06:55,460 --> 01:06:56,880
分个幅是一样的

2247
01:06:56,880 --> 01:06:57,840
都是LIFT的分个幅

2248
01:06:57,840 --> 01:06:59,300
是这意思啊

2249
01:06:59,300 --> 01:06:59,760
好

2250
01:06:59,760 --> 01:07:01,440
教你是不是连接的意思啊

2251
01:07:01,440 --> 01:07:01,900
连接谁

2252
01:07:01,900 --> 01:07:03,560
连接我们的Z的点什么

2253
01:07:03,560 --> 01:07:05,700
options.output.pass

2254
01:07:05,700 --> 01:07:06,280
这是什么呀

2255
01:07:06,280 --> 01:07:07,000
输中输入吧

2256
01:07:07,000 --> 01:07:08,200
和谁和file

2257
01:07:08,200 --> 01:07:09,160
和file

2258
01:07:09,160 --> 01:07:09,960
没见明白

2259
01:07:09,960 --> 01:07:12,000
好

2260
01:07:12,000 --> 01:07:13,940
那么它的这个south呢

2261
01:07:13,940 --> 01:07:14,780
就是文件内容是吧

2262
01:07:14,780 --> 01:07:14,980
好

2263
01:07:14,980 --> 01:07:15,540
写入吧

2264
01:07:15,540 --> 01:07:16,140
Z的点什么

2265
01:07:16,140 --> 01:07:16,940
output

2266
01:07:16,940 --> 01:07:19,060
output

2267
01:07:19,060 --> 01:07:19,660
什么呀

2268
01:07:19,660 --> 01:07:21,020
这个file system

2269
01:07:21,020 --> 01:07:23,600
this system

2270
01:07:23,600 --> 01:07:25,720
然后呢

2271
01:07:25,720 --> 01:07:26,160
点什么

2272
01:07:26,160 --> 01:07:30,840
连入文件系统

2273
01:07:30,840 --> 01:07:32,380
那么文件名叫啥

2274
01:07:32,380 --> 01:07:33,140
文件路径啊

2275
01:07:33,140 --> 01:07:35,580
内容呢

2276
01:07:35,580 --> 01:07:37,660
是不是就这个sauce的原代码啊

2277
01:07:37,660 --> 01:07:38,080
对不对

2278
01:07:38,080 --> 01:07:40,040
是不是ok了

2279
01:07:40,040 --> 01:07:42,040
完事了啊

2280
01:07:42,040 --> 01:07:42,980
卸完之后呢

2281
01:07:42,980 --> 01:07:44,680
调我们的callback回调就可以了

2282
01:07:44,680 --> 01:07:46,000
完事了

2283
01:07:46,000 --> 01:07:46,560
callback是谁

2284
01:07:46,560 --> 01:07:48,660
callback的话就是我们这个

2285
01:07:48,660 --> 01:07:52,700
你刚刚调我们的callback回调

2286
01:07:52,700 --> 01:07:54,160
应该是放在

2287
01:07:54,160 --> 01:07:55,140
这

2288
01:07:55,140 --> 01:07:58,600
EmmidSize

2289
01:07:58,600 --> 01:07:59,400
这应该有Quack

2290
01:07:59,400 --> 01:08:00,380
对吧

2291
01:08:00,380 --> 01:08:03,800
对吧

2292
01:08:03,800 --> 01:08:04,840
就可以了

2293
01:08:04,840 --> 01:08:06,880
好

2294
01:08:06,880 --> 01:08:07,520
结果完成了

2295
01:08:07,520 --> 01:08:09,580
你看我们的EmmidSize就写完了

2296
01:08:09,580 --> 01:08:14,620
这就是什么

2297
01:08:14,620 --> 01:08:15,540
写入面线系统

2298
01:08:15,540 --> 01:08:15,900
你看

2299
01:08:15,900 --> 01:08:17,940
我们拿到我们要写入的资源

2300
01:08:17,940 --> 01:08:20,300
拿到我们要写入的资源

2301
01:08:20,300 --> 01:08:21,300
然后呢

2302
01:08:21,300 --> 01:08:22,100
我们要循环它

2303
01:08:22,100 --> 01:08:24,700
拿到它的原代码和文件名

2304
01:08:24,700 --> 01:08:26,100
拼用它的什么写入境

2305
01:08:26,100 --> 01:08:27,600
然后往文件系统里边写

2306
01:08:27,600 --> 01:08:28,940
这就是fi的模块呗

2307
01:08:28,940 --> 01:08:29,360
对吧

2308
01:08:29,360 --> 01:08:30,300
fi的模块啊

2309
01:08:30,300 --> 01:08:32,260
然后就要回到原事了

2310
01:08:32,260 --> 01:08:34,540
那么我们还有没写

2311
01:08:34,540 --> 01:08:35,500
哪没写啊

2312
01:08:35,500 --> 01:08:37,040
是不是state没写啊

2313
01:08:37,040 --> 01:08:38,500
state在哪

2314
01:08:38,500 --> 01:08:39,660
state在这

2315
01:08:39,660 --> 01:08:41,940
我这是不是有过state的对象

2316
01:08:41,940 --> 01:08:43,240
好

2317
01:08:43,240 --> 01:08:44,860
我们建设这个类啊

2318
01:08:44,860 --> 01:08:45,320
建设这个类

2319
01:08:45,320 --> 01:08:46,780
state

2320
01:08:46,780 --> 01:08:48,340
这个的话很简单

2321
01:08:48,340 --> 01:08:48,820
就是个类

2322
01:08:48,820 --> 01:08:49,620
class

2323
01:08:49,620 --> 01:08:51,140
叫state

2324
01:08:51,140 --> 01:08:56,860
State 用来干嘛 用来去保存我们这个文件内容的啊 比如说我们写一个contractor

2325
01:08:56,860 --> 01:09:01,260
然后传过来个completion

2326
01:09:01,260 --> 01:09:07,120
然后这呢 我们可以这样写 z.files

2327
01:09:07,120 --> 01:09:08,000
等于什么

2328
01:09:08,000 --> 01:09:09,100
completion.files

2329
01:09:09,100 --> 01:09:15,220
z.modules

2330
01:09:15,220 --> 01:09:16,500
等于completion.modules

2331
01:09:16,500 --> 01:09:19,420
然后z.chunks

2332
01:09:21,140 --> 01:09:29,700
把我们这个打包后的对象都放到他里面去

2333
01:09:29,700 --> 01:09:32,100
供别人显示看用

2334
01:09:32,100 --> 01:09:35,640
好 这样吧seed就建好了

2335
01:09:35,640 --> 01:09:36,760
然后引进来吧

2336
01:09:36,760 --> 01:09:37,560
引到这来

2337
01:09:37,560 --> 01:09:39,460
在这引

2338
01:09:39,460 --> 01:09:40,660
cand

2339
01:09:40,660 --> 01:09:43,900
然后呢seed等于require什么样

2340
01:09:43,900 --> 01:09:44,820
点刚state

2341
01:09:44,820 --> 01:09:46,380
对吧

2342
01:09:46,380 --> 01:09:49,720
那么最后啊

2343
01:09:49,720 --> 01:09:51,660
你想想我们是不是还查住一步

2344
01:09:51,660 --> 01:09:53,180
哪一步

2345
01:09:53,180 --> 01:09:56,240
我们是不是有一个地方漏了

2346
01:09:56,240 --> 01:09:56,900
哪个地方漏了

2347
01:09:56,900 --> 01:09:58,440
是不是相当于我们生成什么

2348
01:09:58,440 --> 01:10:02,200
这个生成这个set对象的地方

2349
01:10:02,200 --> 01:10:02,960
没有写啊

2350
01:10:02,960 --> 01:10:03,120
是不是

2351
01:10:03,120 --> 01:10:03,900
对吧

2352
01:10:03,900 --> 01:10:06,320
那这对象哪生成的呀

2353
01:10:06,320 --> 01:10:06,780
大家想想

2354
01:10:06,780 --> 01:10:08,060
在哪生成的

2355
01:10:08,060 --> 01:10:10,660
在哪生成的

2356
01:10:10,660 --> 01:10:11,480
你想想

2357
01:10:11,480 --> 01:10:12,800
看在这

2358
01:10:12,800 --> 01:10:15,260
在我们的configure的地方

2359
01:10:15,260 --> 01:10:15,920
看这

2360
01:10:15,920 --> 01:10:17,600
在seal的时候

2361
01:10:17,600 --> 01:10:18,580
对吧

2362
01:10:18,580 --> 01:10:19,760
在seal的时候

2363
01:10:19,760 --> 01:10:20,680
你看

2364
01:10:20,680 --> 01:10:22,340
你这是不是搞个代码块

2365
01:10:22,340 --> 01:10:23,560
对吧

2366
01:10:23,560 --> 01:10:26,140
你要把代码块放到数字里边去了

2367
01:10:26,140 --> 01:10:26,860
对不对

2368
01:10:26,860 --> 01:10:28,340
但是这还差一步

2369
01:10:28,340 --> 01:10:30,060
差一步什么呀

2370
01:10:30,060 --> 01:10:30,480
是不是要

2371
01:10:30,480 --> 01:10:33,120
我们要根据代码块去生成我们的资源文件

2372
01:10:33,120 --> 01:10:33,820
对吧

2373
01:10:33,820 --> 01:10:34,720
生成我们的assess

2374
01:10:34,720 --> 01:10:35,240
是不是

2375
01:10:35,240 --> 01:10:36,600
送送ss

2376
01:10:36,600 --> 01:10:38,360
就是说一说我们要在这地方

2377
01:10:38,360 --> 01:10:39,900
生成咱们的什么呀

2378
01:10:39,900 --> 01:10:40,860
生成咱们assess

2379
01:10:40,860 --> 01:10:42,360
送送我们的资源

2380
01:10:42,360 --> 01:10:42,700
对吧

2381
01:10:42,700 --> 01:10:44,960
那么要生成的话

2382
01:10:44,960 --> 01:10:46,460
我们要需要多一些新的属性

2383
01:10:46,460 --> 01:10:47,040
比如说

2384
01:10:47,040 --> 01:10:48,240
我们的加以属性

2385
01:10:48,240 --> 01:10:50,000
是代码块

2386
01:10:50,000 --> 01:10:53,080
还需要什么呀

2387
01:10:53,080 --> 01:10:54,400
files

2388
01:10:54,400 --> 01:10:57,160
是不是一个数组啊

2389
01:10:57,160 --> 01:10:58,400
还需要什么呀

2390
01:10:58,400 --> 01:10:59,560
需要我们这个叫assess

2391
01:10:59,560 --> 01:11:01,340
是不是那个对象

2392
01:11:01,340 --> 01:11:03,600
就是我们的资源啊

2393
01:11:03,600 --> 01:11:05,340
资源对象

2394
01:11:05,340 --> 01:11:07,500
还需要什么

2395
01:11:07,500 --> 01:11:08,300
需要一个这个

2396
01:11:08,300 --> 01:11:08,880
是什么

2397
01:11:08,880 --> 01:11:10,380
这个文件的一个数组

2398
01:11:10,380 --> 01:11:10,760
是吧

2399
01:11:10,760 --> 01:11:11,260
数组

2400
01:11:11,260 --> 01:11:16,360
就它吧

2401
01:11:16,360 --> 01:11:17,800
另外我们要编译的话

2402
01:11:17,800 --> 01:11:20,460
我们还要引入一些额外的一些模块啊

2403
01:11:20,460 --> 01:11:21,180
比如说引入什么

2404
01:11:21,180 --> 01:11:22,360
引入EGS模练情

2405
01:11:22,360 --> 01:11:25,640
因为我们要通过它来生成我们的原来码

2406
01:11:25,640 --> 01:11:25,860
是不是

2407
01:11:25,860 --> 01:11:26,460
EGS

2408
01:11:26,460 --> 01:11:28,220
还需要什么

2409
01:11:28,220 --> 01:11:29,580
需要我们这个FI的模块

2410
01:11:29,580 --> 01:11:33,360
对吧

2411
01:11:33,360 --> 01:11:34,820
还需要我们这个

2412
01:11:34,820 --> 01:11:38,080
创可有了是吧

2413
01:11:38,080 --> 01:11:39,920
还需要我们这个run的方法

2414
01:11:39,920 --> 01:11:40,320
是不是

2415
01:11:40,320 --> 01:11:42,560
好我们先我们写个模板啊

2416
01:11:42,560 --> 01:11:44,080
什么模板我们来建个模板文件

2417
01:11:44,080 --> 01:11:46,460
叫什么叫

2418
01:11:46,460 --> 01:11:50,420
那么他是怎么来的呢

2419
01:11:50,420 --> 01:11:51,620
他其实啊

2420
01:11:51,620 --> 01:11:52,300
刚刚把这卡过来

2421
01:11:52,300 --> 01:11:54,100
你看这是不是打破后理研码啊

2422
01:11:54,100 --> 01:11:55,600
是不是啊

2423
01:11:55,600 --> 01:11:56,500
我们把它怎么样

2424
01:11:56,500 --> 01:11:57,520
给你拿过来

2425
01:11:57,520 --> 01:11:59,600
把它改改啊

2426
01:11:59,600 --> 01:12:03,400
改改对吧

2427
01:12:03,400 --> 01:12:04,780
把这个该删掉删掉

2428
01:12:04,780 --> 01:12:05,780
不用删掉

2429
01:12:05,780 --> 01:12:09,960
这个不要了是吧

2430
01:12:09,960 --> 01:12:11,520
不要了

2431
01:12:11,520 --> 01:12:12,680
好不要了

2432
01:12:16,460 --> 01:12:19,660
好可以了

2433
01:12:19,660 --> 01:12:22,300
然后你看我把这个拿过来考考作为模板

2434
01:12:22,300 --> 01:12:23,020
是不是模板

2435
01:12:23,020 --> 01:12:25,980
拿过来放到我们这一节的模板形里边

2436
01:12:25,980 --> 01:12:26,520
作为模板

2437
01:12:26,520 --> 01:12:28,440
那哪些地方要变

2438
01:12:28,440 --> 01:12:29,320
哪些地方不要变呢

2439
01:12:29,320 --> 01:12:30,480
你看啊哪些地方要变

2440
01:12:30,480 --> 01:12:32,200
这要变是不是

2441
01:12:32,200 --> 01:12:34,040
这不是我们入入文件啊

2442
01:12:34,040 --> 01:12:34,640
要变吧

2443
01:12:34,640 --> 01:12:35,900
怎么换成一个什么

2444
01:12:35,900 --> 01:12:37,480
换了我们叫它什么

2445
01:12:37,480 --> 01:12:38,360
叫它这个安锤

2446
01:12:38,360 --> 01:12:39,260
对吧

2447
01:12:39,260 --> 01:12:39,720
安锤ID

2448
01:12:39,720 --> 01:12:40,340
对吧

2449
01:12:40,340 --> 01:12:41,540
就是入画ID

2450
01:12:41,540 --> 01:12:43,480
那么这儿呢

2451
01:12:43,480 --> 01:12:44,300
你看啊

2452
01:12:44,300 --> 01:12:45,300
这儿是不是一个

2453
01:12:45,300 --> 01:12:47,220
也是个循环迭代啊

2454
01:12:47,220 --> 01:12:48,520
你看这是它的模画ID

2455
01:12:48,520 --> 01:12:49,940
这是它的模画内容是不是

2456
01:12:49,940 --> 01:12:50,660
对吧

2457
01:12:50,660 --> 01:12:52,420
所以我们这个属性啊

2458
01:12:52,420 --> 01:12:53,240
又可以迭代出来是不是

2459
01:12:53,240 --> 01:12:54,480
怎么迭代呢

2460
01:12:54,480 --> 01:12:56,700
这个我们一会再说

2461
01:12:56,700 --> 01:12:57,480
总而言之

2462
01:12:57,480 --> 01:12:59,540
我们先让模板先读出来啊

2463
01:12:59,540 --> 01:13:00,320
怎么读啊

2464
01:13:00,320 --> 01:13:00,760
这样读

2465
01:13:00,760 --> 01:13:04,060
F5.read the file sync

2466
01:13:04,060 --> 01:13:07,200
读那个模板啊

2467
01:13:07,200 --> 01:13:07,940
读咱们这个

2468
01:13:07,940 --> 01:13:09,060
第二杠什么呀

2469
01:13:09,060 --> 01:13:10,560
may.egs mobile

2470
01:13:10,560 --> 01:13:13,160
然后呢 编码的Ut8

2471
01:13:13,160 --> 01:13:19,800
然后它等于什么 等于这个maytemplate

2472
01:13:19,800 --> 01:13:21,600
对吧 template

2473
01:13:21,600 --> 01:13:32,700
刚读取当前幕 这应该应该去 应该这样写啊 我们写个pass.paw6

2474
01:13:34,260 --> 01:13:41,460
点叫用dr name 下边的什么样 这个may.js 对吧 多么万情

2475
01:13:41,460 --> 01:13:49,260
然后呢 用阶子 用一阶子准备啊 去编一下吧 compile 编一下啊 把这个may

2476
01:13:49,260 --> 01:13:50,020
 他们放进去

2477
01:13:50,020 --> 01:13:57,000
对吧 然后他会反回这个runner方法 对不对 就可以了啊 我们叫他mayrunner就可以了

2478
01:14:00,060 --> 01:14:01,800
他会返回一个mate runner 的方法

2479
01:14:01,800 --> 01:14:02,160
对不对

2480
01:14:02,160 --> 01:14:04,200
通过他可以怎么样进行渲染是不是啊

2481
01:14:04,200 --> 01:14:04,860
啊怎么渲染

2482
01:14:04,860 --> 01:14:08,060
那么我们你看我们刚才是不是填着两个属性

2483
01:14:08,060 --> 01:14:08,900
一个叫什么

2484
01:14:08,900 --> 01:14:10,020
file 的一个assess

2485
01:14:10,020 --> 01:14:12,720
现在是不是要往里边加东西了

2486
01:14:12,720 --> 01:14:13,360
怎么加呢

2487
01:14:13,360 --> 01:14:13,860
你看啊

2488
01:14:13,860 --> 01:14:14,620
在这

2489
01:14:14,620 --> 01:14:15,420
要找到seal的地方

2490
01:14:15,420 --> 01:14:16,160
你看啊

2491
01:14:16,160 --> 01:14:18,520
在我这个发了trunk之后啊

2492
01:14:18,520 --> 01:14:19,260
我再调方吧

2493
01:14:19,260 --> 01:14:20,660
叫什么叫这个叫什么

2494
01:14:20,660 --> 01:14:23,160
create trunk assets

2495
01:14:23,160 --> 01:14:26,560
什么意思

2496
01:14:26,560 --> 01:14:29,060
创建代码块对应的资源

2497
01:14:29,060 --> 01:14:29,860
是不是啊

2498
01:14:29,860 --> 01:14:33,300
好 我们写这方吧啊 叫 create trunk access

2499
01:14:33,300 --> 01:14:36,220
然后里边怎么写

2500
01:14:36,220 --> 01:14:37,660
区块代码块吧

2501
01:14:37,660 --> 01:14:43,100
led i 等于 0 i 小于 z 点 trunks length

2502
01:14:43,100 --> 01:14:44,100
那就叫

2503
01:14:44,100 --> 01:14:46,260
对吧

2504
01:14:46,260 --> 01:14:48,460
然后呢 然后每个代码块 trunk 等于什么

2505
01:14:48,460 --> 01:14:52,860
等于一个这个 z 点 trunks i

2506
01:14:52,860 --> 01:14:54,360
是吧

2507
01:14:54,360 --> 01:14:57,300
然后呢 我们要去

2508
01:14:57,920 --> 01:14:59,920
给trunks啊 来个数 来个变量

2509
01:14:59,920 --> 01:15:06,260
叫files 就是这个代码块啊 对应的文件 莫是空速度 是不是 对吧啊

2510
01:15:06,260 --> 01:15:08,460
然后呢 我们要建个文件吧

2511
01:15:08,460 --> 01:15:12,080
file等于谁 等于一个这个

2512
01:15:12,080 --> 01:15:14,580
应该是代码块的名字

2513
01:15:14,580 --> 01:15:17,860
加上什么呀 加上一个叫

2514
01:15:17,860 --> 01:15:23,480
.js 你比如说代码块的名字是may的话 就是什么 就是may.js 是不是啊 may.js

2515
01:15:25,820 --> 01:15:29,120
这样的话其实对应的我们应该这应该最好改成这改成改成一个叫

2516
01:15:29,120 --> 01:15:31,220
方框妹方框内部是不是

2517
01:15:31,220 --> 01:15:33,020
这方代码块的名字是不是

2518
01:15:33,020 --> 01:15:33,660
代码块的名字

2519
01:15:33,660 --> 01:15:35,120
所以这的话你该就可以用什么

2520
01:15:35,120 --> 01:15:36,560
那边的解释是吧

2521
01:15:36,560 --> 01:15:38,420
那解释好放进去吧

2522
01:15:38,420 --> 01:15:40,280
chunksfile的点什么

2523
01:15:40,280 --> 01:15:41,120
pushfile

2524
01:15:41,120 --> 01:15:43,820
把文件呢放到这个数字里边去

2525
01:15:43,820 --> 01:15:45,020
是不是就可以了啊

2526
01:15:45,020 --> 01:15:45,680
数字里边就可以了

2527
01:15:45,680 --> 01:15:48,120
然后最后怎么样

2528
01:15:48,120 --> 01:15:49,480
我们要写什么

2529
01:15:49,480 --> 01:15:51,320
z.imit

2530
01:15:51,320 --> 01:15:52,520
assets

2531
01:15:52,520 --> 01:15:53,420
assets

2532
01:15:54,220 --> 01:15:59,360
这个 key 呢 就是我们的 file 文件名 值就是我们的什么呀 是不是就是我们的

2533
01:15:59,360 --> 01:15:59,660
 source呀

2534
01:15:59,660 --> 01:16:02,480
是不是代码呀

2535
01:16:02,480 --> 01:16:07,880
那么关键的代码怎么拿呢 对吧 代码怎么拿呀 想想

2536
01:16:07,880 --> 01:16:13,260
就是 let source 等于什么 是不是这个 may run 是游戏结果呀

2537
01:16:13,260 --> 01:16:15,860
是模板游戏结果呀

2538
01:16:15,860 --> 01:16:21,880
啊 那这一个放个对象 对吧 第一个呢 是创 in entry id 是入口模式 id 啊

2539
01:16:21,880 --> 01:16:22,380
 谁

2540
01:16:23,320 --> 01:16:26,260
入口moduid不就是tronk的什么呀

2541
01:16:26,260 --> 01:16:29,960
他这个entrymoduid嘛

2542
01:16:29,960 --> 01:16:36,620
你看咱们这个代码块的各栏目里边是不是有一个modu

2543
01:16:36,620 --> 01:16:37,420
你可以把它付给什么

2544
01:16:37,420 --> 01:16:39,060
付给我们entrymodu

2545
01:16:39,060 --> 01:16:39,820
对吧

2546
01:16:39,820 --> 01:16:40,680
是不是入口moduid

2547
01:16:40,680 --> 01:16:42,120
那么他的什么

2548
01:16:42,120 --> 01:16:44,480
他的这个moduid是什么odd是吧

2549
01:16:44,480 --> 01:16:46,060
就是入口moduid

2550
01:16:46,060 --> 01:16:49,720
这是入值词代码块的

2551
01:16:49,720 --> 01:16:53,280
入口moduid

2552
01:16:53,320 --> 01:16:57,140
d 是吧 梦回来的 好 拿过来

2553
01:16:57,140 --> 01:17:01,180
可以了吧 好 这么那个 entry id 是吧

2554
01:17:01,180 --> 01:17:02,980
还需要什么呀

2555
01:17:02,980 --> 01:17:05,260
我是不是还需要去

2556
01:17:05,260 --> 01:17:08,380
构建什么呀 构建这样一个对象啊

2557
01:17:08,380 --> 01:17:11,940
是吧 这这样构建出来呀 这是啥

2558
01:17:11,940 --> 01:17:16,820
这对象是不是代码块的模块啊 是不是啊 好 怎么写啊

2559
01:17:16,820 --> 01:17:19,120
我给怎么样 写上一个modules

2560
01:17:19,120 --> 01:17:21,680
是谁 不就是tunc的modules吗

2561
01:17:23,320 --> 01:17:26,580
是不是因为上面我是不是给创伤复了一个模特的呀

2562
01:17:26,580 --> 01:17:27,720
是吧

2563
01:17:27,720 --> 01:17:30,380
看吧创的模特负给什么模特的对吧

2564
01:17:30,380 --> 01:17:31,520
那在这怎么样

2565
01:17:31,520 --> 01:17:33,020
是不是可以循环这个模特的呀

2566
01:17:33,020 --> 01:17:33,820
对吧

2567
01:17:33,820 --> 01:17:34,820
什么改啊

2568
01:17:34,820 --> 01:17:35,620
改怎么改

2569
01:17:35,620 --> 01:17:37,480
循环吧

2570
01:17:37,480 --> 01:17:38,160
好

2571
01:17:38,160 --> 01:17:38,880
请循环

2572
01:17:38,880 --> 01:17:40,760
lid i 等于0

2573
01:17:40,760 --> 01:17:43,920
i 小于什么呀

2574
01:17:43,920 --> 01:17:46,980
他这个数组是吧

2575
01:17:46,980 --> 01:17:47,980
应该是一个什么

2576
01:17:47,980 --> 01:17:49,380
modules.lans

2577
01:17:49,380 --> 01:17:49,980
对吧

2578
01:17:49,980 --> 01:17:50,780
看见见

2579
01:17:53,320 --> 01:17:56,560
对吧 你看说循环

2580
01:17:56,560 --> 01:17:58,760
是吧

2581
01:17:58,760 --> 01:18:03,020
然后中间呢 你看这是一个副循环

2582
01:18:03,020 --> 01:18:07,760
lens i等于0 i小于modules点lens i加加对吧

2583
01:18:07,760 --> 01:18:10,860
然后这要写啥

2584
01:18:10,860 --> 01:18:13,500
它是不是要每个循环里边放个这玩意啊

2585
01:18:13,500 --> 01:18:15,100
是不是k value啊

2586
01:18:15,100 --> 01:18:18,900
这是k吧 这是value吧 放个这玩意是吧 拿过来放到这放到中间

2587
01:18:18,900 --> 01:18:21,280
但是放中间之前怎么样 先关掉

2588
01:18:21,280 --> 01:18:23,780
是BGS模板引擎的这个特点啊

2589
01:18:23,780 --> 01:18:24,200
关掉了

2590
01:18:24,200 --> 01:18:24,840
再打开

2591
01:18:24,840 --> 01:18:29,060
我这好像你看这的话

2592
01:18:29,060 --> 01:18:29,760
复合循环

2593
01:18:29,760 --> 01:18:30,780
循环对吧

2594
01:18:30,780 --> 01:18:31,100
没错

2595
01:18:31,100 --> 01:18:31,740
嗯

2596
01:18:31,740 --> 01:18:32,180
放到这

2597
01:18:32,180 --> 01:18:33,900
那这两个是不是不要了

2598
01:18:33,900 --> 01:18:34,880
闪掉了啊

2599
01:18:34,880 --> 01:18:35,340
再多闪掉

2600
01:18:35,340 --> 01:18:36,300
不要了

2601
01:18:36,300 --> 01:18:39,120
那么他的这个

2602
01:18:39,120 --> 01:18:40,600
这儿是放应该放个key吧

2603
01:18:40,600 --> 01:18:41,680
key放啥

2604
01:18:41,680 --> 01:18:46,480
不用模块id嘛

2605
01:18:46,480 --> 01:18:47,280
啊

2606
01:18:47,280 --> 01:18:48,180
你看我这应该什么

2607
01:18:48,180 --> 01:18:48,640
应该是

2608
01:18:48,640 --> 01:18:50,320
应该再去给他

2609
01:18:50,320 --> 01:18:52,120
应该是modules什么呀

2610
01:18:52,120 --> 01:18:54,160
modulesi

2611
01:18:54,160 --> 01:18:55,320
对吧

2612
01:18:55,320 --> 01:18:56,160
没个模块

2613
01:18:56,160 --> 01:18:56,880
它的什么呀

2614
01:18:56,880 --> 01:18:57,320
moduleid

2615
01:18:57,320 --> 01:18:59,440
是不是啊

2616
01:18:59,440 --> 01:19:01,100
是modules

2617
01:19:01,100 --> 01:19:02,760
moduleid

2618
01:19:02,760 --> 01:19:05,100
对吧

2619
01:19:05,100 --> 01:19:07,900
那它的值呢

2620
01:19:07,900 --> 01:19:09,680
它的值应该啥

2621
01:19:09,680 --> 01:19:11,720
这不应该放它的值啊

2622
01:19:11,720 --> 01:19:13,680
放这个模块的代码内容啊

2623
01:19:13,680 --> 01:19:13,860
好

2624
01:19:13,860 --> 01:19:15,800
放什么

2625
01:19:15,800 --> 01:19:17,440
放一个modulesi

2626
01:19:17,440 --> 01:19:18,560
对吧

2627
01:19:18,560 --> 01:19:20,040
它的什么呀

2628
01:19:20,040 --> 01:19:21,720
它的这个

2629
01:19:21,720 --> 01:19:23,260
下文件sauce

2630
01:19:23,260 --> 01:19:24,840
是不是可以了

2631
01:19:24,840 --> 01:19:26,020
是原版内容啊

2632
01:19:26,020 --> 01:19:26,320
对不对

2633
01:19:26,320 --> 01:19:28,180
原版内容

2634
01:19:28,180 --> 01:19:30,360
对吧

2635
01:19:30,360 --> 01:19:32,420
modules

2636
01:19:32,420 --> 01:19:36,340
这个地方的话

2637
01:19:36,340 --> 01:19:37,440
我看一下啊

2638
01:19:37,440 --> 01:19:38,260
这地方的话

2639
01:19:38,260 --> 01:19:42,620
看这样写合不合适啊

2640
01:19:42,620 --> 01:19:43,400
你看我这个地方

2641
01:19:43,400 --> 01:19:45,880
我这地方这个modules的话

2642
01:19:45,880 --> 01:19:46,660
我是个kvalue

2643
01:19:46,660 --> 01:19:48,460
k呢

2644
01:19:48,460 --> 01:19:48,740
你看

2645
01:19:48,740 --> 01:19:50,080
这地方我

2646
01:19:50,080 --> 01:19:52,060
你看这是个modules

2647
01:19:52,060 --> 01:19:53,100
这是个数组

2648
01:19:53,100 --> 01:19:53,900
这是个数组吧

2649
01:19:53,900 --> 01:19:55,960
这是那个对象

2650
01:19:55,960 --> 01:19:56,740
kvalue是吧

2651
01:19:56,740 --> 01:19:57,380
kvalue

2652
01:19:57,380 --> 01:19:59,200
看我这的话

2653
01:19:59,200 --> 01:20:00,760
我这个地方moduleid是模块id

2654
01:20:00,760 --> 01:20:01,960
这是它模块的一个圆码

2655
01:20:01,960 --> 01:20:02,780
编辑或者圆码

2656
01:20:02,780 --> 01:20:03,240
对啊

2657
01:20:03,240 --> 01:20:04,060
没错啊

2658
01:20:04,060 --> 01:20:05,620
好就可以了吧

2659
01:20:05,620 --> 01:20:07,080
这样前面有没事的啊

2660
01:20:07,080 --> 01:20:08,440
其实到了这

2661
01:20:08,440 --> 01:20:09,860
基本上我们就写完了

2662
01:20:09,860 --> 01:20:10,340
是不是啊

2663
01:20:10,340 --> 01:20:11,280
写完了啊

2664
01:20:11,280 --> 01:20:11,820
你看啊

2665
01:20:11,820 --> 01:20:12,840
看我们这个completion

2666
01:20:12,840 --> 01:20:16,600
这个方法没写是吧

2667
01:20:16,600 --> 01:20:18,600
反正方法在最后再写下这方法啊

2668
01:20:18,600 --> 01:20:19,580
叫imitfile

2669
01:20:19,580 --> 01:20:20,960
imitns

2670
01:20:20,960 --> 01:20:23,180
有什么用我们的file

2671
01:20:23,180 --> 01:20:26,000
和什么我们的sauce

2672
01:20:26,000 --> 01:20:26,400
原码

2673
01:20:26,400 --> 01:20:30,680
sauce原码的话

2674
01:20:30,680 --> 01:20:31,200
你看啊

2675
01:20:31,200 --> 01:20:31,900
这应该是什么

2676
01:20:31,900 --> 01:20:33,300
z.assess

2677
01:20:33,300 --> 01:20:35,680
k呢就我们的file

2678
01:20:35,680 --> 01:20:37,520
实际上就是我们这个sauce原码

2679
01:20:37,520 --> 01:20:38,160
对不对

2680
01:20:38,160 --> 01:20:39,280
然后呢

2681
01:20:39,280 --> 01:20:40,160
z.files

2682
01:20:40,160 --> 01:20:43,180
把这个file也放到数字里边去

2683
01:20:43,180 --> 01:20:43,480
是吧

2684
01:20:43,480 --> 01:20:45,000
就黑了

2685
01:20:48,600 --> 01:20:49,620
放送里边去

2686
01:20:49,620 --> 01:20:53,940
这个sauce的话

2687
01:20:53,940 --> 01:20:55,360
应该这是编译后的代码

2688
01:20:55,360 --> 01:20:56,940
编译代码

2689
01:20:56,940 --> 01:21:00,660
file source

2690
01:21:00,660 --> 01:21:01,960
没错吧

2691
01:21:01,960 --> 01:21:03,120
这不就完事了吗

2692
01:21:03,120 --> 01:21:07,440
到这基本上又全部写完了

2693
01:21:07,440 --> 01:21:08,320
全部写完了

2694
01:21:08,320 --> 01:21:09,620
这个

2695
01:21:09,620 --> 01:21:18,500
看一看

2696
01:21:18,600 --> 01:21:20,600
这个东西咱们看一下我这个

2697
01:21:20,600 --> 01:21:37,600
看这个webpack

2698
01:21:48,600 --> 01:21:52,600
其实我给大家写了一版是吧

2699
01:21:52,600 --> 01:21:56,600
但是这个给大家写了一版

2700
01:21:56,600 --> 01:22:00,600
嗯

2701
01:22:04,600 --> 01:22:13,160
一样的啊一样的其实其实一样的你看只不过你看有个name完成modulefiles还有是module的是吧我没写module

2702
01:22:13,160 --> 01:22:14,520
加个module

2703
01:22:14,520 --> 01:22:25,320
就这个这个没有没有什么用啊这两个这两个其实不用写是吧因为因为你这几个处置肯定空啊后面都会覆盖掉了是吧其实这个地方可以不用写

2704
01:22:25,320 --> 01:22:31,720
嗯好那么试试吧到这我们就写完了啊我们看下效果能不能打印出来呢

2705
01:22:31,720 --> 01:22:33,440
把这个先住掉吧

2706
01:22:33,440 --> 01:22:34,960
把地的下面的帮助接地住掉

2707
01:22:34,960 --> 01:22:35,800
删掉啊

2708
01:22:35,800 --> 01:22:36,120
删掉

2709
01:22:36,120 --> 01:22:36,580
我们

2710
01:22:36,580 --> 01:22:38,180
我们这样

2711
01:22:38,180 --> 01:22:38,700
我们把它

2712
01:22:38,700 --> 01:22:42,480
没事啊

2713
01:22:42,480 --> 01:22:43,720
因为它打包出来

2714
01:22:43,720 --> 01:22:44,580
名字应该是妹是不是

2715
01:22:44,580 --> 01:22:45,840
跟个不重名啊

2716
01:22:45,840 --> 01:22:46,480
所以我们就

2717
01:22:46,480 --> 01:22:47,480
来吧

2718
01:22:47,480 --> 01:22:48,440
试试啊

2719
01:22:48,440 --> 01:22:52,860
好

2720
01:22:52,860 --> 01:22:53,980
我们来去执行一下

2721
01:22:53,980 --> 01:22:56,320
state定义过了

2722
01:22:56,320 --> 01:22:56,620
是吧

2723
01:22:56,620 --> 01:22:57,820
compiler第九行

2724
01:22:57,820 --> 01:22:59,720
compiler第九行

2725
01:22:59,720 --> 01:23:29,720
第9号第1号第1号第1号第2号第9号第2号第9号

2726
01:23:29,720 --> 01:23:33,000
手滑了啊

2727
01:23:33,000 --> 01:23:36,920
chunks没有定义

2728
01:23:36,920 --> 01:23:38,060
这哪啊

2729
01:23:38,060 --> 01:23:39,060
这是completion第64

2730
01:23:39,060 --> 01:23:39,660
对吧

2731
01:23:39,660 --> 01:23:40,900
completion第64

2732
01:23:40,900 --> 01:23:43,800
第64网在这

2733
01:23:43,800 --> 01:23:46,300
completion没有定义

2734
01:23:46,300 --> 01:23:46,840
看哪

2735
01:23:46,840 --> 01:23:49,520
他说什么

2736
01:23:49,520 --> 01:23:50,180
他是说这个

2737
01:23:50,180 --> 01:23:52,900
他是说什么

2738
01:23:52,900 --> 01:23:54,020
说什么没定义啊

2739
01:23:54,020 --> 01:23:55,580
chunks没有定义

2740
01:23:55,580 --> 01:23:55,840
是吧

2741
01:23:55,840 --> 01:23:56,900
他说chunks没有定义

2742
01:23:56,900 --> 01:23:57,820
chunks在哪呢

2743
01:23:57,820 --> 01:23:58,780
看一下这chunks

2744
01:23:58,780 --> 01:23:59,380
这个是吧

2745
01:23:59,380 --> 01:24:07,860
这应该是trunk 不是trunk 是不是他的一个modules

2746
01:24:07,860 --> 01:24:10,140
对不对 应该是trunk 不是trunks

2747
01:24:10,140 --> 01:24:22,100
callback 扣的 对的 是吧 这对的 这对的是吧 你翻的6 callback

2748
01:24:22,100 --> 01:24:23,600
哎呀 这个代码在哪

2749
01:24:23,600 --> 01:24:29,140
我的天 这是啥呀

2750
01:24:30,140 --> 01:24:31,980
compiler第45行 是吧

2751
01:24:31,980 --> 01:24:33,820
compiler第45行

2752
01:24:33,820 --> 01:24:36,640
在这 是吧

2753
01:24:36,640 --> 01:24:39,980
他是说这个 你看啊

2754
01:24:39,980 --> 01:24:42,620
这个z.hook.imit call sync

2755
01:24:42,620 --> 01:24:46,440
然后呢 completion 然后 error 对吧

2756
01:24:46,440 --> 01:24:50,640
然后回到里边呢 我去掉这个方法 是不是 掉emitfile

2757
01:24:50,640 --> 01:24:53,620
然后他说什么呀 他说这个

2758
01:24:53,620 --> 01:24:57,280
compiler第45行

2759
01:24:57,280 --> 01:24:59,280
45行

2760
01:24:59,380 --> 01:25:03,160
我一走到hook子里边来 是不是hook的类型不对呀 你看啊

2761
01:25:03,160 --> 01:25:08,140
这个hook的类型 emit什么类型啊 看一下 emit什么hook

2762
01:25:08,140 --> 01:25:14,340
 emit是一个async-serieshook 是吧 那么看原码里面什么hook呀

2763
01:25:14,340 --> 01:25:22,960
看这emithook啊 看原码里面的话 它是一个compiler 是吧 网上找

2764
01:25:27,760 --> 01:25:32,820
async 05 没错啊 也是个异步的啊 它也是什么 也是一个

2765
01:25:32,820 --> 01:25:41,820
嗯 没错吧 也是一个async 05 是不是啊 有个completion 是吧 有个这样的参数

2766
01:25:41,820 --> 01:25:46,180
嗯 看一下啊

2767
01:25:46,180 --> 01:25:49,760
completion 是吧 没错啊 数足 对吧 没错

2768
01:25:49,760 --> 01:25:53,160
completion 这样参数 是吧

2769
01:25:56,160 --> 01:26:03,200
Emit是吧 好 再来看啊 看他他说什么 他说这个我们看下来怎么说的 他说这个

2770
01:26:03,200 --> 01:26:09,880
45行21行 他说下网行callback不是一个函数 下网行callback不是函数

2771
01:26:09,880 --> 01:26:21,320
看一下 看这啊 z.whose emit call a sync对吧 然后一步 第一个参重呢 哦

2772
01:26:23,080 --> 01:26:25,380
EVO的话这肯定要你看这写错了吧

2773
01:26:25,380 --> 01:26:27,180
EVO的话是不是要传一个cobac呀

2774
01:26:27,180 --> 01:26:28,920
像是不是这样

2775
01:26:28,920 --> 01:26:31,520
因为你看EVO的话如果怎么表示结束啊

2776
01:26:31,520 --> 01:26:33,580
需要传cobac呀是不是

2777
01:26:33,580 --> 01:26:36,900
他要不传cobac的话他就不能表示EVO结束是不是啊

2778
01:26:36,900 --> 01:26:38,700
所以说一定要带什么一定要带这个

2779
01:26:38,700 --> 01:26:42,540
对吧这要传一个cobac才能表示这个EVO结束是吧啊

2780
01:26:42,540 --> 01:26:46,880
所以说你看啊咱们这个

2781
01:26:46,880 --> 01:26:52,080
咱们这个方法执行之前啊你要注意一下啊

2782
01:26:52,080 --> 01:26:55,660
如果你要这样写的话 你要传一个回调

2783
01:26:55,660 --> 01:27:00,520
ZH.hooks.imit call sync completion error

2784
01:27:22,080 --> 01:27:30,000
Z 点 Image 点 靠 对吧 你看这地方Image Set 是吧 这有Coreback

2785
01:27:30,000 --> 01:27:33,720
我是在这掉的是吧 掉了

2786
01:27:33,720 --> 01:27:39,560
啊 这有Coreback 我在这掉了 没错呀

2787
01:27:39,560 --> 01:27:47,670
但是如果说这是义务代码的话啊 如果说是义务代码的话 按理说我应该掉

2788
01:27:47,670 --> 01:27:48,640
 你看 这有个error

2789
01:27:49,600 --> 01:27:51,840
这就是callback 对这不用 这就是callback

2790
01:27:51,840 --> 01:27:53,840
这就是callback 是不是啊

2791
01:27:53,840 --> 01:27:56,920
那招后的emit 对吧

2792
01:27:56,920 --> 01:28:01,000
emit 就是我们这个emit实践是吧 call sync

2793
01:28:01,000 --> 01:28:03,100
这就是回调啊 对吧 就是回调

2794
01:28:03,100 --> 01:28:07,600
你看原码里面怎么做的啊

2795
01:28:07,600 --> 01:28:08,640
你看原码里面怎么写的

2796
01:28:08,640 --> 01:28:11,600
把这代码考了 你看原码里面怎么写的啊

2797
01:28:11,600 --> 01:28:13,600
咱们的mickdlp 是吧

2798
01:28:13,600 --> 01:28:14,520
mickdlp 咱们掉一下

2799
01:28:19,460 --> 01:28:24,020
圆码 哪个是圆码 这是圆码 是吧 这是它的圆码 是吧 好 回来找一下啊

2800
01:28:24,020 --> 01:28:25,420
 你看啊

2801
01:28:25,420 --> 01:28:35,580
为了点它 叫make drp 是吧 调用了这个方法 然后呢 我们去

2802
01:28:35,580 --> 01:28:38,660
你看 make drp

2803
01:28:38,660 --> 01:28:45,980
对吧 make drp 调用了make drp 调用了它 你看传入了这个这个是文件的一个

2804
01:28:47,180 --> 01:28:50,260
这一个路径是吧 然后 write out 对吧

2805
01:28:50,260 --> 01:28:56,900
这也是这么写的啊 你看他上面是不是有一个一米特

2806
01:28:56,900 --> 01:28:59,320
有是不是有个事件啊啊

2807
01:28:59,320 --> 01:29:02,900
一米的负友是吧 看他一米的赛斯

2808
01:29:02,900 --> 01:29:08,940
他会走他 然后他里边呢会有一个一米的 files 输出文件 那么在下面呢

2809
01:29:08,940 --> 01:29:09,580
 你看他会

2810
01:29:13,380 --> 01:29:17,980
像他主战 你看 这是不是有个就这个卡带嘛 这点呼个的emit call sync 是吧

2811
01:29:17,980 --> 01:29:20,740
你看哦 有个他

2812
01:29:20,740 --> 01:29:27,500
你看他走到这来 error 和他 是不是 你看他里边如果错误的话 掉callback

2813
01:29:27,500 --> 01:29:33,060
然后呢 拿到了一个输出路径 对吧 然后呢 我去怎么样 我去这个make dlp

2814
01:29:33,060 --> 01:29:35,100
 创建了这样一个他

2815
01:29:35,100 --> 01:29:38,300
然后呢 执行一个回调 对吧

2816
01:29:40,660 --> 01:29:43,340
好 咱们看一眼 咱们来给大家debug 看一眼效果啊

2817
01:29:43,340 --> 01:29:48,380
咱们在这 咱们在这掉下

2818
01:29:48,380 --> 01:29:55,340
一米的饭

2819
01:29:55,340 --> 01:29:56,540
嗯 好

2820
01:29:56,540 --> 01:30:01,700
好 回到咱们代码里面了 回到我们CRDS 咱们来一下断点

2821
01:30:10,460 --> 01:30:14,280
走 走 走 走 走

2822
01:30:14,280 --> 01:30:19,040
走得快了啊 我要再来一次

2823
01:30:19,040 --> 01:30:33,040
啊 走 走 走 好 到这儿了 你看 玩家走

2824
01:30:33,040 --> 01:30:36,380
他这边首先拿到hooks 什么呀 imithook

2825
01:30:36,380 --> 01:30:39,280
他是什么 他是个异步的串引钩子 是吧

2826
01:30:40,080 --> 01:30:43,240
然后呢 你看这是一个什么 这是一个callback是吧

2827
01:30:43,240 --> 01:30:46,840
这一个callback callback async

2828
01:30:46,840 --> 01:30:50,680
是吧 然后里边会瞅两层 一个是combination 当前的

2829
01:30:50,680 --> 01:30:51,680
or adfund

2830
01:30:51,680 --> 01:30:54,320
这怎么adfund的呢 没有传啊

2831
01:30:54,320 --> 01:30:57,580
耶 你看 image assets

2832
01:30:57,580 --> 01:31:01,780
这是 adfund的吗 adfund 为啥呢

2833
01:31:01,780 --> 01:31:05,480
耶 果真 这应该传啥

2834
01:31:05,480 --> 01:31:08,580
你看 attribution 这

2835
01:31:10,080 --> 01:31:13,560
哎 那你反的是吧 那compile的 你看compile怎么传的

2836
01:31:13,560 --> 01:31:18,920
哦 这是不是 这是不是 你看啊 掉它的时候 你看掉

2837
01:31:18,920 --> 01:31:22,800
你看掉uncompile的时候 你看这是传的 你看

2838
01:31:22,800 --> 01:31:26,640
这是不是没有传餐啊 你看 这我坑啊 你看

2839
01:31:26,640 --> 01:31:29,760
no compilation 是吧 拿过来躺到这

2840
01:31:29,760 --> 01:31:32,440
嗯 是吧 再坑了啊

2841
01:31:39,360 --> 01:31:43,000
好走啊 你看这次就这了吧 你看有了是吧 也有了看error

2842
01:31:43,000 --> 01:31:44,760
走 咱们进来

2843
01:31:44,760 --> 01:31:48,400
剪之后你看他

2844
01:31:48,400 --> 01:31:53,160
没有输出是吧 我们看一下看这

2845
01:31:53,160 --> 01:32:02,920
他这还是报这个错是吧

2846
01:32:02,920 --> 01:32:05,480
是啥 Zedra Callback

2847
01:32:05,480 --> 01:32:08,280
Codefactory

2848
01:32:09,360 --> 01:32:13,380
反而四六行是吧

2849
01:32:13,380 --> 01:32:15,680
comparate的四六行

2850
01:32:15,680 --> 01:32:17,780
他还是这个地方报错了

2851
01:32:17,780 --> 01:32:19,700
这地方有值啊

2852
01:32:19,700 --> 01:32:20,560
我传过来了是不是

2853
01:32:20,560 --> 01:32:21,500
传过来了

2854
01:32:21,500 --> 01:32:22,820
然后呢

2855
01:32:22,820 --> 01:32:24,240
你看这是一个异步的话

2856
01:32:24,240 --> 01:32:27,060
靠和sync什么异步啊

2857
01:32:27,060 --> 01:32:28,440
异步的话

2858
01:32:28,440 --> 01:32:30,120
你看我们这个钩子是怎么用的啊

2859
01:32:30,120 --> 01:32:32,360
你看这个这有个table是吧

2860
01:32:32,360 --> 01:32:36,420
就传一个

2861
01:32:36,420 --> 01:32:38,160
你看就传了一个回调啊

2862
01:32:38,160 --> 01:32:45,410
是不是 高斯尼传一个回调 就可以了 你看我这的话 你看 钩子传过来 它根本没进来

2863
01:32:45,410 --> 01:32:45,660
 是吧

2864
01:32:45,660 --> 01:32:48,440
那看看我的Z着Hooks的Emit

2865
01:32:48,440 --> 01:32:49,960
这个是吧

2866
01:32:49,960 --> 01:32:57,480
Near了一个SyncHook 你看 传过 哎呀 你看 这传一个书组啊

2867
01:32:57,480 --> 01:33:03,220
对吧 传一个书组啊 怎么搞成一个 这玩意了

2868
01:33:03,220 --> 01:33:05,780
哎呀 你看 这谁搞的

2869
01:33:07,860 --> 01:33:13,420
已经很困了 是吧 好 终于完事了吧 我不要错了吧

2870
01:33:13,420 --> 01:33:16,020
好 我们看一下生什么吧 看一下这个代码

2871
01:33:16,020 --> 01:33:22,220
你看 没点接的生成了吧 好 我看能不能跑啊 咱们写一下我们的

2872
01:33:22,220 --> 01:33:24,320
index.itmr

2873
01:33:24,320 --> 01:33:29,300
好 咱们生成道路结构 来生成一下引入咱们的script

2874
01:33:29,300 --> 01:33:34,420
谁呀 咱们这个 major.js 好 咱们来看能不能跑动啊

2875
01:33:36,420 --> 01:33:39,980
行 邮件 检查 空谈

2876
01:33:39,980 --> 01:33:45,920
我们看一下原代码啊 看原代码是不是这个预法有问题啊 看这个matergs

2877
01:33:45,920 --> 01:33:49,380
首先啊 你看咱们这个入口文件

2878
01:33:49,380 --> 01:33:54,460
咦 空的呀 它转移了啊 转移了

2879
01:33:54,460 --> 01:33:59,620
看这是不是空的呀 这是不是转移了呀 对不对 所以说我们要不要让转移

2880
01:33:59,620 --> 01:34:01,180
 不让转移怎么办呢 早到我们这个

2881
01:34:01,180 --> 01:34:05,300
completion 是吧 然后这呢 我们改一下啊

2882
01:34:06,420 --> 01:34:08,460
这个模板模板有没有改一下

2883
01:34:08,460 --> 01:34:10,580
没改一下

2884
01:34:10,580 --> 01:34:11,380
怎么改呢

2885
01:34:11,380 --> 01:34:13,160
把这个等号改成个简号

2886
01:34:13,160 --> 01:34:14,540
不要转移

2887
01:34:14,540 --> 01:34:16,540
都改成一个简号

2888
01:34:16,540 --> 01:34:17,180
看到吧

2889
01:34:17,180 --> 01:34:17,780
改成简号

2890
01:34:17,780 --> 01:34:18,740
再来一次

2891
01:34:18,740 --> 01:34:21,260
对

2892
01:34:21,260 --> 01:34:22,020
改成简号

2893
01:34:22,020 --> 01:34:26,080
然后这的话

2894
01:34:26,080 --> 01:34:27,200
你看这个module的

2895
01:34:27,200 --> 01:34:29,580
这是你看这是一个module的source

2896
01:34:29,580 --> 01:34:29,920
对

2897
01:34:29,920 --> 01:34:31,520
你看这是一个module的source

2898
01:34:31,520 --> 01:34:36,380
你看这是个

2899
01:34:36,380 --> 01:34:38,440
let一个ta是吧

2900
01:34:38,440 --> 01:34:40,560
没错吧

2901
01:34:40,560 --> 01:34:42,180
感觉应该是完全ok的啊

2902
01:34:42,180 --> 01:34:42,580
没有问题

2903
01:34:42,580 --> 01:34:44,860
好我们看一下这个打包的代码

2904
01:34:44,860 --> 01:34:46,700
没点js是吧

2905
01:34:46,700 --> 01:34:47,100
看一下

2906
01:34:47,100 --> 01:34:50,300
首先啊你看这个这是有了是吧

2907
01:34:50,300 --> 01:34:51,960
这也是有这也都对了吧

2908
01:34:51,960 --> 01:34:53,280
这没有是

2909
01:34:53,280 --> 01:34:53,980
为啥没有值呢

2910
01:34:53,980 --> 01:34:54,620
你看啊

2911
01:34:54,620 --> 01:34:57,180
是不是如果文件没有给它填上啊

2912
01:34:57,180 --> 01:34:57,900
为啥没填上呢

2913
01:34:57,900 --> 01:34:58,380
你看啊

2914
01:34:58,380 --> 01:34:59,060
这个都对了啊

2915
01:34:59,060 --> 01:35:00,620
这个就关掉了

2916
01:35:00,620 --> 01:35:03,840
看一下啊

2917
01:35:03,840 --> 01:35:05,580
看一下咱们这个completion

2918
01:35:05,580 --> 01:35:06,880
看这

2919
01:35:06,880 --> 01:35:09,940
这的话你看我这个地方写的时候

2920
01:35:09,940 --> 01:35:16,620
好 这是Entry

2921
01:35:16,620 --> 01:35:19,020
Entry的话

2922
01:35:19,020 --> 01:35:20,240
你看这的问题啊

2923
01:35:20,240 --> 01:35:21,060
Entry给的值

2924
01:35:21,060 --> 01:35:22,580
Trunk Entry Module

2925
01:35:22,580 --> 01:35:24,140
肯定是个Module

2926
01:35:24,140 --> 01:35:25,620
Entry Module的名字啊

2927
01:35:25,620 --> 01:35:27,080
不太不太对啊

2928
01:35:27,080 --> 01:35:28,320
你看把这个

2929
01:35:28,320 --> 01:35:30,160
你看我New Trunk的时候啊

2930
01:35:30,160 --> 01:35:31,480
你看我New Trunk的时候

2931
01:35:31,480 --> 01:35:35,460
把Entry Module传进去了是吧

2932
01:35:36,580 --> 01:35:40,420
untread module 跟名字一样吗 看这个trunk里边叫啥

2933
01:35:40,420 --> 01:35:43,120
untread module

2934
01:35:43,120 --> 01:35:46,740
把module 复给了untread module 是吧

2935
01:35:46,740 --> 01:35:49,340
那么我在complete的时候呢

2936
01:35:49,340 --> 01:35:57,060
你看 循环它 然后传给它 是不是啊 传给了trunk

2937
01:35:57,060 --> 01:36:02,180
那trunk 它的这个啊 那么它trunk trunk4吧

2938
01:36:03,820 --> 01:36:08,260
啊 你个seal 是拿到了每个trunk 是不是 拿到每个trunk里边的每个模块

2939
01:36:08,260 --> 01:36:13,620
然后呢 这是循环trunk 拿到每个trunk 给它加文件 对吧

2940
01:36:13,620 --> 01:36:21,180
然后这是按对mode 我知道 这是入口模块啊 咱们没有给id 是吧 对吧 入口模块没有给id啊

2941
01:36:21,180 --> 01:36:23,620
你看找到我们这个build 你看找到咱们这个

2942
01:36:23,620 --> 01:36:30,740
normal model 是吧 这少一句话代码啊 你看这 我们又没有给这this加id

2943
01:36:31,580 --> 01:36:33,120
你看一站模块是不是给了

2944
01:36:33,120 --> 01:36:34,420
这给了呀这给了

2945
01:36:34,420 --> 01:36:35,540
但是我们这没有给啊

2946
01:36:35,540 --> 01:36:36,180
你看什么

2947
01:36:36,180 --> 01:36:38,820
z.module id 等于啥

2948
01:36:38,820 --> 01:36:40,520
等于啥

2949
01:36:40,520 --> 01:36:42,240
模块id怎么取啊

2950
01:36:42,240 --> 01:36:43,940
是不是就是这个

2951
01:36:43,940 --> 01:36:44,820
就搁这搁这一样啊

2952
01:36:44,820 --> 01:36:46,140
你看就这么来的吧

2953
01:36:46,140 --> 01:36:47,980
就这样子来了是吧

2954
01:36:47,980 --> 01:36:49,220
拿过来放到这

2955
01:36:49,220 --> 01:36:51,840
点刚什么呀

2956
01:36:51,840 --> 01:36:54,880
当前的这个上海文相对于什么呀

2957
01:36:54,880 --> 01:36:55,640
z.request

2958
01:36:55,640 --> 01:36:57,840
是不是

2959
01:36:57,840 --> 01:37:00,080
就是当前的根目录

2960
01:37:00,080 --> 01:37:02,820
到我们这个目录它的一个相处性是不是

2961
01:37:02,820 --> 01:37:03,940
也就这么快递 是吧

2962
01:37:03,940 --> 01:37:04,820
好 我们再来一次

2963
01:37:04,820 --> 01:37:09,520
好 我们再来看一下代码啊

2964
01:37:09,520 --> 01:37:11,120
底下这不有了啊 看一下这个

2965
01:37:11,120 --> 01:37:14,840
Mate.js 有了啊 有了啊

2966
01:37:14,840 --> 01:37:16,520
好 我们看一下啊 刷新

2967
01:37:16,520 --> 01:37:19,040
哎 有了 哈哈 终于成功了是吧

2968
01:37:19,040 --> 01:37:21,720
啊 好 这个例子终于写完了啊

2969
01:37:21,720 --> 01:37:23,340
这个其实后面还有啊

2970
01:37:23,340 --> 01:37:24,480
比如说怎么实现loader啊

2971
01:37:24,480 --> 01:37:26,080
怎么实现这代码分割啊

2972
01:37:26,080 --> 01:37:27,220
怎么懒加载啊 是吧

2973
01:37:27,220 --> 01:37:29,280
但是今天我们就只能写到这儿了

2974
01:37:30,080 --> 01:37:30,840
啊有点小事

2975
01:37:30,840 --> 01:37:33,160
看还有几个同学在啊还有几个同学在

2976
01:37:33,160 --> 01:37:36,360
47个同学啊你们都是英雄啊

2977
01:37:36,360 --> 01:37:42,800
呃这个看一下啊看一下大家的问题啊看他的问题

2978
01:37:42,800 --> 01:37:49,080
嗯好

2979
01:37:49,080 --> 01:37:54,920
感谢大家参与啊大家很辛苦啊你看听了几个小时

2980
01:37:54,920 --> 01:37:57,360
四个半小时啊很在很厉害啊

2981
01:37:58,760 --> 01:38:03,500
这个讲到这个语法术分析了 这会儿其实不难啊 其实不难啊

2982
01:38:03,500 --> 01:38:07,960
背包6对背包6

2983
01:38:07,960 --> 01:38:09,720
替换

2984
01:38:09,720 --> 01:38:11,860
其实主要是受益赖啊

2985
01:38:11,860 --> 01:38:13,520
对

2986
01:38:13,520 --> 01:38:18,220
对 这重点 对不对 他的 id 变了 变了1st 开头了

2987
01:38:18,220 --> 01:38:25,120
左手骨要做处理吗 左手骨其实很简单 对吧 我给大家改道一下啊 左手骨怎么支持呢

2988
01:38:25,120 --> 01:38:26,060
 其实很简单 你看啊

2989
01:38:26,860 --> 01:38:28,220
回来实现一下吧

2990
01:38:28,220 --> 01:38:28,860
比如说你看啊

2991
01:38:28,860 --> 01:38:30,900
比如说如果说我们代码是这样的

2992
01:38:30,900 --> 01:38:31,500
我的安锤呀

2993
01:38:31,500 --> 01:38:32,060
你看啊

2994
01:38:32,060 --> 01:38:33,140
这是一个多入口是吧

2995
01:38:33,140 --> 01:38:34,460
是一个对象

2996
01:38:34,460 --> 01:38:36,900
看我们的安锤1

2997
01:38:36,900 --> 01:38:40,660
是什么是一个安锤1的JS是吧

2998
01:38:40,660 --> 01:38:42,860
那么安锤2

2999
01:38:42,860 --> 01:38:44,900
是什么是一个安锤2的JS

3000
01:38:44,900 --> 01:38:48,860
可以吧

3001
01:38:48,860 --> 01:38:49,100
好

3002
01:38:49,100 --> 01:38:50,260
你看回到咱们家里来啊

3003
01:38:50,260 --> 01:38:52,860
你看我这个这我来两个入口

3004
01:38:52,860 --> 01:38:55,660
安锤1

3005
01:38:55,660 --> 01:38:56,660
对吧

3006
01:38:56,860 --> 01:38:57,140
试试

3007
01:38:57,140 --> 01:39:01,860
可以吧

3008
01:39:01,860 --> 01:39:05,700
啊 然后呢 安随1依赖什么 依赖了一个抬头1

3009
01:39:05,700 --> 01:39:10,900
抬头1 是吧

3010
01:39:10,900 --> 01:39:12,420
那么

3011
01:39:12,420 --> 01:39:14,780
安随2呢 依赖他到2

3012
01:39:14,780 --> 01:39:19,780
好 抬头2 是吧 可以吧 可以

3013
01:39:19,780 --> 01:39:21,180
那么 然后呢

3014
01:39:21,180 --> 01:39:26,540
他如果你看 如果安随1的抬头1 抬头2的话 你看 他是什么情况呀

3015
01:39:26,860 --> 01:39:28,680
是不是说我安队一里边什么

3016
01:39:28,680 --> 01:39:30,060
引入太多一啊

3017
01:39:30,060 --> 01:39:31,980
安队二里边太多二啊

3018
01:39:31,980 --> 01:39:32,540
对不对

3019
01:39:32,540 --> 01:39:34,840
然后你看他怎么支持啊

3020
01:39:34,840 --> 01:39:35,440
很简单啊

3021
01:39:35,440 --> 01:39:37,300
你看回到咱们这个哪

3022
01:39:37,300 --> 01:39:38,860
是不是有插件啊

3023
01:39:38,860 --> 01:39:39,160
叫什么

3024
01:39:39,160 --> 01:39:41,500
叫安队 option plug in啊

3025
01:39:41,500 --> 01:39:41,740
对吧

3026
01:39:41,740 --> 01:39:42,060
你看这

3027
01:39:42,060 --> 01:39:44,920
正是安队啊

3028
01:39:44,920 --> 01:39:46,020
安队有要判断吧

3029
01:39:46,020 --> 01:39:47,460
安队如果说

3030
01:39:47,460 --> 01:39:48,960
type of

3031
01:39:48,960 --> 01:39:50,800
他是什么

3032
01:39:50,800 --> 01:39:52,280
这个安队啊

3033
01:39:52,280 --> 01:39:53,900
是一个字符刷的话

3034
01:39:53,900 --> 01:39:54,300
是不是

3035
01:39:54,300 --> 01:39:55,800
是不是走这个逻辑啊

3036
01:39:55,800 --> 01:39:57,300
就是单个入口啊

3037
01:39:57,300 --> 01:39:57,700
对吧

3038
01:39:57,700 --> 01:39:59,120
ELS呢

3039
01:39:59,120 --> 01:40:02,080
是不是就是对象啊

3040
01:40:02,080 --> 01:40:02,700
是对象

3041
01:40:02,700 --> 01:40:03,460
对啊

3042
01:40:03,460 --> 01:40:03,960
我们可以怎么样

3043
01:40:03,960 --> 01:40:04,900
复用完吧

3044
01:40:04,900 --> 01:40:05,180
复

3045
01:40:05,180 --> 01:40:05,860
怎么样

3046
01:40:05,860 --> 01:40:06,660
entry

3047
01:40:06,660 --> 01:40:08,260
因什么呀

3048
01:40:08,260 --> 01:40:09,420
这个entry

3049
01:40:09,420 --> 01:40:11,100
当然我这也不能捡entry了啊

3050
01:40:11,100 --> 01:40:12,460
我写个letid吧

3051
01:40:12,460 --> 01:40:13,520
对吧

3052
01:40:13,520 --> 01:40:14,920
或者name吧

3053
01:40:14,920 --> 01:40:15,440
对吧

3054
01:40:15,440 --> 01:40:15,740
name

3055
01:40:15,740 --> 01:40:19,080
这个叫entryname

3056
01:40:19,080 --> 01:40:22,220
是不是要可以迭代它的每个书情啊

3057
01:40:22,220 --> 01:40:23,240
然后每个书情都怎么样

3058
01:40:23,240 --> 01:40:24,540
都去这样走一下

3059
01:40:24,540 --> 01:40:24,820
是吧

3060
01:40:24,820 --> 01:40:25,180
你看

3061
01:40:25,180 --> 01:40:27,060
是不是都这样去走下呀

3062
01:40:27,060 --> 01:40:29,780
都觉得给他

3063
01:40:29,780 --> 01:40:30,120
nue什么

3064
01:40:30,120 --> 01:40:30,880
nue一个这个玩意儿

3065
01:40:30,880 --> 01:40:31,160
是吧

3066
01:40:31,160 --> 01:40:32,780
nue一个

3067
01:40:32,780 --> 01:40:34,980
然后呢

3068
01:40:34,980 --> 01:40:35,640
entree name

3069
01:40:35,640 --> 01:40:36,300
换成什么

3070
01:40:36,300 --> 01:40:36,560
may

3071
01:40:36,560 --> 01:40:38,340
may是摸认的啊

3072
01:40:38,340 --> 01:40:38,960
如果你有名

3073
01:40:38,960 --> 01:40:39,720
就给用你名字

3074
01:40:39,720 --> 01:40:39,920
是不是

3075
01:40:39,920 --> 01:40:41,580
那么这个entree呢

3076
01:40:41,580 --> 01:40:42,060
换什么

3077
01:40:42,060 --> 01:40:43,660
换成一个entree的什么样

3078
01:40:43,660 --> 01:40:44,600
方块

3079
01:40:44,600 --> 01:40:46,180
然后这个entree name

3080
01:40:46,180 --> 01:40:47,620
是不是

3081
01:40:47,620 --> 01:40:48,860
就这么简单

3082
01:40:48,860 --> 01:40:49,700
对吧

3083
01:40:49,700 --> 01:40:51,060
那么看啊

3084
01:40:51,060 --> 01:40:51,720
给大家演示一下

3085
01:40:51,720 --> 01:40:53,080
现在你看

3086
01:40:53,080 --> 01:40:53,760
如果我现在

3087
01:40:53,760 --> 01:40:55,920
重新打包一下

3088
01:40:55,920 --> 01:40:58,060
output

3089
01:40:58,060 --> 01:41:00,160
output改啥了

3090
01:41:00,160 --> 01:41:02,280
这少个豆花吧

3091
01:41:02,280 --> 01:41:08,680
你看是不是生了

3092
01:41:08,680 --> 01:41:09,280
你看生了

3093
01:41:09,280 --> 01:41:10,740
我们看一下啊

3094
01:41:10,740 --> 01:41:12,280
看一下它是不是生了两个文件

3095
01:41:12,280 --> 01:41:13,720
按锤按锤按锤按锤按锤按锤按锤按锤按锤按锤按锤按啊

3096
01:41:13,720 --> 01:41:15,200
好那你如果引入什么

3097
01:41:15,200 --> 01:41:18,200
按锤一 对吧 我来复这一份啊

3098
01:41:18,200 --> 01:41:19,960
所以引入按锤一

3099
01:41:19,960 --> 01:41:23,200
好 再引入一个按锤二

3100
01:41:23,200 --> 01:41:29,200
对吧 你看 你试试

3101
01:41:29,200 --> 01:41:31,200
好 我们先看我们第一个啊

3102
01:41:31,200 --> 01:41:33,200
看我们这个引入按锤一吧

3103
01:41:33,200 --> 01:41:36,200
是打按锤一啊 打印太多一啊

3104
01:41:36,200 --> 01:41:38,200
那么我这个按锤二呢

3105
01:41:38,200 --> 01:41:40,200
是不是打印太多二啊

3106
01:41:40,200 --> 01:41:43,200
我没改啊 没改

3107
01:41:43,200 --> 01:41:45,240
没改我我我哪我没改啊

3108
01:41:45,240 --> 01:41:47,940
应该引入

3109
01:41:47,940 --> 01:41:48,620
应该引入是吧

3110
01:41:48,620 --> 01:41:49,440
应该在JS

3111
01:41:49,440 --> 01:41:52,920
是不是答应太多了

3112
01:41:52,920 --> 01:41:53,300
对不对

3113
01:41:53,300 --> 01:41:55,380
多入口其实实现起来非常简单啊

3114
01:41:55,380 --> 01:41:56,280
就是什么叫多入口

3115
01:41:56,280 --> 01:41:58,420
就是多个单入口对吧啊

3116
01:41:58,420 --> 01:41:59,060
所以就很简单

3117
01:41:59,060 --> 01:42:02,280
这个Defendency里边的Value

3118
01:42:02,280 --> 01:42:03,760
是筷子里边Value什么

3119
01:42:03,760 --> 01:42:04,620
Value什么

3120
01:42:04,620 --> 01:42:07,900
Value是被依赖模块的

3121
01:42:07,900 --> 01:42:11,160
绝对入境

3122
01:42:11,160 --> 01:42:13,180
绝对入境

3123
01:42:13,180 --> 01:42:15,340
对没错

3124
01:42:15,340 --> 01:42:16,860
什么情况下

3125
01:42:16,860 --> 01:42:17,960
一个truck会成多个文件

3126
01:42:17,960 --> 01:42:19,620
一般情况下

3127
01:42:19,620 --> 01:42:20,520
一般

3128
01:42:20,520 --> 01:42:23,180
一般来说

3129
01:42:23,180 --> 01:42:26,500
一个truck会生成一个文件

3130
01:42:26,500 --> 01:42:29,240
但是我们是不是有个speedtrucks

3131
01:42:29,240 --> 01:42:31,280
是不是优化

3132
01:42:31,280 --> 01:42:32,160
我们可以把什么

3133
01:42:32,160 --> 01:42:34,060
比如说把一个truck怎么样

3134
01:42:34,060 --> 01:42:34,780
进行分割

3135
01:42:34,780 --> 01:42:35,120
是不是

3136
01:42:35,120 --> 01:42:38,900
import的话是

3137
01:42:38,900 --> 01:42:40,800
音末是相当于

3138
01:42:40,800 --> 01:42:42,040
它会生多个代码块

3139
01:42:42,040 --> 01:42:45,020
WiPad中输出

3140
01:42:45,020 --> 01:42:46,320
套用的Eden不写了吗

3141
01:42:46,320 --> 01:42:47,540
套用模板ES

3142
01:42:47,540 --> 01:42:50,400
这个不是写了吗

3143
01:42:50,400 --> 01:42:52,360
一部也

3144
01:42:52,360 --> 01:42:53,580
一部的创业条模板

3145
01:42:53,580 --> 01:42:55,240
对一部也会套用创个模板

3146
01:42:55,240 --> 01:42:55,900
对会的

3147
01:42:55,900 --> 01:42:58,480
WiPad也是用ES吗

3148
01:42:58,480 --> 01:42:59,580
原码用ES吗

3149
01:42:59,580 --> 01:43:00,140
不是的

3150
01:43:00,140 --> 01:43:02,200
WiPad不是用的ES

3151
01:43:02,200 --> 01:43:04,460
不是用的ES

3152
01:43:04,460 --> 01:43:06,720
它里边代码也很牛逼啊

3153
01:43:06,720 --> 01:43:07,060
用的什么

3154
01:43:07,060 --> 01:43:07,540
用的是

3155
01:43:07,540 --> 01:43:08,640
用的是

3156
01:43:08,640 --> 01:43:11,860
用的是字无创的数组

3157
01:43:11,860 --> 01:43:13,800
它里边是搞了个数组

3158
01:43:13,800 --> 01:43:14,520
自己拼的

3159
01:43:14,520 --> 01:43:15,820
没用意解释

3160
01:43:15,820 --> 01:43:18,280
故事是写死的

3161
01:43:18,280 --> 01:43:19,640
你看给他看一眼

3162
01:43:19,640 --> 01:43:20,400
给他看一眼圆满

3163
01:43:20,400 --> 01:43:22,840
圆满里边

3164
01:43:22,840 --> 01:43:24,680
圆满里边是不是有个这个东西

3165
01:43:24,680 --> 01:43:27,860
给他看一眼

3166
01:43:27,860 --> 01:43:31,300
圆满再给大家看一下

3167
01:43:31,300 --> 01:43:33,940
给他看圆满

3168
01:43:33,940 --> 01:43:35,960
圆满里边他用的是数组

3169
01:43:35,960 --> 01:43:36,820
用的是自创数组

3170
01:43:36,820 --> 01:43:37,740
不是用的模板引擎

3171
01:43:37,740 --> 01:43:38,680
没有用过问题

3172
01:43:38,680 --> 01:43:41,520
再看一眼啊

3173
01:43:41,520 --> 01:43:43,860
再看一眼这个发射的时候

3174
01:43:43,860 --> 01:43:45,060
在哪

3175
01:43:45,060 --> 01:43:46,080
在这个

3176
01:43:46,080 --> 01:43:49,660
Coreback吧

3177
01:43:49,660 --> 01:43:51,040
可能掉Coreback在这

3178
01:43:51,040 --> 01:43:52,180
掉

3179
01:43:52,180 --> 01:43:52,960
走

3180
01:43:52,960 --> 01:43:54,060
走

3181
01:43:54,060 --> 01:43:55,260
走

3182
01:43:55,260 --> 01:43:56,420
走

3183
01:43:56,420 --> 01:43:58,020
走

3184
01:43:58,020 --> 01:43:58,520
好

3185
01:43:58,520 --> 01:43:59,280
掉Coreback走

3186
01:43:59,280 --> 01:44:00,380
回来了啊

3187
01:44:00,380 --> 01:44:01,980
你在这的话

3188
01:44:01,980 --> 01:44:02,740
你看我要干嘛

3189
01:44:02,740 --> 01:44:04,400
我是不是要去发射文件啊

3190
01:44:04,400 --> 01:44:04,900
对不对

3191
01:44:04,900 --> 01:44:06,720
看他怎么发射的啊

3192
01:44:06,720 --> 01:44:07,660
嗯

3193
01:44:07,660 --> 01:44:08,060
进来

3194
01:44:08,060 --> 01:44:10,960
你看指定输入目录是不是

3195
01:44:10,960 --> 01:44:12,220
然后发射文件

3196
01:44:12,220 --> 01:44:13,460
怎么发射的

3197
01:44:13,460 --> 01:44:14,340
用的是这个库啊

3198
01:44:14,340 --> 01:44:15,160
并发的库是不是

3199
01:44:15,160 --> 01:44:16,460
然后拿到什么

3200
01:44:16,460 --> 01:44:17,140
它是资源

3201
01:44:17,140 --> 01:44:19,660
然后你看啊

3202
01:44:19,660 --> 01:44:20,160
进来

3203
01:44:20,160 --> 01:44:23,360
看了吧

3204
01:44:23,360 --> 01:44:26,480
你看是不是也是拼了一个路径啊

3205
01:44:26,480 --> 01:44:27,460
target pass啊

3206
01:44:27,460 --> 01:44:28,800
然后往里边写是吧

3207
01:44:28,800 --> 01:44:29,980
你看它是往里边写的

3208
01:44:29,980 --> 01:44:32,480
但是其实它的原码生成的在哪

3209
01:44:32,480 --> 01:44:33,500
它在seal阶段啊

3210
01:44:33,500 --> 01:44:34,100
是在seal阶段

3211
01:44:34,100 --> 01:44:35,740
这我们这已经看不见了

3212
01:44:35,740 --> 01:44:37,640
它是在seal阶段生成的

3213
01:44:37,640 --> 01:44:40,680
这个已经过去了 我们直接重新过来啊 它seal阶段生成了

3214
01:44:40,680 --> 01:44:44,700
seal阶段在哪呢 来看一下seal阶段啊

3215
01:44:44,700 --> 01:44:50,480
seal的话应该在这 看在这 它在这个方法里边生成了

3216
01:44:50,480 --> 01:44:57,520
seal 你看进来

3217
01:44:57,520 --> 01:45:00,640
呃 它是这样的 你看啊 它是这样的

3218
01:45:00,640 --> 01:45:03,580
嗯 before trunks 是吧 生用代码块之前

3219
01:45:04,740 --> 01:45:10,140
他是 你看 他是先去升了一些入口点啊 你看也是差不多的代码啊 咱们往下找

3220
01:45:10,140 --> 01:45:18,740
他是什么 勾进一个tronk的一个依赖图啊 然后开始优化啊 优化

3221
01:45:18,740 --> 01:45:23,860
他优化完了以后呢 他会走这个covac

3222
01:45:28,960 --> 01:45:34,560
锅了 锅了 哎呀 刚才 刚才点快点锅了 他其实不说了 他其实用的是 用的数组

3223
01:45:34,560 --> 01:45:35,740
 对吧 对 叔叔

3224
01:45:35,740 --> 01:45:38,120
嗯 说

3225
01:45:38,120 --> 01:45:48,420
好 马上给大家发账户啊 马上发 稍等 马上发 我会把这个客家也放到这个仓库里边去

3226
01:45:48,420 --> 01:45:50,000
 给大家给大家看啊 给大家看

3227
01:45:50,000 --> 01:45:55,760
这个既然再没问题了 我又再把这个问题再给大家演示一下啊 给他说一下他怎么是用代码的

3228
01:45:55,760 --> 01:45:57,360
 是不是 给他看一眼 看一眼啊

3229
01:45:58,020 --> 01:46:02,260
刚才刚才走过了 给大家回来啊 它在这 它其实是在这

3230
01:46:02,260 --> 01:46:10,620
它其实啊 是在这个sale阶段sale阶段 你看啊 在这地方它会有一个代码啊

3231
01:46:10,620 --> 01:46:11,420
 叫什么 叫set

3232
01:46:11,420 --> 01:46:13,660
叫create什么呀

3233
01:46:13,660 --> 01:46:19,990
那什么 看这个create trunk set 看 看这 刚才我是不是也写这方法了 create

3234
01:46:19,990 --> 01:46:22,140
 trunk set 在这写的啊 我们看一下怎么做的

3235
01:46:23,780 --> 01:46:25,520
进来这module的资源是吧

3236
01:46:25,520 --> 01:46:29,020
module的资源我们就先不看了

3237
01:46:29,020 --> 01:46:30,340
咱们先直接看这个

3238
01:46:30,340 --> 01:46:32,440
叫create什么chunk set

3239
01:46:32,440 --> 01:46:34,140
对呢我们是不是这个方法叫什么

3240
01:46:34,140 --> 01:46:35,440
我们是不是也有这方法呀

3241
01:46:35,440 --> 01:46:36,140
对不对

3242
01:46:36,140 --> 01:46:36,980
我们也写的啊

3243
01:46:36,980 --> 01:46:38,240
你看进来就哪这啊

3244
01:46:38,240 --> 01:46:38,740
你看这

3245
01:46:38,740 --> 01:46:40,020
这怎么做的呀

3246
01:46:40,020 --> 01:46:40,580
你看啊

3247
01:46:40,580 --> 01:46:42,880
数中目录对吧

3248
01:46:42,880 --> 01:46:44,740
然后呢是不是拿到每个代码块

3249
01:46:44,740 --> 01:46:47,880
然后生命一个代码块上生命的变量数组啊

3250
01:46:47,880 --> 01:46:49,040
files啊对不对

3251
01:46:49,040 --> 01:46:49,640
拿走

3252
01:46:49,640 --> 01:46:52,640
原代码文件文件是不是有文件模板

3253
01:46:53,240 --> 01:46:58,880
然后这你看啊 判断如果有运营时的话有个模 用什么 用mate template 命运是吧

3254
01:46:58,880 --> 01:47:02,600
 用什么 用这个trunk template 这是什么 这是个主模板 这是懒家带的模板

3255
01:47:02,600 --> 01:47:02,880
 是不是

3256
01:47:02,880 --> 01:47:09,540
今天我们已经来不及讲这个东西了啊 咱们就看了一下 你看 然后他会怎么生了一个manifest的文件啊

3257
01:47:09,540 --> 01:47:12,560
 就是类似我们这个描述文件 是不是 然后走

3258
01:47:12,560 --> 01:47:16,440
然后会怎么样 他会去渲染渲染我们这个

3259
01:47:21,600 --> 01:47:26,540
你看然后你看 转的是不是渲染啊 怎么渲染的 我们进去看看啊 他怎么渲染的啊

3260
01:47:26,540 --> 01:47:30,300
这个类似我们这个yes里面转的方法 对不对 进来看看

3261
01:47:30,300 --> 01:47:38,060
你看 掉了掉我们的collegiate made template 去渲染 对吧 把hashi啊 trunk 什么模块的这个

3262
01:47:38,060 --> 01:47:42,600
依赖啊 传给他是不是啊 传给他 很多什么东西啊 我们进来还在他们渲染的

3263
01:47:42,600 --> 01:47:49,470
看这转的不算不渲染什么渲染 你看 越上的启动部分 启动部分 你看是啥

3264
01:47:49,470 --> 01:47:50,260
 是不是我们这个

3265
01:47:51,160 --> 01:47:52,320
这是啥就是我们这个

3266
01:47:52,320 --> 01:47:54,720
打包货的文件那个头部那个部分啊

3267
01:47:54,720 --> 01:47:55,520
就是就是这部分

3268
01:47:55,520 --> 01:47:56,160
就这部分在吗

3269
01:47:56,160 --> 01:47:57,300
这要不是随碍不是看到了吧

3270
01:47:57,300 --> 01:47:58,220
渲染这部分

3271
01:47:58,220 --> 01:47:59,820
看怎么渲染的呢

3272
01:47:59,820 --> 01:48:00,420
看一眼啊

3273
01:48:00,420 --> 01:48:04,620
嗯在这看啊

3274
01:48:04,620 --> 01:48:05,320
看怎么渲染的

3275
01:48:05,320 --> 01:48:06,720
看进去看看吧

3276
01:48:06,720 --> 01:48:07,600
看进去看看啊

3277
01:48:07,600 --> 01:48:08,760
这是圆码部分

3278
01:48:08,760 --> 01:48:09,360
看到了吧

3279
01:48:09,360 --> 01:48:10,600
看啊进去

3280
01:48:10,600 --> 01:48:13,000
他看到了吧

3281
01:48:13,000 --> 01:48:13,720
他里边是个什么

3282
01:48:13,720 --> 01:48:14,560
是个数堵看到了吧

3283
01:48:14,560 --> 01:48:15,020
数堵

3284
01:48:15,020 --> 01:48:16,760
看啊往里边看啊

3285
01:48:16,760 --> 01:48:17,200
push

3286
01:48:17,200 --> 01:48:20,120
那这货的不是在靠对吧

3287
01:48:21,160 --> 01:48:24,720
走走拿到他的内容是吧 你看打印

3288
01:48:24,720 --> 01:48:34,520
哎你看看他就这么来的 你看啊 他就这么这么来的 就这样一个就这样一个一个拼出来看到了吧

3289
01:48:34,520 --> 01:48:40,220
就这样一个拼出来看到了吧 就在了就在了看看到了吧 就这样来的 你看

3290
01:48:40,220 --> 01:48:44,960
写死的 都是写死的 是不是啊 就是拿这个足处拼这个速度看到了吧

3291
01:48:44,960 --> 01:48:46,140
Timely S3

3292
01:48:46,140 --> 01:48:47,400
你看刚刚是不是

3293
01:48:47,400 --> 01:48:48,980
什么什么WiFi GDB Callback

3294
01:48:48,980 --> 01:48:51,120
然后锁进

3295
01:48:51,120 --> 01:48:52,460
很弱的

3296
01:48:52,460 --> 01:48:54,200
很弱的

3297
01:48:54,200 --> 01:48:55,680
你看就这样写死的

3298
01:48:55,680 --> 01:48:57,260
Jason May Timely Plugin

3299
01:48:57,260 --> 01:48:58,600
就在这定义了

3300
01:48:58,600 --> 01:49:00,040
就这个自握图图

3301
01:49:00,040 --> 01:49:00,480
看到了吧

3302
01:49:00,480 --> 01:49:01,920
没什么神秘的

3303
01:49:01,920 --> 01:49:03,360
Source你看

3304
01:49:03,360 --> 01:49:04,940
返回一个

3305
01:49:04,940 --> 01:49:07,720
走

3306
01:49:07,720 --> 01:49:08,680
返回了

3307
01:49:08,680 --> 01:49:09,740
走

3308
01:49:09,740 --> 01:49:10,260
你看

3309
01:49:10,260 --> 01:49:11,600
你看

3310
01:49:11,600 --> 01:49:12,540
是空窗

3311
01:49:12,540 --> 01:49:13,480
往下走

3312
01:49:13,480 --> 01:49:13,680
你看

3313
01:49:13,680 --> 01:49:15,160
你看往里边放吧

3314
01:49:15,160 --> 01:49:16,560
啊

3315
01:49:16,560 --> 01:49:18,080
function require

3316
01:49:18,080 --> 01:49:21,700
你看是不是是不是在拼这个拼这个租范啊

3317
01:49:21,700 --> 01:49:23,000
被他拼你看在拼

3318
01:49:23,000 --> 01:49:25,100
没谈不得啊

3319
01:49:25,100 --> 01:49:25,760
你看往那拼

3320
01:49:25,760 --> 01:49:28,920
那我这呢是你看启动部分

3321
01:49:28,920 --> 01:49:30,600
还可以用那些勾的是吧

3322
01:49:30,600 --> 01:49:33,880
看八块是吧

3323
01:49:33,880 --> 01:49:34,640
看到了吧

3324
01:49:34,640 --> 01:49:36,460
这是不是就是头部那一部分啊

3325
01:49:36,460 --> 01:49:37,420
对吧

3326
01:49:37,420 --> 01:49:38,300
你看什么什么什么

3327
01:49:38,300 --> 01:49:40,080
这个require function是吧

3328
01:49:40,080 --> 01:49:41,040
什么什么对吧

3329
01:49:41,040 --> 01:49:42,340
下堆走

3330
01:49:42,340 --> 01:49:44,440
然后转点

3331
01:49:44,440 --> 01:49:45,920
开始转点了

3332
01:49:45,920 --> 01:49:48,540
前缀啊什么东西

3333
01:49:48,540 --> 01:49:50,520
反正就是不停的拼这个读书

3334
01:49:50,520 --> 01:49:52,020
一点点拼是吧

3335
01:49:52,020 --> 01:49:52,980
一步拼出来了

3336
01:49:52,980 --> 01:49:54,860
你看sauce

3337
01:49:54,860 --> 01:49:57,340
你看看到了吧

3338
01:49:57,340 --> 01:49:59,440
是不是就是这样一个数读啊

3339
01:49:59,440 --> 01:50:02,040
你看这就是那个模块那部分代码

3340
01:50:02,040 --> 01:50:03,820
是不是什么k啊

3341
01:50:03,820 --> 01:50:05,180
方询啊是不是

3342
01:50:05,180 --> 01:50:07,840
是模块那部分代码

3343
01:50:07,840 --> 01:50:08,800
对不对

3344
01:50:08,800 --> 01:50:12,000
然后拼完的话

3345
01:50:12,000 --> 01:50:19,610
看 到这就完事了 看是不是就sauce 得达到了 拿到了 他的sauce就是咱们的原代码

3346
01:50:19,610 --> 01:50:20,080
 是不是 丘准

3347
01:50:20,080 --> 01:50:26,400
就是就是这个部分啊 对吧 啊 然后最后的核在一起完事了 你看是不是Ami的size啊

3348
01:50:26,400 --> 01:50:30,360
 文件 原代码 是不是啊 文件原代码

3349
01:50:30,360 --> 01:50:36,110
这他原代码 是不是啊 原代码 这方我们是不是也写了 也把它放到我们的速度里边去

3350
01:50:36,110 --> 01:50:39,480
 是吧 蛮事 对不对 好 最后你解答了啊

3351
01:50:42,000 --> 01:50:44,820
好 那个 看一下啊

3352
01:50:44,820 --> 01:50:51,480
哎呦 这么多问题吗

3353
01:50:51,480 --> 01:50:54,280
咱们这个

3354
01:50:54,280 --> 01:50:56,080
来解答一下啊

3355
01:50:56,080 --> 01:50:58,900
解答最后有问题咱们就下课了

3356
01:50:58,900 --> 01:51:04,520
这个

3357
01:51:04,520 --> 01:51:08,620
对 这个问题解决了 罗邓薇的问题我已经解决了

3358
01:51:08,620 --> 01:51:11,740
好

3359
01:51:13,000 --> 01:51:14,600
真正课会讲啊 会讲的啊

3360
01:51:14,600 --> 01:51:18,700
我的嫂子应响了 今天没大喇叭 这个嫂子应响了

3361
01:51:18,700 --> 01:51:20,600
开发

3362
01:51:20,600 --> 01:51:23,700
开发什么是多长时间

3363
01:51:23,700 --> 01:51:27,200
下一期 对吧 好好 欢迎欢迎欢迎

3364
01:51:27,200 --> 01:51:30,100
这是应该正式的

3365
01:51:30,100 --> 01:51:32,300
对对对对

3366
01:51:32,300 --> 01:51:36,300
对

3367
01:51:36,300 --> 01:51:40,100
大量平均消耗性能吧 这个还好啊 还好

3368
01:51:40,700 --> 01:51:42,640
他其实他没有带来拼

3369
01:51:42,640 --> 01:51:44,560
他就是个勾引一个数组是不是

3370
01:51:44,560 --> 01:51:45,700
最后一起拼

3371
01:51:45,700 --> 01:51:47,260
他不是说不停的加加加加拼

3372
01:51:47,260 --> 01:51:48,340
不是不停的加

3373
01:51:48,340 --> 01:51:49,440
他就是勾引一个数组

3374
01:51:49,440 --> 01:51:50,460
然后最后拼在一起啊

3375
01:51:50,460 --> 01:51:54,460
对对比一接他要快一些对吧

3376
01:51:54,460 --> 01:51:56,900
对嗯

3377
01:51:56,900 --> 01:51:58,700
设计太厉害啊

3378
01:51:58,700 --> 01:51:58,940
对啊

3379
01:51:58,940 --> 01:52:00,840
这代码真的是很难维护啊

3380
01:52:00,840 --> 01:52:01,900
很难维护

3381
01:52:01,900 --> 01:52:03,000
发明专利的是吧

3382
01:52:03,000 --> 01:52:03,740
好好

3383
01:52:03,740 --> 01:52:05,140
那个今天课到这了啊

3384
01:52:05,140 --> 01:52:06,700
咱们就少点半了

3385
01:52:06,700 --> 01:52:08,260
咱们该下课了是吧

3386
01:52:08,260 --> 01:52:09,960
好那个感谢大家的参与啊

3387
01:52:09,960 --> 01:52:12,460
感谢 感谢 感谢 感谢 感谢 下课了 下课了啊

3388
01:52:12,460 --> 01:52:17,760
感谢大家 还有43位同学

3389
01:52:17,760 --> 01:52:20,400
以后都是主播的动量 是吧

3390
01:52:20,400 --> 01:52:21,600
很棒啊

3391
01:52:21,600 --> 01:52:25,060
能能跟下来的真的是很棒很棒啊

3392
01:52:25,060 --> 01:52:27,560
以后都是国家的动量 太棒了

