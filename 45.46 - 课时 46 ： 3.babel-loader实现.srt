1
00:00:00,000 --> 00:00:02,340
那本节呢

2
00:00:02,340 --> 00:00:04,260
我们就来实现一下这个bibble loader

3
00:00:04,260 --> 00:00:04,760
那好

4
00:00:04,760 --> 00:00:05,500
那第一步呢

5
00:00:05,500 --> 00:00:07,220
我们就需要安装babel相关的

6
00:00:07,220 --> 00:00:08,600
比如说我们用的babel的话呢

7
00:00:08,600 --> 00:00:09,240
需要babel块

8
00:00:09,240 --> 00:00:10,120
那这里面呢

9
00:00:10,120 --> 00:00:11,360
我就不下载babel loader了

10
00:00:11,360 --> 00:00:12,560
因为我们要自己去实现

11
00:00:12,560 --> 00:00:13,180
好了

12
00:00:13,180 --> 00:00:13,540
我们呢

13
00:00:13,540 --> 00:00:15,260
还需要一个babel presetenv

14
00:00:15,260 --> 00:00:16,940
它是把我们的高版本语法呢

15
00:00:16,940 --> 00:00:18,200
转成我们的D版本语法

16
00:00:18,200 --> 00:00:18,860
es5的

17
00:00:18,860 --> 00:00:19,380
那好

18
00:00:19,380 --> 00:00:19,900
那这里呢

19
00:00:19,900 --> 00:00:20,120
一样

20
00:00:20,120 --> 00:00:21,580
我们可以在这里先安装一下

21
00:00:21,580 --> 00:00:21,940
clear

22
00:00:21,940 --> 00:00:24,040
那么去呀

23
00:00:24,040 --> 00:00:24,600
add

24
00:00:24,600 --> 00:00:26,540
我们先安装babel的核心包

25
00:00:26,540 --> 00:00:27,820
babel-core

26
00:00:27,820 --> 00:00:28,920
完了可能呢

27
00:00:28,920 --> 00:00:30,860
我们还需要这样一个转换包是吧

28
00:00:30,860 --> 00:00:32,220
我们需要安装这个babel

29
00:00:32,220 --> 00:00:34,420
-我们的pre-set-env

30
00:00:34,420 --> 00:00:35,880
在以前都配过了

31
00:00:35,880 --> 00:00:37,420
那这里呢我就直接来用

32
00:00:37,420 --> 00:00:38,680
用的话呢非常方便

33
00:00:38,680 --> 00:00:40,320
我先把这个loader都住掉

34
00:00:40,320 --> 00:00:41,160
因为太多了

35
00:00:41,160 --> 00:00:41,820
我把它住掉

36
00:00:41,820 --> 00:00:43,420
我在上面重新写一个啊

37
00:00:43,420 --> 00:00:44,820
这样的话留个留个别分

38
00:00:44,820 --> 00:00:46,620
那我在这里呢来一个rules

39
00:00:46,620 --> 00:00:49,320
同样我们可以呢去匹配

40
00:00:49,320 --> 00:00:51,620
比如说我需要呢匹配正责

41
00:00:51,620 --> 00:00:53,520
以这个所有的gs接位的

42
00:00:53,520 --> 00:00:55,920
我应该用我们的这个loader是吧

43
00:00:55,920 --> 00:00:57,320
那这个loader呢分别叫什么呢

44
00:00:57,320 --> 00:00:58,620
是不是叫babelloader

45
00:00:58,620 --> 00:00:59,980
我可以写个对象的格式

46
00:00:59,980 --> 00:01:02,220
loader叫babel loader

47
00:01:02,220 --> 00:01:03,820
同样这里面我要跟他说

48
00:01:03,820 --> 00:01:05,660
哎用babel loader转化的时候

49
00:01:05,660 --> 00:01:07,180
需要用上我们的预设

50
00:01:07,180 --> 00:01:08,980
预设的话是不是个数组类型的

51
00:01:08,980 --> 00:01:12,180
这里面我们应该写叫@babel-什么的

52
00:01:12,180 --> 00:01:15,180
preset因为告诉他用他来转化

53
00:01:15,180 --> 00:01:17,380
这时候我们应该再写个这样的文件

54
00:01:17,380 --> 00:01:18,300
叫babel loader

55
00:01:18,300 --> 00:01:19,900
然后我们在loader下

56
00:01:19,900 --> 00:01:23,180
就借上这样一个文件叫babel-loader

57
00:01:23,180 --> 00:01:25,420
OK

58
00:01:25,420 --> 00:01:27,580
现在我们把它打进来是吧

59
00:01:27,580 --> 00:01:28,740
这个叫loader

60
00:01:28,740 --> 00:01:31,080
并且呢我们要把它默认导出sauce

61
00:01:31,080 --> 00:01:33,980
最后呢我们可以在这 return一个sauce

62
00:01:33,980 --> 00:01:36,040
最后呢把这loader也搞定是吧

63
00:01:36,040 --> 00:01:39,580
module.ispos等于我们这样一个sauce

64
00:01:39,580 --> 00:01:41,340
不叫sauce叫loader

65
00:01:41,340 --> 00:01:42,040
那好了啊

66
00:01:42,040 --> 00:01:43,720
那现在我们要做什么事呢

67
00:01:43,720 --> 00:01:46,140
第一步我们需要先拿到它的预设

68
00:01:46,140 --> 00:01:47,380
我要告诉人家babel呢

69
00:01:47,380 --> 00:01:49,580
应该用这个预设呢来转化我们的代码

70
00:01:49,580 --> 00:01:51,520
那这里面肯定需要用的那个babel模块

71
00:01:51,520 --> 00:01:53,340
所以说呢我在这里呢可以很

72
00:01:53,340 --> 00:01:56,140
哎直接把这个babel的模块引进来啊

73
00:01:56,140 --> 00:01:59,140
它就babel等于require的atbabel块

74
00:01:59,140 --> 00:02:00,740
以前我们没有自己去用它的

75
00:02:00,740 --> 00:02:01,220
只知道

76
00:02:01,220 --> 00:02:03,600
为什么我们用babel需要用的这个模块

77
00:02:03,600 --> 00:02:05,660
其实在内部它是用它来转化的

78
00:02:05,660 --> 00:02:06,400
那好了

79
00:02:06,400 --> 00:02:07,740
那除了这个包以后呢

80
00:02:07,740 --> 00:02:08,660
我们需要干嘛呢

81
00:02:08,660 --> 00:02:09,400
是不是要转化呀

82
00:02:09,400 --> 00:02:10,520
那怎么转化呢

83
00:02:10,520 --> 00:02:13,520
是不是要根据当前我们传过来这个参数来转化呀

84
00:02:13,520 --> 00:02:14,960
那我怎么拿到这个参数呢

85
00:02:14,960 --> 00:02:16,140
这参数写的有问题啊

86
00:02:16,140 --> 00:02:17,820
应该叫options中的塔

87
00:02:17,820 --> 00:02:19,780
那这个参数我怎么拿到

88
00:02:19,780 --> 00:02:20,420
那这时候呢

89
00:02:20,420 --> 00:02:21,360
我们需要一个工具库

90
00:02:21,360 --> 00:02:22,520
这个工具库呢

91
00:02:22,520 --> 00:02:23,740
专门可以帮我们干这事

92
00:02:23,740 --> 00:02:24,860
那我在这里呢

93
00:02:24,860 --> 00:02:25,520
就直接安装

94
00:02:25,520 --> 00:02:26,540
叫亚亚的

95
00:02:26,540 --> 00:02:27,740
它的名字也非常好记

96
00:02:27,740 --> 00:02:29,480
叫loader的什么工具类

97
00:02:29,480 --> 00:02:29,780
对吧

98
00:02:29,780 --> 00:02:30,860
-youtils

99
00:02:30,860 --> 00:02:33,740
它里面有一个非常好的方法

100
00:02:33,740 --> 00:02:36,160
就是可以去拿到我们当前传记的参数

101
00:02:36,160 --> 00:02:36,900
那好

102
00:02:36,900 --> 00:02:38,920
我回到我们的bibloader

103
00:02:38,920 --> 00:02:39,600
这里呢

104
00:02:39,600 --> 00:02:40,820
把这个模块引进来啊

105
00:02:40,820 --> 00:02:42,560
它叫loader-youtils

106
00:02:42,560 --> 00:02:43,660
最后呢

107
00:02:43,660 --> 00:02:45,160
我们来转换一下啊

108
00:02:45,160 --> 00:02:45,500
叫什么

109
00:02:45,500 --> 00:02:46,100
叫这个

110
00:02:46,100 --> 00:02:49,080
叫loader-youtils

111
00:02:49,080 --> 00:02:49,400
是吧

112
00:02:49,400 --> 00:02:50,340
没有提示啊

113
00:02:50,340 --> 00:02:51,060
写上

114
00:02:51,060 --> 00:02:53,400
那这时候我们怎么去拿到这个选项呢

115
00:02:53,400 --> 00:02:54,020
非常简单

116
00:02:54,020 --> 00:02:55,240
它里面有个方法

117
00:02:55,240 --> 00:02:57,740
哎叫gat什么呢叫options

118
00:02:57,740 --> 00:02:59,640
来看看效果啊

119
00:02:59,640 --> 00:03:00,680
我把谁放下来呢

120
00:03:00,680 --> 00:03:01,740
这里啊明确

121
00:03:01,740 --> 00:03:04,240
我们loader里面他有个关键字就是this

122
00:03:04,240 --> 00:03:07,840
这的this其实就是我们所谓的叫loader的上下文对吧

123
00:03:07,840 --> 00:03:10,040
这里面我们先来看看这有啥东西

124
00:03:10,040 --> 00:03:12,440
其实东西还不少loader contest

125
00:03:12,440 --> 00:03:14,540
我们在这打印一下

126
00:03:14,540 --> 00:03:16,640
等会我们也会一一来介绍它的用法

127
00:03:16,640 --> 00:03:18,140
这些参数哪些能用上是吧

128
00:03:18,140 --> 00:03:20,240
consolelog我们把sauce打出来

129
00:03:20,240 --> 00:03:21,740
为了能看到它是个对象

130
00:03:21,740 --> 00:03:23,340
我们不是打sauce

131
00:03:23,340 --> 00:03:24,440
应该去打我们的this

132
00:03:24,440 --> 00:03:25,940
这里为了能看到效果

133
00:03:25,940 --> 00:03:28,040
我们可以直接取它的keys

134
00:03:28,040 --> 00:03:29,240
比如说把zs放进来

135
00:03:29,240 --> 00:03:32,440
一样为了方便

136
00:03:32,440 --> 00:03:32,940
好了

137
00:03:32,940 --> 00:03:34,000
我们把zs放在这

138
00:03:34,000 --> 00:03:34,540
是吧

139
00:03:34,540 --> 00:03:35,640
在这看一下效果

140
00:03:35,640 --> 00:03:36,540
我运行一下

141
00:03:36,540 --> 00:03:40,440
在这里面npxwebpack

142
00:03:40,440 --> 00:03:43,340
运行

143
00:03:43,340 --> 00:03:46,140
哦有点报错是吧

144
00:03:46,140 --> 00:03:48,240
他告诉我当前的require拼错了

145
00:03:48,240 --> 00:03:50,240
我把这个哦果然根本没有提示的原因是他

146
00:03:50,240 --> 00:03:50,640
是吧

147
00:03:50,640 --> 00:03:52,340
这回引进来了就应该有提示了

148
00:03:52,340 --> 00:03:53,640
我在这里通信运行一下

149
00:03:54,440 --> 00:03:57,980
看是不是有很多的属性

150
00:03:57,980 --> 00:03:59,340
这个属性里面有很多

151
00:03:59,340 --> 00:04:01,080
比如说当前的loader的索引

152
00:04:01,080 --> 00:04:02,780
loader的有多少个loader

153
00:04:02,780 --> 00:04:03,680
什么loader pass

154
00:04:03,680 --> 00:04:04,260
loader query

155
00:04:04,260 --> 00:04:05,620
在后面会一介绍到

156
00:04:05,620 --> 00:04:07,240
这里面我们可以看到

157
00:04:07,240 --> 00:04:09,480
现在我们是不是能拿到这样一个参数

158
00:04:09,480 --> 00:04:09,880
对吧

159
00:04:09,880 --> 00:04:11,200
我把它先删掉

160
00:04:11,200 --> 00:04:12,600
我把它拿过来

161
00:04:12,600 --> 00:04:13,560
它叫options

162
00:04:13,560 --> 00:04:15,640
我们来看看

163
00:04:15,640 --> 00:04:17,320
是不是有我们所谓的预设

164
00:04:17,320 --> 00:04:18,660
coslog options

165
00:04:18,660 --> 00:04:21,140
往这里我们跑一下

166
00:04:21,140 --> 00:04:24,660
你看确实有这个预设

167
00:04:24,660 --> 00:04:26,480
这时候我们就需要怎么样

168
00:04:26,480 --> 00:04:27,820
核心目会登场了

169
00:04:27,820 --> 00:04:28,880
我需要怎么样

170
00:04:28,880 --> 00:04:30,040
用它来怎么样

171
00:04:30,040 --> 00:04:30,480
转化

172
00:04:30,480 --> 00:04:32,540
它的转化方法里面有几个参数

173
00:04:32,540 --> 00:04:33,820
第一你要告诉人家

174
00:04:33,820 --> 00:04:35,240
你要转化是哪一个选项

175
00:04:35,240 --> 00:04:36,040
哪个代码

176
00:04:36,040 --> 00:04:36,960
肯定是圆码

177
00:04:36,960 --> 00:04:38,980
第二个我们要告诉人家

178
00:04:38,980 --> 00:04:40,200
转化的选项哪些

179
00:04:40,200 --> 00:04:42,520
这里面它就有一个参数叫precess

180
00:04:42,520 --> 00:04:43,960
这precess哪来的

181
00:04:43,960 --> 00:04:46,500
其实就是我们的options点什么precess

182
00:04:46,500 --> 00:04:47,940
当然了可能还有插件

183
00:04:47,940 --> 00:04:49,540
为了方便可以怎么做

184
00:04:49,540 --> 00:04:50,660
它是个对象了

185
00:04:50,660 --> 00:04:51,740
那么可以把对象怎么样

186
00:04:51,740 --> 00:04:52,520
直接展开

187
00:04:52,520 --> 00:04:55,260
那同样我们都知道啊

188
00:04:55,260 --> 00:04:57,880
如果要是用我们这样一个babel来变译

189
00:04:57,880 --> 00:04:59,460
那肯定需要有个东西的吧

190
00:04:59,460 --> 00:04:59,960
很重要

191
00:04:59,960 --> 00:05:01,440
要不调试的时候没有调试了

192
00:05:01,440 --> 00:05:02,920
那就是我们的sauce map

193
00:05:02,920 --> 00:05:04,600
那这时候啊

194
00:05:04,600 --> 00:05:05,460
我们拿完以后呢

195
00:05:05,460 --> 00:05:06,380
它会有个回调了

196
00:05:06,380 --> 00:05:06,500
哎

197
00:05:06,500 --> 00:05:07,400
但是你看啊

198
00:05:07,400 --> 00:05:09,100
这个转化过程一看就是一步的

199
00:05:09,100 --> 00:05:10,060
但是这个时候呢

200
00:05:10,060 --> 00:05:12,060
我们是不是同步的返回肯定不行了

201
00:05:12,060 --> 00:05:12,940
所以说呀

202
00:05:12,940 --> 00:05:14,680
这时候我们需要学个API

203
00:05:14,680 --> 00:05:15,660
这个API呢

204
00:05:15,660 --> 00:05:16,820
可以帮我们实现什么

205
00:05:16,820 --> 00:05:18,240
就是一步的回调

206
00:05:18,240 --> 00:05:19,940
那就不能写return了

207
00:05:19,940 --> 00:05:21,700
不能把 return 放到这里是吧

208
00:05:21,700 --> 00:05:22,260
那好

209
00:05:22,260 --> 00:05:23,220
那我在这里呢

210
00:05:23,220 --> 00:05:24,620
直接就用一下

211
00:05:24,620 --> 00:05:25,220
它叫什么呢

212
00:05:25,220 --> 00:05:25,900
叫 cb

213
00:05:25,900 --> 00:05:26,980
那怎么来的呢

214
00:05:26,980 --> 00:05:28,300
叫 this 第二个 think

215
00:05:28,300 --> 00:05:29,300
其实内部啊

216
00:05:29,300 --> 00:05:30,580
它就会有一个这样的变量

217
00:05:30,580 --> 00:05:31,580
默认这个变量呢

218
00:05:31,580 --> 00:05:33,180
可能叫什么是同步执行

219
00:05:33,180 --> 00:05:33,580
对吧

220
00:05:33,580 --> 00:05:34,420
同步的话呢

221
00:05:34,420 --> 00:05:36,140
就会默认去掉这个函数

222
00:05:36,140 --> 00:05:37,460
那如果是一步的话呢

223
00:05:37,460 --> 00:05:39,100
它就会把这个 cb 给你

224
00:05:39,100 --> 00:05:39,980
你一掉怎么样

225
00:05:39,980 --> 00:05:40,700
它就执行

226
00:05:40,700 --> 00:05:41,580
所以这里面啊

227
00:05:41,580 --> 00:05:43,340
我就什么时候执行完

228
00:05:43,340 --> 00:05:45,420
我就在这掉什么 cb 就好了

229
00:05:45,420 --> 00:05:47,220
那这里面有什么呢

230
00:05:47,220 --> 00:05:47,860
有 error

231
00:05:47,860 --> 00:05:49,900
还有我们刚才说的这个叫 result

232
00:05:49,900 --> 00:05:50,740
结果

233
00:05:50,740 --> 00:05:52,600
那rayzout里面有什么东西呢

234
00:05:52,600 --> 00:05:53,100
咱可以看看

235
00:05:53,100 --> 00:05:55,160
第一我们说了这个cb明确

236
00:05:55,160 --> 00:05:56,340
因为是异步的对吧

237
00:05:56,340 --> 00:05:57,000
异步的

238
00:05:57,000 --> 00:05:59,380
如果要你在这个第一个参数里面随便贴点值

239
00:05:59,380 --> 00:06:00,840
他会认为这是个错误

240
00:06:00,840 --> 00:06:01,920
咱来看看是不是这样

241
00:06:01,920 --> 00:06:04,100
这里面的rayzout其实就失效了

242
00:06:04,100 --> 00:06:05,180
写上一排写

243
00:06:05,180 --> 00:06:06,120
运行

244
00:06:06,120 --> 00:06:09,520
你看到是不是报错了

245
00:06:09,520 --> 00:06:10,660
说这东西几乎失败了

246
00:06:10,660 --> 00:06:12,320
说有一个不正常的发射系

247
00:06:12,320 --> 00:06:12,540
对吧

248
00:06:12,540 --> 00:06:13,200
有一个错误

249
00:06:13,200 --> 00:06:13,920
错误是123

250
00:06:13,920 --> 00:06:16,260
所以这东西我们第一个参数永远是错误

251
00:06:16,260 --> 00:06:17,000
没有错误的话

252
00:06:17,000 --> 00:06:18,300
应该放应该放这个是吧

253
00:06:18,300 --> 00:06:19,300
看转换没有报错

254
00:06:19,300 --> 00:06:21,220
那同样第二个参数呢

255
00:06:21,220 --> 00:06:22,860
就应该是我们结果中的什么东西

256
00:06:22,860 --> 00:06:23,440
有code

257
00:06:23,440 --> 00:06:25,480
code呀就是我们编译出来的什么

258
00:06:25,480 --> 00:06:26,080
那个代码

259
00:06:26,080 --> 00:06:27,940
也就是把es6编译成es5

260
00:06:27,940 --> 00:06:29,360
那同样他这里面呢

261
00:06:29,360 --> 00:06:30,520
还有个叫result

262
00:06:30,520 --> 00:06:31,320
里面有个什么东西呢

263
00:06:31,320 --> 00:06:31,880
有个叫map

264
00:06:31,880 --> 00:06:33,680
map就是我们所谓的那个saucemap

265
00:06:33,680 --> 00:06:35,740
就是说我们当前这个cb里面

266
00:06:35,740 --> 00:06:36,980
应该传这么几个参数

267
00:06:36,980 --> 00:06:37,720
第一个是错误

268
00:06:37,720 --> 00:06:38,480
第二是代码

269
00:06:38,480 --> 00:06:40,180
第三个呢是我们的saucemap

270
00:06:40,180 --> 00:06:41,520
那都传完以后

271
00:06:41,520 --> 00:06:42,460
这return就不要了

272
00:06:42,460 --> 00:06:43,260
写上浪费时间

273
00:06:43,260 --> 00:06:44,980
那好在这里面跑一下

274
00:06:44,980 --> 00:06:47,500
看看是不是可以实现呢

275
00:06:47,500 --> 00:06:49,540
这时候确实转化好了

276
00:06:49,540 --> 00:06:50,540
还有一个inlan loader

277
00:06:50,540 --> 00:06:51,500
为了方便

278
00:06:51,500 --> 00:06:52,800
我把这inlan loader也住掉

279
00:06:52,800 --> 00:06:54,680
我写个es6

280
00:06:54,680 --> 00:06:55,780
看看es5有没有

281
00:06:55,780 --> 00:06:57,280
比如说来个class

282
00:06:57,280 --> 00:06:58,680
叫珠峰培训

283
00:06:58,680 --> 00:06:59,580
珠峰培训

284
00:06:59,580 --> 00:07:00,300
完了

285
00:07:00,300 --> 00:07:02,360
在这里我们可以在这来个contrater

286
00:07:02,360 --> 00:07:05,120
完了我们在这里来个this.name

287
00:07:05,120 --> 00:07:06,340
等于珠峰

288
00:07:06,340 --> 00:07:07,600
完了

289
00:07:07,600 --> 00:07:09,860
并且我们可以来个随便携带

290
00:07:09,860 --> 00:07:11,000
来getname

291
00:07:11,000 --> 00:07:12,940
return叫this.name

292
00:07:12,940 --> 00:07:15,340
完了

293
00:07:15,340 --> 00:07:16,480
最后我们可以在这里

294
00:07:16,480 --> 00:07:18,120
去拗他一下是吧

295
00:07:18,120 --> 00:07:19,940
来他一个珠峰等于

296
00:07:19,940 --> 00:07:22,180
拗一个珠峰培训

297
00:07:22,180 --> 00:07:24,740
完并且我们来个珠峰点概念是吧

298
00:07:24,740 --> 00:07:25,380
OK

299
00:07:25,380 --> 00:07:26,740
完了为了能看到效果吧

300
00:07:26,740 --> 00:07:28,580
我也把这个cancel打印一下

301
00:07:28,580 --> 00:07:30,880
看看值能不能过来

302
00:07:30,880 --> 00:07:31,840
把它放在这

303
00:07:31,840 --> 00:07:34,080
完了里面我来运行一下

304
00:07:34,080 --> 00:07:36,420
完了可以看一下

305
00:07:36,420 --> 00:07:37,680
是不是能实现我们的功能

306
00:07:37,680 --> 00:07:38,580
OK

307
00:07:38,580 --> 00:07:40,040
看看我们的sauce map有没有

308
00:07:40,040 --> 00:07:40,840
发现没有

309
00:07:40,840 --> 00:07:41,740
因为我们说了

310
00:07:41,740 --> 00:07:44,520
你不能光在loader中配置sauce map

311
00:07:44,520 --> 00:07:45,340
你还需要干嘛

312
00:07:45,340 --> 00:07:47,000
是不是在我们的配置文件里面

313
00:07:47,000 --> 00:07:48,600
你也不能得加上这样一个属性

314
00:07:48,600 --> 00:07:49,380
叫dv tool

315
00:07:49,380 --> 00:07:50,820
但是他说了有四种模式

316
00:07:50,820 --> 00:07:51,300
是吧

317
00:07:51,300 --> 00:07:51,860
tool

318
00:07:51,860 --> 00:07:52,920
完了里面

319
00:07:52,920 --> 00:07:55,340
你就可以给他一个叫sauce-map

320
00:07:55,340 --> 00:07:57,380
告诉他需要远马映射

321
00:07:57,380 --> 00:07:59,460
这时候我们bybook转化的时候

322
00:07:59,460 --> 00:08:00,240
才会怎么样

323
00:08:00,240 --> 00:08:02,080
去通过这样一个sauce-map来生成

324
00:08:02,080 --> 00:08:03,360
我们来运行一下

325
00:08:03,360 --> 00:08:04,640
看看效果

326
00:08:04,640 --> 00:08:05,560
是不是ok的

327
00:08:05,560 --> 00:08:06,540
好一下

328
00:08:06,540 --> 00:08:08,300
你发现这时候没有包错

329
00:08:08,300 --> 00:08:09,860
同样他说一个build

330
00:08:09,860 --> 00:08:10,540
还有个叫什么

331
00:08:10,540 --> 00:08:12,060
叫build.js.map

332
00:08:12,060 --> 00:08:13,440
这就是我们的远马映射文件

333
00:08:13,440 --> 00:08:14,280
好了

334
00:08:14,280 --> 00:08:19,920
好了 我们来看看吧 能不能跑一下啊 我在这里呢 新建一个html 要不看不到效果

335
00:08:19,920 --> 00:08:27,440
完了里面呢 我就把我们当前的这样一个东西引进来 script 我们就引一下我们这样一个掉杠

336
00:08:27,440 --> 00:08:32,200
build.js 完了 我们跑一跑 是吧 看看能不能达到我们的预期啊 运行

337
00:08:32,200 --> 00:08:39,280
这里面呢 应该就会出现一个圆满映射就 source map 是吧 那好 net source

338
00:08:39,280 --> 00:08:41,160
 是吧 这里sauce 这刷新

339
00:08:41,680 --> 00:08:42,680
他看外派看里面没有

340
00:08:42,680 --> 00:08:44,360
发现确实是有的

341
00:08:44,360 --> 00:08:45,960
你看克拉的公平是我的圆码

342
00:08:45,960 --> 00:08:47,660
但是他的名字很怪异

343
00:08:47,660 --> 00:08:48,120
叫安诺

344
00:08:48,120 --> 00:08:49,880
这时候我们需要干嘛

345
00:08:49,880 --> 00:08:51,340
我们需要干一件事

346
00:08:51,340 --> 00:08:53,540
就是要给这south map指定个名字

347
00:08:53,540 --> 00:08:54,580
要不这不找不到了

348
00:08:54,580 --> 00:08:56,060
不知道是谁了

349
00:08:56,060 --> 00:08:57,160
好我们可以在这里

350
00:08:57,160 --> 00:08:58,440
再加上一个属性叫什么

351
00:08:58,440 --> 00:08:59,320
叫feel name

352
00:08:59,320 --> 00:09:00,300
feel name什么意思

353
00:09:00,300 --> 00:09:01,840
就是文件名

354
00:09:01,840 --> 00:09:02,740
文件名

355
00:09:02,740 --> 00:09:04,480
好了这feel name怎么来的

356
00:09:04,480 --> 00:09:06,460
然后就叫this点什么

357
00:09:06,460 --> 00:09:07,780
我们都知道

358
00:09:07,780 --> 00:09:09,520
其实他里面this有很多属性

359
00:09:09,520 --> 00:09:12,060
这就是其中的一个叫request pass

360
00:09:12,060 --> 00:09:13,780
我们可以看一看

361
00:09:13,780 --> 00:09:15,560
这个request pass是什么东西

362
00:09:15,560 --> 00:09:17,720
它其实就是当前我们

363
00:09:17,720 --> 00:09:19,460
你要处理这个文件的绝对路径

364
00:09:19,460 --> 00:09:20,440
好来试试

365
00:09:20,440 --> 00:09:21,240
coslog

366
00:09:21,240 --> 00:09:23,160
打过来

367
00:09:23,160 --> 00:09:24,500
在这运行一下

368
00:09:24,500 --> 00:09:26,800
看个效果

369
00:09:26,800 --> 00:09:28,640
写错了

370
00:09:28,640 --> 00:09:30,280
不叫request

371
00:09:30,280 --> 00:09:31,340
叫resource

372
00:09:31,340 --> 00:09:31,960
对吧

373
00:09:31,960 --> 00:09:33,000
resource pass

374
00:09:33,000 --> 00:09:33,660
写错了

375
00:09:33,660 --> 00:09:35,180
这里一样改过来

376
00:09:35,180 --> 00:09:36,240
把它改了

377
00:09:36,240 --> 00:09:37,320
再运行一下

378
00:09:37,320 --> 00:09:39,720
它就是一个绝对路径

379
00:09:39,720 --> 00:09:41,520
还是有问题

380
00:09:41,520 --> 00:09:43,220
名字不太对

381
00:09:43,220 --> 00:09:45,760
叫I-E-S-O-U-R-C

382
00:09:45,760 --> 00:09:46,200
是吧

383
00:09:46,200 --> 00:09:47,080
不太对

384
00:09:47,080 --> 00:09:48,160
resource path

385
00:09:48,160 --> 00:09:49,440
写的时候注意点

386
00:09:49,440 --> 00:09:50,360
我再运行

387
00:09:50,360 --> 00:09:53,660
它就是个绝对路径

388
00:09:53,660 --> 00:09:53,920
你看

389
00:09:53,920 --> 00:09:54,760
是不是绝对路径

390
00:09:54,760 --> 00:09:56,960
我是不是应该把这个名字取出来

391
00:09:56,960 --> 00:09:58,540
作为它的source path的名字

392
00:09:58,540 --> 00:09:59,640
很好办了

393
00:09:59,640 --> 00:10:00,400
非常简单

394
00:10:00,400 --> 00:10:01,100
你可以在这

395
00:10:01,100 --> 00:10:01,700
第二什么

396
00:10:01,700 --> 00:10:02,420
第二split

397
00:10:02,420 --> 00:10:03,940
我需要取最后一段

398
00:10:03,940 --> 00:10:04,480
是吧

399
00:10:04,480 --> 00:10:05,300
杠风格

400
00:10:05,300 --> 00:10:06,660
杠风格取出来的

401
00:10:06,660 --> 00:10:07,280
是不是一个数组

402
00:10:07,280 --> 00:10:08,960
数组的什么最后一段

403
00:10:08,960 --> 00:10:09,700
我可以怎么办

404
00:10:09,700 --> 00:10:10,780
直接点泡

405
00:10:10,780 --> 00:10:13,840
好我在这泡一下

406
00:10:13,840 --> 00:10:14,560
一起看看效果

407
00:10:14,560 --> 00:10:16,540
能不能实现

408
00:10:16,540 --> 00:10:18,080
刷新了

409
00:10:18,080 --> 00:10:19,080
我在这刷新一下

410
00:10:19,080 --> 00:10:20,500
你看我当前的里面

411
00:10:20,500 --> 00:10:22,100
是不是就叫index.js了

412
00:10:22,100 --> 00:10:23,780
这样我的名字就对了

413
00:10:23,780 --> 00:10:23,960
是不是

414
00:10:23,960 --> 00:10:25,940
我一个叫支援码的是吧

415
00:10:25,940 --> 00:10:27,240
ok这样的话

416
00:10:27,240 --> 00:10:29,180
我们的一个bibble loader就实现了

417
00:10:29,180 --> 00:10:31,160
接下来我们就来说一下

418
00:10:31,160 --> 00:10:34,800
怎么去使用这些更多的API

