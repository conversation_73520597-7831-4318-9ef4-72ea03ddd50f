1
00:00:00,000 --> 00:00:04,580
好 我们把刚才的代码呢 再移上去 是吧 这是上面同步的前四个

2
00:00:04,580 --> 00:00:09,220
完了 我们再来说一下 刚才呀 我们说了 这个学习的过程啊 可能 哎

3
00:00:09,220 --> 00:00:11,120
 我可能边学react边学node

4
00:00:11,120 --> 00:00:14,040
就是我们希望啊 这两个功能可以怎么样 可以并发

5
00:00:14,040 --> 00:00:18,260
那这时候啊 肯定少不了我们的一步了 哎 包括我们有很多需求 对吧 比如说

6
00:00:18,260 --> 00:00:22,780
像我们这个开发中 同时呢 多个什么 就是发送

7
00:00:22,780 --> 00:00:24,520
多个请求

8
00:00:24,520 --> 00:00:29,880
完了 干嘛呢 我希望这些请求啊 都发送完以后 完了 我执行回调

9
00:00:29,880 --> 00:00:31,160
说这时候都OK了

10
00:00:31,160 --> 00:00:32,700
那这时候我要怎么做呢

11
00:00:32,700 --> 00:00:33,440
非常简单

12
00:00:33,440 --> 00:00:35,760
我们就需要一些异步的勾子了

13
00:00:35,760 --> 00:00:37,720
而且异步其实也分为两种

14
00:00:37,720 --> 00:00:38,880
描述一下

15
00:00:38,880 --> 00:00:41,220
异步的勾子

16
00:00:41,220 --> 00:00:43,520
分别有两种

17
00:00:43,520 --> 00:00:44,620
第一种叫什么呢

18
00:00:44,620 --> 00:00:45,200
叫串行

19
00:00:45,200 --> 00:00:46,620
什么意思呢

20
00:00:46,620 --> 00:00:48,280
就是我们第一个异步回调执行完

21
00:00:48,280 --> 00:00:49,640
再执行第二个

22
00:00:49,640 --> 00:00:50,620
那还有一种叫什么呢

23
00:00:50,620 --> 00:00:51,420
叫并行

24
00:00:51,420 --> 00:00:53,680
那这个肯定是并行的性能更好

25
00:00:53,680 --> 00:00:54,340
但是呢

26
00:00:54,340 --> 00:00:55,120
有一些需求呢

27
00:00:55,120 --> 00:00:55,960
就是像我们刚才

28
00:00:55,960 --> 00:00:57,480
是不是只有第一个学完以后才知道

29
00:00:57,480 --> 00:00:58,900
第一个人的结果是什么样

30
00:00:58,900 --> 00:01:00,580
那这种可能就需要串行

31
00:01:00,580 --> 00:01:01,800
那并行的话呢

32
00:01:01,800 --> 00:01:02,620
我们可能就需要

33
00:01:02,620 --> 00:01:03,840
对吧

34
00:01:03,840 --> 00:01:05,400
等待所有怎么样

35
00:01:05,400 --> 00:01:07,340
所有并发的

36
00:01:07,340 --> 00:01:08,620
异步实践

37
00:01:08,620 --> 00:01:11,000
执行后

38
00:01:11,000 --> 00:01:11,740
对吧

39
00:01:11,740 --> 00:01:13,100
再执行

40
00:01:13,100 --> 00:01:14,360
回调方法

41
00:01:14,360 --> 00:01:16,000
什么意思

42
00:01:16,000 --> 00:01:16,560
咱来看看

43
00:01:16,560 --> 00:01:17,660
光说可能听起来

44
00:01:17,660 --> 00:01:18,800
不是很容易理解

45
00:01:18,800 --> 00:01:19,560
那这里呢

46
00:01:19,560 --> 00:01:20,400
我们就来一个叫

47
00:01:20,400 --> 00:01:21,020
async

48
00:01:21,020 --> 00:01:22,160
parahook

49
00:01:22,160 --> 00:01:24,480
就叫异步并行的勾子

50
00:01:24,480 --> 00:01:25,200
那这里呢

51
00:01:25,200 --> 00:01:25,820
我也new一下

52
00:01:25,820 --> 00:01:28,140
那现在这位探索就不要了

53
00:01:28,140 --> 00:01:29,880
现在我们需要干嘛呢

54
00:01:29,880 --> 00:01:31,640
希望他们两个可以同时执行

55
00:01:31,640 --> 00:01:33,180
那执行的时候怎么样

56
00:01:33,180 --> 00:01:34,160
是不是比如在这里

57
00:01:34,160 --> 00:01:35,060
我来个订阅器吧

58
00:01:35,060 --> 00:01:36,120
set them out

59
00:01:36,120 --> 00:01:38,440
来个一千

60
00:01:38,440 --> 00:01:40,340
放在这

61
00:01:40,340 --> 00:01:41,960
同时这里也一样

62
00:01:41,960 --> 00:01:43,160
一千

63
00:01:43,160 --> 00:01:46,900
那现在我们执行的时候

64
00:01:46,900 --> 00:01:47,380
你会发现

65
00:01:47,380 --> 00:01:49,340
我们是用什么来注册的呢

66
00:01:49,340 --> 00:01:50,640
是不是用当前这样一个

67
00:01:50,640 --> 00:01:51,800
一步勾子来注册的

68
00:01:51,800 --> 00:01:53,340
那此时运行会报错

69
00:01:53,340 --> 00:01:55,260
他说当前这样一个方法

70
00:01:55,260 --> 00:01:56,380
没有靠方法

71
00:01:56,380 --> 00:01:57,960
那一步的方法

72
00:01:57,960 --> 00:01:59,600
需要通过一个方法来执行

73
00:01:59,600 --> 00:02:00,780
这个方法叫什么

74
00:02:00,780 --> 00:02:02,440
并不叫我们以前的靠了

75
00:02:02,440 --> 00:02:03,140
应该叫靠

76
00:02:03,140 --> 00:02:04,660
现在是异步的了

77
00:02:04,660 --> 00:02:06,300
叫调动异步方法

78
00:02:06,300 --> 00:02:07,600
并且我可以传参数

79
00:02:07,600 --> 00:02:08,780
和以前还是一样的

80
00:02:08,780 --> 00:02:09,840
这名字我也改过去

81
00:02:09,840 --> 00:02:12,440
现在来看效果

82
00:02:12,440 --> 00:02:14,740
如果我们注册的是异步钩子

83
00:02:14,740 --> 00:02:15,620
那肯定怎么样

84
00:02:15,620 --> 00:02:17,960
当这两个方法都执行完以后

85
00:02:17,960 --> 00:02:19,020
我肯定要做哪些事

86
00:02:19,020 --> 00:02:20,720
你肯定需要给他提供一个什么

87
00:02:20,720 --> 00:02:21,320
回调

88
00:02:21,320 --> 00:02:22,960
这里面比如说

89
00:02:22,960 --> 00:02:24,920
当我们node read都学完以后

90
00:02:24,920 --> 00:02:27,020
我可以加上一个n的方法

91
00:02:27,020 --> 00:02:29,140
结束的方法

92
00:02:29,140 --> 00:02:30,980
好我在这里再运行一下

93
00:02:30,980 --> 00:02:33,760
看有问题是吧

94
00:02:33,760 --> 00:02:34,820
现在怎么执行的

95
00:02:34,820 --> 00:02:37,320
他会先掉这个回调是吧

96
00:02:37,320 --> 00:02:39,060
回调完了再去干嘛呀

97
00:02:39,060 --> 00:02:39,580
执行一步

98
00:02:39,580 --> 00:02:41,080
这个一步因为他要怎么样

99
00:02:41,080 --> 00:02:43,200
是等待一秒之后才会成功

100
00:02:43,200 --> 00:02:45,480
所以这时候我们发现一个问题

101
00:02:45,480 --> 00:02:47,340
就是使用type注册的方法

102
00:02:47,340 --> 00:02:49,120
他并不能实现我们的一步

103
00:02:49,120 --> 00:02:52,300
你说我们希望是他和他同时执行完

104
00:02:52,300 --> 00:02:53,460
再去执行的

105
00:02:53,460 --> 00:02:55,140
那这时候我要怎么做呢

106
00:02:55,140 --> 00:02:56,520
就不要用type了

107
00:02:56,520 --> 00:02:57,600
我们描述一下

108
00:02:57,600 --> 00:02:58,520
我执行方法

109
00:02:58,520 --> 00:02:59,960
执行方法

110
00:02:59,960 --> 00:03:01,860
执行方法

111
00:03:01,860 --> 00:03:02,120
对吧

112
00:03:02,120 --> 00:03:04,500
分为叫type注册

113
00:03:04,500 --> 00:03:06,140
注册方法

114
00:03:06,140 --> 00:03:07,860
注册方法

115
00:03:07,860 --> 00:03:10,020
type注册

116
00:03:10,020 --> 00:03:11,160
好了还有呢

117
00:03:11,160 --> 00:03:12,500
就是我们刚才要讲的

118
00:03:12,500 --> 00:03:13,780
这个叫type和sync

119
00:03:13,780 --> 00:03:15,940
说我们注册的时候

120
00:03:15,940 --> 00:03:17,020
如果用type注册

121
00:03:17,020 --> 00:03:17,760
就是同步的

122
00:03:17,760 --> 00:03:18,860
我们那是写它原理了

123
00:03:18,860 --> 00:03:19,800
有没有一个缝循环

124
00:03:19,800 --> 00:03:21,780
那如果要type和sync

125
00:03:21,780 --> 00:03:23,180
它就需要怎么样

126
00:03:23,180 --> 00:03:23,960
改一下

127
00:03:23,960 --> 00:03:24,700
不叫type

128
00:03:24,700 --> 00:03:25,540
叫type和sync

129
00:03:25,540 --> 00:03:28,220
并且里面会加上一个所谓的回调

130
00:03:28,220 --> 00:03:31,020
说有这个回调

131
00:03:31,020 --> 00:03:33,140
就可以标识当前一步

132
00:03:33,140 --> 00:03:34,040
什么时候执行完

133
00:03:34,040 --> 00:03:35,620
比如说过了一秒之后

134
00:03:35,620 --> 00:03:36,940
我再去调用我们的CD

135
00:03:36,940 --> 00:03:39,040
表示一秒之后他执行完了

136
00:03:39,040 --> 00:03:40,940
同样那每个人怎么样

137
00:03:40,940 --> 00:03:42,480
他身上都会有这样一个回调

138
00:03:42,480 --> 00:03:43,820
这样也加上

139
00:03:43,820 --> 00:03:46,020
这样来做

140
00:03:46,020 --> 00:03:47,120
那现在好了

141
00:03:47,120 --> 00:03:49,760
那如果其中任何一个人没有执行完

142
00:03:49,760 --> 00:03:51,760
那此时最后这个回调

143
00:03:51,760 --> 00:03:53,000
永远都不会执行

144
00:03:53,000 --> 00:03:54,220
这样个效果

145
00:03:54,220 --> 00:03:54,780
你看看

146
00:03:54,780 --> 00:03:55,400
过一秒

147
00:03:55,400 --> 00:03:56,280
可能这个时候

148
00:03:56,280 --> 00:03:57,320
node write多完事了

149
00:03:57,320 --> 00:03:59,180
但是发现有个问题是吧

150
00:03:59,180 --> 00:04:00,300
又没有执行办

151
00:04:00,300 --> 00:04:01,380
那也就是说呀

152
00:04:01,380 --> 00:04:03,840
只有我们两个cb都执行完了

153
00:04:03,840 --> 00:04:04,980
此时这个函数怎么样

154
00:04:04,980 --> 00:04:05,820
才会执行

155
00:04:05,820 --> 00:04:08,340
那好了

156
00:04:08,340 --> 00:04:09,560
那这时候原理是什么样的

157
00:04:09,560 --> 00:04:10,240
但是这样的

158
00:04:10,240 --> 00:04:11,140
就是我们呀

159
00:04:11,140 --> 00:04:12,500
每次对吧

160
00:04:12,500 --> 00:04:13,460
调完一个异步

161
00:04:13,460 --> 00:04:14,840
就执行一个函数

162
00:04:14,840 --> 00:04:16,600
这个函数里面会过记录器

163
00:04:16,600 --> 00:04:17,680
他会判断

164
00:04:17,680 --> 00:04:18,500
如果呀

165
00:04:18,500 --> 00:04:19,460
这个记录器的数量

166
00:04:19,460 --> 00:04:21,720
等于了我们当前注册回调的数量

167
00:04:21,720 --> 00:04:22,220
那好

168
00:04:22,220 --> 00:04:23,440
就会让最终的回调怎么样

169
00:04:23,440 --> 00:04:24,000
执行

170
00:04:24,000 --> 00:04:24,960
那好了

171
00:04:24,960 --> 00:04:25,580
这里面呢

172
00:04:25,580 --> 00:04:26,480
我就把这代码呢

173
00:04:26,480 --> 00:04:27,520
稍微改一下

174
00:04:27,520 --> 00:04:30,960
完这里面不叫sync了

175
00:04:30,960 --> 00:04:31,160
对吧

176
00:04:31,160 --> 00:04:32,400
应该叫ersync了

177
00:04:32,400 --> 00:04:32,720
对吧

178
00:04:32,720 --> 00:04:33,640
一步了了

179
00:04:33,640 --> 00:04:34,620
完了ersync了什么呢

180
00:04:34,620 --> 00:04:37,120
叫pyril是吧

181
00:04:37,120 --> 00:04:37,940
pyril

182
00:04:37,940 --> 00:04:40,780
hook

183
00:04:40,780 --> 00:04:43,600
叫一步并行的钩子

184
00:04:43,600 --> 00:04:43,880
是吧

185
00:04:43,880 --> 00:04:44,940
这里也一样啊

186
00:04:44,940 --> 00:04:45,240
放在这

187
00:04:45,240 --> 00:04:46,520
那此时啊

188
00:04:46,520 --> 00:04:47,580
我这名字我就删掉了

189
00:04:47,580 --> 00:04:48,740
其实其他代码呀

190
00:04:48,740 --> 00:04:49,420
没有变是吧

191
00:04:49,420 --> 00:04:50,640
还是一样的

192
00:04:50,640 --> 00:04:52,360
rect node

193
00:04:52,360 --> 00:04:52,820
完了

194
00:04:52,820 --> 00:04:54,720
webpack为了少一点我就删掉了

195
00:04:54,720 --> 00:04:55,600
那现在呢

196
00:04:55,600 --> 00:04:56,380
它不叫靠了

197
00:04:56,380 --> 00:04:57,500
叫call think

198
00:04:57,500 --> 00:04:58,920
一步就call think

199
00:04:58,920 --> 00:05:00,360
完了这个里面呢

200
00:05:00,360 --> 00:05:01,680
也叫type think是吧

201
00:05:01,680 --> 00:05:02,560
一样的

202
00:05:02,560 --> 00:05:03,720
这里也一样

203
00:05:03,720 --> 00:05:05,000
完了分别呢

204
00:05:05,000 --> 00:05:05,660
回调里面呢

205
00:05:05,660 --> 00:05:06,440
就有两参数了

206
00:05:06,440 --> 00:05:06,920
第一个是name

207
00:05:06,920 --> 00:05:08,420
第二个就是我们所谓的cb

208
00:05:08,420 --> 00:05:09,560
它也一样

209
00:05:09,560 --> 00:05:09,820
是吧

210
00:05:09,820 --> 00:05:10,480
把cb放在这

211
00:05:10,480 --> 00:05:12,320
但是为了看到效果吧

212
00:05:12,320 --> 00:05:13,620
我还是把它加个一步吧

213
00:05:13,620 --> 00:05:14,160
定义气

214
00:05:14,160 --> 00:05:16,500
一秒是并发的

215
00:05:16,500 --> 00:05:17,520
也就是说这两个函数

216
00:05:17,520 --> 00:05:18,320
还是同时之行

217
00:05:18,320 --> 00:05:19,380
放在这

218
00:05:19,380 --> 00:05:20,680
完了一样

219
00:05:20,680 --> 00:05:21,400
把它呢

220
00:05:21,400 --> 00:05:22,000
粘过来

221
00:05:22,000 --> 00:05:23,540
这里面我就改个名字就好了

222
00:05:23,540 --> 00:05:24,380
这叫node

223
00:05:24,380 --> 00:05:27,620
完了等他们都执行完以后

224
00:05:27,620 --> 00:05:29,800
会触发我们最终的回答函数

225
00:05:29,800 --> 00:05:31,840
完了里面我可以干一件事

226
00:05:31,840 --> 00:05:33,700
比如说写一句话

227
00:05:33,700 --> 00:05:34,840
叫nd

228
00:05:34,840 --> 00:05:37,200
那你想

229
00:05:37,200 --> 00:05:38,720
他俩的执行结果是不是

230
00:05:38,720 --> 00:05:40,780
上来就把这两个数都执行

231
00:05:40,780 --> 00:05:41,620
完了执行的时候

232
00:05:41,620 --> 00:05:42,880
我们需要给他传一下一个

233
00:05:42,880 --> 00:05:44,140
除了这个name

234
00:05:44,140 --> 00:05:45,340
还需要再传一个方法

235
00:05:45,340 --> 00:05:47,060
这个方法肯定是要我们自己提供

236
00:05:47,060 --> 00:05:48,300
提供好以后

237
00:05:48,300 --> 00:05:49,500
每个人掉一次

238
00:05:49,500 --> 00:05:50,180
那好

239
00:05:50,180 --> 00:05:52,320
当数量达到我们预计的时候

240
00:05:52,320 --> 00:05:53,920
就使用这个方法

241
00:05:53,920 --> 00:05:55,660
这里面我就稍微改一改了

242
00:05:55,660 --> 00:05:56,660
怎么改呢

243
00:05:56,660 --> 00:05:58,300
我们需要先拿到

244
00:05:58,300 --> 00:06:00,740
你看这里面不叫type了

245
00:06:00,740 --> 00:06:03,360
应该叫type or think 是吧

246
00:06:03,360 --> 00:06:04,440
call我就不叫call

247
00:06:04,440 --> 00:06:05,340
应该叫call or think

248
00:06:05,340 --> 00:06:09,280
并且call or think传递的参数

249
00:06:09,280 --> 00:06:12,180
并不再是我们当前那个名了

250
00:06:12,180 --> 00:06:12,900
还多了个函数

251
00:06:12,900 --> 00:06:14,940
我就需要把这函数拿出来

252
00:06:14,940 --> 00:06:16,640
怎么拿出来也非常方便

253
00:06:16,640 --> 00:06:17,920
我们可以通过args

254
00:06:17,920 --> 00:06:19,400
点什么pop

255
00:06:19,400 --> 00:06:21,840
数组去到最后一个

256
00:06:21,840 --> 00:06:23,480
最后一个我们就叫它什么

257
00:06:23,480 --> 00:06:25,700
最终的函数叫final callback

258
00:06:25,700 --> 00:06:32,300
这里面相当于拿出最终的函数

259
00:06:32,300 --> 00:06:35,340
完了之后我们要干什么事

260
00:06:35,340 --> 00:06:37,660
是不是需要让这些函数怎么样都执行

261
00:06:37,660 --> 00:06:39,040
肯定是并发执行

262
00:06:39,040 --> 00:06:39,720
肯定是for each

263
00:06:39,720 --> 00:06:40,860
因为不是串行

264
00:06:40,860 --> 00:06:43,100
串行的话是第一个走完自走第二

265
00:06:43,100 --> 00:06:44,800
我们在这里就一样

266
00:06:44,800 --> 00:06:47,020
让我们当前这样一个tasks

267
00:06:47,020 --> 00:06:50,480
里面拿到每一个task

268
00:06:50,480 --> 00:06:52,560
并且让task干嘛

269
00:06:52,560 --> 00:06:53,680
执行

270
00:06:53,680 --> 00:06:55,800
执行的时候参数传什么

271
00:06:55,800 --> 00:06:57,260
第一个传name

272
00:06:57,260 --> 00:06:59,240
name是不是就这里传的参数

273
00:06:59,240 --> 00:07:00,300
除了最后一个的

274
00:07:00,300 --> 00:07:01,240
那好我还是一样

275
00:07:01,240 --> 00:07:03,280
把这args怎么样放进去

276
00:07:03,280 --> 00:07:05,560
当然这args已经把最后一个删掉了

277
00:07:05,560 --> 00:07:08,120
完了后面我们还需要传一个方法

278
00:07:08,120 --> 00:07:08,680
叫cd

279
00:07:08,680 --> 00:07:09,840
这cd哪来的呢

280
00:07:09,840 --> 00:07:10,900
我就先给你传个down

281
00:07:10,900 --> 00:07:11,600
现在还没有

282
00:07:11,600 --> 00:07:12,820
那好我现在就写一个

283
00:07:12,820 --> 00:07:15,160
这里面来个functiondown

284
00:07:15,160 --> 00:07:17,340
那好了

285
00:07:17,340 --> 00:07:18,000
我们说了

286
00:07:18,000 --> 00:07:20,000
每次人家用户一掉档的时候

287
00:07:20,000 --> 00:07:20,880
我是不是要计数

288
00:07:20,880 --> 00:07:21,700
那好了

289
00:07:21,700 --> 00:07:22,680
肯定离不开一个

290
00:07:22,680 --> 00:07:23,220
对吧

291
00:07:23,220 --> 00:07:24,140
赖他一个index

292
00:07:24,140 --> 00:07:25,300
等于多少

293
00:07:25,300 --> 00:07:26,340
等于零

294
00:07:26,340 --> 00:07:28,420
完了

295
00:07:28,420 --> 00:07:29,060
每次一掉档

296
00:07:29,060 --> 00:07:29,340
好

297
00:07:29,340 --> 00:07:29,980
index干嘛

298
00:07:29,980 --> 00:07:31,340
加价

299
00:07:31,340 --> 00:07:31,860
完了

300
00:07:31,860 --> 00:07:32,460
我可以判断

301
00:07:32,460 --> 00:07:33,200
什么时候

302
00:07:33,200 --> 00:07:34,100
这个index

303
00:07:34,100 --> 00:07:35,780
如果等于了我们当前的

304
00:07:35,780 --> 00:07:36,880
这个函数的个数

305
00:07:36,880 --> 00:07:37,400
那好

306
00:07:37,400 --> 00:07:37,960
说明怎么样

307
00:07:37,960 --> 00:07:38,820
都知情完了

308
00:07:38,820 --> 00:07:40,180
那我可以拿到

309
00:07:40,180 --> 00:07:41,060
tasks的

310
00:07:41,060 --> 00:07:41,460
论词

311
00:07:41,460 --> 00:07:42,400
对吧

312
00:07:42,400 --> 00:07:43,260
this

313
00:07:43,260 --> 00:07:44,520
tasks

314
00:07:44,520 --> 00:07:48,680
完了如果达到以后怎么样

315
00:07:48,680 --> 00:07:49,920
是不是我需要干一件事

316
00:07:49,920 --> 00:07:50,560
就是怎么样

317
00:07:50,560 --> 00:07:52,780
是执行我们的这个final callback

318
00:07:52,780 --> 00:07:56,180
这样的话一个异步的实现就OK了

319
00:07:56,180 --> 00:07:58,080
其实这个原理非常像什么呢

320
00:07:58,080 --> 00:08:00,060
可能大家也写过叫promise.al

321
00:08:00,060 --> 00:08:03,060
当所有的异步都完事了

322
00:08:03,060 --> 00:08:03,680
我就执行它

323
00:08:03,680 --> 00:08:05,560
那好了咱来看看效果吧

324
00:08:05,560 --> 00:08:08,300
应该就可以实现我们预期的结果

325
00:08:08,300 --> 00:08:10,680
有点抱错是吧

326
00:08:10,680 --> 00:08:11,580
说this的问题

327
00:08:11,580 --> 00:08:14,380
每次写的时候都要把它改成见到算数

328
00:08:14,380 --> 00:08:18,540
或许写的时候注意点就好了

329
00:08:18,540 --> 00:08:20,480
这里再运行

330
00:08:20,480 --> 00:08:23,480
是过了一秒之后

331
00:08:23,480 --> 00:08:24,500
全值运行完

332
00:08:24,500 --> 00:08:24,880
好了

333
00:08:24,880 --> 00:08:26,980
其实就是个记录器的过程

334
00:08:26,980 --> 00:08:27,920
那好了

335
00:08:27,920 --> 00:08:30,280
那我们现在写了一个异步方法

336
00:08:30,280 --> 00:08:31,900
但是我们一旦想到异步

337
00:08:31,900 --> 00:08:33,600
你会第一想到的是promise

338
00:08:33,600 --> 00:08:34,340
比如刚才我们说了

339
00:08:34,340 --> 00:08:35,340
有promise更方便

340
00:08:35,340 --> 00:08:36,800
你就不需要这样写了

341
00:08:36,800 --> 00:08:37,540
而我们使用什么

342
00:08:37,540 --> 00:08:38,240
是不是resolve

343
00:08:38,240 --> 00:08:39,620
你这里也没有回掉了

344
00:08:39,620 --> 00:08:40,440
那好了

345
00:08:40,440 --> 00:08:41,580
这代码拷贝一份

346
00:08:41,580 --> 00:08:42,300
保存一下

347
00:08:42,300 --> 00:08:43,180
其实你看

348
00:08:43,180 --> 00:08:44,940
这个库里面提供了很多思想

349
00:08:44,940 --> 00:08:46,820
这个思想其实和我们以前那个什么

350
00:08:46,820 --> 00:08:48,180
node原码里的一些思想

351
00:08:48,180 --> 00:08:48,700
很像

352
00:08:48,700 --> 00:08:50,420
这里把它保存一下

353
00:08:50,420 --> 00:08:52,320
好了

354
00:08:52,320 --> 00:08:53,300
我把它改一下

355
00:08:53,300 --> 00:08:54,580
这里面改个名字

356
00:08:54,580 --> 00:08:55,420
它不叫什么

357
00:08:55,420 --> 00:08:56,200
还是这个名字

358
00:08:56,200 --> 00:08:57,520
现在我们叫它什么

359
00:08:57,520 --> 00:08:58,600
叫执行的时候

360
00:08:58,600 --> 00:08:59,760
还是并行

361
00:08:59,760 --> 00:09:02,240
但是我们现在不叫type了

362
00:09:02,240 --> 00:09:02,680
叫什么呢

363
00:09:02,680 --> 00:09:04,220
叫type promise

364
00:09:04,220 --> 00:09:06,000
一听名字什么意思

365
00:09:06,000 --> 00:09:07,020
就是我注册的

366
00:09:07,020 --> 00:09:08,800
不再是个普通的函数了

367
00:09:08,800 --> 00:09:09,760
而是一个什么

368
00:09:09,760 --> 00:09:11,000
promise

369
00:09:11,000 --> 00:09:12,880
这里面你不要写setmout了

370
00:09:12,880 --> 00:09:14,060
要写个retain什么

371
00:09:14,060 --> 00:09:15,860
new promise

372
00:09:15,860 --> 00:09:18,820
当前是一个promise

373
00:09:18,820 --> 00:09:21,260
分别的参数呢

374
00:09:21,260 --> 00:09:22,580
是result reject

375
00:09:22,580 --> 00:09:24,840
完了我把它呢

376
00:09:24,840 --> 00:09:26,120
这一步还是放在里面

377
00:09:26,120 --> 00:09:27,780
多刷了一段啊

378
00:09:27,780 --> 00:09:30,040
加上来个set mount

379
00:09:30,040 --> 00:09:31,280
完了一切

380
00:09:31,280 --> 00:09:33,980
把当前的这个东西呢移进去

381
00:09:33,980 --> 00:09:37,360
同样这个一步呢

382
00:09:37,360 --> 00:09:38,840
我也把它展过来是吧

383
00:09:38,840 --> 00:09:40,220
看这个写法其实很方便

384
00:09:40,220 --> 00:09:41,480
这里呢改成rect

385
00:09:41,480 --> 00:09:43,840
成功以后不再有CB了

386
00:09:43,840 --> 00:09:45,880
那CB我就直接干掉就好了

387
00:09:45,880 --> 00:09:48,180
完了写的时候

388
00:09:48,180 --> 00:09:49,480
每次成功了

389
00:09:49,480 --> 00:09:50,040
一秒到了

390
00:09:50,040 --> 00:09:52,540
我就掉一下我们所谓的这个回奏成功

391
00:09:52,540 --> 00:09:53,880
然后把这个东西放在这

392
00:09:53,880 --> 00:09:55,300
把这个东西放在这

393
00:09:55,300 --> 00:09:56,360
就可以了

394
00:09:56,360 --> 00:09:58,200
每次成功都OK

395
00:09:58,200 --> 00:10:00,100
那这时候我们掉的时候也是怎么样

396
00:10:00,100 --> 00:10:00,300
是不是

397
00:10:00,300 --> 00:10:01,340
type promise

398
00:10:01,340 --> 00:10:03,200
那后面你不能这样写了

399
00:10:03,200 --> 00:10:06,420
因为是不相当于所有的promise都执行完

400
00:10:06,420 --> 00:10:07,280
它应该怎么办

401
00:10:07,280 --> 00:10:08,180
是不是在掉这方法

402
00:10:08,180 --> 00:10:09,460
那你就想到了

403
00:10:09,460 --> 00:10:10,460
其实就是个promise all

404
00:10:10,460 --> 00:10:11,460
那这里面一样

405
00:10:11,460 --> 00:10:12,480
我可以怎么写呢

406
00:10:12,480 --> 00:10:13,140
这样写

407
00:10:13,140 --> 00:10:13,660
他可以怎么样

408
00:10:13,660 --> 00:10:14,580
第二什么

409
00:10:14,580 --> 00:10:17,780
放在这

410
00:10:17,780 --> 00:10:20,480
就是当我们这个上面以后

411
00:10:20,480 --> 00:10:21,800
他们执行完以后

412
00:10:21,800 --> 00:10:22,720
会返回一个什么

413
00:10:22,720 --> 00:10:23,840
新的promise

414
00:10:23,840 --> 00:10:25,220
这个promise可以怎么样

415
00:10:25,220 --> 00:10:25,620
去认

416
00:10:25,620 --> 00:10:27,660
认的时候同样告诉我这机会

417
00:10:27,660 --> 00:10:28,160
OK了

418
00:10:28,160 --> 00:10:29,660
看这个写法

419
00:10:29,660 --> 00:10:31,800
其实和异步是很像的

420
00:10:31,800 --> 00:10:33,260
包错是吧

421
00:10:33,260 --> 00:10:34,460
他说认有问题是吧

422
00:10:34,460 --> 00:10:35,300
再来看看

423
00:10:35,300 --> 00:10:37,040
不叫cowasink叫啥

424
00:10:37,040 --> 00:10:38,640
他的方法名字也变了

425
00:10:38,640 --> 00:10:39,740
叫promise

426
00:10:41,460 --> 00:10:43,240
出来了

427
00:10:43,240 --> 00:10:44,840
你看是不是上来以后

428
00:10:44,840 --> 00:10:46,040
一个两个成功了

429
00:10:46,040 --> 00:10:46,300
是吧

430
00:10:46,300 --> 00:10:48,180
但是好像没有走这个N的

431
00:10:48,180 --> 00:10:48,740
那咱来看看

432
00:10:48,740 --> 00:10:50,420
第一个掉了一秒

433
00:10:50,420 --> 00:10:51,360
第二个也掉了

434
00:10:51,360 --> 00:10:51,940
是吧

435
00:10:51,940 --> 00:10:53,380
完了是不是少掉了呢

436
00:10:53,380 --> 00:10:54,300
好像没有啊

437
00:10:54,300 --> 00:10:54,780
我再来一次

438
00:10:54,780 --> 00:10:55,640
软一下

439
00:10:55,640 --> 00:10:59,400
好像执行完没有反应

440
00:10:59,400 --> 00:11:00,180
是吧

441
00:11:00,180 --> 00:11:01,420
我再来一次啊

442
00:11:01,420 --> 00:11:03,300
Node React

443
00:11:03,300 --> 00:11:03,720
对吧

444
00:11:03,720 --> 00:11:05,240
完了这里面没有走

445
00:11:05,240 --> 00:11:06,940
看看什么原因啊

446
00:11:06,940 --> 00:11:08,020
这里面没有改名字

447
00:11:08,020 --> 00:11:09,360
叫Type Promise

448
00:11:09,360 --> 00:11:10,420
再运行一下

449
00:11:10,420 --> 00:11:13,760
你看是不是两个人走走完

450
00:11:13,760 --> 00:11:14,640
会触发这个N的

451
00:11:14,640 --> 00:11:16,440
那我们现在可以归纳一下

452
00:11:16,440 --> 00:11:18,000
其实这个Typeable库里面

453
00:11:18,000 --> 00:11:18,420
对吧

454
00:11:18,420 --> 00:11:19,660
这个Typeable库中

455
00:11:19,660 --> 00:11:22,500
有三种对吧

456
00:11:22,500 --> 00:11:24,120
注册的方法

457
00:11:24,120 --> 00:11:25,560
分别叫什么呢

458
00:11:25,560 --> 00:11:26,220
第一个就是Type

459
00:11:26,220 --> 00:11:27,460
Typeable库

460
00:11:27,460 --> 00:11:28,180
我们写全

461
00:11:28,180 --> 00:11:28,920
写全吧

462
00:11:28,920 --> 00:11:30,380
第一个叫Type

463
00:11:30,380 --> 00:11:31,260
它代表什么呢

464
00:11:31,260 --> 00:11:32,000
就是同步注册

465
00:11:32,000 --> 00:11:32,960
同步注册

466
00:11:32,960 --> 00:11:34,320
那同样有同步注册

467
00:11:34,320 --> 00:11:34,720
还有什么呢

468
00:11:34,720 --> 00:11:36,760
还有刚才看到那个叫E部注册

469
00:11:36,760 --> 00:11:37,780
它会多一个什么东西

470
00:11:37,780 --> 00:11:38,500
CB

471
00:11:38,500 --> 00:11:39,800
那最后呢

472
00:11:39,800 --> 00:11:41,820
还有一种就是我们所谓的这个叫对吧

473
00:11:41,820 --> 00:11:43,060
叫type promise

474
00:11:43,060 --> 00:11:44,480
它注册的就是什么

475
00:11:44,480 --> 00:11:46,880
就是注册的是promise

476
00:11:46,880 --> 00:11:50,120
那好了

477
00:11:50,120 --> 00:11:52,600
这就是我们这个所谓的三个方法

478
00:11:52,600 --> 00:11:54,640
那同样的注册的时候呢

479
00:11:54,640 --> 00:11:55,940
我们调的时候也分为两种

480
00:11:55,940 --> 00:11:57,000
三种是吧

481
00:11:57,000 --> 00:11:57,660
第一个叫call

482
00:11:57,660 --> 00:11:59,800
第二个呢叫我们的call or think

483
00:11:59,800 --> 00:12:02,400
第三个呢叫我们的promise

484
00:12:02,400 --> 00:12:03,160
三种

485
00:12:03,160 --> 00:12:05,780
那这个方法咱就顺便实现一下吧

486
00:12:05,780 --> 00:12:06,640
其实难度也不高

487
00:12:06,640 --> 00:12:08,520
那我在这里呢就放过来

488
00:12:08,520 --> 00:12:11,180
我要这里改一下

489
00:12:11,180 --> 00:12:12,040
这都不用改

490
00:12:12,040 --> 00:12:14,540
这个叫type promise

491
00:12:14,540 --> 00:12:15,800
其实这个方法

492
00:12:15,800 --> 00:12:17,060
它的功能就是一样的

493
00:12:17,060 --> 00:12:18,200
就是放在速度里

494
00:12:18,200 --> 00:12:19,180
我要此时这个方法

495
00:12:19,180 --> 00:12:20,500
它叫promise

496
00:12:20,500 --> 00:12:22,300
我要把它删掉

497
00:12:22,300 --> 00:12:24,360
完了里面

498
00:12:24,360 --> 00:12:25,340
我把这个方法

499
00:12:25,340 --> 00:12:26,620
这就不要了

500
00:12:26,620 --> 00:12:29,080
这应该叫type promise

501
00:12:29,080 --> 00:12:30,520
这个还加上去也好

502
00:12:30,520 --> 00:12:34,160
这里面应该叫written new promise

503
00:12:34,160 --> 00:12:35,640
代码不去沾了

504
00:12:35,640 --> 00:12:40,200
他那放去

505
00:12:40,200 --> 00:12:41,980
他执行完以后

506
00:12:41,980 --> 00:12:42,840
执行的是react

507
00:12:42,840 --> 00:12:44,400
他那沾过来是吧

508
00:12:44,400 --> 00:12:45,500
他执行的是node

509
00:12:45,500 --> 00:12:48,720
当然了不叫cb

510
00:12:48,720 --> 00:12:49,760
应该叫vizal

511
00:12:49,760 --> 00:12:52,160
OK

512
00:12:52,160 --> 00:12:53,140
完了同样呢

513
00:12:53,140 --> 00:12:53,600
这里面呢

514
00:12:53,600 --> 00:12:54,720
我把这个方法也改一下

515
00:12:54,720 --> 00:12:55,460
叫promise

516
00:12:55,460 --> 00:12:56,480
完了他呢

517
00:12:56,480 --> 00:12:56,980
括号

518
00:12:56,980 --> 00:12:58,380
第二个字

519
00:12:58,380 --> 00:13:00,880
看这样一个过程啊

520
00:13:00,880 --> 00:13:01,640
那现在啊

521
00:13:01,640 --> 00:13:02,420
我们的方法上面

522
00:13:02,420 --> 00:13:03,320
这没有改啊

523
00:13:03,320 --> 00:13:04,280
一定改清楚了

524
00:13:04,280 --> 00:13:05,320
他要promise

525
00:13:05,320 --> 00:13:06,760
那上面type promise promise

526
00:13:06,760 --> 00:13:07,040
OK

527
00:13:07,040 --> 00:13:09,620
那这时候我们注册的这些方法呀

528
00:13:09,620 --> 00:13:10,900
是不是都放到书组里了

529
00:13:10,900 --> 00:13:12,480
那我们需要让这方法怎么样

530
00:13:12,480 --> 00:13:13,300
是不是一次执行

531
00:13:13,300 --> 00:13:14,840
并且呢拿到什么呀

532
00:13:14,840 --> 00:13:17,380
是不是告诉他成功了一个成功两个

533
00:13:17,380 --> 00:13:18,960
当这两个都成功以后怎么样

534
00:13:18,960 --> 00:13:20,000
才会让第三个成功

535
00:13:20,000 --> 00:13:22,080
所以这里面我会这样做

536
00:13:22,080 --> 00:13:25,640
先把这个task中的什么一次循环

537
00:13:25,640 --> 00:13:27,860
里面拿到每一个task

538
00:13:27,860 --> 00:13:30,980
这个task指的就是每一个函数

539
00:13:30,980 --> 00:13:32,460
我会让这函数干嘛呢

540
00:13:32,460 --> 00:13:32,820
执行

541
00:13:32,820 --> 00:13:35,120
那参数呢

542
00:13:35,120 --> 00:13:35,980
我们放谁呀

543
00:13:35,980 --> 00:13:37,420
是不是放上我们当前这个

544
00:13:37,420 --> 00:13:38,860
这没有CB了

545
00:13:38,860 --> 00:13:39,720
CB就不要了

546
00:13:39,720 --> 00:13:41,180
所以就当前我们传过来这参数

547
00:13:41,180 --> 00:13:43,540
我把这参数传进去

548
00:13:43,540 --> 00:13:46,680
那执行完以后

549
00:13:46,680 --> 00:13:47,700
是不是这样执行

550
00:13:47,700 --> 00:13:48,160
还不行

551
00:13:48,160 --> 00:13:50,020
因为我要把这两个promise怎么样

552
00:13:50,020 --> 00:13:51,040
是不是都拿到

553
00:13:51,040 --> 00:13:52,900
那forEast是执行完就完事了

554
00:13:52,900 --> 00:13:53,840
我需要干嘛呢

555
00:13:53,840 --> 00:13:54,500
用Map

556
00:13:54,500 --> 00:13:57,380
你说把他俩的执行结果的promise

557
00:13:57,380 --> 00:13:58,820
都存放起来

558
00:13:58,820 --> 00:13:59,640
存放起来以后

559
00:13:59,640 --> 00:14:02,380
我可以认为它是一个task

560
00:14:02,380 --> 00:14:04,280
干嘛呢

561
00:14:04,280 --> 00:14:06,160
是不是两个promise都执行完

562
00:14:06,160 --> 00:14:07,560
我才去调这个方法

563
00:14:07,560 --> 00:14:08,480
那好了

564
00:14:08,480 --> 00:14:09,220
果断怎么样

565
00:14:09,220 --> 00:14:09,800
是不是最后

566
00:14:09,800 --> 00:14:11,560
他肯定要返回的是promise吧

567
00:14:11,560 --> 00:14:13,020
那就promise调什么

568
00:14:13,020 --> 00:14:16,400
完了

569
00:14:16,400 --> 00:14:17,320
把当前呢

570
00:14:17,320 --> 00:14:18,700
我们这个tasks放去

571
00:14:18,700 --> 00:14:22,300
这里面放的其实就是一个promise array

572
00:14:22,300 --> 00:14:23,820
我把它放去以后

573
00:14:23,820 --> 00:14:26,040
他会默认去让两个人怎么样都执行

574
00:14:26,040 --> 00:14:27,160
执行完之后呢

575
00:14:27,160 --> 00:14:28,020
返回个promise

576
00:14:28,020 --> 00:14:29,160
所以在最后呢

577
00:14:29,160 --> 00:14:30,420
我们可以在这去调the

578
00:14:30,420 --> 00:14:31,860
然后这是最终的结果

579
00:14:31,860 --> 00:14:32,660
那好了

580
00:14:32,660 --> 00:14:33,760
我们来看一下

581
00:14:33,760 --> 00:14:35,440
是不是可以的是吧

582
00:14:35,440 --> 00:14:38,500
有个小爆错

583
00:14:38,500 --> 00:14:41,240
这个promises是哪哪哪出来的

584
00:14:41,240 --> 00:14:43,040
看看多占了一个是吧

585
00:14:43,040 --> 00:14:43,320
叫

586
00:14:43,320 --> 00:14:45,860
这应该没有了吧

587
00:14:45,860 --> 00:14:48,260
这个叫什么叫promise是吧

588
00:14:48,260 --> 00:14:50,080
运行一下

589
00:14:50,080 --> 00:14:53,180
是不是两个都执行完以后

590
00:14:53,180 --> 00:14:54,380
执行最后一个

591
00:14:54,380 --> 00:14:55,320
那这样的话

592
00:14:55,320 --> 00:14:57,380
我们就把刚才那个所谓的叫

593
00:14:57,380 --> 00:15:00,160
叫异步并发的钩子实现了一下

594
00:15:00,160 --> 00:15:01,440
其实异步并发里面

595
00:15:01,440 --> 00:15:02,360
还有一个东西叫什么呢

596
00:15:02,360 --> 00:15:03,400
其实这个也很好理解

597
00:15:03,400 --> 00:15:04,220
叫什么呢

598
00:15:04,220 --> 00:15:07,140
叫Sync Perio

599
00:15:07,140 --> 00:15:09,220
Perio

600
00:15:09,220 --> 00:15:10,380
Biohook是吧

601
00:15:10,380 --> 00:15:13,380
叫一步并发的一个什么

602
00:15:13,380 --> 00:15:14,400
带保险的钩子

603
00:15:14,400 --> 00:15:15,300
其实非常简单

604
00:15:15,300 --> 00:15:16,020
后面我会说

605
00:15:16,020 --> 00:15:17,520
大家可以自己去想一想

606
00:15:17,520 --> 00:15:19,880
就是如果当前我们这个callback

607
00:15:19,880 --> 00:15:21,700
或者调用了什么reject

608
00:15:21,700 --> 00:15:23,800
那说明是不是可能就抛出异常了

609
00:15:23,800 --> 00:15:24,980
那可以怎么样呢

610
00:15:24,980 --> 00:15:26,420
是不是执行完这个方法以后

611
00:15:26,420 --> 00:15:27,260
就不会怎么样

612
00:15:27,260 --> 00:15:28,200
向下执行

613
00:15:28,200 --> 00:15:29,440
这样可以

614
00:15:29,440 --> 00:15:30,840
所以说这是一个什么

615
00:15:30,840 --> 00:15:32,180
叫带保险的一个什么

616
00:15:32,180 --> 00:15:34,220
异步并发的一个方法

617
00:15:34,220 --> 00:15:35,140
写上对吧

618
00:15:35,140 --> 00:15:36,720
带保险的

619
00:15:36,720 --> 00:15:39,120
异步并发的

620
00:15:39,120 --> 00:15:40,600
钩子

621
00:15:40,600 --> 00:15:42,240
这个我就不去说了

622
00:15:42,240 --> 00:15:43,440
其实写法也很像

623
00:15:43,440 --> 00:15:44,460
也就是他会多一个什么

624
00:15:44,460 --> 00:15:44,940
多个error

625
00:15:44,940 --> 00:15:46,480
这里面我就不加了

626
00:15:46,480 --> 00:15:47,360
那好

627
00:15:47,360 --> 00:15:48,240
那之后呢

628
00:15:48,240 --> 00:15:49,160
我们再来说一下吧

629
00:15:49,160 --> 00:15:50,660
除了我们所谓的这个叫

630
00:15:50,660 --> 00:15:51,700
异步并发

631
00:15:51,700 --> 00:15:53,480
肯定还最多比较用的还是什么

632
00:15:53,480 --> 00:15:55,220
就是我们所谓的异步串行

633
00:15:55,220 --> 00:15:57,480
可能上一个人会依赖下一个人

634
00:15:57,480 --> 00:15:58,340
有这样的关系

635
00:15:58,340 --> 00:15:59,980
那我们再来看看这三个方法

636
00:15:59,980 --> 00:16:00,300
是吧

637
00:16:00,300 --> 00:16:01,980
好

