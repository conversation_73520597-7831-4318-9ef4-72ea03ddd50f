1
00:00:00,000 --> 00:00:04,760
ok 那我们接着来写这个build的module模块是吧

2
00:00:04,760 --> 00:00:06,280
它的作用呢主要就是什么

3
00:00:06,280 --> 00:00:08,300
就是构建模块用的

4
00:00:08,300 --> 00:00:10,720
那现在我们就可以来看一下

5
00:00:10,720 --> 00:00:12,640
那我们现在有了这样一个路径

6
00:00:12,640 --> 00:00:14,700
那我们肯定希望拿到这个路径的内容

7
00:00:14,700 --> 00:00:16,440
那好 我在这里呢可以直接

8
00:00:16,440 --> 00:00:19,340
let source等于我们这样一个文件

9
00:00:19,340 --> 00:00:21,500
叫fs.readfilesync

10
00:00:21,500 --> 00:00:23,920
发现这个fs模块没有意义是吧

11
00:00:23,920 --> 00:00:27,140
ok 那我在上面呢就把这样一个模块呢引进来

12
00:00:27,140 --> 00:00:28,580
引过来啊

13
00:00:28,580 --> 00:00:33,380
这里let一个fs等于require一个fs

14
00:00:33,380 --> 00:00:37,580
并且我们可以在这里面引用一下pass require pass

15
00:00:37,580 --> 00:00:40,380
现在我们就可以来做这件事的

16
00:00:40,380 --> 00:00:42,580
读文件读路径这是个绝对路径

17
00:00:42,580 --> 00:00:43,780
我把路径的放在这

18
00:00:43,780 --> 00:00:46,980
并且跟他说我读出来的结果是utl8

19
00:00:46,980 --> 00:00:49,780
但这样一个方法我们希望怎么样可以服用

20
00:00:49,780 --> 00:00:52,180
我们就可以在这里面专门写这样一个方法

21
00:00:52,180 --> 00:00:54,380
叫getsauce获取缘码

22
00:00:54,380 --> 00:00:56,880
这里我可以直接这样写let content

23
00:00:57,080 --> 00:00:59,180
完了这里面我就调这个方法就好了

24
00:00:59,180 --> 00:01:00,380
叫this.

25
00:01:00,380 --> 00:01:04,120
他完了把这个东西放过来放在这里

26
00:01:04,120 --> 00:01:07,380
那同样我把这个readfilesync移到这里去

27
00:01:07,380 --> 00:01:10,720
最后我把这个结果导出

28
00:01:10,720 --> 00:01:14,140
那现在我们就可以把这个module path传过来

29
00:01:14,140 --> 00:01:17,640
那这样做的话是不是就可以拿到我们当前模块丢的内容了

30
00:01:17,640 --> 00:01:20,040
那拿到内容以后我们需要干嘛呢

31
00:01:20,040 --> 00:01:22,180
是不是我们需要拿到内容

32
00:01:22,180 --> 00:01:25,220
拿到模块的内容

33
00:01:25,220 --> 00:01:25,980
还有什么呢

34
00:01:25,980 --> 00:01:27,400
还有我们模块的ID是吧

35
00:01:27,400 --> 00:01:28,920
这个就是模块的ID

36
00:01:28,920 --> 00:01:31,100
那模块的ID

37
00:01:31,100 --> 00:01:32,700
我们需要稍微改造一下

38
00:01:32,700 --> 00:01:34,600
因为现在我们这个module path

39
00:01:34,600 --> 00:01:35,660
是个绝对路径

40
00:01:35,660 --> 00:01:37,180
但是我们刚才可以看到

41
00:01:37,180 --> 00:01:38,820
我们打包出来的这个路径

42
00:01:38,820 --> 00:01:40,620
其实是个相对路径

43
00:01:40,620 --> 00:01:40,960
那好了

44
00:01:40,960 --> 00:01:42,040
那我们也就是说

45
00:01:42,040 --> 00:01:42,960
我们module path

46
00:01:42,960 --> 00:01:43,920
应该是什么呢

47
00:01:43,920 --> 00:01:45,460
是总路径

48
00:01:45,460 --> 00:01:47,060
完了去减去我们当前

49
00:01:47,060 --> 00:01:48,340
这样一个工作路径

50
00:01:48,340 --> 00:01:50,220
就是它的一个所谓的叫相对路径

51
00:01:50,220 --> 00:01:51,040
那好了

52
00:01:51,040 --> 00:01:51,780
那我在这里

53
00:01:51,780 --> 00:01:53,020
我就可以去怎么做了呢

54
00:01:53,020 --> 00:01:54,080
用这样一个方法

55
00:01:54,080 --> 00:01:54,640
叫这个

56
00:01:54,640 --> 00:01:56,220
我们需要拿到这个module name

57
00:01:56,220 --> 00:01:57,800
完了等于什么呢

58
00:01:57,800 --> 00:01:58,640
叫module

59
00:01:58,640 --> 00:02:01,780
叫这个pass的pass有个方法

60
00:02:01,780 --> 00:02:02,860
叫什么呢叫raditive

61
00:02:02,860 --> 00:02:03,920
raditive什么意思

62
00:02:03,920 --> 00:02:04,700
就叫相对路径

63
00:02:04,700 --> 00:02:06,840
就是说我们可以通过一个

64
00:02:06,840 --> 00:02:08,200
对吧两个绝对路径

65
00:02:08,200 --> 00:02:09,800
获取这两个绝对路径的差

66
00:02:09,800 --> 00:02:11,000
那我们可以呢

67
00:02:11,000 --> 00:02:12,680
把这个this.root放在这

68
00:02:12,680 --> 00:02:13,980
完了并且呢

69
00:02:13,980 --> 00:02:14,680
我们还可以呢

70
00:02:14,680 --> 00:02:16,920
把我们当前这样一个modulepass放在这

71
00:02:16,920 --> 00:02:18,340
那这时候呢

72
00:02:18,340 --> 00:02:19,760
我们就可以获取一个什么呢

73
00:02:19,760 --> 00:02:21,300
叫模块的名字

74
00:02:21,300 --> 00:02:21,640
那好了

75
00:02:21,640 --> 00:02:22,620
它就是个对吧

76
00:02:22,620 --> 00:02:23,100
相对路径

77
00:02:23,100 --> 00:02:24,020
那这时候呢

78
00:02:24,020 --> 00:02:24,620
它相对路径

79
00:02:24,620 --> 00:02:25,560
它并没有什么

80
00:02:25,560 --> 00:02:26,620
它取出来就这样的

81
00:02:26,620 --> 00:02:28,060
src下的index.js

82
00:02:28,060 --> 00:02:29,480
我们还需要加个什么的

83
00:02:29,480 --> 00:02:29,840
第二杠

84
00:02:29,840 --> 00:02:31,140
这才叫相对是吧

85
00:02:31,140 --> 00:02:33,140
这里我们再加一个第二杠

86
00:02:33,140 --> 00:02:34,460
放在这

87
00:02:34,460 --> 00:02:36,320
现在我们理所当然

88
00:02:36,320 --> 00:02:37,680
应该就能拿到sauce

89
00:02:37,680 --> 00:02:38,860
和我们的module name

90
00:02:38,860 --> 00:02:39,700
costal log

91
00:02:39,700 --> 00:02:41,060
我们来打印一下

92
00:02:41,060 --> 00:02:42,140
第一个叫sauce

93
00:02:42,140 --> 00:02:44,260
第二个就是我们的module name

94
00:02:44,260 --> 00:02:46,060
完了我们打印一下

95
00:02:46,060 --> 00:02:46,700
看看结果

96
00:02:46,700 --> 00:02:48,520
能不能拿到我们这对应的结果

97
00:02:48,520 --> 00:02:49,860
这里不是

98
00:02:49,860 --> 00:02:51,560
把它切过来

99
00:02:51,560 --> 00:02:52,640
叫这个执行

100
00:02:52,640 --> 00:02:55,160
之前的时候报了个错

101
00:02:55,160 --> 00:02:56,660
说C盘下的找不到这个bin

102
00:02:56,660 --> 00:02:58,360
那肯定是我这个

103
00:02:58,360 --> 00:02:59,440
引用的时候有问题

104
00:02:59,440 --> 00:03:00,040
我看一下

105
00:03:00,040 --> 00:03:02,580
找一下我们当前这样一个引用关系

106
00:03:02,580 --> 00:03:05,020
这里面我要取的是

107
00:03:05,020 --> 00:03:06,840
不是这里面写错了吧

108
00:03:06,840 --> 00:03:07,760
应该是取的是什么呢

109
00:03:07,760 --> 00:03:09,700
是当前文件加下的什么

110
00:03:09,700 --> 00:03:12,920
是不是webpack.config.js

111
00:03:12,920 --> 00:03:14,620
那这样取的话

112
00:03:14,620 --> 00:03:16,240
指的就是当前我们的配置文件

113
00:03:16,240 --> 00:03:16,520
是吧

114
00:03:16,520 --> 00:03:17,400
也不能取第二类

115
00:03:17,400 --> 00:03:18,000
OK

116
00:03:18,000 --> 00:03:19,260
那这样改完以后呢

117
00:03:19,260 --> 00:03:20,040
我们再来试一下

118
00:03:20,040 --> 00:03:22,360
看能不能拿到

119
00:03:22,640 --> 00:03:24,000
require拼错了

120
00:03:24,000 --> 00:03:25,700
require再找一下

121
00:03:25,700 --> 00:03:27,240
那就是这里是吧

122
00:03:27,240 --> 00:03:28,460
require

123
00:03:28,460 --> 00:03:29,060
ok

124
00:03:29,060 --> 00:03:30,440
再跑一下

125
00:03:30,440 --> 00:03:32,940
这时候果然拿到了

126
00:03:32,940 --> 00:03:34,220
第一是文件内容

127
00:03:34,220 --> 00:03:36,380
第二是我们的文件的路径

128
00:03:36,380 --> 00:03:38,500
现在有了这样一个东西以后

129
00:03:38,500 --> 00:03:39,940
我们可以再接着操作

130
00:03:39,940 --> 00:03:40,720
操作什么

131
00:03:40,720 --> 00:03:43,440
我们是不是需要把模块中这个内容

132
00:03:43,440 --> 00:03:44,560
是不是require

133
00:03:44,560 --> 00:03:45,980
集成下滑线下滑线

134
00:03:45,980 --> 00:03:46,940
require webpack

135
00:03:46,940 --> 00:03:49,860
完了还希望把我们当前这样一个引用物镜

136
00:03:49,860 --> 00:03:51,840
你说引用物镜你不能这样写

137
00:03:51,840 --> 00:03:53,300
我们应该是把它变成什么

138
00:03:53,300 --> 00:03:55,380
dr-src下的a.js

139
00:03:55,380 --> 00:03:56,320
你可以看这

140
00:03:56,320 --> 00:03:57,660
你看是不是

141
00:03:57,660 --> 00:03:59,480
dr-src下的a.js

142
00:03:59,480 --> 00:04:00,440
而且引用的时候

143
00:04:00,440 --> 00:04:01,320
是不是也是一样的

144
00:04:01,320 --> 00:04:01,940
找找按

145
00:04:01,940 --> 00:04:02,540
这个

146
00:04:02,540 --> 00:04:03,420
靠背台

147
00:04:03,420 --> 00:04:04,200
你看这里面

148
00:04:04,200 --> 00:04:05,180
是不是就可以去引用

149
00:04:05,180 --> 00:04:06,580
这样一个src.js

150
00:04:06,580 --> 00:04:08,200
所以我们要做的什么事

151
00:04:08,200 --> 00:04:10,000
就是把我们的原码进行改造

152
00:04:10,000 --> 00:04:11,900
把require方法的名字改掉

153
00:04:11,900 --> 00:04:13,720
并且把我们的路径也改掉

154
00:04:13,720 --> 00:04:15,260
OK了

155
00:04:15,260 --> 00:04:16,880
这时候我们需要做的什么事

156
00:04:16,880 --> 00:04:18,420
就是解析我们这个原码

157
00:04:18,420 --> 00:04:19,080
叫pass

158
00:04:19,080 --> 00:04:20,080
pass谁

159
00:04:20,080 --> 00:04:21,460
解析我们这样一个source

160
00:04:21,460 --> 00:04:23,760
那解析的时候我们要做什么事呢

161
00:04:23,760 --> 00:04:26,300
是不是主要是改这个引用模块的名字

162
00:04:26,300 --> 00:04:27,740
就叫这个名字

163
00:04:27,740 --> 00:04:30,580
并且呢我们还需要把原来这个什么a.js

164
00:04:30,580 --> 00:04:32,800
前面再加上这样一个src

165
00:04:32,800 --> 00:04:34,540
那这个src啊

166
00:04:34,540 --> 00:04:38,060
其实就是当前我们这样一个模块名字的什么

167
00:04:38,060 --> 00:04:38,960
是不是复路径啊

168
00:04:38,960 --> 00:04:41,340
那模块名字是不是in.js src下的

169
00:04:41,340 --> 00:04:42,820
那我是不是可以取什么

170
00:04:42,820 --> 00:04:45,480
是不是这个路径的复路径就是src啊

171
00:04:45,480 --> 00:04:48,020
那这里呢我可以直接去写叫pass

172
00:04:48,020 --> 00:04:50,040
第二什么呢叫DR0

173
00:04:50,040 --> 00:04:52,540
他呢就可以取什么呢

174
00:04:52,540 --> 00:04:54,540
取当前我们这个模块的名字

175
00:04:54,540 --> 00:04:54,880
那好

176
00:04:54,880 --> 00:04:56,060
这附路径是吧

177
00:04:56,060 --> 00:04:58,140
那取到的其实就是这个src

178
00:04:58,140 --> 00:04:59,620
那这时候好了

179
00:04:59,620 --> 00:05:02,020
那拿到以后我们需要干什么呢

180
00:05:02,020 --> 00:05:03,220
解析的时候对吧

181
00:05:03,220 --> 00:05:09,340
解析需要把这个需要把sauce约满进行改造

182
00:05:09,340 --> 00:05:12,520
完了并且呢还需要干什么呢

183
00:05:12,520 --> 00:05:13,780
是不是返回一个什么

184
00:05:13,780 --> 00:05:15,740
返回一个依赖列表

185
00:05:15,740 --> 00:05:17,000
依赖

186
00:05:17,000 --> 00:05:18,480
依赖列表

187
00:05:18,480 --> 00:05:20,420
为什么要返回个依赖列表

188
00:05:20,420 --> 00:05:22,760
你想想我们是不是要通过这个依赖怎么样

189
00:05:22,760 --> 00:05:23,900
继续去看

190
00:05:23,900 --> 00:05:25,760
比如说看看我们当前那个A

191
00:05:25,760 --> 00:05:27,080
A里面说又有了base

192
00:05:27,080 --> 00:05:29,160
那应该是不是把base也放到这底下去

193
00:05:29,160 --> 00:05:30,560
所以要循环的去怎么样

194
00:05:30,560 --> 00:05:31,380
干这件事

195
00:05:31,380 --> 00:05:32,300
那好了

196
00:05:32,300 --> 00:05:34,160
那我们在这里就先模拟一下

197
00:05:34,160 --> 00:05:36,060
这里面应该返回的是一个什么

198
00:05:36,060 --> 00:05:37,320
叫source code

199
00:05:37,320 --> 00:05:40,440
完了还有一个就是我们一个所谓的依赖

200
00:05:40,440 --> 00:05:41,120
depend

201
00:05:41,120 --> 00:05:42,080
dependence

202
00:05:42,080 --> 00:05:43,660
那好了

203
00:05:43,660 --> 00:05:46,000
他们都从这个方法里面拿到的

204
00:05:46,000 --> 00:05:47,960
完了最后我们可以一想一下

205
00:05:47,960 --> 00:05:49,760
如果拿到了这样一个东西以后

206
00:05:49,760 --> 00:05:50,900
我是不是可以怎么样

207
00:05:50,900 --> 00:05:53,940
是不是可以把我们当前这个模块里面

208
00:05:53,940 --> 00:05:54,980
装上这样一个对象了

209
00:05:54,980 --> 00:05:56,400
完了通过什么呢

210
00:05:56,400 --> 00:05:56,860
模块名

211
00:05:56,860 --> 00:05:58,820
那模块名是不是就是我们的module name

212
00:05:58,820 --> 00:06:00,300
完了它的值呢

213
00:06:00,300 --> 00:06:02,280
是不是就是我们这样一个所谓的source code

214
00:06:02,280 --> 00:06:05,020
这相当于是不是就是把什么

215
00:06:05,020 --> 00:06:06,860
把对吧相对路径

216
00:06:06,860 --> 00:06:13,420
和模块中的对吧中的内容怎么样

217
00:06:13,420 --> 00:06:14,820
对应起来

218
00:06:14,820 --> 00:06:15,640
就可以做到了

219
00:06:15,640 --> 00:06:17,080
起来是吧

220
00:06:17,080 --> 00:06:18,580
但是这里面还有个问题

221
00:06:18,580 --> 00:06:20,840
我们还没有用到这个A字Android

222
00:06:20,840 --> 00:06:22,480
如果是主入口的话

223
00:06:22,480 --> 00:06:24,120
我是不是要把这主入口的名字

224
00:06:24,120 --> 00:06:25,680
存到这个AndroidID里去

225
00:06:25,680 --> 00:06:26,860
所以这里呢

226
00:06:26,860 --> 00:06:28,620
我们要在这个之前要在干一件事

227
00:06:28,620 --> 00:06:28,940
是吧

228
00:06:28,940 --> 00:06:29,900
都在这吧

229
00:06:29,900 --> 00:06:31,740
如果当前呢

230
00:06:31,740 --> 00:06:33,400
它是一个对吧

231
00:06:33,400 --> 00:06:34,360
Android主入口

232
00:06:34,360 --> 00:06:35,160
那好了

233
00:06:35,160 --> 00:06:36,960
那我们是不是同样的要干这件事

234
00:06:36,960 --> 00:06:37,680
是入口

235
00:06:37,680 --> 00:06:38,460
那我怎么样

236
00:06:38,460 --> 00:06:40,640
是不是就把这个this叫AndroidID

237
00:06:40,640 --> 00:06:43,200
等于我们当前这样一个module pass

238
00:06:43,200 --> 00:06:44,480
不是module pass

239
00:06:44,480 --> 00:06:45,320
是module内

240
00:06:45,320 --> 00:06:49,540
这相当于就是保存入口的名字

241
00:06:49,540 --> 00:06:51,040
OK

242
00:06:51,040 --> 00:06:52,240
那再来看看吧

243
00:06:52,240 --> 00:06:53,680
现在这样一个套路呢

244
00:06:53,680 --> 00:06:54,100
OK了

245
00:06:54,100 --> 00:06:55,920
那我们再需要写这样一个方法

246
00:06:55,920 --> 00:06:56,640
叫pass

247
00:06:56,640 --> 00:06:58,140
这是我们核心的逻辑

248
00:06:58,140 --> 00:06:59,780
那我们把pass呢放在这里

249
00:06:59,780 --> 00:07:01,320
写上这样的东西是吧

250
00:07:01,320 --> 00:07:02,500
叫pass

251
00:07:02,500 --> 00:07:06,460
看看怎么去解析我们的代码

252
00:07:06,460 --> 00:07:06,700
对吧

253
00:07:06,700 --> 00:07:07,800
这是解析

254
00:07:07,800 --> 00:07:08,680
对吧

255
00:07:08,680 --> 00:07:09,100
圆码

256
00:07:09,100 --> 00:07:10,100
好了

257
00:07:10,100 --> 00:07:11,480
参数呢分别是什么呢

258
00:07:11,480 --> 00:07:12,200
第一是圆码

259
00:07:12,200 --> 00:07:12,860
叫sauce

260
00:07:12,860 --> 00:07:14,380
第二呢就是我们所谓的

261
00:07:14,380 --> 00:07:15,580
到时候拼一下这样一个路径

262
00:07:15,580 --> 00:07:16,600
叫ParentPath

263
00:07:16,600 --> 00:07:17,380
复路径

264
00:07:17,380 --> 00:07:18,580
再来看看

265
00:07:18,580 --> 00:07:20,340
现在是不是可以拿到这个东西

266
00:07:20,340 --> 00:07:21,060
Costlog

267
00:07:21,060 --> 00:07:22,060
咱写一步

268
00:07:22,060 --> 00:07:22,980
侧一步

269
00:07:22,980 --> 00:07:23,380
Costlog

270
00:07:23,380 --> 00:07:24,800
比如说打印一下这个sauce

271
00:07:24,800 --> 00:07:26,520
还有我们这样一个ParentPath

272
00:07:26,520 --> 00:07:27,820
OK

273
00:07:27,820 --> 00:07:29,320
我们来运行一下

274
00:07:29,320 --> 00:07:31,380
把这个文件点过来

275
00:07:31,380 --> 00:07:35,240
稍微点慢

276
00:07:35,240 --> 00:07:36,360
稍微等等

277
00:07:36,360 --> 00:07:41,080
他说好像是OK了

278
00:07:41,080 --> 00:07:43,660
第一是不是引到了我们这样一个路径

279
00:07:43,660 --> 00:07:45,520
第二个是不是复路径就是SRC

280
00:07:45,520 --> 00:07:47,440
这里面告诉我的说不能解构

281
00:07:47,440 --> 00:07:48,260
因为没有这属性

282
00:07:48,260 --> 00:07:49,500
因为这属性里面怎么样

283
00:07:49,500 --> 00:07:50,920
现在反复的是暗地范

284
00:07:50,920 --> 00:07:52,280
那别急对吧

285
00:07:52,280 --> 00:07:53,180
咱一步步来做

286
00:07:53,180 --> 00:07:55,040
那这个pass里面我要怎么做呢

287
00:07:55,040 --> 00:07:56,680
我说了主要就是靠什么呢

288
00:07:56,680 --> 00:07:59,640
是不是叫靠这个AST解析预法术

289
00:07:59,640 --> 00:08:01,280
解析预法术

