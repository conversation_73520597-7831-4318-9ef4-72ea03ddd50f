1
00:00:00,000 --> 00:00:02,100
这一节呢

2
00:00:02,100 --> 00:00:05,160
我们来讲一下这个webpack中的蓝加载功能

3
00:00:05,160 --> 00:00:06,360
那这里面非常简单

4
00:00:06,360 --> 00:00:08,060
我们先把这个还是一样

5
00:00:08,060 --> 00:00:09,060
先备份一份吧

6
00:00:09,060 --> 00:00:10,560
我们先建个文件家

7
00:00:10,560 --> 00:00:11,100
那这里呢

8
00:00:11,100 --> 00:00:13,800
我们就在这里面建上一个新建

9
00:00:13,800 --> 00:00:17,500
我刚才写的那个叫splet trunks

10
00:00:17,500 --> 00:00:18,340
我把这个代码呢

11
00:00:18,340 --> 00:00:19,760
直接都粘进去啊

12
00:00:19,760 --> 00:00:21,300
这是我的配置文件

13
00:00:21,300 --> 00:00:23,500
把它放到splet trunks

14
00:00:23,500 --> 00:00:25,240
我们同样的把这个webpack配置呢

15
00:00:25,240 --> 00:00:26,900
也拷贝一份放进来

16
00:00:26,900 --> 00:00:27,500
完了这里呢

17
00:00:27,500 --> 00:00:28,040
我都关掉

18
00:00:28,040 --> 00:00:29,160
关闭其他

19
00:00:29,160 --> 00:00:33,000
完了并且呢我把这src目录啊为了哎这就可以了都干掉了

20
00:00:33,000 --> 00:00:35,620
那这里面呢我把这个优化项的干掉

21
00:00:35,620 --> 00:00:39,120
那同样呢这个other呢我也就删掉了啊

22
00:00:39,120 --> 00:00:40,800
这是一个基本的wepile page

23
00:00:40,800 --> 00:00:44,800
那这里面一样啊我再建一个文件index.js

24
00:00:44,800 --> 00:00:46,820
完了我并且我希望干嘛呢

25
00:00:46,820 --> 00:00:48,720
我可能页面上一直都有个按钮

26
00:00:48,720 --> 00:00:51,460
比如说我一点按钮呢我再去加载某段js

27
00:00:51,460 --> 00:00:52,620
这是有可能的啊

28
00:00:52,620 --> 00:00:54,200
比如说一些播放那也是这样

29
00:00:54,200 --> 00:00:56,260
比如我点按钮说再去加载资源啊

30
00:00:56,260 --> 00:00:57,500
那这样的话非常方便

31
00:00:57,500 --> 00:00:59,100
那我肯定还有一个资源的对吧

32
00:00:59,100 --> 00:01:01,260
比如说叫sauce.js

33
00:01:01,260 --> 00:01:02,740
这个资源里面就非常简单

34
00:01:02,740 --> 00:01:03,340
假如说呀

35
00:01:03,340 --> 00:01:09,300
我就强制的写句代码叫module.esports等一个中备讯

36
00:01:09,300 --> 00:01:11,100
或者我这里面就yes路代码最好了

37
00:01:11,100 --> 00:01:11,300
对吧

38
00:01:11,300 --> 00:01:14,700
esport default中备讯我导着去

39
00:01:14,700 --> 00:01:15,340
那这边呀

40
00:01:15,340 --> 00:01:16,980
我们是不是应该构建一个按钮

41
00:01:16,980 --> 00:01:17,740
比如说按钮呢

42
00:01:17,740 --> 00:01:19,260
我就在这样写吧

43
00:01:19,260 --> 00:01:20,900
let一个button

44
00:01:20,900 --> 00:01:24,900
button等于document.create

45
00:01:24,900 --> 00:01:27,140
element

46
00:01:27,140 --> 00:01:28,860
创建一个这样的button

47
00:01:28,860 --> 00:01:33,480
这里面呢,我们是不是可以给这个button,贴上一个顶线事件, add event

48
00:01:33,480 --> 00:01:35,560
 listener,来一个click事件,

49
00:01:35,560 --> 00:01:38,660
完了并且呢,给个回调,

50
00:01:38,660 --> 00:01:45,760
完了此时呢,我就先来一句log吧,code log,click,

51
00:01:45,760 --> 00:01:49,400
那之后呢,我把这个button扔进去,

52
00:01:49,400 --> 00:01:56,100
document.body.opend child,

53
00:01:57,600 --> 00:01:59,280
opend child

54
00:01:59,280 --> 00:01:59,980
然后里面呢

55
00:01:59,980 --> 00:02:01,680
我就把这个button的放进去

56
00:02:01,680 --> 00:02:03,480
b u t t o n

57
00:02:03,480 --> 00:02:05,520
那这就是一个gs代码是吧

58
00:02:05,520 --> 00:02:07,640
那现在我们要做的事就是点完以后呢

59
00:02:07,640 --> 00:02:08,240
不是谈

60
00:02:08,240 --> 00:02:09,760
而是把这个资源加载出来

61
00:02:09,760 --> 00:02:10,640
那这时候呢

62
00:02:10,640 --> 00:02:12,000
我们先用一下是吧

63
00:02:12,000 --> 00:02:13,040
非常简单

64
00:02:13,040 --> 00:02:14,440
npm run什么呢

65
00:02:14,440 --> 00:02:15,280
run dv

66
00:02:15,280 --> 00:02:16,240
我先跑下来啊

67
00:02:16,240 --> 00:02:17,040
那执行一下

68
00:02:17,040 --> 00:02:19,080
这里一样

69
00:02:19,080 --> 00:02:20,960
我们可以看看端号3000

70
00:02:20,960 --> 00:02:23,280
那我们就直接打开网页网页了啊

71
00:02:23,280 --> 00:02:23,880
这里面呢

72
00:02:23,880 --> 00:02:25,560
他应该给我来个按钮是吧

73
00:02:25,560 --> 00:02:26,680
按钮出来

74
00:02:26,680 --> 00:02:27,480
哦这按钮没在

75
00:02:27,600 --> 00:02:57,040
字是吧,好丑,然后在这里面,button,对,比如说,点,点,这个,internetml,等一个hello,刷新一项,是吧,有点慢,是吧,出来,出来,出来,他告诉我,嗯,好玩了,完事了,是吧,我点,那这里面是不是他就应该给我出来一个,呃,值,叫click,那现在我们要做什么事呢,我这里面其实要加载某个资源,是吧,那是不是加载这个sauce,那好,我可以用这样一个语法,

76
00:02:57,280 --> 00:02:59,580
语法并不是我们对吧

77
00:02:59,580 --> 00:03:02,380
现在这个版本就是我们所谓的es6语法

78
00:03:02,380 --> 00:03:04,280
它是一个草案中的语法

79
00:03:04,280 --> 00:03:07,080
而且是把pick中提供的是吧

80
00:03:07,080 --> 00:03:09,080
我们用的时候可以通过input的语法

81
00:03:09,080 --> 00:03:10,380
往这去引比如引谁

82
00:03:10,380 --> 00:03:12,080
我就要引sauce文件

83
00:03:12,080 --> 00:03:13,980
其实非常简单

84
00:03:13,980 --> 00:03:15,180
它就是通过什么

85
00:03:15,180 --> 00:03:16,380
jsonp来实现的对吧

86
00:03:16,380 --> 00:03:18,880
就是jsonp实现对吧

87
00:03:18,880 --> 00:03:21,280
动态加载文件

88
00:03:21,280 --> 00:03:22,880
好了

89
00:03:22,880 --> 00:03:25,380
而且它加载完返回的是个什么东西

90
00:03:25,380 --> 00:03:26,500
是个promise

91
00:03:26,500 --> 00:03:28,260
可以在这里面打出它的结果

92
00:03:28,260 --> 00:03:29,180
edate

93
00:03:29,180 --> 00:03:31,400
就是说它是个promise

94
00:03:31,400 --> 00:03:32,340
加载完以后

95
00:03:32,340 --> 00:03:34,540
会把东西放到date里面去

96
00:03:34,540 --> 00:03:36,720
同样再来试试看看效果

97
00:03:36,720 --> 00:03:39,700
这里面别应该会报错

98
00:03:39,700 --> 00:03:40,680
你看过来报错了

99
00:03:40,680 --> 00:03:41,880
这东西不支持

100
00:03:41,880 --> 00:03:43,360
你需要加上这样一个叫什么

101
00:03:43,360 --> 00:03:45,680
叫愈法动态导入的一个插件

102
00:03:45,680 --> 00:03:47,320
这是一个babel插件

103
00:03:47,320 --> 00:03:49,680
好了咱就听人家的

104
00:03:49,680 --> 00:03:51,920
你加这插件

105
00:03:51,920 --> 00:03:53,200
我来来个杠地

106
00:03:53,200 --> 00:03:54,740
那这个插件啊

107
00:03:54,740 --> 00:03:55,740
我们还需要怎么样

108
00:03:55,740 --> 00:03:58,340
是不是在我们的vipac中配置一下

109
00:03:58,340 --> 00:03:59,220
那这里一样

110
00:03:59,220 --> 00:04:00,140
有预设

111
00:04:00,140 --> 00:04:02,920
还可以再加上一个所谓的叫插件

112
00:04:02,920 --> 00:04:04,000
那插件的话

113
00:04:04,000 --> 00:04:05,860
是不是把这东西奔一粘就OK了

114
00:04:05,860 --> 00:04:06,820
那好了啊

115
00:04:06,820 --> 00:04:08,040
再来试一试吧

116
00:04:08,040 --> 00:04:09,140
再来乱一下

117
00:04:09,140 --> 00:04:10,300
npm run

118
00:04:10,300 --> 00:04:11,140
dv

119
00:04:11,140 --> 00:04:13,220
那这时候呢

120
00:04:13,220 --> 00:04:13,940
又会开始打包

121
00:04:13,940 --> 00:04:15,120
那打包的话呢

122
00:04:15,120 --> 00:04:16,280
又会执行3000是吧

123
00:04:16,280 --> 00:04:17,100
有起伏6万7

124
00:04:17,100 --> 00:04:17,900
很棒啊

125
00:04:17,900 --> 00:04:18,380
这种感觉

126
00:04:18,380 --> 00:04:19,120
那好了

127
00:04:19,120 --> 00:04:20,400
那在这看看效果

128
00:04:20,400 --> 00:04:21,540
就是现在啊

129
00:04:21,540 --> 00:04:21,880
我一点

130
00:04:21,880 --> 00:04:24,460
你看出来的是什么是不是就是一个猫丢

131
00:04:24,460 --> 00:04:27,260
而且他会把结果放到哪去

132
00:04:27,260 --> 00:04:28,020
default上

133
00:04:28,020 --> 00:04:28,820
他会怎么样

134
00:04:28,820 --> 00:04:30,460
当成这是一个es模块

135
00:04:30,460 --> 00:04:32,000
这里面可以怎么做

136
00:04:32,000 --> 00:04:33,120
回到我这里面

137
00:04:33,120 --> 00:04:35,260
是不是应该叫data.default

138
00:04:35,260 --> 00:04:36,260
我来点什么

139
00:04:36,260 --> 00:04:39,160
default指的是不是就是我们这样一个结果

140
00:04:39,160 --> 00:04:41,260
看到了我就直接点了

141
00:04:41,260 --> 00:04:41,740
我点

142
00:04:41,740 --> 00:04:42,940
是揣住门讯

143
00:04:42,940 --> 00:04:44,920
而且你看这资源就能看得懂

144
00:04:44,920 --> 00:04:45,940
我把它往上拉一拉

145
00:04:45,940 --> 00:04:48,280
现在我们刚开始我一刷新

146
00:04:48,280 --> 00:04:50,500
他加载的是不是只有index.js

147
00:04:50,500 --> 00:04:51,920
你看有index

148
00:04:51,920 --> 00:04:54,340
完了这时候我们把它清空一下

149
00:04:54,340 --> 00:04:54,660
是吧

150
00:04:54,660 --> 00:04:55,360
清空在这

151
00:04:55,360 --> 00:04:56,800
有一点

152
00:04:56,800 --> 00:04:58,380
你看是不是加载了一点几次

153
00:04:58,380 --> 00:04:59,400
这一里面干嘛

154
00:04:59,400 --> 00:05:02,120
是不是放的就是我们这样一个结果

155
00:05:02,120 --> 00:05:03,340
结果里面是不是放的就是

156
00:05:03,340 --> 00:05:05,460
你看是把结果放到default的属性上

157
00:05:05,460 --> 00:05:06,200
等这样个结果

158
00:05:06,200 --> 00:05:09,820
所以说我们在这里面引入完组件以后

159
00:05:09,820 --> 00:05:11,140
引入完文件以后

160
00:05:11,140 --> 00:05:11,940
成功后

161
00:05:11,940 --> 00:05:14,140
我们可以拿到data里的default的属性

162
00:05:14,140 --> 00:05:16,320
default的属性就是它的结果

163
00:05:16,320 --> 00:05:17,800
这就是一个懒加载的应用

164
00:05:17,800 --> 00:05:19,600
当然了包括懒加载

165
00:05:19,600 --> 00:05:20,420
其实是包括什么

166
00:05:20,420 --> 00:05:23,700
比如说view的浪加载路由浪加载

167
00:05:23,700 --> 00:05:23,920
是吧

168
00:05:23,920 --> 00:05:25,460
还有我们的react的浪加载

169
00:05:25,460 --> 00:05:26,820
都是靠这样来实现

170
00:05:26,820 --> 00:05:28,020
你可以看看

171
00:05:28,020 --> 00:05:29,940
它其实生成的就是什么

172
00:05:29,940 --> 00:05:31,200
就是两个文件

173
00:05:31,200 --> 00:05:32,500
npm run build

174
00:05:32,500 --> 00:05:34,240
分别生成一个什么

175
00:05:34,240 --> 00:05:35,540
1一个index

176
00:05:35,540 --> 00:05:36,760
到时候怎么样

177
00:05:36,760 --> 00:05:39,300
它会通过文件去调用我们文件

178
00:05:39,300 --> 00:05:40,360
你看是个1

179
00:05:40,360 --> 00:05:43,600
1里面是不是就是我们所谓的质赞P

180
00:05:43,600 --> 00:05:44,340
一个方法

181
00:05:44,340 --> 00:05:46,340
这里面我就先不去说圆码了

182
00:05:46,340 --> 00:05:46,780
好

183
00:05:46,780 --> 00:05:49,080
我们就知道了路由浪加载的实现

184
00:05:49,080 --> 00:05:50,520
其实靠的就是我们这样一个

185
00:05:50,520 --> 00:05:51,420
Import语法

