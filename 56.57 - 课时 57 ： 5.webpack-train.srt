1
00:00:00,000 --> 00:00:01,000
sing来注册的

2
00:00:01,000 --> 00:00:03,720
也是什么一个名字和一个回调

3
00:00:03,720 --> 00:00:06,040
回调里边有callback fn

4
00:00:06,040 --> 00:00:07,480
对吧有回调按数是不是

5
00:00:07,480 --> 00:00:08,880
出发怎么出发

6
00:00:08,880 --> 00:00:10,140
用call a thing来出发

7
00:00:10,140 --> 00:00:11,020
对吧

8
00:00:11,020 --> 00:00:11,840
另外还有什么

9
00:00:11,840 --> 00:00:12,620
这是promise

10
00:00:12,620 --> 00:00:13,760
promise你看

11
00:00:13,760 --> 00:00:14,780
promise

12
00:00:14,780 --> 00:00:16,320
你看这也是它的原理

13
00:00:16,320 --> 00:00:16,980
也是原理啊

14
00:00:16,980 --> 00:00:18,080
我们就没共写了

15
00:00:18,080 --> 00:00:18,640
我们就不写了

16
00:00:18,640 --> 00:00:20,820
然后这什么promise

17
00:00:20,820 --> 00:00:21,780
就通过promise来注册

18
00:00:21,780 --> 00:00:22,580
怎么出发

19
00:00:22,580 --> 00:00:24,100
通过promise来出发

20
00:00:24,100 --> 00:00:26,560
然后这是它的原理

21
00:00:26,560 --> 00:00:28,400
这个大家下去自己看就可以了

22
00:00:28,400 --> 00:00:29,300
看看就可以了

23
00:00:29,300 --> 00:00:31,200
然后这个我们上课就不再写这个了

24
00:00:31,200 --> 00:00:32,540
咱们先直播主题啊

25
00:00:32,540 --> 00:00:34,000
我们写我们这会有环节

26
00:00:34,000 --> 00:00:35,440
就是实现YPAC啊

27
00:00:35,440 --> 00:00:37,560
咱们写这个写这个东西啊

28
00:00:37,560 --> 00:00:40,000
就是从现在开始啊

29
00:00:40,000 --> 00:00:41,840
我带大家实现一个完整的YPAC

30
00:00:41,840 --> 00:00:43,540
我们尽可能的模拟什么呀

31
00:00:43,540 --> 00:00:44,960
模拟这个原生的实现

32
00:00:44,960 --> 00:00:46,040
这样啊

33
00:00:46,040 --> 00:00:47,060
我现在打开两个项目

34
00:00:47,060 --> 00:00:47,900
你看啊

35
00:00:47,900 --> 00:00:48,800
一个是我们自己项目

36
00:00:48,800 --> 00:00:50,240
你看是不是自己项目啊

37
00:00:50,240 --> 00:00:52,560
你看我建议工业家叫YPAC

38
00:00:52,560 --> 00:00:54,860
是不是啊

39
00:00:54,860 --> 00:00:56,060
这是我们自己写的啊

40
00:00:56,060 --> 00:00:57,860
然后把刚才那点也打开

41
00:00:57,860 --> 00:00:59,700
我们来新建一个window

42
00:00:59,700 --> 00:01:02,860
然后呢我们打开根本的项目

43
00:01:02,860 --> 00:01:05,900
嗯

44
00:01:05,900 --> 00:01:09,860
好这这次是吧就他

45
00:01:09,860 --> 00:01:13,000
好咱们呀给我我这样啊

46
00:01:13,000 --> 00:01:15,300
我一边debug咱们一边模拟他来写

47
00:01:15,300 --> 00:01:18,760
我们尽量呢尽量能够百分百还原啊

48
00:01:18,760 --> 00:01:20,100
关于他是这个编义流程

49
00:01:20,100 --> 00:01:23,600
好你看我现在我先把这个调书坊书打开

50
00:01:23,600 --> 00:01:26,120
打开我们CI打开我们这个啊打开我们这个

51
00:01:26,700 --> 00:01:29,160
就他也行 其实也可以一样的 对吧

52
00:01:29,160 --> 00:01:38,040
这个其实也可以 我们就在这打一打吧 这也可以啊

53
00:01:38,040 --> 00:01:41,160
好 我们开始调试 我们一边调试一边写是吧

54
00:01:41,160 --> 00:01:48,660
好 你看第八个是吧 走

55
00:01:48,660 --> 00:01:52,700
刚先创建什么 创建wipag是不是 走进去

56
00:01:53,860 --> 00:01:56,200
那你会发现webpackwebpack是什么webpack是个函数啊

57
00:01:56,200 --> 00:01:59,060
是不是接着一个option和callback吧

58
00:01:59,060 --> 00:02:02,020
好咱们一边对着他一边写啊对他写

59
00:02:02,020 --> 00:02:03,660
好我们现在呀打开我们的目录

60
00:02:03,660 --> 00:02:05,560
我们建一个入口文件叫inxs

61
00:02:05,560 --> 00:02:08,000
这个呀就是我们的webpack了

62
00:02:08,000 --> 00:02:08,920
ok吧

63
00:02:08,920 --> 00:02:10,700
那么他也是个函数

64
00:02:10,700 --> 00:02:11,360
对不对

65
00:02:11,360 --> 00:02:13,620
function什么webpack是个函数

66
00:02:13,620 --> 00:02:14,560
最后要导出吧

67
00:02:14,560 --> 00:02:17,760
module比较什么adpost等于webpack

68
00:02:17,760 --> 00:02:18,560
对吧

69
00:02:18,560 --> 00:02:21,300
然后呢他有什么

70
00:02:21,300 --> 00:02:21,960
两个参数

71
00:02:21,960 --> 00:02:23,700
一个是option一个callback

72
00:02:23,700 --> 00:02:25,140
好,来写啊,一个是option

73
00:02:25,140 --> 00:02:28,200
一个什么,一个是callback,一个回调

74
00:02:28,200 --> 00:02:33,640
就是写出事啊,这个webpack是一个什么,函数

75
00:02:33,640 --> 00:02:37,240
第一个参数是什么

76
00:02:37,240 --> 00:02:38,940
是一个options

77
00:02:38,940 --> 00:02:41,340
第二个参数

78
00:02:41,340 --> 00:02:46,220
是什么,是一个callback

79
00:02:46,220 --> 00:02:48,860
是不是啊,是一个回调,这个callback

80
00:02:48,860 --> 00:02:51,920
那么我们下面要干嘛

81
00:02:51,920 --> 00:02:54,280
是不要写这个外派的事情逻辑啊

82
00:02:54,280 --> 00:02:54,880
对吧

83
00:02:54,880 --> 00:02:56,920
那里边怎么多

84
00:02:56,920 --> 00:02:57,920
你看第一步怎么样

85
00:02:57,920 --> 00:02:59,360
我们先这个往下走啊

86
00:02:59,360 --> 00:02:59,920
不重要

87
00:02:59,920 --> 00:03:00,640
我忽略掉了啊

88
00:03:00,640 --> 00:03:01,800
我这些重要的逻辑啊

89
00:03:01,800 --> 00:03:02,440
往下走

90
00:03:02,440 --> 00:03:04,560
这一步在怎么验证我们这个什么呀

91
00:03:04,560 --> 00:03:06,800
验证我们这个呃

92
00:03:06,800 --> 00:03:07,960
配置文件的合法性

93
00:03:07,960 --> 00:03:08,880
啊

94
00:03:08,880 --> 00:03:09,800
你不要刻意写错的话

95
00:03:09,800 --> 00:03:10,840
稍微报错的啊

96
00:03:10,840 --> 00:03:13,360
看如果没有配置文件的话

97
00:03:13,360 --> 00:03:14,080
如果有错的话

98
00:03:14,080 --> 00:03:15,120
也跑错误

99
00:03:15,120 --> 00:03:16,320
然后你看干嘛

100
00:03:16,320 --> 00:03:18,480
是不是穿进了一个compiler

101
00:03:18,480 --> 00:03:18,760
对吧

102
00:03:18,760 --> 00:03:20,120
生命了一个变量compiler啊

103
00:03:20,120 --> 00:03:20,400
好

104
00:03:20,400 --> 00:03:21,280
我们生命变量

105
00:03:21,920 --> 00:03:24,180
声明一个变量叫compiler

106
00:03:24,180 --> 00:03:26,740
Let一个compiler

107
00:03:26,740 --> 00:03:28,480
compiler的什么意思

108
00:03:28,480 --> 00:03:30,340
代表什么呀

109
00:03:30,340 --> 00:03:32,160
本次编译对象

110
00:03:32,160 --> 00:03:35,720
代表本次编译对象

111
00:03:35,720 --> 00:03:38,940
这个一次编译

112
00:03:38,940 --> 00:03:42,440
一次编译只会有一个什么呀

113
00:03:42,440 --> 00:03:43,100
compiler

114
00:03:43,100 --> 00:03:45,820
就像一个饭店一样

115
00:03:45,820 --> 00:03:46,660
这次有一个什么

116
00:03:46,660 --> 00:03:47,800
一个这个店长

117
00:03:47,800 --> 00:03:51,000
但是厨师可以很多个

118
00:03:51,000 --> 00:03:53,580
是吧 好 我们来声明一个compiler

119
00:03:53,580 --> 00:03:55,800
然后呢 往下走

120
00:03:55,800 --> 00:04:01,480
看啊 它为什么

121
00:04:01,480 --> 00:04:03,520
你看这怎么叫 这要干嘛

122
00:04:03,520 --> 00:04:05,200
WiPag Option Default 什么意思

123
00:04:05,200 --> 00:04:06,580
处理默认参数

124
00:04:06,580 --> 00:04:08,880
这个我们先不管它啊 我们先往下做

125
00:04:08,880 --> 00:04:10,760
我们之前中了 你看 下面怎么样

126
00:04:10,760 --> 00:04:12,120
你看compiler等于什么

127
00:04:12,120 --> 00:04:14,640
6月compiler 什么意思啊 创建compiler实例

128
00:04:14,640 --> 00:04:16,760
传与什么呀 我们的contact出现

129
00:04:16,760 --> 00:04:18,780
contact出现什么呀 上下文

130
00:04:18,780 --> 00:04:20,300
就是当前项目呀

131
00:04:20,300 --> 00:04:22,000
他是在哪个项目下面跑的

132
00:04:22,000 --> 00:04:22,420
上海文

133
00:04:22,420 --> 00:04:22,820
好

134
00:04:22,820 --> 00:04:23,900
我们现在把这个搞一下

135
00:04:23,900 --> 00:04:26,660
我们来

136
00:04:26,660 --> 00:04:27,320
你看

137
00:04:27,320 --> 00:04:28,480
他等于什么

138
00:04:28,480 --> 00:04:30,140
等于一个new competitor

139
00:04:30,140 --> 00:04:33,020
new一个compatter实力

140
00:04:33,020 --> 00:04:34,720
然后传进我们的options

141
00:04:34,720 --> 00:04:35,400
点什么

142
00:04:35,400 --> 00:04:36,180
点context

143
00:04:36,180 --> 00:04:38,660
但是context我们做一下兼容

144
00:04:38,660 --> 00:04:40,060
我们可以做一下兼容

145
00:04:40,060 --> 00:04:40,460
就是说

146
00:04:40,460 --> 00:04:42,140
他等于谁呢

147
00:04:42,140 --> 00:04:44,240
如果他你传了值了

148
00:04:44,240 --> 00:04:45,080
有你自己传的

149
00:04:45,080 --> 00:04:46,140
如果没有传的

150
00:04:46,140 --> 00:04:46,580
我们用什么

151
00:04:46,580 --> 00:04:47,600
默认用process

152
00:04:47,600 --> 00:04:48,240
点什么

153
00:04:48,240 --> 00:04:53,620
用个process.cwd

154
00:04:53,620 --> 00:04:55,100
可以用它是不是

155
00:04:55,100 --> 00:04:58,280
就说如果你传了

156
00:04:58,280 --> 00:04:59,320
用你传的这个值

157
00:04:59,320 --> 00:05:00,380
如果没有传的

158
00:05:00,380 --> 00:05:00,660
用什么

159
00:05:00,660 --> 00:05:01,900
用当前的工作目录

160
00:05:01,900 --> 00:05:04,420
就说这个上下文啊

161
00:05:04,420 --> 00:05:04,960
非常重要

162
00:05:04,960 --> 00:05:07,040
上下文

163
00:05:07,040 --> 00:05:10,840
地址非常重要

164
00:05:10,840 --> 00:05:13,520
这个或者呢

165
00:05:13,520 --> 00:05:13,840
是什么

166
00:05:13,840 --> 00:05:17,300
是指向原来的上下文

167
00:05:17,300 --> 00:05:21,820
就是或者叫参数里上下文吧

168
00:05:21,820 --> 00:05:23,380
参数里的上下文

169
00:05:23,380 --> 00:05:25,700
如果参数没传的话

170
00:05:25,700 --> 00:05:28,160
默认就指什么默认就指向

171
00:05:28,160 --> 00:05:30,900
就是当前的工作目录

172
00:05:30,900 --> 00:05:33,160
就是说你在那执行这个命令

173
00:05:33,160 --> 00:05:34,020
他就指向哪是不是

174
00:05:34,020 --> 00:05:38,100
那一般你回来跟目录执行

175
00:05:38,100 --> 00:05:39,020
那就指是跟目录呗

176
00:05:39,020 --> 00:05:40,760
指是谁指是用的第四这个目录

177
00:05:40,760 --> 00:05:41,760
他是这样的啊

178
00:05:41,760 --> 00:05:43,700
你们做一下这样兼容处理是不是

179
00:05:43,700 --> 00:05:44,520
你看我这传了吗

180
00:05:44,520 --> 00:05:45,540
看配这文件有吗

181
00:05:45,540 --> 00:05:47,160
没有啊

182
00:05:47,160 --> 00:05:48,760
没有传下呗

183
00:05:48,760 --> 00:05:50,060
比如说我要传一个contest是吧

184
00:05:50,060 --> 00:05:50,780
这可以传啊

185
00:05:50,780 --> 00:05:51,220
contest

186
00:05:51,220 --> 00:05:55,760
是什么默认是process.cw就可以了是吧

187
00:05:55,760 --> 00:05:55,980
嗯

188
00:05:55,980 --> 00:06:00,060
就上海门默认默认是什么

189
00:06:00,060 --> 00:06:03,720
默认就是我们这个默认是我们的工作目录

190
00:06:03,720 --> 00:06:07,220
可以吧

191
00:06:07,220 --> 00:06:08,620
好走

192
00:06:08,620 --> 00:06:09,740
你看啊

193
00:06:09,740 --> 00:06:11,740
是不是创建这个compiler对象

194
00:06:11,740 --> 00:06:12,600
对不对

195
00:06:12,600 --> 00:06:13,960
然后呢

196
00:06:13,960 --> 00:06:15,620
你看我们这么做

197
00:06:15,620 --> 00:06:16,880
compiler

198
00:06:16,880 --> 00:06:18,980
他的什么options

199
00:06:18,980 --> 00:06:20,340
等于options

200
00:06:20,340 --> 00:06:21,100
是不是

201
00:06:21,100 --> 00:06:23,380
就是把我们传下来的option对象啊

202
00:06:23,380 --> 00:06:24,300
就是谁就是他吧

203
00:06:24,300 --> 00:06:25,080
就是对象怎么样

204
00:06:25,080 --> 00:06:26,580
付给我们的compiler

205
00:06:26,580 --> 00:06:27,720
以后会有用啊

206
00:06:27,720 --> 00:06:30,680
就是把option怎么样传给compileroptions

207
00:06:30,680 --> 00:06:31,520
对吧

208
00:06:31,520 --> 00:06:32,800
是不是

209
00:06:32,800 --> 00:06:34,740
然后呢

210
00:06:34,740 --> 00:06:36,640
和原码

211
00:06:36,640 --> 00:06:37,960
跟那原码往下走啊

212
00:06:37,960 --> 00:06:38,380
往下走

213
00:06:38,380 --> 00:06:39,660
是不是传起来compiler对象了

214
00:06:39,660 --> 00:06:40,500
好往下走

215
00:06:40,500 --> 00:06:42,140
这个我们先打个断点啊

216
00:06:42,140 --> 00:06:43,640
一会再印了是吧

217
00:06:43,640 --> 00:06:44,500
是吧

218
00:06:44,500 --> 00:06:45,780
是把option传给了compileroptions

219
00:06:45,780 --> 00:06:47,480
然后下午你看

220
00:06:47,480 --> 00:06:48,020
你又来什么

221
00:06:48,020 --> 00:06:49,680
你又来个Node environment plugin

222
00:06:49,680 --> 00:06:50,940
你有了个插件

223
00:06:50,940 --> 00:06:52,960
然后apply confider

224
00:06:52,960 --> 00:06:54,720
就说这里面要涉及到怎么写插件

225
00:06:54,720 --> 00:06:56,020
就是插件是个类

226
00:06:56,020 --> 00:06:57,700
然后有apply方法

227
00:06:57,700 --> 00:06:58,580
对吧

228
00:06:58,580 --> 00:06:59,420
然后可以给它传餐

229
00:06:59,420 --> 00:07:01,100
它里边可以整个可言户

230
00:07:01,100 --> 00:07:01,340
是不是

231
00:07:01,340 --> 00:07:02,080
整个逻辑吧

232
00:07:02,080 --> 00:07:03,640
那它在干嘛呢

233
00:07:03,640 --> 00:07:04,260
我们进去看看

234
00:07:04,260 --> 00:07:06,900
这个node environment plugin在干嘛呢

235
00:07:06,900 --> 00:07:07,360
进去看看

236
00:07:07,360 --> 00:07:11,220
先拿到option的是吧

237
00:07:11,220 --> 00:07:12,100
它apply在干嘛

238
00:07:12,100 --> 00:07:14,040
apply在干嘛

239
00:07:14,040 --> 00:07:14,640
apply在干嘛

240
00:07:14,640 --> 00:07:15,300
它是在干嘛

241
00:07:15,300 --> 00:07:17,600
是不是在去这个日志上

242
00:07:17,600 --> 00:07:18,580
我们也不管它

243
00:07:18,580 --> 00:07:20,660
重点就在两句

244
00:07:20,660 --> 00:07:21,940
你看指定了吗

245
00:07:21,940 --> 00:07:22,960
指定counter的什么

246
00:07:22,960 --> 00:07:25,040
input file setup

247
00:07:25,040 --> 00:07:27,540
就是一个什么输入的文件系统

248
00:07:27,540 --> 00:07:28,480
是它是不是

249
00:07:28,480 --> 00:07:30,220
还有一个输入的文件系统

250
00:07:30,220 --> 00:07:30,660
是它

251
00:07:30,660 --> 00:07:33,060
所以我们现在模拟一个这个材件

252
00:07:33,060 --> 00:07:34,400
我们来写这个材件

253
00:07:34,400 --> 00:07:37,800
它其实我们这样

254
00:07:37,800 --> 00:07:38,640
我们建一个文件夹

255
00:07:38,640 --> 00:07:39,780
叫plugins

256
00:07:39,780 --> 00:07:41,000
放入搜索材件

257
00:07:41,000 --> 00:07:42,660
好

258
00:07:42,660 --> 00:07:43,320
我们建一个什么

259
00:07:43,320 --> 00:07:44,060
建一个这个材件

260
00:07:44,060 --> 00:07:44,860
叫什么叫这个

261
00:07:44,860 --> 00:07:46,680
叫js

262
00:07:46,680 --> 00:07:49,740
好我们写个类class什么呀

263
00:07:49,740 --> 00:07:51,680
叫node environment plugin

264
00:07:51,680 --> 00:07:54,260
然后呢它要导出吧

265
00:07:54,260 --> 00:07:56,060
module.adpost

266
00:07:56,060 --> 00:07:58,100
等于个它是不是啊

267
00:07:58,100 --> 00:08:00,800
然后它呢有个什么有个apply方法

268
00:08:00,800 --> 00:08:02,900
然后参注呢就是compiler

269
00:08:02,900 --> 00:08:04,400
就是我们变运对象吧

270
00:08:04,400 --> 00:08:04,960
compiler

271
00:08:04,960 --> 00:08:07,340
然后里边呢

272
00:08:07,340 --> 00:08:09,940
其实核心有两句话啊

273
00:08:09,940 --> 00:08:11,060
一个一句话第一句话叫什么

274
00:08:11,060 --> 00:08:13,200
你看compiler点什么

275
00:08:13,200 --> 00:08:14,400
负两个值

276
00:08:14,400 --> 00:08:16,840
一个叫InputFileSystem

277
00:08:16,840 --> 00:08:18,540
一个叫OutputFileSystem

278
00:08:18,540 --> 00:08:20,580
一个叫InputFileSystem

279
00:08:20,580 --> 00:08:22,400
我们俩等于FS就可以了

280
00:08:22,400 --> 00:08:22,880
比较简单

281
00:08:22,880 --> 00:08:31,260
叫Output

282
00:08:31,260 --> 00:08:34,680
这什么叫Input什么Output呢

283
00:08:34,680 --> 00:08:37,220
当你读文件的时候

284
00:08:37,220 --> 00:08:40,480
用谁来读

285
00:08:40,480 --> 00:08:43,600
用哪个模块来读

286
00:08:43,600 --> 00:08:45,520
这可以指定是吧

287
00:08:45,520 --> 00:08:46,160
模块来读

288
00:08:46,160 --> 00:08:48,920
对吧

289
00:08:48,920 --> 00:08:50,700
那么output一次雷碎什么呀

290
00:08:50,700 --> 00:08:51,700
当你写文件的时候

291
00:08:51,700 --> 00:08:53,480
像硬盘上啊

292
00:08:53,480 --> 00:08:55,020
或者要写入文件的时候

293
00:08:55,020 --> 00:08:57,160
对吧

294
00:08:57,160 --> 00:08:58,920
通过哪个模块来写

295
00:08:58,920 --> 00:09:00,720
我们现在呀

296
00:09:00,720 --> 00:09:02,080
一锅会了都用什么FS

297
00:09:02,080 --> 00:09:03,840
Avid是node回应模块吧

298
00:09:03,840 --> 00:09:04,960
LadFS等于什么

299
00:09:04,960 --> 00:09:06,160
RequireFS

300
00:09:06,160 --> 00:09:07,180
注意啊

301
00:09:07,180 --> 00:09:08,100
你node如果不好的话

302
00:09:08,100 --> 00:09:09,380
你肯定要补一股是吧

303
00:09:09,380 --> 00:09:11,340
咱们这个周日是

304
00:09:11,340 --> 00:09:13,000
咱们开新门啊

305
00:09:13,000 --> 00:09:14,400
可再有兴趣可以来参加对吧

306
00:09:14,400 --> 00:09:16,220
你可以把这个系统学习

307
00:09:16,220 --> 00:09:18,240
因为你说不系统学习的话

308
00:09:18,240 --> 00:09:19,320
可能说这个东西

309
00:09:19,320 --> 00:09:22,140
别的东西你接触一下有困难啊

310
00:09:22,140 --> 00:09:26,400
而且今天我们报名还有优惠啊

311
00:09:26,400 --> 00:09:27,820
大家可以考虑一下是吧

312
00:09:27,820 --> 00:09:28,600
咱们最后

313
00:09:28,600 --> 00:09:30,040
但是最后两天了

314
00:09:30,040 --> 00:09:32,120
好你看啊

315
00:09:32,120 --> 00:09:33,140
这fs对吧

316
00:09:33,140 --> 00:09:34,460
node environment plugin

317
00:09:34,460 --> 00:09:36,120
是不是导入这个模块啊

318
00:09:36,120 --> 00:09:37,160
可以了

319
00:09:37,160 --> 00:09:38,760
好那现在你看怎么用啊

320
00:09:38,760 --> 00:09:40,360
看我回到这个

321
00:09:40,360 --> 00:09:41,660
大巴里面来

322
00:09:41,660 --> 00:09:43,100
回到我们的Wattac里面来

323
00:09:43,100 --> 00:09:44,820
照顾里面啊

324
00:09:44,820 --> 00:09:46,160
你看它怎么写的

325
00:09:46,160 --> 00:09:47,480
是不是直接new就可以了

326
00:09:47,480 --> 00:09:48,440
你看它是直接new的

327
00:09:48,440 --> 00:09:52,820
它是不是直接new的呀

328
00:09:52,820 --> 00:09:53,580
然后apply的呀

329
00:09:53,580 --> 00:09:54,240
我们也直接new

330
00:09:54,240 --> 00:09:56,060
好我们new一个什么

331
00:09:56,060 --> 00:09:57,920
这个node里面plugin

332
00:09:57,920 --> 00:09:58,960
第二什么apply

333
00:09:58,960 --> 00:10:01,440
把compare传进去

334
00:10:01,440 --> 00:10:03,380
它来干嘛呀

335
00:10:03,380 --> 00:10:04,040
就是设置什么

336
00:10:04,040 --> 00:10:06,560
设置node的环境

337
00:10:06,560 --> 00:10:08,480
就是读写

338
00:10:08,480 --> 00:10:09,640
用哪个模块来读写呗

339
00:10:09,640 --> 00:10:10,560
用哪个模块

340
00:10:10,560 --> 00:10:11,520
就这意思啊

341
00:10:11,520 --> 00:10:12,880
用哪个模块

342
00:10:12,880 --> 00:10:15,140
那么你想想看啊

343
00:10:15,140 --> 00:10:16,400
这个它要引进来吧

344
00:10:16,400 --> 00:10:17,440
把它引进来

345
00:10:17,440 --> 00:10:20,740
let它造一个require

346
00:10:20,740 --> 00:10:21,660
咱们什么呀

347
00:10:21,660 --> 00:10:22,340
咱们这个叫

348
00:10:22,340 --> 00:10:23,140
第二杠

349
00:10:23,140 --> 00:10:24,360
node environment plugin

350
00:10:24,360 --> 00:10:24,780
好吧

351
00:10:24,780 --> 00:10:25,960
这个插件啊

352
00:10:25,960 --> 00:10:26,720
写好了啊

353
00:10:26,720 --> 00:10:27,740
插件就是个类

354
00:10:27,740 --> 00:10:29,060
然后有个插件方法

355
00:10:29,060 --> 00:10:30,960
然后你可以往里边传言参数

356
00:10:30,960 --> 00:10:31,720
它干净的事情

357
00:10:31,720 --> 00:10:32,280
对吧

358
00:10:32,280 --> 00:10:33,100
什么事情呢

359
00:10:33,100 --> 00:10:33,760
不一定是不是

360
00:10:33,760 --> 00:10:35,760
就是插件作用啊

361
00:10:35,760 --> 00:10:36,700
好往下看

362
00:10:36,700 --> 00:10:38,360
看下边的干嘛

363
00:10:38,480 --> 00:10:42,320
你看他拿到什么我们的plugins是不是速度啊

364
00:10:42,320 --> 00:10:44,120
这啥是不是我们这个

365
00:10:44,120 --> 00:10:46,920
这啥是不是我们这个

366
00:10:46,920 --> 00:10:49,480
就这里边这个什么

367
00:10:49,480 --> 00:10:50,760
这个对象啊

368
00:10:50,760 --> 00:10:53,080
就他里边是不是有个plugins呀

369
00:10:53,080 --> 00:10:54,860
就用户自定的这个组件呀

370
00:10:54,860 --> 00:10:55,640
对

371
00:10:55,640 --> 00:10:58,960
你看拿到了这个自定的自定的什么插件

372
00:10:58,960 --> 00:11:00,240
然后进行循环

373
00:11:00,240 --> 00:11:01,520
对吧掉他什么样

374
00:11:01,520 --> 00:11:03,560
他会掉他Apply方法

375
00:11:03,560 --> 00:11:04,840
把Compars传进去

376
00:11:04,840 --> 00:11:06,900
什么意思

377
00:11:06,900 --> 00:11:08,180
你看比如说呀

378
00:11:08,480 --> 00:11:10,260
我在这我自己写了什么写了一个

379
00:11:10,260 --> 00:11:11,960
插件啊比如说

380
00:11:11,960 --> 00:11:13,160
叫my plugin

381
00:11:13,160 --> 00:11:17,540
等于require什么呀

382
00:11:17,540 --> 00:11:18,880
这个my plugin

383
00:11:18,880 --> 00:11:20,800
第二个

384
00:11:20,800 --> 00:11:23,560
plugins

385
00:11:23,560 --> 00:11:26,900
下面的my plugin

386
00:11:26,900 --> 00:11:28,700
可以吧好

387
00:11:28,700 --> 00:11:30,380
我写下这个这个这个插件啊

388
00:11:30,380 --> 00:11:32,160
你看啊我先来建一下这个文件

389
00:11:32,160 --> 00:11:36,180
好里面建一个什么建一个my plugin

390
00:11:38,480 --> 00:11:42,160
这插件的结构都是定死的

391
00:11:42,160 --> 00:11:42,940
什么定死的

392
00:11:42,940 --> 00:11:43,420
都是一个类

393
00:11:43,420 --> 00:11:44,620
class my plugin

394
00:11:44,620 --> 00:11:46,080
有apply方法

395
00:11:46,080 --> 00:11:46,720
对不对

396
00:11:46,720 --> 00:11:49,140
然后里面传有个参数叫compiler

397
00:11:49,140 --> 00:11:49,740
都是这样

398
00:11:49,740 --> 00:11:50,560
这就是插件

399
00:11:50,560 --> 00:11:52,280
compiler

400
00:11:52,280 --> 00:11:54,360
然后我这我就简单打印一下就可以了

401
00:11:54,360 --> 00:11:55,900
打印一下我的my plugin完事

402
00:11:55,900 --> 00:11:57,780
很简单

403
00:11:57,780 --> 00:11:59,860
然后我就导出吧

404
00:11:59,860 --> 00:12:02,040
module.adpost导出我们的my plugin

405
00:12:02,040 --> 00:12:04,740
可以吧

406
00:12:04,740 --> 00:12:06,180
好

407
00:12:06,180 --> 00:12:06,820
然后呢

408
00:12:06,820 --> 00:12:08,780
你看我们干干嘛了

409
00:12:08,780 --> 00:12:10,100
是不是干这配置了

410
00:12:10,100 --> 00:12:10,980
你看这要干嘛

411
00:12:10,980 --> 00:12:11,700
拥有这个插件吧

412
00:12:11,700 --> 00:12:12,120
拥一下

413
00:12:12,120 --> 00:12:13,720
把它拥过来

414
00:12:13,720 --> 00:12:15,000
放到这就可以了

415
00:12:15,000 --> 00:12:15,160
是不是

416
00:12:15,160 --> 00:12:16,440
拥有这个插件

417
00:12:16,440 --> 00:12:17,200
放这就可以了

418
00:12:17,200 --> 00:12:19,360
那么我在这里边干嘛呢

419
00:12:19,360 --> 00:12:21,120
是不是要调用这个插件去数字化呀

420
00:12:21,120 --> 00:12:21,700
怎么调用

421
00:12:21,700 --> 00:12:22,280
判断吧

422
00:12:22,280 --> 00:12:23,380
如果说

423
00:12:23,380 --> 00:12:24,960
什么呀

424
00:12:24,960 --> 00:12:27,180
如果是options.plyins存在的话

425
00:12:27,180 --> 00:12:29,200
并且什么呀

426
00:12:29,200 --> 00:12:31,240
它是数字的话

427
00:12:31,240 --> 00:12:32,260
就array.什么

428
00:12:32,260 --> 00:12:32,840
id array

429
00:12:32,840 --> 00:12:34,360
谁呀

430
00:12:34,360 --> 00:12:36,540
我这个options.什么

431
00:12:36,540 --> 00:12:40,580
如果它是一个数组的话

432
00:12:40,580 --> 00:12:41,080
怎么办

433
00:12:41,080 --> 00:12:42,940
是不是要循环啊

434
00:12:42,940 --> 00:12:43,540
好循环

435
00:12:43,540 --> 00:12:46,320
所以循环

436
00:12:46,320 --> 00:12:47,960
拿到每个plugin

437
00:12:47,960 --> 00:12:49,980
然后掉它的什么呀

438
00:12:49,980 --> 00:12:51,160
plug方法

439
00:12:51,160 --> 00:12:53,500
把我们的kompiler传进去就可以了

440
00:12:53,500 --> 00:12:53,660
是不是

441
00:12:53,660 --> 00:12:55,580
是不是可以了

442
00:12:55,580 --> 00:12:57,480
就是实行所有的什么

443
00:12:57,480 --> 00:12:57,940
查件

444
00:12:57,940 --> 00:13:00,900
就是实行所有的查件

445
00:13:00,900 --> 00:13:02,640
就可以了啊

446
00:13:02,640 --> 00:13:03,660
你看就成功了

447
00:13:03,660 --> 00:13:04,480
好大家往下看

448
00:13:04,480 --> 00:13:05,860
往下走

449
00:13:05,860 --> 00:13:06,220
你看

450
00:13:06,220 --> 00:13:07,460
是不是在循环呀

451
00:13:07,460 --> 00:13:08,860
我这边没有配置

452
00:13:08,860 --> 00:13:09,660
所以它有空的

453
00:13:09,660 --> 00:13:10,520
就不往下走了

454
00:13:10,520 --> 00:13:12,300
然后你看什么

455
00:13:12,300 --> 00:13:14,460
Compiler.hook environment call

456
00:13:14,460 --> 00:13:15,840
和after environment call

457
00:13:15,840 --> 00:13:16,920
这是啥意思

458
00:13:16,920 --> 00:13:19,520
这个其实是在什么

459
00:13:19,520 --> 00:13:20,240
是在这个

460
00:13:20,240 --> 00:13:21,540
call什么意思

461
00:13:21,540 --> 00:13:22,500
是不是调用的意思

462
00:13:22,500 --> 00:13:24,480
调用让这个environment

463
00:13:24,480 --> 00:13:24,880
这个什么

464
00:13:24,880 --> 00:13:26,060
gold的事件发生

465
00:13:26,060 --> 00:13:26,900
是吧

466
00:13:26,900 --> 00:13:27,880
你看可以考过来

467
00:13:27,880 --> 00:13:29,380
是可以考过来

468
00:13:29,380 --> 00:13:29,940
放在这

469
00:13:29,940 --> 00:13:30,880
可以了是吧

470
00:13:30,880 --> 00:13:33,880
看到吧

471
00:13:33,880 --> 00:13:36,820
compiler.hooks.environment.call

472
00:13:36,820 --> 00:13:39,480
触发这个实践和这个实践

473
00:13:39,480 --> 00:13:41,440
那么他们怎么来的呢

474
00:13:41,440 --> 00:13:43,660
就compiler会有个属性要什么hooks

475
00:13:43,660 --> 00:13:44,780
hooks会有属性要什么

476
00:13:44,780 --> 00:13:46,020
叫environment和after environment

477
00:13:46,020 --> 00:13:46,500
是不是

478
00:13:46,500 --> 00:13:48,000
肯定是这样的吧

479
00:13:48,000 --> 00:13:49,420
这是不是触发他们呀

480
00:13:49,420 --> 00:13:51,040
那肯定有地方可能会监听他们

481
00:13:51,040 --> 00:13:51,360
对不对

482
00:13:51,360 --> 00:13:51,940
对吧

483
00:13:51,940 --> 00:13:53,120
这只是触发而已啊

484
00:13:53,120 --> 00:13:53,580
不是监听

485
00:13:53,580 --> 00:13:54,300
不是监听

486
00:13:54,300 --> 00:13:55,940
好就完事了啊

487
00:13:55,940 --> 00:13:57,360
就是触发什么呀

488
00:13:57,360 --> 00:13:59,460
触发environment实践执行

489
00:14:03,000 --> 00:14:04,200
还要触发什么呀

490
00:14:04,200 --> 00:14:06,220
这个after environment 实际之情

491
00:14:06,220 --> 00:14:12,160
就可以了吧就可以了

492
00:14:12,160 --> 00:14:15,560
好然后呢再往下走

493
00:14:15,560 --> 00:14:18,160
就这最后一步了啊

494
00:14:18,160 --> 00:14:19,220
你看他在干嘛

495
00:14:19,220 --> 00:14:19,720
你看

496
00:14:19,720 --> 00:14:21,800
compiler.option等于什么

497
00:14:21,800 --> 00:14:24,920
new一个webpack option apply the process

498
00:14:24,920 --> 00:14:26,700
他new了一个这个东西

499
00:14:26,700 --> 00:14:29,620
叫好像选项应用的一个类

500
00:14:29,620 --> 00:14:31,420
然后叫他的process的方法

501
00:14:31,420 --> 00:14:32,420
处理的options

502
00:14:32,460 --> 00:14:33,620
对吧好把他考过来

503
00:14:33,620 --> 00:14:35,020
放在这

504
00:14:35,020 --> 00:14:37,060
看到吧

505
00:14:37,060 --> 00:14:38,600
当然我给大家手写吧

506
00:14:38,600 --> 00:14:40,140
就方便大家来看啊

507
00:14:40,140 --> 00:14:41,680
比如说我给大家讲写啊

508
00:14:41,680 --> 00:14:43,460
你有一个什么webpack

509
00:14:43,460 --> 00:14:44,240
什么呀

510
00:14:44,240 --> 00:14:45,260
叫这个options

511
00:14:45,260 --> 00:14:47,820
啊

512
00:14:47,820 --> 00:14:49,360
然后点什么process

513
00:14:49,360 --> 00:14:50,120
啊

514
00:14:50,120 --> 00:14:50,640
处理

515
00:14:50,640 --> 00:14:51,140
处理谁呢

516
00:14:51,140 --> 00:14:52,680
处理咱们这个options

517
00:14:52,680 --> 00:14:53,960
传过已经compiler

518
00:14:53,960 --> 00:14:54,740
是不是就可以了

519
00:14:54,740 --> 00:14:57,040
你有这个类

520
00:14:57,040 --> 00:14:58,580
然后已经处理我们的选项

521
00:14:58,580 --> 00:15:00,100
处理我们的这个选项

522
00:15:00,100 --> 00:15:00,620
options

523
00:15:01,640 --> 00:15:05,400
到底里边在干嘛呢 我们不知道 先不管啊 还没看呢 是不是啊 没看呢

524
00:15:05,400 --> 00:15:11,580
啊 咱们别着急 咱们回过头来啊 咱们看看这什么 看看这个compiler到底怎么实现呢

525
00:15:11,580 --> 00:15:12,760
 是不是啊 实现他啊

526
00:15:12,760 --> 00:15:17,800
那怎么看呢 咱们回过头来看看 怎么回过头来啊 我们重新怎么样

527
00:15:17,800 --> 00:15:23,320
重置一下断定 重新进来 是不是啊 定不到这个new compiler里面

528
00:15:23,320 --> 00:15:30,280
是不是进来了 进去 哎 你看 这个compiler是什么是一个类 记忆自什么

529
00:15:30,280 --> 00:15:31,240
 table 是不是啊

530
00:15:31,640 --> 00:15:32,740
然后你看它是个类

531
00:15:32,740 --> 00:15:34,000
进了table

532
00:15:34,000 --> 00:15:35,780
然后传过来什么contest

533
00:15:35,780 --> 00:15:37,860
然后它里边会有很多的勾子是不是

534
00:15:37,860 --> 00:15:39,540
有什么shouldimity啊

535
00:15:39,540 --> 00:15:41,420
down啊什么什么很多很多啊

536
00:15:41,420 --> 00:15:42,520
我们一会儿来讲到

537
00:15:42,520 --> 00:15:43,160
咱们一个来

538
00:15:43,160 --> 00:15:45,040
然后它过那个属性呢

539
00:15:45,040 --> 00:15:46,160
我们重点看一下啊

540
00:15:46,160 --> 00:15:46,620
它是个类

541
00:15:46,620 --> 00:15:48,100
有什么有hooks有跟个勾子

542
00:15:48,100 --> 00:15:51,500
然后这个

543
00:15:51,500 --> 00:15:53,560
嗯

544
00:15:53,560 --> 00:15:54,360
属性很多吧

545
00:15:54,360 --> 00:15:56,100
什么output file system

546
00:15:56,100 --> 00:15:56,820
input file system

547
00:15:56,820 --> 00:15:58,180
是不是很多呀

548
00:15:58,180 --> 00:15:59,240
不管不管怎么说法

549
00:15:59,240 --> 00:15:59,980
那肯定是个类是不是

550
00:15:59,980 --> 00:16:01,240
我们见下来个类啊

551
00:16:01,240 --> 00:16:02,720
好 回到我那边来

552
00:16:02,720 --> 00:16:04,800
咱们呀 这个先建一个类

553
00:16:04,800 --> 00:16:06,440
叫什么就要compiler是不是

554
00:16:06,440 --> 00:16:07,140
建一个类

555
00:16:07,140 --> 00:16:11,800
好 我们来建一个compilergs

556
00:16:11,800 --> 00:16:13,720
然后写个类

557
00:16:13,720 --> 00:16:15,640
class什么compiler

558
00:16:15,640 --> 00:16:17,280
然后它进入谁呀

559
00:16:17,280 --> 00:16:18,120
进入我们这个table

560
00:16:18,120 --> 00:16:19,040
table吧

561
00:16:19,040 --> 00:16:20,460
extense我们的table

562
00:16:20,460 --> 00:16:21,920
是不是

563
00:16:21,920 --> 00:16:23,440
它哪来的

564
00:16:23,440 --> 00:16:24,540
是不是引进来啊

565
00:16:24,540 --> 00:16:26,420
好 引进来结构table

566
00:16:26,420 --> 00:16:28,100
等于什么 等于我们这个叫

567
00:16:28,100 --> 00:16:30,160
require谁呀

568
00:16:31,240 --> 00:16:32,200
table的酷吧

569
00:16:32,200 --> 00:16:34,140
table的酷

570
00:16:34,140 --> 00:16:35,760
就是说它是个鸡肋

571
00:16:35,760 --> 00:16:36,780
我们可以引入它是不是

572
00:16:36,780 --> 00:16:37,360
引入它

573
00:16:37,360 --> 00:16:41,400
那么引入它之后呢

574
00:16:41,400 --> 00:16:42,420
要定义很多

575
00:16:42,420 --> 00:16:43,220
我们要定义什么

576
00:16:43,220 --> 00:16:44,120
定义个够难数吧

577
00:16:44,120 --> 00:16:44,740
constructor

578
00:16:44,740 --> 00:16:46,600
constructor

579
00:16:46,600 --> 00:16:47,680
然后super

580
00:16:47,680 --> 00:16:49,560
这会传来什么

581
00:16:49,560 --> 00:16:51,060
传过来一个上下文

582
00:16:51,060 --> 00:16:51,640
上下文吧

583
00:16:51,640 --> 00:16:52,300
contest吧

584
00:16:52,300 --> 00:16:53,220
拿过来传给他

585
00:16:53,220 --> 00:16:53,620
是不是

586
00:16:53,620 --> 00:16:54,900
他在传什么

587
00:16:54,900 --> 00:16:55,960
这不用传了

588
00:16:55,960 --> 00:16:58,020
constructor

589
00:16:58,020 --> 00:16:58,300
对

590
00:16:58,300 --> 00:17:00,180
够难数

591
00:17:00,180 --> 00:17:02,380
那么它会定义很多勾子

592
00:17:02,380 --> 00:17:03,660
那是叫hooks

593
00:17:03,660 --> 00:17:05,240
然后等于一个对象

594
00:17:05,240 --> 00:17:06,340
很多勾子

595
00:17:06,340 --> 00:17:08,260
咱们默认有几个勾子呢

596
00:17:08,260 --> 00:17:09,960
你看刚才我们出发点勾子

597
00:17:09,960 --> 00:17:12,340
是不是叫了compiler.hook environment

598
00:17:12,340 --> 00:17:13,960
那肯定有这个勾子吧

599
00:17:13,960 --> 00:17:15,020
inverment是不是

600
00:17:15,020 --> 00:17:17,700
叫什么叫设置环境变量

601
00:17:17,700 --> 00:17:20,080
设置咱们的这个FS模块

602
00:17:20,080 --> 00:17:20,320
是不是

603
00:17:20,320 --> 00:17:22,360
它会叫什么thinkhook

604
00:17:22,360 --> 00:17:25,240
然后猜出呢空的没有

605
00:17:25,240 --> 00:17:28,140
好把这个hook引进来

606
00:17:28,140 --> 00:17:30,080
他有点hook啊

607
00:17:30,080 --> 00:17:31,440
是不是这个afterimamente啊

608
00:17:31,440 --> 00:17:32,340
好拿过来放到这

609
00:17:32,340 --> 00:17:35,840
等于也是new aboutsynchook

610
00:17:35,840 --> 00:17:38,440
好空速度就可以了

611
00:17:38,440 --> 00:17:39,060
是不是啊

612
00:17:39,060 --> 00:17:40,340
他们现在没什么用啊

613
00:17:40,340 --> 00:17:41,320
没什么用啊

614
00:17:41,320 --> 00:17:42,100
好

615
00:17:42,100 --> 00:17:43,180
会准备好之后呢

616
00:17:43,180 --> 00:17:43,800
干嘛了

617
00:17:43,800 --> 00:17:46,040
我们是不是要有一个Z的option对象

618
00:17:46,040 --> 00:17:48,620
option等于一个空向

619
00:17:48,620 --> 00:17:53,760
然后Z的contest等于contest就可以了

620
00:17:53,760 --> 00:17:56,980
就是保存什么呀

621
00:17:56,980 --> 00:17:57,720
保存

622
00:17:57,720 --> 00:17:59,460
当前的上下文

623
00:17:59,460 --> 00:18:00,080
路径

624
00:18:00,080 --> 00:18:02,840
说了半天上下文

625
00:18:02,840 --> 00:18:03,560
到底是什么

626
00:18:03,560 --> 00:18:04,960
就是个角度路径

627
00:18:04,960 --> 00:18:05,620
只要谁

628
00:18:05,620 --> 00:18:06,560
只要当你工作目录

629
00:18:06,560 --> 00:18:07,800
在那里边只要谁

630
00:18:07,800 --> 00:18:08,480
是不是只要这个

631
00:18:08,480 --> 00:18:10,320
第四的工作目录啊

632
00:18:10,320 --> 00:18:11,360
就第四的路径

633
00:18:11,360 --> 00:18:12,580
比如说就是他

634
00:18:12,580 --> 00:18:15,920
右键烤为路径

635
00:18:15,920 --> 00:18:17,020
就是他

636
00:18:17,020 --> 00:18:18,800
看到吗

637
00:18:18,800 --> 00:18:19,420
就是什么

638
00:18:19,420 --> 00:18:20,380
就是个第四的目录

639
00:18:20,380 --> 00:18:20,740
就是他

640
00:18:20,740 --> 00:18:22,720
明白了吧

641
00:18:22,720 --> 00:18:22,960
就是他

642
00:18:22,960 --> 00:18:23,600
这个工作目录

643
00:18:23,600 --> 00:18:25,000
就是我们的上下文路径

644
00:18:25,000 --> 00:18:25,700
对不对

645
00:18:25,700 --> 00:18:26,700
很重要啊

646
00:18:26,700 --> 00:18:27,900
记住这个记住这个路径

647
00:18:27,900 --> 00:18:29,480
是吧

648
00:18:29,480 --> 00:18:30,540
好

649
00:18:30,540 --> 00:18:30,980
然后你看

650
00:18:30,980 --> 00:18:32,520
然后这空白有什么吗

651
00:18:32,520 --> 00:18:33,440
是不是有让方法呀

652
00:18:33,440 --> 00:18:34,920
因为你看我们在这怎么写的

653
00:18:34,920 --> 00:18:36,360
是不是是不是掉了让方法

654
00:18:36,360 --> 00:18:37,840
传入一个回弹术啊

655
00:18:37,840 --> 00:18:38,060
好

656
00:18:38,060 --> 00:18:39,460
我们给大家让方法

657
00:18:39,460 --> 00:18:39,960
让

658
00:18:39,960 --> 00:18:41,840
对吧

659
00:18:41,840 --> 00:18:42,140
好

660
00:18:42,140 --> 00:18:43,200
我们传一个回调

661
00:18:43,200 --> 00:18:43,660
callback

662
00:18:43,660 --> 00:18:45,260
对吧

663
00:18:45,260 --> 00:18:47,080
然后呢

664
00:18:47,080 --> 00:18:48,340
我们调用callback吧

665
00:18:48,340 --> 00:18:50,000
传一个传一个no

666
00:18:50,000 --> 00:18:51,440
和什么

667
00:18:51,440 --> 00:18:52,120
和这个a

668
00:18:52,120 --> 00:18:53,920
这个缘译运营结束

669
00:18:53,920 --> 00:18:56,320
我先跑通啊

670
00:18:56,320 --> 00:18:56,900
一步跑通

671
00:18:56,900 --> 00:18:59,080
是不是可以了

672
00:18:59,080 --> 00:19:01,480
然后到了第一步啊

673
00:19:01,480 --> 00:19:03,040
其实已经能够跑了

674
00:19:03,040 --> 00:19:03,800
不行你看啊

675
00:19:03,800 --> 00:19:05,280
我回到这个应该一边来

676
00:19:05,280 --> 00:19:06,420
我先把这个住掉了是不是

677
00:19:06,420 --> 00:19:07,400
现在啊

678
00:19:07,400 --> 00:19:08,600
已经能够往下跑了

679
00:19:08,600 --> 00:19:09,580
我们来试试啊

680
00:19:09,580 --> 00:19:10,380
我们来试试

681
00:19:10,380 --> 00:19:11,060
好我们

682
00:19:11,060 --> 00:19:13,260
我们回到这个代码里面来

683
00:19:13,260 --> 00:19:17,900
进入CMB

684
00:19:17,900 --> 00:19:22,100
好

685
00:19:22,100 --> 00:19:23,200
咱们输入一个命令啊

686
00:19:23,200 --> 00:19:24,560
叫什么叫node什么呀

687
00:19:24,560 --> 00:19:27,940
说一个node-clgs

688
00:19:27,940 --> 00:19:35,160
诶 你看到了吧

689
00:19:35,160 --> 00:19:38,820
诶 打出这么多东西出来啊

690
00:19:38,820 --> 00:19:39,940
这个不正常啊

691
00:19:39,940 --> 00:19:40,660
你看啊

692
00:19:40,660 --> 00:19:42,440
看这个东西太多了

693
00:19:42,440 --> 00:19:43,900
看一下啊

694
00:19:43,900 --> 00:19:47,180
看 第一次对吧

695
00:19:47,180 --> 00:19:49,520
我输了一个node-cli

696
00:19:49,520 --> 00:19:50,500
是不是这个啊

697
00:19:50,500 --> 00:19:51,940
没 应该用自己的吧

698
00:19:51,940 --> 00:19:53,400
要点个wipag是不是

699
00:19:53,400 --> 00:19:54,540
应该用自己的啊

700
00:19:54,540 --> 00:19:58,500
那他这个地方的话

701
00:19:58,500 --> 00:19:59,520
传过了一个error

702
00:19:59,520 --> 00:20:00,660
这个sit没有传是吧

703
00:20:00,660 --> 00:20:02,460
没有传的话我们就先不能这么用了是吧

704
00:20:02,460 --> 00:20:04,840
我们先把这个删掉

705
00:20:04,840 --> 00:20:09,080
对吧

706
00:20:09,080 --> 00:20:10,660
打印我们的error就可以了是吧

707
00:20:10,660 --> 00:20:11,640
打印我们sit

708
00:20:11,640 --> 00:20:14,680
好

709
00:20:14,680 --> 00:20:16,660
好我们来跑一下啊

710
00:20:16,660 --> 00:20:18,000
用我们自己的库跑一下

711
00:20:18,000 --> 00:20:19,880
这一次是吧

712
00:20:19,880 --> 00:20:20,680
回车

713
00:20:20,680 --> 00:20:24,520
他说找不到这个模块是吧

714
00:20:24,520 --> 00:20:26,580
哦 我这应该怎么写 应该是

715
00:20:26,580 --> 00:20:30,180
src 应该webpack 是吧

716
00:20:30,180 --> 00:20:34,520
所以有目录 有一个plugin的目录啊 这应该怎么 应该是第二杠

717
00:20:34,520 --> 00:20:40,220
应该是第二杠什么呀plugin下边的node版本是吧 是不是这样写啊

718
00:20:40,220 --> 00:20:42,580
少了一目录啊

719
00:20:42,580 --> 00:20:44,580
好 再来

720
00:20:44,580 --> 00:20:51,760
compile 没有定义 看啊 compile 没有定义 是没赢啊 赢一下吧 把compile 赢一下

721
00:20:51,760 --> 00:20:53,880
 是吧 刚才没写 现在写上了 赢一下

722
00:20:53,880 --> 00:20:59,260
led-compiler=require-compiler

723
00:21:23,880 --> 00:21:50,320
让他让他让他进行什么进进这个执行吧

724
00:21:50,320 --> 00:21:51,580
看现在又可以了是吧

725
00:21:51,580 --> 00:21:53,420
看先打印

726
00:21:53,420 --> 00:21:55,160
MAPLINE是不是打印出来了

727
00:21:55,160 --> 00:21:55,700
是不是执行了

728
00:21:55,700 --> 00:21:57,640
然后你看所有对象是个闹

729
00:21:57,640 --> 00:21:58,760
然后变严肃

730
00:21:58,760 --> 00:21:59,520
反正了吧

731
00:21:59,520 --> 00:22:01,900
那么相当于我们第一步就跑通了

732
00:22:01,900 --> 00:22:02,860
对吧

733
00:22:02,860 --> 00:22:04,320
第一步我们干嘛

734
00:22:04,320 --> 00:22:05,520
我们是跑通了WIPAC了

735
00:22:05,520 --> 00:22:06,240
对不对

736
00:22:06,240 --> 00:22:06,840
可怕了

737
00:22:06,840 --> 00:22:10,780
这大家到现在大家有问题吗

738
00:22:10,780 --> 00:22:11,540
我们过一下啊

739
00:22:11,540 --> 00:22:11,880
过一下

740
00:22:11,880 --> 00:22:13,480
首先你看我们怎么多的呀

741
00:22:13,480 --> 00:22:14,240
我们先你看

742
00:22:14,240 --> 00:22:16,440
在这儿是不是引入了我们自己的WIPAC

743
00:22:16,440 --> 00:22:17,540
对吧

744
00:22:17,540 --> 00:22:18,900
引入我们自己的配置文件

745
00:22:18,900 --> 00:22:20,180
8分之间传给了WiPAC

746
00:22:20,180 --> 00:22:21,060
对吧

747
00:22:21,060 --> 00:22:21,580
然后掉到了

748
00:22:21,580 --> 00:22:23,200
返回了控制软房网呀

749
00:22:23,200 --> 00:22:23,800
对吧

750
00:22:23,800 --> 00:22:24,960
你看啊

751
00:22:24,960 --> 00:22:26,740
我引入谁

752
00:22:26,740 --> 00:22:28,760
是不是引入这个应该解释

753
00:22:28,760 --> 00:22:31,560
引入这样一个方形函数啊

754
00:22:31,560 --> 00:22:34,600
然后传过来一个options和回调函数啊

755
00:22:34,600 --> 00:22:35,000
对不对

756
00:22:35,000 --> 00:22:38,040
当时我这个地方传了option回调函数

757
00:22:38,040 --> 00:22:39,600
这个回调传了吗

758
00:22:39,600 --> 00:22:40,360
你看这个地方

759
00:22:40,360 --> 00:22:43,940
没有没有没有接收是吧

760
00:22:43,940 --> 00:22:44,560
没有接收的话

761
00:22:44,560 --> 00:22:46,880
我这个回调我可以不要了啊

762
00:22:46,880 --> 00:22:47,460
不要了

763
00:22:47,460 --> 00:22:52,660
好 然后你看里边怎么样 第一步怎么样 我们先给这个contact的复值

764
00:22:52,660 --> 00:22:56,700
很有用啊 会不会很有用 因为插到我们的这个路径的话 用它来插着

765
00:22:56,700 --> 00:23:00,860
它就是个角度路径 代表当前的工作目录啊 当前商量的环境

766
00:23:00,860 --> 00:23:06,220
然后你看构建什么 compiler的实力啊 我们通过它来启动我们的编译 是吧

767
00:23:06,220 --> 00:23:09,860
然后呢 那我们把它呀 option 附给了 compiler 对吧

768
00:23:09,860 --> 00:23:14,410
然后呢 我们去什么呀 设置了一个什么 设置了一个 compiler 什么呀 是不是设置了

769
00:23:14,410 --> 00:23:15,160
 compiler 的这个

770
00:23:15,160 --> 00:23:17,300
outputing

771
00:23:17,460 --> 00:23:22,260
input file system 和 input file system 这个模样 对吧 就是读写文件用哪个模块来读写

772
00:23:22,260 --> 00:23:30,000
这意思啊 你想 我们要加带模块是比较读模块那种 我们要往文件 往精凡上写文件的时候比较写文件呀

773
00:23:30,000 --> 00:23:32,300
用哪个模块用他来写啊

774
00:23:32,300 --> 00:23:37,820
有人可能说了 那用 fs 模块写似的味道了吗 不一定 在真正的项目中

775
00:23:37,820 --> 00:23:42,780
 我们可能会有各种各样的模块 有的为了实现优化的 有的是为了有的是为了提高效率的

776
00:23:42,780 --> 00:23:46,820
这个不是 不一定 fs 不过我们非常为了简化都用 fs 就可以了 是吧

777
00:23:48,460 --> 00:23:49,320
好你往下走

778
00:23:49,320 --> 00:23:53,220
然后怎么你看我让查一下全部执行

779
00:23:53,220 --> 00:23:55,120
看我们这些扒印是不是执行了

780
00:23:55,120 --> 00:23:56,360
对吧打印了吧

781
00:23:56,360 --> 00:23:58,960
然后你看我是我叫我触发了一个

782
00:23:58,960 --> 00:24:00,260
一个人们的这沟的执行

783
00:24:00,260 --> 00:24:01,600
为什么能这能调用啊

784
00:24:01,600 --> 00:24:03,300
因为我在这是不是有两个沟的啊

785
00:24:03,300 --> 00:24:04,460
都是同步沟的吧

786
00:24:04,460 --> 00:24:05,960
对吧现在现在没有用的啊

787
00:24:05,960 --> 00:24:07,560
没有用的没有人监听他是不是

788
00:24:07,560 --> 00:24:08,760
那你可以监听啊

789
00:24:08,760 --> 00:24:10,600
你比如说你可以这样监听啊怎么监听

790
00:24:10,600 --> 00:24:12,720
你可以在你个卖扒拉印里边这么写

791
00:24:12,720 --> 00:24:15,500
啊我这不是我我这是不是有个卖扒拉印啊

792
00:24:16,920 --> 00:24:18,200
你这可以这么写 怎么写呢

793
00:24:18,200 --> 00:24:23,060
Compiler 点什么 点Hooks 点什么

794
00:24:23,060 --> 00:24:26,400
是不是点这个Environment啊 对不对

795
00:24:26,400 --> 00:24:29,720
点什么 点Tab 注册

796
00:24:29,720 --> 00:24:32,280
啊 名字呢 我们叫拆下名字 My plugin

797
00:24:32,280 --> 00:24:34,840
然后这个这个回调

798
00:24:34,840 --> 00:24:41,500
没有参数啊 所以说 因为这没有参数啊 这是空的没有参数是不是啊 答应一下

799
00:24:41,500 --> 00:24:45,600
说什么 说这个 My plugin environment 是不是啊

800
00:24:46,920 --> 00:24:48,780
这样是不是这样也监听了是不是

801
00:24:48,780 --> 00:24:53,340
是不是他自己监定是监定什么监定咱们这个hooks

802
00:24:53,340 --> 00:24:57,360
也监定我们这个compilerhooks的environment的这个时间

803
00:24:57,360 --> 00:25:00,680
那么当我在这我这是不是出发了

804
00:25:00,680 --> 00:25:02,680
他监定函数是不是执行啊

805
00:25:02,680 --> 00:25:04,080
对吧我们再来一次看看

806
00:25:04,080 --> 00:25:14,320
看myplan environment是不是监听了就是实际上监听谁监听

807
00:25:14,320 --> 00:25:16,560
是不是啊就是意思啊

808
00:25:16,920 --> 00:25:20,500
好到了这一步还有大家有问题吗

809
00:25:20,500 --> 00:25:40,720
好可以啊

810
00:25:44,820 --> 00:25:48,400
好我们来回家一下问题啊

811
00:25:48,400 --> 00:25:51,980
对老学员

812
00:25:51,980 --> 00:25:51,980
对

813
00:25:51,980 --> 00:25:57,560
加入班级学习自然又学会了强的还能变得更强

814
00:25:57,560 --> 00:26:01,140
其实我们有一半学员都20k以上的就是学的时候啊

815
00:26:01,140 --> 00:26:02,140
学的时候啊

816
00:26:02,140 --> 00:26:04,440
嗯 和我同届的啊

817
00:26:04,440 --> 00:26:09,540
靠的 method apply 是调用 function 对对对对

818
00:26:09,540 --> 00:26:12,140
其实就跟什么 就跟 跟这个是一个意思

819
00:26:12,140 --> 00:26:14,140
就是给 跟是不是一样的啊

820
00:26:14,140 --> 00:26:18,540
这就是调整方法呗 一样的 一样的 靠方法就靠调用嘛

821
00:26:18,540 --> 00:26:21,740
同居的啊 同居的

822
00:26:21,740 --> 00:26:23,240
光影

823
00:26:23,240 --> 00:26:31,040
嗯 光影是学 学长啊

824
00:26:31,140 --> 00:26:33,780
学长 男孩啊 男孩

825
00:26:33,780 --> 00:26:40,440
学弟 七七的是吧 好 欢迎欢迎

826
00:26:40,440 --> 00:26:46,580
这么多学弟啊 九七的啊 好

827
00:26:46,580 --> 00:26:53,480
对 看完会用是 对对对对 这个是高手啊 这个高手 看完会用什么

828
00:26:53,480 --> 00:26:57,620
memory fs 什么意思 比如说我们 webxdv server用的是memory fs 什么意思

829
00:26:57,620 --> 00:27:00,320
把那个文件写到啥 写到硬盘上 写到我们内存里边去

830
00:27:00,780 --> 00:27:04,920
对吧 不写硬盘写内存啊 所以memoryfs对对 这是高手啊 高手啊

831
00:27:04,920 --> 00:27:13,220
对 自己写了个sing who 对对对啊 其实我们正式课都会写 对吧 但是我们现在我们这个课只能写一个啊

832
00:27:13,220 --> 00:27:14,240
 只能写一个啊

833
00:27:14,240 --> 00:27:17,340
呃

834
00:27:17,340 --> 00:27:22,340
出发他 为什么是他 而不是他 不一样啊 对吧

835
00:27:22,340 --> 00:27:26,340
我们没写完 因为9个我们就写了一个是吧

836
00:27:30,180 --> 00:27:31,620
对对对对没错

837
00:27:31,620 --> 00:27:40,860
好楼肿

838
00:27:40,860 --> 00:27:43,900
好那我们开始了啊咱们上课了

839
00:27:43,900 --> 00:27:49,220
大家有问题就有问题就提啊就提啊

840
00:27:49,220 --> 00:27:55,500
这样的楼肿楼肿年薪一点50万吧啊

841
00:27:55,500 --> 00:27:57,820
没有没有

842
00:27:59,380 --> 00:28:00,060
40万啊

843
00:28:00,060 --> 00:28:06,780
好 我们看一下啊

844
00:28:06,780 --> 00:28:08,220
咱们写到哪了

845
00:28:08,220 --> 00:28:09,540
是不是开始执行了

846
00:28:09,540 --> 00:28:10,540
是不是到这了

847
00:28:10,540 --> 00:28:11,820
下面该写哪了

848
00:28:11,820 --> 00:28:12,880
是不是开始你看啊

849
00:28:12,880 --> 00:28:14,220
刚才有东西没写是吧

850
00:28:14,220 --> 00:28:14,740
哪里没写

851
00:28:14,740 --> 00:28:15,980
是不是这没写啊

852
00:28:15,980 --> 00:28:18,180
new ypack map option apply

853
00:28:18,180 --> 00:28:19,820
the approcessed option 没写吧

854
00:28:19,820 --> 00:28:21,480
这个方法非常非常重要

855
00:28:21,480 --> 00:28:22,680
我们来看这方法

856
00:28:22,680 --> 00:28:23,840
他别我们整个什么

857
00:28:23,840 --> 00:28:25,520
整个这个编译的什么

858
00:28:25,520 --> 00:28:26,180
一个入口

859
00:28:26,180 --> 00:28:27,140
我们来看一下啊

860
00:28:27,140 --> 00:28:29,140
我们来编一下

861
00:28:29,380 --> 00:28:30,180
走

862
00:28:30,180 --> 00:28:31,380
好

863
00:28:31,380 --> 00:28:31,840
走

864
00:28:31,840 --> 00:28:34,980
咱们先让它过去

865
00:28:34,980 --> 00:28:35,640
咱们过去

866
00:28:35,640 --> 00:28:37,220
咱们先一个个来看

867
00:28:37,220 --> 00:28:37,660
先过去

868
00:28:37,660 --> 00:28:38,540
让它过去

869
00:28:38,540 --> 00:28:38,920
好

870
00:28:38,920 --> 00:28:40,200
重点咱们看下边

871
00:28:40,200 --> 00:28:41,480
看下边

872
00:28:41,480 --> 00:28:41,940
看啊

873
00:28:41,940 --> 00:28:42,900
看这里边怎么写

874
00:28:42,900 --> 00:28:44,220
这里边有个非常重要的质点

875
00:28:44,220 --> 00:28:45,280
我们进去看看

876
00:28:45,280 --> 00:28:49,560
先定位一个这个Wypeg option apply的实力

877
00:28:49,560 --> 00:28:51,680
然后看它process

878
00:28:51,680 --> 00:28:53,860
它pro里边我们只看一行单位就可以了

879
00:28:53,860 --> 00:28:54,380
哪行呢

880
00:28:54,380 --> 00:28:55,640
看这一行

881
00:28:59,380 --> 00:29:07,020
他里边核心代码就有一行是这一行 你看啊

882
00:29:07,020 --> 00:29:14,320
对啊 黑的就两家 new entry option plugin.apply

883
00:29:14,320 --> 00:29:21,880
就是这个插件什么叫 叫入口插件啊 我可以电话是不是 new entry option

884
00:29:21,880 --> 00:29:23,540
 apply apply什么 compiler

885
00:29:23,540 --> 00:29:28,660
然后他你看这是什么呀 住的插件吧 立刻怎么样就出发了是不是

886
00:29:28,660 --> 00:29:34,290
是啊 看车是不是你看看拍了 或可 安水 option 要靠是不是 立刻出发了

887
00:29:34,290 --> 00:29:37,700
 然后传两个参数 什么单数啊 康泰特和我们的安锤

888
00:29:37,700 --> 00:29:42,900
看康泰特怎么上海文吧 是不是第四啊 那安锤的入口吧 是不是什么的

889
00:29:42,900 --> 00:29:44,620
 来特定代解释啊 对

890
00:29:44,620 --> 00:29:49,700
好 我们重点看这两个代码怎么写的啊 这是重点重点 核英文核心

891
00:29:49,700 --> 00:29:54,860
看一下啊 叫 new entry option plugin.ply compiler 对吧

892
00:29:55,380 --> 00:29:57,380
compiler hooks entry option.call

893
00:30:25,380 --> 00:30:27,680
然后在里边的合计代码就是这样的啊

894
00:30:27,680 --> 00:30:29,980
你看他有什么有方法叫什么叫process

895
00:30:29,980 --> 00:30:31,780
是不是处理啊 process

896
00:30:31,780 --> 00:30:33,820
对吧

897
00:30:33,820 --> 00:30:36,640
然后里边呢有两个参数一个叫什么option

898
00:30:36,640 --> 00:30:41,000
选项吧 options一个叫什么一个叫compiler

899
00:30:41,000 --> 00:30:43,560
是不是一个编辑器啊对吧

900
00:30:43,560 --> 00:30:44,840
然后在里边呢

901
00:30:44,840 --> 00:30:47,140
你只有一行代码一个compiler

902
00:30:47,140 --> 00:30:48,420
第2什么

903
00:30:48,420 --> 00:30:49,180
第2hox

904
00:30:49,180 --> 00:30:50,460
第2什么

905
00:30:50,460 --> 00:30:53,540
after

906
00:30:53,540 --> 00:30:54,560
plugins

907
00:30:55,380 --> 00:30:58,860
2.call compiler

908
00:30:58,860 --> 00:31:02,860
什么呀

909
00:31:02,860 --> 00:31:05,180
就是触发什么after plug in

910
00:31:05,180 --> 00:31:07,020
这个勾子的执行

911
00:31:07,020 --> 00:31:09,180
那after plug in

912
00:31:09,180 --> 00:31:09,700
什么意思呢

913
00:31:09,700 --> 00:31:11,040
你看原码里面怎么写的

914
00:31:11,040 --> 00:31:12,080
原码里面啊

915
00:31:12,080 --> 00:31:12,660
哎呦

916
00:31:12,660 --> 00:31:14,220
哎呀

917
00:31:14,220 --> 00:31:14,780
这个地方

918
00:31:14,780 --> 00:31:19,480
是不是让它执行了

919
00:31:19,480 --> 00:31:21,180
它的话

920
00:31:21,180 --> 00:31:22,600
它在compiler里面

921
00:31:22,600 --> 00:31:22,860
是吧

922
00:31:22,860 --> 00:31:24,280
那我们肯定要有个这样的勾子

923
00:31:24,280 --> 00:31:24,700
是不是

924
00:31:24,700 --> 00:31:26,200
好吧 勾的键下吧

925
00:31:26,200 --> 00:31:28,660
回到confiler里边键下了勾的

926
00:31:28,660 --> 00:31:31,360
好

927
00:31:31,360 --> 00:31:34,220
new也new什么sync hook

928
00:31:34,220 --> 00:31:36,760
对吧 就可以了

929
00:31:36,760 --> 00:31:38,760
好 这样就可以了

930
00:31:38,760 --> 00:31:41,400
好 那相当于到了这一步

931
00:31:41,400 --> 00:31:43,660
我们第一步我们已经完事了吧

932
00:31:43,660 --> 00:31:45,120
就走完了 走完了

933
00:31:45,120 --> 00:31:47,480
好

934
00:31:47,480 --> 00:31:50,120
那么重点我们看第二步

935
00:31:50,120 --> 00:31:51,400
第一步我们搞定了

936
00:31:51,400 --> 00:31:52,480
我们已经跑步的流程了

937
00:31:52,480 --> 00:31:53,600
我们已经把confiler键好了

938
00:31:53,600 --> 00:31:53,780
是不是

939
00:31:53,780 --> 00:31:56,480
康派的负责我们负责我们这个整个编译流程

940
00:31:56,480 --> 00:31:57,360
好

941
00:31:57,360 --> 00:31:58,660
那第二步干嘛了

942
00:31:58,660 --> 00:32:00,340
我们该去真正开始编译了

943
00:32:00,340 --> 00:32:02,220
怎么能开始编译呢

944
00:32:02,220 --> 00:32:03,640
咱们要从这开始写起

945
00:32:03,640 --> 00:32:05,280
还要从这个WiPagOption

946
00:32:05,280 --> 00:32:05,860
什么样

947
00:32:05,860 --> 00:32:07,280
Apply开始写起

948
00:32:07,280 --> 00:32:07,700
对吧

949
00:32:07,700 --> 00:32:08,860
在这里边呢

950
00:32:08,860 --> 00:32:09,360
我们要怎么写

951
00:32:09,360 --> 00:32:10,480
我们首先要引着个插件

952
00:32:10,480 --> 00:32:12,080
你看你看他是不是引着插件啊

953
00:32:12,080 --> 00:32:12,300
你看

954
00:32:12,300 --> 00:32:16,540
他这用的插件叫啥呀

955
00:32:16,540 --> 00:32:17,840
注意啊

956
00:32:17,840 --> 00:32:19,160
这才是很重要啊

957
00:32:19,160 --> 00:32:21,680
是不是这个叫EntryOptionPlugin啊

958
00:32:21,680 --> 00:32:21,860
好

959
00:32:21,860 --> 00:32:22,700
把这个开架建一下

960
00:32:22,700 --> 00:32:24,560
核心的核心

961
00:32:24,560 --> 00:32:27,040
好我们建一个插件

962
00:32:27,040 --> 00:32:30,660
好我们写个

963
00:32:30,660 --> 00:32:32,060
class什么样

964
00:32:32,060 --> 00:32:33,400
entery option plugin

965
00:32:33,400 --> 00:32:34,400
寫个类

966
00:32:34,400 --> 00:32:35,200
导出吧

967
00:32:35,200 --> 00:32:39,240
module.lpos等于一个

968
00:32:39,240 --> 00:32:40,440
entery option plugin

969
00:32:40,440 --> 00:32:43,180
然后既然插件肯定有方法叫什么

970
00:32:43,180 --> 00:32:43,480
apply

971
00:32:43,480 --> 00:32:45,620
定似的肯定会有apply方法

972
00:32:45,620 --> 00:32:49,540
那里边干嘛呢

973
00:32:49,540 --> 00:32:51,040
注意啊

974
00:32:51,040 --> 00:32:51,780
这玩意怎么用呢

975
00:32:51,780 --> 00:32:52,600
你看啊这么用的啊

976
00:32:52,600 --> 00:32:55,300
你看回到咱们的wipigoption的plip里边

977
00:32:55,300 --> 00:32:58,120
我们先把这个插件给它引进来

978
00:32:58,120 --> 00:32:59,860
let entry option plugin

979
00:32:59,860 --> 00:33:01,980
等于什么require叫什么

980
00:33:01,980 --> 00:33:06,160
叫.plugin下边的什么呀

981
00:33:06,160 --> 00:33:10,880
.plugin下边的什么呀

982
00:33:10,880 --> 00:33:12,300
entry option plugin

983
00:33:12,300 --> 00:33:12,620
是不是

984
00:33:12,620 --> 00:33:14,720
我们要怎么样引入的开架吧

985
00:33:14,720 --> 00:33:16,540
那引入之后怎么样

986
00:33:16,540 --> 00:33:18,600
是不是这点事情啊

987
00:33:18,600 --> 00:33:20,320
你看圆码里边这边执行了

988
00:33:20,320 --> 00:33:20,880
怎么执行的

989
00:33:20,880 --> 00:33:22,400
是不是new的材件啊

990
00:33:22,400 --> 00:33:22,720
对不对

991
00:33:22,720 --> 00:33:23,160
好

992
00:33:23,160 --> 00:33:24,640
new我们的entry open plugin

993
00:33:24,640 --> 00:33:25,880
对吧

994
00:33:25,880 --> 00:33:27,000
然后呢

995
00:33:27,000 --> 00:33:27,880
点什么apply

996
00:33:27,880 --> 00:33:30,320
叫什么

997
00:33:30,320 --> 00:33:31,660
叫这个compiler

998
00:33:31,660 --> 00:33:36,060
把compiler上映它是吧

999
00:33:36,060 --> 00:33:38,840
就是我们去运行什么

1000
00:33:38,840 --> 00:33:40,580
运行这个entry open plugin plugin材件

1001
00:33:40,580 --> 00:33:40,920
是吧

1002
00:33:40,920 --> 00:33:41,480
材件

1003
00:33:41,480 --> 00:33:45,180
你看啊

1004
00:33:45,180 --> 00:33:46,620
它用来干什么呢

1005
00:33:46,620 --> 00:33:47,600
用来挂在什么

1006
00:33:47,600 --> 00:33:48,280
我们的入口点

1007
00:33:50,880 --> 00:33:52,700
他会怎么样

1008
00:33:52,700 --> 00:33:53,800
他会这个监听

1009
00:33:53,800 --> 00:33:58,400
咱们这个监听咱们这个make事件

1010
00:33:58,400 --> 00:34:04,500
然后马上什么出发了

1011
00:34:04,500 --> 00:34:05,340
怎么出发呀

1012
00:34:05,340 --> 00:34:06,500
就是compiler

1013
00:34:06,500 --> 00:34:09,000
叫什么

1014
00:34:09,000 --> 00:34:10,140
dlhooks

1015
00:34:10,140 --> 00:34:11,540
dlentry

1016
00:34:11,540 --> 00:34:12,120
是吧

1017
00:34:12,120 --> 00:34:12,840
option

1018
00:34:12,840 --> 00:34:13,720
靠

1019
00:34:13,720 --> 00:34:15,400
靠谁呀

1020
00:34:15,400 --> 00:34:16,780
靠咱们这个option

1021
00:34:16,780 --> 00:34:17,380
dlcontest

1022
00:34:17,380 --> 00:34:20,700
然后还有我们option

1023
00:34:20,700 --> 00:34:22,140
这点什么 点entry

1024
00:34:22,140 --> 00:34:25,440
重点啊 看这两号代码是吧

1025
00:34:25,440 --> 00:34:25,900
你看

1026
00:34:25,900 --> 00:34:27,500
第一号代码在怎么样

1027
00:34:27,500 --> 00:34:30,740
是不是在实行这个查件 对吧

1028
00:34:30,740 --> 00:34:31,900
第二个怎么样

1029
00:34:31,900 --> 00:34:33,800
干什么在触发什么

1030
00:34:33,800 --> 00:34:36,160
触发compiler上面的什么

1031
00:34:36,160 --> 00:34:37,600
entry option的一个hook

1032
00:34:37,600 --> 00:34:38,400
是不是对

1033
00:34:38,400 --> 00:34:41,260
那么你看你看这两个很像

1034
00:34:41,260 --> 00:34:42,700
是不是很像

1035
00:34:42,700 --> 00:34:43,800
也很像

1036
00:34:43,800 --> 00:34:46,100
他负责监听他负责出发

1037
00:34:46,100 --> 00:34:47,300
明白了吧

1038
00:34:47,300 --> 00:34:49,300
他负责监听他负责出发

1039
00:34:49,300 --> 00:34:50,660
那么看他怎么接听呢

1040
00:34:50,700 --> 00:34:52,700
他是不是肯定要监听这个事件

1041
00:34:52,700 --> 00:34:54,700
Compiler,hook,android,option

1042
00:34:54,700 --> 00:34:55,700
对不对

1043
00:34:55,700 --> 00:34:56,700
好我们进去看看

1044
00:34:56,700 --> 00:34:59,700
好进到咱们插件里面来

1045
00:34:59,700 --> 00:35:01,700
进到这个android,option,plugin

1046
00:35:01,700 --> 00:35:03,700
看他的apply方法

1047
00:35:03,700 --> 00:35:04,700
那他在干嘛呢

1048
00:35:04,700 --> 00:35:05,700
对吧

1049
00:35:05,700 --> 00:35:06,700
他这么写啊

1050
00:35:06,700 --> 00:35:07,700
传过来个什么呀

1051
00:35:07,700 --> 00:35:08,700
Compiler

1052
00:35:08,700 --> 00:35:11,700
Compiler.hooks

1053
00:35:11,700 --> 00:35:13,700
.hooks

1054
00:35:13,700 --> 00:35:15,700
.hooks

1055
00:35:15,700 --> 00:35:16,700
.hooks

1056
00:35:16,700 --> 00:35:17,700
Option

1057
00:35:17,700 --> 00:35:18,700
type监听

1058
00:35:18,700 --> 00:35:19,700
名字呢

1059
00:35:19,700 --> 00:35:20,600
就一直查一下名字

1060
00:35:20,600 --> 00:35:21,820
这个名字随便写的

1061
00:35:21,820 --> 00:35:22,460
没有什么意义

1062
00:35:22,460 --> 00:35:22,760
对吧

1063
00:35:22,760 --> 00:35:23,320
好

1064
00:35:23,320 --> 00:35:24,140
再来回调

1065
00:35:24,140 --> 00:35:28,700
回调里边呢

1066
00:35:28,700 --> 00:35:29,420
有一些参数

1067
00:35:29,420 --> 00:35:30,000
有什么参数啊

1068
00:35:30,000 --> 00:35:30,200
你看

1069
00:35:30,200 --> 00:35:30,860
有什么参数

1070
00:35:30,860 --> 00:35:31,080
你看

1071
00:35:31,080 --> 00:35:32,740
是不是两个参数啊

1072
00:35:32,740 --> 00:35:33,840
contact和entry

1073
00:35:33,840 --> 00:35:36,020
山海文的一个目录

1074
00:35:36,020 --> 00:35:38,240
学书境和我们的入口门间的相对书境吧

1075
00:35:38,240 --> 00:35:38,740
好

1076
00:35:38,740 --> 00:35:39,360
传过来

1077
00:35:39,360 --> 00:35:41,160
一个是contact

1078
00:35:41,160 --> 00:35:42,360
一个是entry

1079
00:35:42,360 --> 00:35:42,700
是不是

1080
00:35:42,700 --> 00:35:43,800
好

1081
00:35:43,800 --> 00:35:44,740
里边怎么写了

1082
00:35:44,740 --> 00:35:45,880
里边你看我怎么写

1083
00:35:45,880 --> 00:35:46,640
我new了一个什么

1084
00:35:46,640 --> 00:35:47,500
new了一个single

1085
00:35:47,500 --> 00:35:50,880
Entre plugin

1086
00:35:50,880 --> 00:35:51,960
对吧

1087
00:35:51,960 --> 00:35:53,440
然后点什么

1088
00:35:53,440 --> 00:35:55,620
这个什么

1089
00:35:55,620 --> 00:35:57,100
这个compiler

1090
00:35:57,100 --> 00:35:59,480
什么意思

1091
00:35:59,480 --> 00:36:02,180
就是我开始

1092
00:36:02,180 --> 00:36:03,940
先听什么呀

1093
00:36:03,940 --> 00:36:05,580
单个入口

1094
00:36:05,580 --> 00:36:08,420
什么叫单个入口吧

1095
00:36:08,420 --> 00:36:10,100
其实你看咱们这个配置文件里面

1096
00:36:10,100 --> 00:36:10,840
写的时候啊

1097
00:36:10,840 --> 00:36:11,760
你看你会发现

1098
00:36:11,760 --> 00:36:13,580
这可以写成了吗

1099
00:36:13,580 --> 00:36:15,020
写成了一个族串

1100
00:36:15,020 --> 00:36:16,520
也可以写成一个对象

1101
00:36:16,520 --> 00:36:16,840
是不是

1102
00:36:16,840 --> 00:36:18,520
那这叫什么这叫单入口

1103
00:36:18,520 --> 00:36:19,600
这叫多入口

1104
00:36:19,600 --> 00:36:22,840
当然了一个多入口是有多个单入口组成的

1105
00:36:22,840 --> 00:36:23,920
对吧

1106
00:36:23,920 --> 00:36:25,340
后面给大家解释啊

1107
00:36:25,340 --> 00:36:26,460
你看是不是这样的呀

1108
00:36:26,460 --> 00:36:26,660
你看

1109
00:36:26,660 --> 00:36:29,640
你有一个single entry plugin

1110
00:36:29,640 --> 00:36:31,280
第二apply compiler

1111
00:36:31,280 --> 00:36:32,120
是不是啊

1112
00:36:32,120 --> 00:36:33,240
就是监听的意思啊

1113
00:36:33,240 --> 00:36:34,460
你有了一个这样的插件

1114
00:36:34,460 --> 00:36:35,560
然后进行监听

1115
00:36:35,560 --> 00:36:36,100
对吧

1116
00:36:36,100 --> 00:36:37,640
那么他在干嘛

1117
00:36:37,640 --> 00:36:38,920
他有点参数啊

1118
00:36:38,920 --> 00:36:39,820
他就三个参数

1119
00:36:39,820 --> 00:36:41,740
第一个呢就是我们的context上文路径

1120
00:36:41,740 --> 00:36:42,920
第二个呢

1121
00:36:42,920 --> 00:36:44,800
是我们这个entry入口文件

1122
00:36:44,800 --> 00:36:45,640
第三个呢

1123
00:36:45,640 --> 00:36:46,820
是我们这个名字

1124
00:36:46,820 --> 00:36:47,900
妹

1125
00:36:47,900 --> 00:36:49,600
就是你看啊

1126
00:36:49,600 --> 00:36:50,780
上下文contest

1127
00:36:50,780 --> 00:36:56,000
上下文

1128
00:36:56,000 --> 00:36:58,020
就是决度硬是吧

1129
00:36:58,020 --> 00:36:59,760
android呢

1130
00:36:59,760 --> 00:37:01,040
是入口文件

1131
00:37:01,040 --> 00:37:03,300
对吧

1132
00:37:03,300 --> 00:37:04,240
妹呢

1133
00:37:04,240 --> 00:37:04,700
是什么

1134
00:37:04,700 --> 00:37:05,180
是一个

1135
00:37:05,180 --> 00:37:06,920
这个入口文件

1136
00:37:06,920 --> 00:37:10,560
叫代码块的名字

1137
00:37:10,560 --> 00:37:13,340
默认名称

1138
00:37:13,340 --> 00:37:14,300
就说

1139
00:37:14,300 --> 00:37:15,640
如果你只有一个登入口的话

1140
00:37:15,640 --> 00:37:16,400
名字就要什么

1141
00:37:16,400 --> 00:37:16,800
就要妹

1142
00:37:16,800 --> 00:37:17,740
注意啊

1143
00:37:17,740 --> 00:37:18,340
如果你看

1144
00:37:18,340 --> 00:37:19,720
如果你这这样写的

1145
00:37:19,720 --> 00:37:20,860
那么他就要魅

1146
00:37:20,860 --> 00:37:22,240
如果你这样写的

1147
00:37:22,240 --> 00:37:23,220
是不是就是这个名字啊

1148
00:37:23,220 --> 00:37:23,500
就是K

1149
00:37:23,500 --> 00:37:24,520
对吧

1150
00:37:24,520 --> 00:37:25,280
就这个名字啊

1151
00:37:25,280 --> 00:37:25,560
对不对

1152
00:37:25,560 --> 00:37:26,560
所以默认就是魅啊

1153
00:37:26,560 --> 00:37:27,420
因为我们单入口的话

1154
00:37:27,420 --> 00:37:27,880
默认就是魅

1155
00:37:27,880 --> 00:37:28,340
对吧

1156
00:37:28,340 --> 00:37:29,380
好

1157
00:37:29,380 --> 00:37:30,000
所以你看我

1158
00:37:30,000 --> 00:37:31,240
我这怎么没有了一个什么

1159
00:37:31,240 --> 00:37:33,100
Single Entry Plugin

1160
00:37:33,100 --> 00:37:34,240
然后传递

1161
00:37:34,240 --> 00:37:35,060
越来越来越了一个什么

1162
00:37:35,060 --> 00:37:36,660
Entry和Contest

1163
00:37:36,660 --> 00:37:37,360
Entry和魅

1164
00:37:37,360 --> 00:37:38,680
然后进行吧

1165
00:37:38,680 --> 00:37:39,360
好

1166
00:37:39,360 --> 00:37:40,820
那么给鞋子插件了

1167
00:37:40,820 --> 00:37:41,520
Single Entry Plugin

1168
00:37:41,520 --> 00:37:43,120
我们来写一下啊

1169
00:37:43,120 --> 00:37:44,820
简查

1170
00:37:44,820 --> 00:37:47,940
他其实啊

1171
00:37:47,940 --> 00:37:49,800
就是真的一个监听我们的这个

1172
00:37:49,800 --> 00:37:51,600
就是我们的编译

1173
00:37:51,600 --> 00:37:52,740
是由他开启的

1174
00:37:52,740 --> 00:37:54,480
好我们来写class

1175
00:37:54,480 --> 00:37:55,480
什么样

1176
00:37:55,480 --> 00:38:01,260
我们想导出module.adpost

1177
00:38:01,260 --> 00:38:02,020
等于什么

1178
00:38:02,020 --> 00:38:02,860
single entry plugin

1179
00:38:02,860 --> 00:38:03,440
对吧

1180
00:38:03,440 --> 00:38:05,000
然后他们有apply方法

1181
00:38:05,000 --> 00:38:07,740
一般有个什么

1182
00:38:07,740 --> 00:38:09,300
有个compare的什么样

1183
00:38:09,300 --> 00:38:11,700
compare的一个对象

1184
00:38:11,700 --> 00:38:13,580
是不是一个编译对象啊

1185
00:38:13,580 --> 00:38:13,900
对不对

1186
00:38:13,900 --> 00:38:16,000
你看啊

1187
00:38:16,000 --> 00:38:16,720
他有个

1188
00:38:16,720 --> 00:38:18,780
你看他是不是有个三个参数啊

1189
00:38:18,780 --> 00:38:19,960
分别是我们的context

1190
00:38:19,960 --> 00:38:21,880
Android和may啊

1191
00:38:21,880 --> 00:38:22,580
是不是啊

1192
00:38:22,580 --> 00:38:22,760
好

1193
00:38:22,760 --> 00:38:23,280
有个短数

1194
00:38:23,280 --> 00:38:24,660
接收一下啊

1195
00:38:24,660 --> 00:38:25,520
个短数接收一下

1196
00:38:25,520 --> 00:38:27,080
constructor

1197
00:38:27,080 --> 00:38:28,460
对吧

1198
00:38:28,460 --> 00:38:29,180
好

1199
00:38:29,180 --> 00:38:29,980
里边传入了一个什么

1200
00:38:29,980 --> 00:38:30,920
传入一个第一个呢

1201
00:38:30,920 --> 00:38:31,900
是我们这个context

1202
00:38:31,900 --> 00:38:32,940
第二个呢

1203
00:38:32,940 --> 00:38:33,360
是什么什么

1204
00:38:33,360 --> 00:38:33,960
是我们这个

1205
00:38:33,960 --> 00:38:36,100
是不是Android啊

1206
00:38:36,100 --> 00:38:36,960
入口模块啊

1207
00:38:36,960 --> 00:38:37,920
第三个是我们什么

1208
00:38:37,920 --> 00:38:38,780
名字吧

1209
00:38:38,780 --> 00:38:39,060
name

1210
00:38:39,060 --> 00:38:39,620
对吧

1211
00:38:39,620 --> 00:38:40,280
第三个name

1212
00:38:40,280 --> 00:38:42,600
对吧

1213
00:38:42,600 --> 00:38:43,180
好

1214
00:38:43,180 --> 00:38:43,900
那么写上了吧

1215
00:38:43,900 --> 00:38:47,560
就是zdrcontext等于context

1216
00:38:47,560 --> 00:38:53,040
zdrentry等于ntry

1217
00:38:53,040 --> 00:38:55,900
zdr什么name等于name

1218
00:38:55,900 --> 00:38:56,780
对吧

1219
00:38:56,780 --> 00:38:59,540
好

1220
00:38:59,540 --> 00:39:00,060
这是什么

1221
00:39:00,060 --> 00:39:02,500
这是我们这个上下文目录

1222
00:39:02,500 --> 00:39:03,560
这个呢

1223
00:39:03,560 --> 00:39:04,380
是我们的入口文件

1224
00:39:04,380 --> 00:39:08,860
这是一个相对路径

1225
00:39:08,860 --> 00:39:09,160
是吧

1226
00:39:09,160 --> 00:39:10,540
这是绝对的

1227
00:39:13,180 --> 00:39:17,180
就是d4对吧 绝对的就是我们的d4

1228
00:39:17,180 --> 00:39:22,180
好 这是他的名字

1229
00:39:22,180 --> 00:39:24,180
莫名就是妹对吧 就是妹

1230
00:39:24,180 --> 00:39:26,180
好 可以了

1231
00:39:26,180 --> 00:39:28,180
那Pyler里边干嘛

1232
00:39:28,180 --> 00:39:30,180
Pyler里边他要接定一个事件啊 什么事件啊

1233
00:39:30,180 --> 00:39:32,180
就是Compiler 第二什么

1234
00:39:32,180 --> 00:39:34,180
第二hooks第二make

1235
00:39:34,180 --> 00:39:38,180
对吧 第二什么 第二这个type async

1236
00:39:38,180 --> 00:39:40,180
就是中国type async

1237
00:39:40,180 --> 00:39:41,180
可以注册一个 你看要注意啊

1238
00:39:41,180 --> 00:39:42,180
这个make呀

1239
00:39:42,180 --> 00:39:43,340
是什么

1240
00:39:43,340 --> 00:39:45,760
是一个eve的勾子

1241
00:39:45,760 --> 00:39:48,540
刚才没有细讲

1242
00:39:48,540 --> 00:39:49,600
你们来不及了

1243
00:39:49,600 --> 00:39:51,020
那个是个eve的勾子

1244
00:39:51,020 --> 00:39:52,000
然后可以进行注册

1245
00:39:52,000 --> 00:39:52,740
type sync

1246
00:39:52,740 --> 00:39:54,800
你看它里边会有什么

1247
00:39:54,800 --> 00:39:56,640
第一个参数是个名字

1248
00:39:56,640 --> 00:39:58,780
就是我们这个single entry plug-in

1249
00:39:58,780 --> 00:39:59,620
就是名字

1250
00:39:59,620 --> 00:40:03,400
第二个参数是个回调

1251
00:40:03,400 --> 00:40:06,900
回调里边有两个参数

1252
00:40:06,900 --> 00:40:07,820
一个是什么

1253
00:40:07,820 --> 00:40:08,700
一个是什么completion

1254
00:40:08,700 --> 00:40:11,600
第二就是我们这个什么

1255
00:40:11,600 --> 00:40:12,700
现在就是我们的Covac

1256
00:40:12,700 --> 00:40:15,920
就说当这个实践完成之后

1257
00:40:15,920 --> 00:40:16,840
调Covac表什么

1258
00:40:16,840 --> 00:40:18,180
比如这个勾引已经结束了

1259
00:40:18,180 --> 00:40:19,480
这个接连路已经结束了

1260
00:40:19,480 --> 00:40:19,660
是不是

1261
00:40:19,660 --> 00:40:21,740
另外什么叫Compulation

1262
00:40:21,740 --> 00:40:22,720
Compulation代表什么

1263
00:40:22,720 --> 00:40:23,580
代表一次的编译

1264
00:40:23,580 --> 00:40:24,900
对吧

1265
00:40:24,900 --> 00:40:26,840
你看要指导两个对象

1266
00:40:26,840 --> 00:40:27,540
一个叫什么

1267
00:40:27,540 --> 00:40:28,720
一个叫Compiler

1268
00:40:28,720 --> 00:40:33,300
代表什么

1269
00:40:33,300 --> 00:40:35,480
代表整次整个编译对象

1270
00:40:35,480 --> 00:40:38,840
那么Compulate代表

1271
00:40:38,840 --> 00:40:39,720
代表一次编译

1272
00:40:39,720 --> 00:40:41,640
就每次编译啊

1273
00:40:41,640 --> 00:40:43,080
都会创意一个新的什么complation

1274
00:40:43,080 --> 00:40:43,840
对不对

1275
00:40:43,840 --> 00:40:45,320
但在知识之中啊

1276
00:40:45,320 --> 00:40:46,240
只有一个compiler

1277
00:40:46,240 --> 00:40:47,820
所以大家知道啊

1278
00:40:47,820 --> 00:40:47,980
你看

1279
00:40:47,980 --> 00:40:49,340
它建立了那个时间

1280
00:40:49,340 --> 00:40:50,580
然后呢什么呀

1281
00:40:50,580 --> 00:40:52,920
然后呢我们会得到两参数

1282
00:40:52,920 --> 00:40:54,580
complay和我们的这个callback

1283
00:40:54,580 --> 00:40:57,920
然后在这呢

1284
00:40:57,920 --> 00:40:58,600
我们这么做啊

1285
00:40:58,600 --> 00:40:59,340
我们先解构一下

1286
00:40:59,340 --> 00:41:03,540
解构咱们的context

1287
00:41:03,540 --> 00:41:07,400
解构我们的entry

1288
00:41:07,400 --> 00:41:09,280
解构我们的name

1289
00:41:09,280 --> 00:41:10,380
对吧

1290
00:41:10,380 --> 00:41:11,920
然后呢我们怎么样

1291
00:41:11,920 --> 00:41:13,160
我们去电用什么

1292
00:41:13,160 --> 00:41:14,620
电用completion的什么呀

1293
00:41:14,620 --> 00:41:17,620
他的这个add按键方法

1294
00:41:17,620 --> 00:41:19,200
什么叫add按键啊

1295
00:41:19,200 --> 00:41:20,780
就是增加一个入口文件

1296
00:41:20,780 --> 00:41:22,140
开始编译入口文件

1297
00:41:22,140 --> 00:41:23,200
进行后续的编译

1298
00:41:23,200 --> 00:41:25,000
编译入口文件和他依赖了模块

1299
00:41:25,000 --> 00:41:25,580
是不是

1300
00:41:25,580 --> 00:41:27,580
那么参注呢有三个

1301
00:41:27,580 --> 00:41:28,320
一个是contest

1302
00:41:28,320 --> 00:41:29,880
一个什么这个entree

1303
00:41:29,880 --> 00:41:30,440
一个是name

1304
00:41:30,440 --> 00:41:31,760
还有cobank是不是

1305
00:41:31,760 --> 00:41:33,420
你看

1306
00:41:33,420 --> 00:41:36,340
把三文入口文件和名字

1307
00:41:36,340 --> 00:41:37,520
传给这个completion

1308
00:41:37,520 --> 00:41:38,160
add按键方法

1309
00:41:38,160 --> 00:41:39,540
把cobac给传给他

1310
00:41:39,540 --> 00:41:40,900
他会帮我们掉cobac是不是

1311
00:41:40,900 --> 00:41:42,640
当他就是文件变运完之后呢

1312
00:41:42,640 --> 00:41:44,400
会掉cobac就可以了啊就可以了

1313
00:41:44,400 --> 00:41:46,920
好那么这样的话

1314
00:41:46,920 --> 00:41:48,820
我们这个这个就搞定了吧

1315
00:41:48,820 --> 00:41:50,140
搞定了啊

1316
00:41:50,140 --> 00:41:52,900
好那你想

1317
00:41:52,900 --> 00:41:56,100
那这个这里边是不是建议这个make时间

1318
00:41:56,100 --> 00:41:57,460
make这个勾子啊

1319
00:41:57,460 --> 00:42:00,300
这个make的勾子哪来的

1320
00:42:00,300 --> 00:42:04,620
是不是肯定是也是这个compara上面的一个勾子啊

1321
00:42:04,620 --> 00:42:04,980
对不对

1322
00:42:04,980 --> 00:42:06,720
它是它是evo的勾子是不是

1323
00:42:06,720 --> 00:42:07,920
evo的勾子啊

1324
00:42:07,920 --> 00:42:09,120
我带大家注意一下

1325
00:42:09,120 --> 00:42:10,360
他怎么来的

1326
00:42:10,360 --> 00:42:11,640
好

1327
00:42:11,640 --> 00:42:13,440
那我们看圆码啊

1328
00:42:13,440 --> 00:42:14,060
咱们看圆码

1329
00:42:14,060 --> 00:42:14,920
咱们看一下

1330
00:42:14,920 --> 00:42:15,840
咱们看一下啊

1331
00:42:15,840 --> 00:42:17,400
感觉到哪了

1332
00:42:17,400 --> 00:42:19,380
是不是到这儿你看

1333
00:42:19,380 --> 00:42:21,080
咱们来重新来

1334
00:42:21,080 --> 00:42:22,640
回来啊

1335
00:42:22,640 --> 00:42:27,920
重新定位到这儿

1336
00:42:27,920 --> 00:42:28,080
你看

1337
00:42:28,080 --> 00:42:32,080
咱们先怎么样

1338
00:42:32,080 --> 00:42:32,880
先拥有了一个

1339
00:42:32,880 --> 00:42:33,920
Entry Option Plug-in

1340
00:42:33,920 --> 00:42:34,300
Apply

1341
00:42:34,300 --> 00:42:35,620
是不是监听啊

1342
00:42:35,620 --> 00:42:36,120
进去看看

1343
00:42:36,120 --> 00:42:37,740
来

1344
00:42:37,920 --> 00:42:39,520
他有什么apply方法

1345
00:42:39,520 --> 00:42:40,080
我们进去看看

1346
00:42:40,080 --> 00:42:42,600
apply方在干嘛

1347
00:42:42,600 --> 00:42:43,040
你看啊

1348
00:42:43,040 --> 00:42:43,460
他在干嘛

1349
00:42:43,460 --> 00:42:43,700
你看

1350
00:42:43,700 --> 00:42:46,220
你看是不是这是监听啊

1351
00:42:46,220 --> 00:42:47,720
监听andway option啊

1352
00:42:47,720 --> 00:42:48,680
但是不执行啊

1353
00:42:48,680 --> 00:42:49,320
什么执行的

1354
00:42:49,320 --> 00:42:49,820
看

1355
00:42:49,820 --> 00:42:51,160
我说是不是靠了一个什么

1356
00:42:51,160 --> 00:42:52,080
andway option啊

1357
00:42:52,080 --> 00:42:53,460
是不是触发这个世界啊

1358
00:42:53,460 --> 00:42:54,020
正出来执行

1359
00:42:54,020 --> 00:42:54,360
你看啊

1360
00:42:54,360 --> 00:42:54,820
进来看看

1361
00:42:54,820 --> 00:42:59,560
拿到这个函数了啊

1362
00:42:59,560 --> 00:42:59,940
执行

1363
00:42:59,940 --> 00:43:01,760
你看是不是进来了

1364
00:43:01,760 --> 00:43:02,760
andway option

1365
00:43:02,760 --> 00:43:03,460
执行进来了

1366
00:43:03,460 --> 00:43:04,560
在里边你看

1367
00:43:04,560 --> 00:43:05,380
往那里边走

1368
00:43:05,380 --> 00:43:10,300
是不是一个

1369
00:43:10,300 --> 00:43:11,520
item to plug in

1370
00:43:11,520 --> 00:43:12,840
好 进去

1371
00:43:12,840 --> 00:43:14,820
里边干嘛 你看

1372
00:43:14,820 --> 00:43:16,660
你看 你有一个什么

1373
00:43:16,660 --> 00:43:17,780
single entry plugin

1374
00:43:17,780 --> 00:43:19,840
第一个是contest

1375
00:43:19,840 --> 00:43:21,320
是不是第四啊

1376
00:43:21,320 --> 00:43:22,280
第二个是不是atom

1377
00:43:22,280 --> 00:43:23,380
是不是这个入口没见

1378
00:43:23,380 --> 00:43:25,900
第四个什么

1379
00:43:25,900 --> 00:43:26,400
我们name

1380
00:43:26,400 --> 00:43:27,580
是不是一个名字

1381
00:43:27,580 --> 00:43:28,540
对不对

1382
00:43:28,540 --> 00:43:29,820
我们进去看看吧

1383
00:43:29,820 --> 00:43:32,660
进去我们这个single entry plugin

1384
00:43:32,660 --> 00:43:33,200
进去看看

1385
00:43:33,200 --> 00:43:36,920
你发现它是不三参数

1386
00:43:36,920 --> 00:43:38,060
你看controller里三参数

1387
00:43:38,060 --> 00:43:39,660
contest entry和内部是不是

1388
00:43:39,660 --> 00:43:40,160
走

1389
00:43:40,160 --> 00:43:42,340
拿到之后呢怎么样

1390
00:43:42,340 --> 00:43:43,180
叫什么

1391
00:43:43,180 --> 00:43:45,500
叫他的apply方法是不是

1392
00:43:45,500 --> 00:43:46,420
进去

1393
00:43:46,420 --> 00:43:49,700
你看里边在干嘛

1394
00:43:49,700 --> 00:43:53,220
重点看第二个

1395
00:43:53,220 --> 00:43:56,700
你看是不是一个compiler.make

1396
00:43:56,700 --> 00:43:57,860
鉴定make时间

1397
00:43:57,860 --> 00:43:59,500
而且这一步鉴定啊

1398
00:43:59,500 --> 00:44:00,920
怎么鉴定里边在干嘛

1399
00:44:00,920 --> 00:44:01,660
你看里边在干嘛

1400
00:44:01,660 --> 00:44:03,780
是不是调了我们这个什么什么

1401
00:44:03,780 --> 00:44:04,680
呃

1402
00:44:04,680 --> 00:44:05,480
crate dependency

1403
00:44:05,480 --> 00:44:07,020
然后add entry啊

1404
00:44:07,020 --> 00:44:08,520
这个步我们先省略它

1405
00:44:08,520 --> 00:44:09,120
我们先不管它

1406
00:44:09,120 --> 00:44:10,240
我们先试着看这一行

1407
00:44:10,240 --> 00:44:10,700
对吧

1408
00:44:10,700 --> 00:44:12,940
你看是不是就是add entry增加入口

1409
00:44:12,940 --> 00:44:13,700
然后呢

1410
00:44:13,700 --> 00:44:14,200
contest

1411
00:44:14,200 --> 00:44:16,160
编写的一个文件

1412
00:44:16,160 --> 00:44:17,420
名字和cobac

1413
00:44:17,420 --> 00:44:18,400
对吧

1414
00:44:18,400 --> 00:44:19,840
到此为止

1415
00:44:19,840 --> 00:44:20,500
我们已经写到这了

1416
00:44:20,500 --> 00:44:20,700
是不是

1417
00:44:20,700 --> 00:44:21,780
已经写到这了

1418
00:44:21,780 --> 00:44:22,920
但是这个时候啊

1419
00:44:22,920 --> 00:44:23,740
并不会监听

1420
00:44:23,740 --> 00:44:24,940
是不是就完事了

1421
00:44:24,940 --> 00:44:26,160
就监听完事了

1422
00:44:26,160 --> 00:44:26,720
对吧

1423
00:44:26,720 --> 00:44:28,620
那到底什么时候开始make呢

1424
00:44:28,620 --> 00:44:29,340
对吧

1425
00:44:29,340 --> 00:44:30,100
我们得往后看

1426
00:44:30,100 --> 00:44:31,320
我们得往后看

1427
00:44:31,320 --> 00:44:33,340
什么时候开始make

1428
00:44:33,340 --> 00:44:38,880
那真的往后倒了吧

1429
00:44:38,880 --> 00:44:39,840
我们看看

1430
00:44:39,840 --> 00:44:41,800
坚定到这就结束了

1431
00:44:41,800 --> 00:44:43,140
那什么时候开始make呢

1432
00:44:43,140 --> 00:44:43,760
咱们重点看

1433
00:44:43,760 --> 00:44:44,700
不重要的给它过了

1434
00:44:44,700 --> 00:44:45,980
我们讲中

1435
00:44:45,980 --> 00:44:47,440
直接讲重要的

1436
00:44:47,440 --> 00:44:53,840
好咱们往下看

1437
00:44:53,840 --> 00:44:58,140
还有吗

1438
00:44:58,140 --> 00:44:58,760
还有很多吗

1439
00:45:01,320 --> 00:45:04,000
好咱们走了啊

1440
00:45:04,000 --> 00:45:04,160
你看

1441
00:45:04,160 --> 00:45:07,480
返回compiler

1442
00:45:07,480 --> 00:45:09,040
看是不是开始run了

1443
00:45:09,040 --> 00:45:10,060
就开始run了

1444
00:45:10,060 --> 00:45:11,000
是不是

1445
00:45:11,000 --> 00:45:12,280
那么下面该写什么

1446
00:45:12,280 --> 00:45:13,600
是不是该回到写状方法了

1447
00:45:13,600 --> 00:45:15,220
你看到这一步我们该写什么

1448
00:45:15,220 --> 00:45:16,340
该写状方法了

1449
00:45:16,340 --> 00:45:18,320
看是不是该写compiler里面run方法了

1450
00:45:18,320 --> 00:45:19,020
对吧

1451
00:45:19,020 --> 00:45:20,780
咱们给打印一下

1452
00:45:20,780 --> 00:45:24,140
开始边写什么

1453
00:45:24,140 --> 00:45:26,220
run方法

1454
00:45:26,220 --> 00:45:28,420
开始执行了

1455
00:45:28,420 --> 00:45:29,320
开始运行了是不是

1456
00:45:29,320 --> 00:45:29,920
对吧

1457
00:45:29,920 --> 00:45:31,060
运行了啊

1458
00:45:31,060 --> 00:45:33,060
你看看是不是这样的

1459
00:45:33,060 --> 00:45:35,560
啊 这行

1460
00:45:35,560 --> 00:45:38,240
这个没有定义是吧

1461
00:45:38,240 --> 00:45:39,640
没有引入这个模块是吧

1462
00:45:39,640 --> 00:45:41,120
你一下去这个

1463
00:45:41,120 --> 00:45:44,700
嗯 引一下是吧

1464
00:45:44,700 --> 00:45:46,220
你一下这个模块

1465
00:45:46,220 --> 00:45:50,980
大家尽量听啊

1466
00:45:50,980 --> 00:45:51,940
可能说还听不懂

1467
00:45:51,940 --> 00:45:53,560
但是这个没办法对吧

1468
00:45:53,560 --> 00:45:55,340
你如果对吧

1469
00:45:55,340 --> 00:45:55,960
没有基础的话

1470
00:45:55,960 --> 00:45:57,600
的确听起来有点困难啊

1471
00:45:57,600 --> 00:45:58,420
记得坚持住啊

1472
00:45:58,420 --> 00:45:58,920
坚持住啊

1473
00:45:58,920 --> 00:46:01,900
你可能多听几遍就好了

1474
00:46:01,900 --> 00:46:06,680
好 我们走

1475
00:46:06,680 --> 00:46:09,200
type

1476
00:46:09,200 --> 00:46:11,460
你看我这是不是没有这个勾子

1477
00:46:11,460 --> 00:46:12,840
entry option的勾子

1478
00:46:12,840 --> 00:46:14,280
看是不是没办法没有

1479
00:46:14,280 --> 00:46:15,340
看compiler里面

1480
00:46:15,340 --> 00:46:17,280
compiler里面是不是没有这个勾子

1481
00:46:17,280 --> 00:46:18,380
过这没有

1482
00:46:18,380 --> 00:46:19,440
那怎么办

1483
00:46:19,440 --> 00:46:20,360
建一下呗

1484
00:46:20,360 --> 00:46:20,960
对不对

1485
00:46:20,960 --> 00:46:22,160
我说要建这个勾子

1486
00:46:22,160 --> 00:46:24,160
什么勾子

1487
00:46:24,160 --> 00:46:26,300
你看叫什么叫entry option

1488
00:46:26,300 --> 00:46:28,560
就这个勾子

1489
00:46:28,560 --> 00:46:31,260
好 拿过来

1490
00:46:31,260 --> 00:46:32,560
下的勾子

1491
00:46:32,560 --> 00:46:35,560
option等于newsynchook

1492
00:46:35,560 --> 00:46:36,560
有完事了

1493
00:46:36,560 --> 00:46:38,560
给大家就可以了

1494
00:46:38,560 --> 00:46:44,560
有些勾子是没能监听的

1495
00:46:44,560 --> 00:46:45,560
的确是这样的

1496
00:46:45,560 --> 00:46:48,560
single plugin也需要导入

1497
00:46:48,560 --> 00:46:50,560
single plugin也需要导入

1498
00:46:50,560 --> 00:46:52,560
single plugin

1499
00:46:52,560 --> 00:46:56,560
把这个已经进来

1500
00:46:58,560 --> 00:47:00,560
可以导入进来

1501
00:47:28,560 --> 00:47:29,800
是吧对不对啊

1502
00:47:29,800 --> 00:47:30,600
所以很明显呀

1503
00:47:30,600 --> 00:47:33,660
咱们这个他不是一个这样的勾子是不是啊

1504
00:47:33,660 --> 00:47:34,500
说make什么勾子啊

1505
00:47:34,500 --> 00:47:35,180
make什么什么

1506
00:47:35,180 --> 00:47:35,940
谁是这样勾子啊

1507
00:47:35,940 --> 00:47:36,560
think

1508
00:47:36,560 --> 00:47:37,000
我又有什么

1509
00:47:37,000 --> 00:47:37,760
你有一个啊

1510
00:47:37,760 --> 00:47:38,160
think

1511
00:47:38,160 --> 00:47:39,760
parallel

1512
00:47:39,760 --> 00:47:41,800
啊

1513
00:47:41,800 --> 00:47:42,920
parallel

1514
00:47:42,920 --> 00:47:46,320
parallel hook

1515
00:47:46,320 --> 00:47:48,240
然后里边传了一个参数叫什么

1516
00:47:48,240 --> 00:47:48,720
completion

1517
00:47:48,720 --> 00:47:50,900
他这样一个勾子

1518
00:47:50,900 --> 00:47:52,360
好把这个引入一下吧

1519
00:47:52,360 --> 00:47:53,060
拟拟了啊

1520
00:47:53,060 --> 00:47:57,120
他这个一一步的同步勾子

1521
00:47:57,120 --> 00:47:57,820
谁要一步同步

1522
00:47:57,820 --> 00:47:59,820
就是说首先这一步的里面可以

1523
00:47:59,820 --> 00:48:01,720
他接地行数啊可以是一步代码

1524
00:48:01,720 --> 00:48:02,940
然后每个一步代码呢

1525
00:48:02,940 --> 00:48:02,940
有个回击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击击

1526
00:48:02,940 --> 00:48:03,720
有个回线权数

1527
00:48:03,720 --> 00:48:05,020
调查表表完成

1528
00:48:05,020 --> 00:48:06,900
那么等所有这个

1529
00:48:06,900 --> 00:48:08,040
所有什么呀

1530
00:48:08,040 --> 00:48:09,080
这些一步一步间的权数

1531
00:48:09,080 --> 00:48:10,220
他们会同时开始

1532
00:48:10,220 --> 00:48:12,020
那么等他们求完成之后呢

1533
00:48:12,020 --> 00:48:13,160
他会直接后面的毁掉

1534
00:48:13,160 --> 00:48:14,040
这叫什么叫

1535
00:48:14,040 --> 00:48:15,540
一步的并行钩子

1536
00:48:15,540 --> 00:48:15,940
是不是

1537
00:48:15,940 --> 00:48:16,580
好

1538
00:48:16,580 --> 00:48:17,480
那么后就是什么

1539
00:48:17,480 --> 00:48:18,540
就是一步并行钩子

1540
00:48:18,540 --> 00:48:18,740
是不是

1541
00:48:18,740 --> 00:48:19,560
好

1542
00:48:19,560 --> 00:48:20,440
那么来写啊

1543
00:48:20,440 --> 00:48:21,060
比如说我们现在

1544
00:48:21,060 --> 00:48:23,020
拿到户子之后

1545
00:48:23,020 --> 00:48:25,900
你看这我们的一个MAC是吧

1546
00:48:25,900 --> 00:48:30,300
你看MAC谁监听的呀

1547
00:48:30,300 --> 00:48:31,260
是不是我们这监听的呀

1548
00:48:31,260 --> 00:48:32,560
你看是不是在监听的呀

1549
00:48:32,560 --> 00:48:32,920
对不对

1550
00:48:32,920 --> 00:48:34,060
那就可以了

1551
00:48:34,060 --> 00:48:39,160
看了吧

1552
00:48:39,160 --> 00:48:40,120
插件就行了

1553
00:48:40,120 --> 00:48:41,240
开始写让方法了

1554
00:48:41,240 --> 00:48:42,120
编译完成了

1555
00:48:42,120 --> 00:48:43,040
好

1556
00:48:43,040 --> 00:48:44,140
那么接着写状方法

1557
00:48:44,140 --> 00:48:46,300
接着我们写状方法

1558
00:48:46,300 --> 00:48:49,600
状方法在哪了

1559
00:48:49,600 --> 00:48:50,360
是不是在坑边里边

1560
00:48:50,360 --> 00:48:51,240
状里边写在这写

1561
00:48:51,240 --> 00:48:53,240
先把它干掉

1562
00:48:53,240 --> 00:48:54,440
开始写了

1563
00:48:54,440 --> 00:48:55,340
怎么写

1564
00:48:55,340 --> 00:48:57,220
咱们看原码怎么写的

1565
00:48:57,220 --> 00:48:58,700
我们看原码怎么实现的

1566
00:48:58,700 --> 00:48:59,160
往后走

1567
00:48:59,160 --> 00:49:00,300
看是不是状方法

1568
00:49:00,300 --> 00:49:01,160
第二码把进来

1569
00:49:02,540 --> 00:49:04,060
首先你看啊

1570
00:49:04,060 --> 00:49:05,460
它会怎么办

1571
00:49:05,460 --> 00:49:05,920
你看啊

1572
00:49:05,920 --> 00:49:08,520
它会先生命

1573
00:49:08,520 --> 00:49:09,100
生命一个什么

1574
00:49:09,100 --> 00:49:09,600
生命一个

1575
00:49:09,600 --> 00:49:11,100
on compare的一个什么

1576
00:49:11,100 --> 00:49:11,660
一个回调

1577
00:49:11,660 --> 00:49:13,340
编译完成之后会走这个回调

1578
00:49:13,340 --> 00:49:13,560
是不是

1579
00:49:13,560 --> 00:49:14,400
好

1580
00:49:14,400 --> 00:49:15,120
把它拿过来吧

1581
00:49:15,120 --> 00:49:15,880
拿过来

1582
00:49:15,880 --> 00:49:16,860
放在这

1583
00:49:16,860 --> 00:49:19,840
就是编译完成之后的回调

1584
00:49:19,840 --> 00:49:20,720
对吧

1585
00:49:20,720 --> 00:49:24,580
编译完成之后的回调

1586
00:49:24,580 --> 00:49:29,400
是不是

1587
00:49:29,400 --> 00:49:30,460
再来看

1588
00:49:30,460 --> 00:49:31,340
再来看

1589
00:49:31,340 --> 00:49:32,420
你看

1590
00:49:32,420 --> 00:49:33,100
走

1591
00:49:33,100 --> 00:49:35,200
是要先走

1592
00:49:35,200 --> 00:49:35,640
先走什么

1593
00:49:35,640 --> 00:49:36,760
先走 before run

1594
00:49:36,760 --> 00:49:38,080
就是运行之前

1595
00:49:38,080 --> 00:49:38,800
对吧

1596
00:49:38,800 --> 00:49:39,800
然后走什么

1597
00:49:39,800 --> 00:49:41,020
走 run 运行

1598
00:49:41,020 --> 00:49:41,640
对吧

1599
00:49:41,640 --> 00:49:42,420
然后走什么

1600
00:49:42,420 --> 00:49:42,940
走编译

1601
00:49:42,940 --> 00:49:44,480
先是 before run

1602
00:49:44,480 --> 00:49:45,380
然后什么呀

1603
00:49:45,380 --> 00:49:46,040
然后 run

1604
00:49:46,040 --> 00:49:46,860
然后呢

1605
00:49:46,860 --> 00:49:47,260
编译

1606
00:49:47,260 --> 00:49:48,460
它分入三部曲

1607
00:49:48,460 --> 00:49:48,720
是不是

1608
00:49:48,720 --> 00:49:50,420
好我们也按照这个写法来写

1609
00:49:50,420 --> 00:49:50,760
都是什么

1610
00:49:50,760 --> 00:49:51,620
都是一部的啊

1611
00:49:51,620 --> 00:49:52,580
都是 call think 是不是

1612
00:49:52,580 --> 00:49:53,660
好我们来写一下

1613
00:49:53,660 --> 00:49:55,960
怎么写啊

1614
00:49:55,960 --> 00:49:56,380
怎么现在

1615
00:49:56,380 --> 00:49:57,420
现在我们现在一个叫

1616
00:49:57,420 --> 00:49:58,020
z的点

1617
00:49:58,020 --> 00:49:58,700
hooks

1618
00:49:58,700 --> 00:49:59,460
叫什么

1619
00:49:59,460 --> 00:50:00,360
点 before run

1620
00:50:00,360 --> 00:50:03,760
叫什么叫 call sync

1621
00:50:03,760 --> 00:50:08,320
好 里边呢 我们来一个

1622
00:50:08,320 --> 00:50:11,500
传入一个 this 就是我们的 compiler 是吧

1623
00:50:11,500 --> 00:50:13,180
然后传个回调 error

1624
00:50:13,180 --> 00:50:17,700
然后走什么 走 this.hooks.run

1625
00:50:17,700 --> 00:50:23,180
是不是出发 run 事件之行啊 对不对 也是什么 异步的啊 call sync

1626
00:50:23,180 --> 00:50:27,520
也这传 也传什么 传个 this 和 error 回调

1627
00:50:27,520 --> 00:50:28,860
回调是吧

1628
00:50:28,860 --> 00:50:31,140
然后走什么

1629
00:50:31,140 --> 00:50:32,200
走是不是都编译啊

1630
00:50:32,200 --> 00:50:33,340
这点执行我们的这点

1631
00:50:33,340 --> 00:50:34,340
compiler是吧

1632
00:50:34,340 --> 00:50:35,660
compiler方法对不对

1633
00:50:35,660 --> 00:50:37,580
然后传议我们这个回调

1634
00:50:37,580 --> 00:50:39,080
oncompiler的回调数

1635
00:50:39,080 --> 00:50:39,480
是不是

1636
00:50:39,480 --> 00:50:41,320
就是开始什么

1637
00:50:41,320 --> 00:50:42,060
开始编译

1638
00:50:42,060 --> 00:50:43,440
然后编译完成之后呢

1639
00:50:43,440 --> 00:50:44,080
会走什么

1640
00:50:44,080 --> 00:50:45,540
走编译成功都不是回调

1641
00:50:45,540 --> 00:50:47,260
就这样执行的啊

1642
00:50:47,260 --> 00:50:48,320
但你要这样写的话

1643
00:50:48,320 --> 00:50:49,560
你是不是要多加几勾子了

1644
00:50:49,560 --> 00:50:51,140
before run和 run要加上啊

1645
00:50:51,140 --> 00:50:52,160
好我们加上勾子吧

1646
00:50:52,160 --> 00:50:53,560
好加上啊

1647
00:50:53,560 --> 00:50:55,660
加上我们加上咱们的这个

1648
00:50:55,660 --> 00:50:56,460
比如说before run

1649
00:50:56,460 --> 00:51:02,120
那么它是什么勾的呢

1650
00:51:02,120 --> 00:51:02,840
before run啊

1651
00:51:02,840 --> 00:51:04,720
它是一个think

1652
00:51:04,720 --> 00:51:07,620
叫 theory

1653
00:51:07,620 --> 00:51:11,920
然后它有什么参数呢

1654
00:51:11,920 --> 00:51:12,580
就是什么compiler

1655
00:51:12,580 --> 00:51:14,820
因为你看我这是不是用它的时候传了个this

1656
00:51:14,820 --> 00:51:15,960
就是compiler呀

1657
00:51:15,960 --> 00:51:17,920
那就传compiler过来

1658
00:51:17,920 --> 00:51:21,020
它这个什么

1659
00:51:21,020 --> 00:51:22,780
一步的串行勾的

1660
00:51:22,780 --> 00:51:23,260
什么叫串行

1661
00:51:23,260 --> 00:51:24,880
就是先干一键再干一键

1662
00:51:24,880 --> 00:51:25,600
再干一键是不是

1663
00:51:25,600 --> 00:51:27,400
而且每个都可以一步啊

1664
00:51:27,400 --> 00:51:28,040
转行勾的

1665
00:51:28,040 --> 00:51:29,980
那那run呢run也一样

1666
00:51:29,980 --> 00:51:33,640
叫run是吧run这个实践

1667
00:51:33,640 --> 00:51:36,140
也是什么也是一个这个叫什么

1668
00:51:36,140 --> 00:51:38,640
叫这个呃也是个一步的

1669
00:51:38,640 --> 00:51:40,080
是吧一步转行勾的是吧

1670
00:51:40,080 --> 00:51:41,360
叫run啊

1671
00:51:41,360 --> 00:51:43,200
async service

1672
00:51:43,200 --> 00:51:45,380
siridhook引进来吧

1673
00:51:45,380 --> 00:51:50,460
好那么然后呢

1674
00:51:50,460 --> 00:51:51,960
是不是该写这个方法叫什么

1675
00:51:51,960 --> 00:51:52,920
compile方法了

1676
00:51:52,920 --> 00:51:54,440
对不对啊

1677
00:51:54,440 --> 00:51:56,880
但是写之前啊咱们先把这个勾给建一下

1678
00:51:56,880 --> 00:51:58,540
因为后面肯定有有什么

1679
00:51:58,540 --> 00:52:01,600
有个before compiler compiler和after compiler

1680
00:52:01,600 --> 00:52:03,260
有三个勾的我们都先建好对不对

1681
00:52:03,260 --> 00:52:06,060
先建好啊免得一会再回头建是吧

1682
00:52:06,060 --> 00:52:08,600
好我们先有什么先有我们的before compiler

1683
00:52:08,600 --> 00:52:14,280
compile before compiler就编写之前

1684
00:52:14,280 --> 00:52:15,880
编写之前它是什么

1685
00:52:15,880 --> 00:52:18,480
它是一个它也是一个一步的串线勾的

1686
00:52:18,480 --> 00:52:20,240
啊串线勾的

1687
00:52:20,240 --> 00:52:25,240
它肯定有参数啊

1688
00:52:25,240 --> 00:52:26,180
我们先把它拿过来

1689
00:52:26,180 --> 00:52:27,260
还有什么

1690
00:52:27,260 --> 00:52:27,900
Compiler

1691
00:52:27,900 --> 00:52:30,740
也一样吧

1692
00:52:30,740 --> 00:52:31,460
拿过来

1693
00:52:31,460 --> 00:52:31,980
对不对

1694
00:52:31,980 --> 00:52:32,600
Compiler

1695
00:52:32,600 --> 00:52:36,520
Compiler它是一个应该是个同步的狗

1696
00:52:36,520 --> 00:52:39,660
但这个都是用圆码里面考出来的

1697
00:52:39,660 --> 00:52:41,280
这个这个你看圆码就可以了

1698
00:52:41,280 --> 00:52:41,480
是吧

1699
00:52:41,480 --> 00:52:41,720
你看

1700
00:52:41,720 --> 00:52:42,820
你看下这圆码

1701
00:52:42,820 --> 00:52:44,120
你看往上看

1702
00:52:44,120 --> 00:52:44,780
往上倒

1703
00:52:44,780 --> 00:52:46,120
看

1704
00:52:46,120 --> 00:52:48,440
你看是不是都在这啊

1705
00:52:48,440 --> 00:52:49,480
你看什么什么

1706
00:52:49,480 --> 00:52:51,540
看是不是对了你看什么

1707
00:52:51,540 --> 00:52:53,040
make对吧

1708
00:52:53,040 --> 00:52:54,700
after compare是不是

1709
00:52:54,700 --> 00:52:58,220
compile你看compile是不是一个同步的勾子呀

1710
00:52:58,220 --> 00:52:59,620
有个param的属性啊对不对

1711
00:52:59,620 --> 00:53:02,180
所以我们按这个写的你看是不是一个同步的勾子

1712
00:53:02,180 --> 00:53:03,480
thinkhook是吧

1713
00:53:03,480 --> 00:53:04,360
有param属性

1714
00:53:04,360 --> 00:53:06,360
对吧

1715
00:53:06,360 --> 00:53:09,840
那有before有compile就是什么就after是不是

1716
00:53:09,840 --> 00:53:11,840
好我们会有什么会有after compare

1717
00:53:11,840 --> 00:53:13,180
对吧

1718
00:53:13,180 --> 00:53:17,560
这些勾子啊这个不一定有用啊

1719
00:53:17,560 --> 00:53:19,440
反正就人家就写着

1720
00:53:19,440 --> 00:53:20,960
咱又写着有铁是吧

1721
00:53:20,960 --> 00:53:21,560
不一定有用

1722
00:53:21,560 --> 00:53:22,600
你要鉴定就鉴定

1723
00:53:22,600 --> 00:53:23,520
不鉴定就拉倒了

1724
00:53:23,520 --> 00:53:24,600
没有要求

1725
00:53:24,600 --> 00:53:26,280
好

1726
00:53:26,280 --> 00:53:31,120
其实还有很多

1727
00:53:31,120 --> 00:53:32,880
你看我们调几个重要的

1728
00:53:32,880 --> 00:53:33,500
比如说有什么

1729
00:53:33,500 --> 00:53:34,380
你看有几个比较重要

1730
00:53:34,380 --> 00:53:35,220
有什么有这个

1731
00:53:35,220 --> 00:53:36,720
AFT COMPIRE是不是

1732
00:53:36,720 --> 00:53:38,320
它这个一波的什么呀

1733
00:53:38,320 --> 00:53:39,180
串情勾动的

1734
00:53:39,180 --> 00:53:41,040
是不是一波串情啊

1735
00:53:41,040 --> 00:53:42,480
还有也比较重要的

1736
00:53:42,480 --> 00:53:43,920
比如说有什么呀

1737
00:53:43,920 --> 00:53:45,000
比如说有这个

1738
00:53:45,000 --> 00:53:48,960
有这个重要的

1739
00:53:48,960 --> 00:53:52,400
你看这个我们也用过了 这个也用过了 是吧 这个也用过了 是吧 这个也用过了

1740
00:53:52,400 --> 00:53:52,840
 都用过了

1741
00:53:52,840 --> 00:53:58,680
那比如说哪个中文没用过呀 比如说我们的这个 这个没用过 叫z的什么completion

1742
00:53:58,680 --> 00:54:01,720
还有这个completion 这两个没用到 是吧 我把它拔过来

1743
00:54:01,720 --> 00:54:06,180
不写了 因为这个时间有限啊 我们拿过来拿过来就可以了

1744
00:54:06,180 --> 00:54:08,960
好 比如说放到这 是不是啊 放到这

1745
00:54:08,960 --> 00:54:14,960
好 这是什么 启动这一次的编译

1746
00:54:14,960 --> 00:54:17,720
这一次呢 开始这一次的编译 是吧

1747
00:54:18,120 --> 00:54:19,660
你看他有两次compiler一和param

1748
00:54:19,660 --> 00:54:20,620
compiler一拍我们是不是

1749
00:54:20,620 --> 00:54:21,420
好

1750
00:54:21,420 --> 00:54:24,140
那么到了这一步

1751
00:54:24,140 --> 00:54:25,920
另外还有个重要的勾子叫什么叫档

1752
00:54:25,920 --> 00:54:27,940
这是不是有个档啊

1753
00:54:27,940 --> 00:54:29,840
档什么意思完成对吧

1754
00:54:29,840 --> 00:54:31,420
完成拿过来

1755
00:54:31,420 --> 00:54:33,320
放到这就可以了是吧

1756
00:54:33,320 --> 00:54:35,900
就是一线完成之后呢会掉用档的勾子

1757
00:54:35,900 --> 00:54:37,640
就是一线完成之后

1758
00:54:37,640 --> 00:54:39,920
就是我们已经写完文件系统了

1759
00:54:39,920 --> 00:54:40,500
是吧

1760
00:54:40,500 --> 00:54:42,700
完整之后会触发什么呀

1761
00:54:42,700 --> 00:54:43,700
档这个勾子

1762
00:54:43,700 --> 00:54:47,920
好

1763
00:54:47,920 --> 00:54:51,120
好 可以了吧 到这一步基本上我们的勾子就被齐了啊 被齐了

1764
00:54:51,120 --> 00:54:55,920
什么时候掉呢 我们写的时候一个个来掉对吧 别着急啊 别着急 一个个来掉啊

1765
00:54:55,920 --> 00:54:59,920
啊

1766
00:54:59,920 --> 00:55:01,920
啊 抗外了是吧

1767
00:55:01,920 --> 00:55:02,920
好 这好

1768
00:55:02,920 --> 00:55:08,920
这是吧

1769
00:55:08,920 --> 00:55:11,920
嗯 好 好 勾子准备好了 是吧 我们要写了啊

1770
00:55:11,920 --> 00:55:14,920
该写哪 是不是该写的一个抗外了方法了 是不是啊

1771
00:55:14,920 --> 00:55:16,920
对吧 抗外了什么意思啊

1772
00:55:16,920 --> 00:55:19,480
编译啊对不对啊编译好我们写空拍着

1773
00:55:19,480 --> 00:55:24,340
好编译啊编译

1774
00:55:24,340 --> 00:55:29,980
你先跟下圆码啊咱们跟上圆码跟上我们的脚步是不是对吧好走

1775
00:55:29,980 --> 00:55:34,320
这个结束了到这这到这结束了

1776
00:55:34,320 --> 00:55:36,380
增加入会有结束了啊结束了

1777
00:55:36,380 --> 00:55:39,960
哎他这个已经到这来了

1778
00:55:39,960 --> 00:55:41,760
他已经到这来了

1779
00:55:46,920 --> 00:55:51,180
这样啊

1780
00:55:51,180 --> 00:55:52,440
咱们重新回头来吧

1781
00:55:52,440 --> 00:55:55,440
咱们跟一下哪块呀

1782
00:55:55,440 --> 00:55:56,440
跟一下这块啊

1783
00:55:56,440 --> 00:55:58,300
跟一下这个wrong方法的逻辑是不是

1784
00:55:58,300 --> 00:55:59,600
wrong方法的逻辑

1785
00:55:59,600 --> 00:56:01,060
wrong方法你看

1786
00:56:01,060 --> 00:56:03,860
咱们先从这次wrong方法开始吧

1787
00:56:03,860 --> 00:56:04,220
走

1788
00:56:04,220 --> 00:56:07,220
这个又不要老

1789
00:56:07,220 --> 00:56:07,760
这都过了

1790
00:56:07,760 --> 00:56:08,740
因为这刚才我讲过了

1791
00:56:08,740 --> 00:56:09,040
是吧

1792
00:56:09,040 --> 00:56:10,100
这都讲过了

1793
00:56:10,100 --> 00:56:10,660
过了

1794
00:56:10,660 --> 00:56:12,940
这也讲过了吧

1795
00:56:12,940 --> 00:56:13,520
过了

1796
00:56:13,520 --> 00:56:17,220
这些过了

1797
00:56:17,220 --> 00:56:20,060
好弱了啊你看进来

1798
00:56:20,060 --> 00:56:21,780
那之后你看先怎么样

1799
00:56:21,780 --> 00:56:23,060
他是不是先走了这个

1800
00:56:23,060 --> 00:56:25,120
看抗拍了的是吧

1801
00:56:25,120 --> 00:56:26,580
你看他b4弱

1802
00:56:26,580 --> 00:56:28,180
然后呢运行啊

1803
00:56:28,180 --> 00:56:30,120
然后呢抗拍了这个进来进到这来

1804
00:56:30,120 --> 00:56:32,520
进来吧进去看

1805
00:56:32,520 --> 00:56:34,620
你看他的干嘛

1806
00:56:34,620 --> 00:56:35,780
你看先怎么样啊

1807
00:56:35,780 --> 00:56:37,520
先勾建了一个什么勾建了一个paramels

1808
00:56:37,520 --> 00:56:39,220
什么意思参数是不是啊

1809
00:56:39,220 --> 00:56:41,080
你有什么呀

1810
00:56:41,080 --> 00:56:42,920
你有空配来群对象的参数

1811
00:56:43,320 --> 00:56:45,120
对吧 里面有啥呢 看一下

1812
00:56:45,120 --> 00:56:51,320
里面有两个工厂 一个叫什么一个normal modifactory 是普通模块工厂 一个什么一个contest

1813
00:56:51,320 --> 00:56:53,560
 modifactory 是什么上线模块工厂

1814
00:56:53,560 --> 00:56:57,400
这两个工厂有什么用呢 后面再说啊 反正就两个工厂 是不是啊

1815
00:56:57,400 --> 00:57:03,280
工厂要干嘛 创建模块的啊 创建模块的啊 后面创建模块用它来创建模块啊

1816
00:57:03,280 --> 00:57:10,220
好 然后怎么样 叫before compiler 然后呢 compiler 然后呢我拥有了一个什么

1817
00:57:10,220 --> 00:57:11,220
 拥有了一个comulation

1818
00:57:11,660 --> 00:57:12,260
然后怎么样

1819
00:57:12,260 --> 00:57:13,220
make开始编译

1820
00:57:13,220 --> 00:57:15,160
主要这个时间很重要

1821
00:57:15,160 --> 00:57:16,860
这些都是白给的啊

1822
00:57:16,860 --> 00:57:17,760
这些都是白给的

1823
00:57:17,760 --> 00:57:18,260
这个什么

1824
00:57:18,260 --> 00:57:20,000
这个是一个新的开始

1825
00:57:20,000 --> 00:57:21,620
就是从他开始编译的

1826
00:57:21,620 --> 00:57:23,020
在哪见过的make

1827
00:57:23,020 --> 00:57:24,660
刚才在我们的什么

1828
00:57:24,660 --> 00:57:26,060
在我们这个single什么

1829
00:57:26,060 --> 00:57:26,600
ply unit

1830
00:57:26,600 --> 00:57:27,720
是不是见过的make

1831
00:57:27,720 --> 00:57:29,420
我们是不是鉴定了这个make时间了

1832
00:57:29,420 --> 00:57:31,060
开始开始

1833
00:57:31,060 --> 00:57:32,560
ad answer开始编译了

1834
00:57:32,560 --> 00:57:33,160
对吧

1835
00:57:33,160 --> 00:57:34,720
这到这个地方的话

1836
00:57:34,720 --> 00:57:35,300
就开始什么

1837
00:57:35,300 --> 00:57:37,260
走刚才那个鉴定的逻辑了

1838
00:57:37,260 --> 00:57:38,660
开始真的开始编译了

1839
00:57:38,660 --> 00:57:39,300
对吧

1840
00:57:39,300 --> 00:57:40,460
你常记住啊

1841
00:57:40,460 --> 00:57:41,260
编译前

1842
00:57:41,260 --> 00:57:44,380
before compiler compiler new completion make

1843
00:57:44,380 --> 00:57:48,020
把这个commit complete传给make是不是就可以了

1844
00:57:48,020 --> 00:57:49,520
然后怎么样finish

1845
00:57:49,520 --> 00:57:51,160
然后呢seal

1846
00:57:51,160 --> 00:57:52,940
然后就完事了吧

1847
00:57:52,940 --> 00:57:53,780
要compiler完事了

1848
00:57:53,780 --> 00:57:55,120
好咱们把地方写呀

1849
00:57:55,120 --> 00:57:56,520
咱们写的一块啊

1850
00:57:56,520 --> 00:57:57,620
写的一块

1851
00:57:57,620 --> 00:57:59,300
怎么做的

1852
00:57:59,300 --> 00:58:00,060
你看啊

1853
00:58:00,060 --> 00:58:01,380
compiler怎么办呢

1854
00:58:01,380 --> 00:58:01,980
他这样的啊

1855
00:58:01,980 --> 00:58:03,780
他是第一步怎么样

1856
00:58:03,780 --> 00:58:04,980
他会传过来一个事件

1857
00:58:04,980 --> 00:58:05,740
叫commit

1858
00:58:05,740 --> 00:58:07,480
uncompiler传过来这

1859
00:58:07,480 --> 00:58:07,840
这儿了吧

1860
00:58:07,840 --> 00:58:08,460
你看

1861
00:58:08,460 --> 00:58:10,040
是把他传给了这个方法

1862
00:58:10,040 --> 00:58:12,340
朋友这来了 对吧

1863
00:58:12,340 --> 00:58:14,400
就是开始编译

1864
00:58:14,400 --> 00:58:15,420
开始编译

1865
00:58:15,420 --> 00:58:19,760
对吧

1866
00:58:19,760 --> 00:58:21,040
这怎么写啊

1867
00:58:21,040 --> 00:58:23,100
刚刚看班怎么写啊

1868
00:58:23,100 --> 00:58:24,640
你看 先怎么现在这点

1869
00:58:24,640 --> 00:58:25,660
hooks点什么

1870
00:58:25,660 --> 00:58:26,160
before

1871
00:58:26,160 --> 00:58:27,200
compile

1872
00:58:27,200 --> 00:58:28,480
compile

1873
00:58:28,480 --> 00:58:31,040
compile

1874
00:58:31,040 --> 00:58:31,800
对吧

1875
00:58:31,800 --> 00:58:33,340
然后呢 我们写上什么

1876
00:58:33,340 --> 00:58:35,120
就要调用一下他的call async

1877
00:58:35,120 --> 00:58:36,920
一步的on async

1878
00:58:36,920 --> 00:58:39,480
然后传一个

1879
00:58:39,480 --> 00:58:40,480
参数吧

1880
00:58:40,480 --> 00:58:41,920
我们传空间过去就可以了

1881
00:58:41,920 --> 00:58:43,360
然后再来再来回调

1882
00:58:43,360 --> 00:58:44,040
error

1883
00:58:44,040 --> 00:58:46,520
然后再走谁

1884
00:58:46,520 --> 00:58:47,640
是不是在我们这个

1885
00:58:47,640 --> 00:58:48,800
compile的call

1886
00:58:48,800 --> 00:58:49,800
这里是点hooks

1887
00:58:49,800 --> 00:58:50,440
第二什么

1888
00:58:50,440 --> 00:58:52,920
compile点call

1889
00:58:52,920 --> 00:58:55,360
因为compile是什么

1890
00:58:55,360 --> 00:58:57,000
是不是一个它什么勾的呀

1891
00:58:57,000 --> 00:58:58,840
是不是一个同步的勾的呀

1892
00:58:58,840 --> 00:58:59,840
所以直接call就可以了

1893
00:58:59,840 --> 00:59:01,520
不能调什么调call sync是不是

1894
00:59:01,520 --> 00:59:03,320
但是你before它是什么

1895
00:59:03,320 --> 00:59:04,440
一步的对吧

1896
00:59:04,440 --> 00:59:05,120
好

1897
00:59:05,120 --> 00:59:07,560
然后走谁

1898
00:59:07,840 --> 00:59:10,740
是不是开始开启一个新的创建一个新的什么呀

1899
00:59:10,740 --> 00:59:11,400
completion啊

1900
00:59:11,400 --> 00:59:12,680
completion

1901
00:59:12,680 --> 00:59:15,440
创建一个新的completion对象

1902
00:59:15,440 --> 00:59:18,480
创建一个新的

1903
00:59:18,480 --> 00:59:23,080
一个新的什么completion对象

1904
00:59:23,080 --> 00:59:27,840
这里边放什么这里边放的本次编的结果啊这里边放着

1905
00:59:27,840 --> 00:59:31,740
本次编译的结果

1906
00:59:31,740 --> 00:59:37,080
包括什么我们的modules啊什么assess啊chance啊都在他里边放是不是啊

1907
00:59:37,080 --> 00:59:39,520
代表本次编的编写这个这个这个结果啊

1908
00:59:39,520 --> 00:59:41,160
叫completion

1909
00:59:41,160 --> 00:59:44,540
然后等于等于完zdl new什么completion

1910
00:59:44,540 --> 00:59:47,720
就开始创建

1911
00:59:47,720 --> 00:59:49,320
那写的方法吧

1912
00:59:49,320 --> 00:59:51,580
好写的方法 newcompletion方法

1913
00:59:51,580 --> 00:59:53,880
然后另外你看啊

1914
00:59:53,880 --> 00:59:55,840
到这之后他会触发谁

1915
00:59:55,840 --> 00:59:57,020
哪个世界啊还记得吧

1916
00:59:57,020 --> 00:59:58,820
所以有zdlhooks点什么

1917
00:59:58,820 --> 01:00:00,320
make啊

1918
01:00:00,320 --> 01:00:01,380
第二什么

1919
01:00:01,380 --> 01:00:01,920
第二

1920
01:00:01,920 --> 01:00:02,520
call sync

1921
01:00:02,520 --> 01:00:03,840
啊

1922
01:00:03,840 --> 01:00:05,040
然后第二参数呢

1923
01:00:05,040 --> 01:00:05,720
传一个

1924
01:00:06,180 --> 01:00:07,540
参数就是我们的completion吧

1925
01:00:07,540 --> 01:00:10,140
第二呢咱们传了一个回调

1926
01:00:10,140 --> 01:00:17,060
必要打印我们这个叫什么打印我们这个要这个make完成

1927
01:00:17,060 --> 01:00:22,940
按理说应该在这什么掉这个什么掉这个uncompile是不是

1928
01:00:22,940 --> 01:00:25,980
咱们先不掉啊咱们先一步走先把这个打印出来是不是

1929
01:00:25,980 --> 01:00:26,580
make完成

1930
01:00:26,580 --> 01:00:30,620
那关键要看这个地方怎么创建completion呢

1931
01:00:30,620 --> 01:00:31,260
对不对

1932
01:00:31,260 --> 01:00:31,940
第一步怎么写啊

1933
01:00:31,940 --> 01:00:33,340
怎么创建completion啊

1934
01:00:33,340 --> 01:00:35,140
对吧

1935
01:00:36,100 --> 01:00:36,780
实际上也考虑

1936
01:00:36,780 --> 01:00:37,960
我们可以这样写

1937
01:00:37,960 --> 01:00:38,800
就是这个地方呀

1938
01:00:38,800 --> 01:00:39,880
我们我们只要这样啊

1939
01:00:39,880 --> 01:00:43,100
看他怎么写怎么创建的看啊

1940
01:00:43,100 --> 01:00:43,780
看他怎么创建的

1941
01:00:43,780 --> 01:00:45,400
咱们走

1942
01:00:45,400 --> 01:00:48,760
他怎么直接到这来了

1943
01:00:48,760 --> 01:00:50,440
直接到make里边来了

1944
01:00:50,440 --> 01:00:51,700
走点快啊

1945
01:00:51,700 --> 01:00:52,840
咱们重新回来啊

1946
01:00:52,840 --> 01:00:53,340
再回来

1947
01:00:53,340 --> 01:00:55,500
跟东亚这个流程啊

1948
01:00:55,500 --> 01:00:58,160
走

1949
01:00:58,160 --> 01:01:01,640
看我把进来

1950
01:01:01,640 --> 01:01:04,640
看先 before compiler 是不是啊

1951
01:01:04,640 --> 01:01:06,040
然后进来咱们让进来

1952
01:01:06,100 --> 01:01:09,640
看 现在before compiler 然后 compiler 是不是啊

1953
01:01:09,640 --> 01:01:14,340
然后怎么样 new compilation 看它怎么创建 怎么看一下 怎么创建的啊 进来

1954
01:01:14,340 --> 01:01:18,340
哎 你看 它掉了掉了create completion 是不是啊

1955
01:01:18,340 --> 01:01:19,800
然后进去

1956
01:01:19,800 --> 01:01:23,100
哎 你看是不是 new complaint 的实力啊

1957
01:01:23,100 --> 01:01:25,060
是不是啊 对吧

1958
01:01:25,060 --> 01:01:28,860
黑暗下面都是废话了 就不就不用看了 是吧

1959
01:01:28,860 --> 01:01:33,370
然后出发什么 出发了我们的自己的completion实践和completion实践之行

1960
01:01:33,370 --> 01:01:33,600
 对吧

1961
01:01:33,600 --> 01:01:35,340
然后返回completion可以了

1962
01:01:35,340 --> 01:01:37,180
他就这样创建的

1963
01:01:37,180 --> 01:01:37,980
所以说呀

1964
01:01:37,980 --> 01:01:38,520
我们需要怎么样

1965
01:01:38,520 --> 01:01:40,100
我们是不是需要一个新的类了

1966
01:01:40,100 --> 01:01:40,880
叫什么

1967
01:01:40,880 --> 01:01:42,600
叫什么

1968
01:01:42,600 --> 01:01:44,420
需要个新的什么类

1969
01:01:44,420 --> 01:01:46,360
是不是complain这个类啊

1970
01:01:46,360 --> 01:01:46,740
好

1971
01:01:46,740 --> 01:01:47,780
我们来创建他的类啊

1972
01:01:47,780 --> 01:01:48,500
我们见个样的类

1973
01:01:48,500 --> 01:01:53,340
complain

1974
01:01:53,340 --> 01:01:58,060
complain是个啥

1975
01:01:58,060 --> 01:01:59,300
他也是个类啊

1976
01:01:59,300 --> 01:02:00,220
是不是也是个类啊

1977
01:02:00,220 --> 01:02:01,100
咱们要重新回来

1978
01:02:01,100 --> 01:02:01,940
回到个位置啊

1979
01:02:01,940 --> 01:02:02,880
咱们重新回来

1980
01:02:02,880 --> 01:02:04,840
看看complain

1981
01:02:04,840 --> 01:02:06,640
咱们进去看看它是什么东西是吧

1982
01:02:06,640 --> 01:02:08,280
它其实也是个类啊

1983
01:02:08,280 --> 01:02:08,880
也是个类

1984
01:02:08,880 --> 01:02:12,040
走进去看看啊

1985
01:02:12,040 --> 01:02:15,680
进去看看

1986
01:02:15,680 --> 01:02:16,580
看到吧

1987
01:02:16,580 --> 01:02:17,380
它也是个类

1988
01:02:17,380 --> 01:02:19,140
它也进什么table是不是

1989
01:02:19,140 --> 01:02:19,940
也进了table

1990
01:02:19,940 --> 01:02:21,080
然后也是够难数

1991
01:02:21,080 --> 01:02:23,000
是不是也有一堆什么hooks啊

1992
01:02:23,000 --> 01:02:23,680
对不对

1993
01:02:23,680 --> 01:02:25,700
结果跟什么跟confine很像是不是啊

1994
01:02:25,700 --> 01:02:26,840
所以干脆把它考过来

1995
01:02:26,840 --> 01:02:29,240
把这个部分考过来看了吧

1996
01:02:29,240 --> 01:02:33,440
是跟那很像

1997
01:02:33,440 --> 01:02:34,140
对不对

1998
01:02:34,140 --> 01:02:34,820
也有什么够难

1999
01:02:34,840 --> 01:02:36,160
难说吧 够难说

2000
01:02:36,160 --> 01:02:43,380
对 也有一些勾 也有一些自己的勾子吧

2001
01:02:43,380 --> 01:02:44,140
Nadarhooks

2002
01:02:44,140 --> 01:02:47,400
等于是一堆勾的是不是 一堆勾的

2003
01:02:47,400 --> 01:02:49,400
你看 也不少啊 你看 它也不少啊

2004
01:02:49,400 --> 01:02:50,780
看 甚至更多 你看

2005
01:02:50,780 --> 01:02:52,100
这么多勾的是不是

2006
01:02:52,100 --> 01:02:54,300
各个勾的 咱们几个重要的讲啊

2007
01:02:54,300 --> 01:02:56,240
咱们就一个几点几个重要的讲

2008
01:02:56,240 --> 01:02:57,900
就这么勾的

2009
01:02:57,900 --> 01:03:00,240
好 把它导出吧

2010
01:03:00,240 --> 01:03:01,480
把我们的completion导出

2011
01:03:01,480 --> 01:03:03,440
completion导出

2012
01:03:03,440 --> 01:03:06,400
把它的格式化一下

2013
01:03:06,400 --> 01:03:09,140
好

2014
01:03:09,140 --> 01:03:09,620
导入它

2015
01:03:09,620 --> 01:03:13,100
module.adpods

2016
01:03:13,100 --> 01:03:14,560
等于什么completion

2017
01:03:14,560 --> 01:03:15,440
是不是导入它

2018
01:03:15,440 --> 01:03:17,400
好

2019
01:03:17,400 --> 01:03:17,940
回来啊

2020
01:03:17,940 --> 01:03:18,140
看

2021
01:03:18,140 --> 01:03:19,440
看我们代码

2022
01:03:19,440 --> 01:03:20,300
我刚才我看

2023
01:03:20,300 --> 01:03:21,200
我是不是在这

2024
01:03:21,200 --> 01:03:23,400
你看

2025
01:03:23,400 --> 01:03:24,720
new了一个completion

2026
01:03:24,720 --> 01:03:25,480
对不对

2027
01:03:25,480 --> 01:03:27,300
newcompletion吧

2028
01:03:27,300 --> 01:03:28,500
它怎么多少点

2029
01:03:28,500 --> 01:03:29,380
你看我可以这样写

2030
01:03:29,380 --> 01:03:30,240
let什么

2031
01:03:30,240 --> 01:03:31,580
completion

2032
01:03:31,580 --> 01:03:32,800
等于newcompletion

2033
01:03:32,800 --> 01:03:33,240
对吧

2034
01:03:33,240 --> 01:03:34,800
要简单啊

2035
01:03:34,800 --> 01:03:36,060
对吧

2036
01:03:36,060 --> 01:03:37,960
把咱们z传进去

2037
01:03:37,960 --> 01:03:38,920
z贼

2038
01:03:38,920 --> 01:03:40,560
z不就是我们的compiler吗

2039
01:03:40,560 --> 01:03:41,100
对不对

2040
01:03:41,100 --> 01:03:41,860
他把什么

2041
01:03:41,860 --> 01:03:43,020
把compiler传给了

2042
01:03:43,020 --> 01:03:43,900
传给了他是不是

2043
01:03:43,900 --> 01:03:44,880
传到这来了

2044
01:03:44,880 --> 01:03:45,180
是吧

2045
01:03:45,180 --> 01:03:45,720
compiler

2046
01:03:45,720 --> 01:03:48,020
对吧

2047
01:03:48,020 --> 01:03:49,260
compiler传给他了

2048
01:03:49,260 --> 01:03:51,160
把compiler传给了compilation

2049
01:03:51,160 --> 01:03:52,060
为什么呀

2050
01:03:52,060 --> 01:03:52,800
有用啊

2051
01:03:52,800 --> 01:03:53,200
会不会有用

2052
01:03:53,200 --> 01:03:54,580
我们先保存一下了吧

2053
01:03:54,580 --> 01:03:55,780
另外

2054
01:03:55,780 --> 01:03:57,740
把option的直接拿过来

2055
01:03:57,740 --> 01:03:57,920
是不是

2056
01:03:57,920 --> 01:04:00,600
也拿过来

2057
01:04:00,600 --> 01:04:02,260
这个option什么意思

2058
01:04:02,260 --> 01:04:02,920
是不是就是那个

2059
01:04:02,920 --> 01:04:05,120
WiFi配置文件那个options啊

2060
01:04:05,120 --> 01:04:05,880
对不对

2061
01:04:05,880 --> 01:04:07,920
也传给我们的complation是吧

2062
01:04:07,920 --> 01:04:09,560
可能后面有用到啊用到

2063
01:04:09,560 --> 01:04:11,580
好这样的话

2064
01:04:11,580 --> 01:04:13,580
你看我们这个实例就讲好了吧

2065
01:04:13,580 --> 01:04:14,660
出发时间吧

2066
01:04:14,660 --> 01:04:15,380
什么时间

2067
01:04:15,380 --> 01:04:17,220
beforecomulation和comulation是不是

2068
01:04:17,220 --> 01:04:18,120
出发一下

2069
01:04:18,120 --> 01:04:19,220
z叫什么hook

2070
01:04:19,220 --> 01:04:20,760
4dl

2071
01:04:20,760 --> 01:04:21,980
谁啊

2072
01:04:21,980 --> 01:04:23,660
z叫comulation是不是

2073
01:04:23,660 --> 01:04:25,520
开启本次的电影是吧

2074
01:04:25,520 --> 01:04:26,720
它什么勾的啊

2075
01:04:26,720 --> 01:04:28,080
是不是sickhook啊

2076
01:04:28,080 --> 01:04:29,060
是同步的啊

2077
01:04:29,060 --> 01:04:29,720
是不是啊

2078
01:04:29,720 --> 01:04:30,260
是同步的

2079
01:04:30,260 --> 01:04:31,380
同步好办啊

2080
01:04:31,380 --> 01:04:32,320
同步就是点什么

2081
01:04:32,320 --> 01:04:35,780
然后参数啥 参数不就是这个

2082
01:04:35,780 --> 01:04:42,940
是吧 没什么参数 是吧 也不对 没报考虑了这个是吧 有考虑和拍什么是吧

2083
01:04:42,940 --> 01:04:48,300
就考虑没靠什么呀 考虑传过来就可以了 是不是啊

2084
01:04:48,300 --> 01:04:55,140
还有什么时间 考虑吗 对吧 好 这是点什么hooks 点

2085
01:04:55,140 --> 01:04:58,340
completion 点什么点靠

2086
01:05:00,640 --> 01:05:01,880
这地方有个参数啊

2087
01:05:01,880 --> 01:05:03,420
我们可以传三啊

2088
01:05:03,420 --> 01:05:05,360
这样的话可以跟他一模一样是吧

2089
01:05:05,360 --> 01:05:06,160
就传过来就可以了

2090
01:05:06,160 --> 01:05:10,240
先出发什么

2091
01:05:10,240 --> 01:05:10,960
Z-Combination

2092
01:05:10,960 --> 01:05:11,980
开出新的电影

2093
01:05:11,980 --> 01:05:12,640
对吧

2094
01:05:12,640 --> 01:05:14,980
你看这个原码里面优势的两行吗

2095
01:05:14,980 --> 01:05:15,280
你看

2096
01:05:15,280 --> 01:05:19,360
这个很多啊

2097
01:05:19,360 --> 01:05:20,320
我这个也没有多少

2098
01:05:20,320 --> 01:05:20,800
然后过吧

2099
01:05:20,800 --> 01:05:21,060
你看

2100
01:05:21,060 --> 01:05:25,200
有entrade是吧

2101
01:05:25,200 --> 01:05:26,380
一大堆

2102
01:05:26,380 --> 01:05:26,620
哎呀

2103
01:05:26,620 --> 01:05:27,920
这个太多了啊

2104
01:05:27,920 --> 01:05:28,760
还好

2105
01:05:28,760 --> 01:05:29,260
然后过完

2106
01:05:30,640 --> 01:05:35,980
好走啊

2107
01:05:35,980 --> 01:05:36,180
你看

2108
01:05:36,180 --> 01:05:37,860
返回了个这个实例吧

2109
01:05:37,860 --> 01:05:38,080
你看

2110
01:05:38,080 --> 01:05:40,520
然后你看

2111
01:05:40,520 --> 01:05:41,980
先出发什么呀

2112
01:05:41,980 --> 01:05:43,880
Z这completion是不是啊

2113
01:05:43,880 --> 01:05:44,720
然后出发什么呀

2114
01:05:44,720 --> 01:05:46,060
completion是不是啊

2115
01:05:46,060 --> 01:05:46,300
走

2116
01:05:46,300 --> 01:05:47,420
最后怎么样

2117
01:05:47,420 --> 01:05:47,960
返回可不了

2118
01:05:47,960 --> 01:05:48,660
返回了吧

2119
01:05:48,660 --> 01:05:49,520
有完事

2120
01:05:49,520 --> 01:05:51,080
完事了啊

2121
01:05:51,080 --> 01:05:52,200
那该干嘛

2122
01:05:52,200 --> 01:05:52,800
什么 make了

2123
01:05:52,800 --> 01:05:53,820
对吧

2124
01:05:53,820 --> 01:05:54,580
该进入make了

2125
01:05:54,580 --> 01:05:55,300
你看啊

2126
01:05:55,300 --> 01:05:56,180
make什么勾的呀

2127
01:05:56,180 --> 01:05:56,980
看一下什么勾的

2128
01:05:56,980 --> 01:05:59,120
make是一个什么

2129
01:05:59,120 --> 01:05:59,560
是一个

2130
01:05:59,560 --> 01:06:02,820
这个async parallel是一个什么

2131
01:06:02,820 --> 01:06:04,580
一步的并发勾子

2132
01:06:04,580 --> 01:06:06,400
所以应该怎么出发

2133
01:06:06,400 --> 01:06:07,820
是不是要是pipthink

2134
01:06:07,820 --> 01:06:08,480
对吧

2135
01:06:08,480 --> 01:06:10,980
好我们来出发这个勾子

2136
01:06:10,980 --> 01:06:12,780
好回到代码里面来

2137
01:06:12,780 --> 01:06:15,600
我这写了是吧

2138
01:06:15,600 --> 01:06:16,520
看什么写了

2139
01:06:16,520 --> 01:06:18,640
那张户有点make call sync

2140
01:06:18,640 --> 01:06:19,120
是吧

2141
01:06:19,120 --> 01:06:22,040
我一旦出发这个make实践了

2142
01:06:22,040 --> 01:06:23,380
那么因为谁会执行

2143
01:06:23,380 --> 01:06:25,400
刚才我们写了一个插件

2144
01:06:25,400 --> 01:06:26,760
谁

2145
01:06:26,760 --> 01:06:28,760
think andreplugin

2146
01:06:28,760 --> 01:06:30,420
它里边是不是鉴定了什么

2147
01:06:30,420 --> 01:06:31,200
这个

2148
01:06:31,200 --> 01:06:35,020
compiler的make实践

2149
01:06:35,020 --> 01:06:36,560
是不是鉴定到了

2150
01:06:36,560 --> 01:06:38,440
你看我这什么出发了

2151
01:06:38,440 --> 01:06:39,920
是不是就靠了出发了

2152
01:06:39,920 --> 01:06:40,760
对不对

2153
01:06:40,760 --> 01:06:41,500
靠出发了

2154
01:06:41,500 --> 01:06:43,260
那么它会让谁执行

2155
01:06:43,260 --> 01:06:44,440
让它执行

2156
01:06:44,440 --> 01:06:45,180
会进到这来

2157
01:06:45,180 --> 01:06:46,920
是不是进到这来

2158
01:06:46,920 --> 01:06:49,020
将这会走到哪

2159
01:06:49,020 --> 01:06:49,520
是不是走到了

2160
01:06:49,520 --> 01:06:51,460
completion的add entry里边去了

2161
01:06:51,460 --> 01:06:54,660
那么completion entry方法在哪

2162
01:06:54,660 --> 01:06:57,340
是不是completion的add entry

2163
01:06:57,340 --> 01:06:57,920
没写呢

2164
01:06:57,920 --> 01:06:59,460
是不是写下呀

2165
01:06:59,460 --> 01:07:00,920
是不是从这开始

2166
01:07:00,920 --> 01:07:01,860
开始编译了

2167
01:07:01,860 --> 01:07:02,540
对不对

2168
01:07:02,540 --> 01:07:04,460
所以要开始从这开始什么

2169
01:07:04,460 --> 01:07:06,080
新的一个编译过程啊

2170
01:07:06,080 --> 01:07:06,700
看这个过程

2171
01:07:06,700 --> 01:07:09,040
好我们来写啊

2172
01:07:09,040 --> 01:07:09,860
来写这个

2173
01:07:09,860 --> 01:07:11,160
这个类是吧

2174
01:07:11,160 --> 01:07:13,680
你看我刚才该引了引了啊

2175
01:07:13,680 --> 01:07:14,460
一个pass吧

2176
01:07:14,460 --> 01:07:15,920
后面可能用到是吧

2177
01:07:15,920 --> 01:07:18,320
这个文件非常重要

2178
01:07:18,320 --> 01:07:22,800
好拿到compiler是吧

2179
01:07:22,800 --> 01:07:23,620
我们的选项

2180
01:07:23,620 --> 01:07:24,920
上下文呢

2181
01:07:24,920 --> 01:07:26,280
也有类子要context

2182
01:07:26,280 --> 01:07:27,200
等于什么

2183
01:07:27,200 --> 01:07:30,780
等于这个compiler.contest

2184
01:07:30,780 --> 01:07:34,120
你拿过来是不是 不可以有用 是吧

2185
01:07:34,120 --> 01:07:37,180
另外 z 叫什么 input file system

2186
01:07:37,180 --> 01:07:43,080
读文件那个那个文件系统也是取自什么呀

2187
01:07:43,080 --> 01:07:46,140
compiler的 input file system

2188
01:07:46,140 --> 01:07:50,500
依此类推这个输出的也一样 是吧

2189
01:07:50,500 --> 01:07:52,800
output一样

2190
01:07:52,800 --> 01:07:56,900
这个圆满里都能看到啊

2191
01:07:57,200 --> 01:07:58,040
原本里都能看到

2192
01:07:58,040 --> 01:07:59,600
好 户子呢

2193
01:07:59,600 --> 01:08:03,100
我们现在是先直写一个叫什么叫 add entry

2194
01:08:03,100 --> 01:08:05,440
然后你有什么

2195
01:08:05,440 --> 01:08:06,600
你有一个sinkhook

2196
01:08:06,600 --> 01:08:08,340
里边参数呢

2197
01:08:08,340 --> 01:08:11,160
给什么给一个entry和什么和name

2198
01:08:11,160 --> 01:08:16,300
和name对吧

2199
01:08:16,300 --> 01:08:17,740
因为非常是勾的

2200
01:08:17,740 --> 01:08:18,300
你看这是不是

2201
01:08:18,300 --> 01:08:20,860
你看这是不是让这个make时间出发

2202
01:08:20,860 --> 01:08:23,840
这是那个compared的是不是

2203
01:08:23,840 --> 01:08:25,900
他有个entread的勾的

2204
01:08:25,900 --> 01:08:27,700
是add Entry的勾的是吧

2205
01:08:27,700 --> 01:08:29,120
然后最后呀

2206
01:08:29,120 --> 01:08:30,480
生命一个数组

2207
01:08:30,480 --> 01:08:31,560
叫什么entries

2208
01:08:31,560 --> 01:08:34,180
entries还记得什么意思吗

2209
01:08:34,180 --> 01:08:35,980
代表什么呀

2210
01:08:35,980 --> 01:08:38,140
代表我们的入口

2211
01:08:38,140 --> 01:08:40,020
代表我们的入口

2212
01:08:40,020 --> 01:08:43,400
里边放的什么呀

2213
01:08:43,400 --> 01:08:44,680
所有的入口模块

2214
01:08:44,680 --> 01:08:47,460
对吧

2215
01:08:47,460 --> 01:08:47,920
入口模块

2216
01:08:47,920 --> 01:08:49,460
对吧

2217
01:08:49,460 --> 01:08:50,420
好

2218
01:08:50,420 --> 01:08:52,060
那么下面该怎么办了

2219
01:08:52,060 --> 01:08:52,460
你看啊

2220
01:08:52,460 --> 01:08:53,460
看原码里怎么干呢

2221
01:08:53,460 --> 01:08:54,900
你看原码里边这个类建好之后呢

2222
01:08:54,900 --> 01:08:55,780
该掉到那个方法了

2223
01:08:55,780 --> 01:09:25,780
调

2224
01:09:25,780 --> 01:09:30,160
哎你看进来了吧好掉我们的爱的安水就是进来了

2225
01:09:30,160 --> 01:09:32,760
对不对什么进来了

2226
01:09:32,760 --> 01:09:35,260
现在第一步是不是就跑通了

2227
01:09:35,260 --> 01:09:38,000
你看啊在我们的这个sing 关系里边

2228
01:09:38,000 --> 01:09:39,340
我鉴定了什么呀

2229
01:09:39,340 --> 01:09:41,660
make 时间在那个里边我怎么样

2230
01:09:41,660 --> 01:09:43,860
我们掉了complete 爱的这个方法

2231
01:09:43,860 --> 01:09:45,640
就是什么就是开始新真的

2232
01:09:45,640 --> 01:09:48,340
你看开始一次新的编译

2233
01:09:48,340 --> 01:09:53,000
开始从入口文件进行什么

2234
01:09:53,000 --> 01:09:54,700
进行必规编译

2235
01:09:54,700 --> 01:09:56,100
明白了吧

2236
01:09:56,100 --> 01:09:56,900
啊

2237
01:09:56,900 --> 01:09:57,980
就这个就是编译过程啊

2238
01:09:57,980 --> 01:09:58,740
就这对吧

2239
01:09:58,740 --> 01:09:59,540
然后呢

2240
01:09:59,540 --> 01:10:00,060
completion

2241
01:10:00,060 --> 01:10:01,440
你看

2242
01:10:01,440 --> 01:10:03,160
然后你看在compiler里边

2243
01:10:03,160 --> 01:10:03,740
我是不是你看

2244
01:10:03,740 --> 01:10:04,940
是不是先掉了run

2245
01:10:04,940 --> 01:10:06,760
run里边掉了这个什么呀

2246
01:10:06,760 --> 01:10:07,300
compiler

2247
01:10:07,300 --> 01:10:08,840
compiler里边掉了吗

2248
01:10:08,840 --> 01:10:10,580
是不是触发了这个make时间了

2249
01:10:10,580 --> 01:10:11,720
触发之后

2250
01:10:11,720 --> 01:10:13,120
视频相对就进到这来了

2251
01:10:13,120 --> 01:10:14,680
就是走这个方法

2252
01:10:14,680 --> 01:10:16,600
然后走到compiler里边去了

2253
01:10:16,600 --> 01:10:17,400
对不对

2254
01:10:17,400 --> 01:10:17,920
进到这来了

2255
01:10:17,920 --> 01:10:19,000
开始编译了是不是

2256
01:10:19,000 --> 01:10:20,700
好我们看一下这个流程啊

2257
01:10:20,700 --> 01:10:21,240
把这个

2258
01:10:21,240 --> 01:10:22,760
试一下能跑动

2259
01:10:22,760 --> 01:10:27,040
可能也没定义是吧

2260
01:10:27,040 --> 01:10:29,240
这个compiler里面要引一下这个模块是吧

2261
01:10:29,240 --> 01:10:29,960
引一下这个模块

2262
01:10:29,960 --> 01:10:36,660
好等于什么require

2263
01:10:36,660 --> 01:10:39,340
我们这个叫.com completion是吧

2264
01:10:39,340 --> 01:10:42,280
拿过来

2265
01:10:42,280 --> 01:10:43,320
这行

2266
01:10:43,320 --> 01:10:47,140
add entry没有定义是吧

2267
01:10:47,140 --> 01:10:48,180
那说明什么

2268
01:10:48,180 --> 01:10:49,720
说明compiler也没拿到是不是

2269
01:10:49,720 --> 01:10:51,080
好think plugin

2270
01:10:51,080 --> 01:10:54,640
你看 说明我这个地方呀 没拿到这个变量 是不是

2271
01:10:54,640 --> 01:10:59,840
为啥没拿到 可能没传单吧 为啥没传单呢 你看啊 看我这怎么怎么传的

2272
01:10:59,840 --> 01:11:02,120
你看我这

2273
01:11:02,120 --> 01:11:08,440
好 在这后的 make call sync 我把 company 传过来 是不是

2274
01:11:08,440 --> 01:11:14,440
我他这没有返回啊 是不是 委呈回去了吧 把 company 委呈回去

2275
01:11:16,240 --> 01:11:17,780
你看我这要是不是要return啊

2276
01:11:17,780 --> 01:11:20,080
才能给他呀才能传给这个make啊

2277
01:11:20,080 --> 01:11:22,180
make呢才能传给我们这个什么

2278
01:11:22,180 --> 01:11:24,980
这个这的这个坚定函数啊对不对

2279
01:11:24,980 --> 01:11:25,980
要这样传啊

2280
01:11:25,980 --> 01:11:33,900
给你看是不是atm进来了就跑通了都疯了啊

2281
01:11:33,900 --> 01:11:37,640
呃注意啊因为这个代码呀这个一次万会写不完

2282
01:11:37,640 --> 01:11:40,740
所以说我们就休息休息个8分钟吧好吧

2283
01:11:40,740 --> 01:11:42,440
啊回忆还得有多少时啊

2284
01:11:42,440 --> 01:11:43,380
回忆大家这个

2285
01:11:43,380 --> 01:11:45,500
先休息5分钟休息8分钟吧

2286
01:11:46,000 --> 01:11:48,940
啊把它调下来看看休息8分钟啊

2287
01:11:48,940 --> 01:11:51,100
喝点水休息

