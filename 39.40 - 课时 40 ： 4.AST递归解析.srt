1
00:00:00,000 --> 00:00:04,180
这个pass方法主要靠的是我们的st来解析我们的语法术

2
00:00:04,180 --> 00:00:05,500
我要进行圆满的转译

3
00:00:05,500 --> 00:00:08,240
这里我们就可以用babel那一系列的东西

4
00:00:08,240 --> 00:00:09,820
比如说我们的babel

5
00:00:09,820 --> 00:00:10,360
对吧

6
00:00:10,360 --> 00:00:10,780
babel loan

7
00:00:10,780 --> 00:00:12,660
还有我们的babel transfers

8
00:00:12,660 --> 00:00:14,000
加上我们的babel

9
00:00:14,000 --> 00:00:14,420
对吧

10
00:00:14,420 --> 00:00:16,980
这里面我写一下有三个模块

11
00:00:16,980 --> 00:00:18,200
这里面我们学用的

12
00:00:18,200 --> 00:00:19,460
第一个叫babel loan

13
00:00:19,460 --> 00:00:21,920
它主要干的事对吧

14
00:00:21,920 --> 00:00:25,040
主要就是把我们这样一个圆满

15
00:00:25,040 --> 00:00:27,380
圆满转换成对吧

16
00:00:27,380 --> 00:00:28,180
st

17
00:00:28,180 --> 00:00:29,640
同样的

18
00:00:29,640 --> 00:00:30,340
还有一个东西叫什么

19
00:00:30,340 --> 00:00:30,820
叫便利

20
00:00:30,820 --> 00:00:32,560
我们需要便利到对应的节点

21
00:00:32,560 --> 00:00:33,760
这时候我们需要的

22
00:00:33,760 --> 00:00:35,660
可能就是babel-traverse

23
00:00:35,660 --> 00:00:39,240
便利完节点以后

24
00:00:39,240 --> 00:00:40,340
其实我们还需要干嘛

25
00:00:40,340 --> 00:00:41,840
是不是把我们当前

26
00:00:41,840 --> 00:00:43,280
这样一个便利好的节点

27
00:00:43,280 --> 00:00:43,940
替换一下

28
00:00:43,940 --> 00:00:44,840
替换的话

29
00:00:44,840 --> 00:00:46,780
肯定需要babel-types

30
00:00:46,780 --> 00:00:48,000
当然名字现在都叫什么

31
00:00:48,000 --> 00:00:50,320
都叫atbabel-traverse

32
00:00:50,320 --> 00:00:52,760
还有atbabel-types

33
00:00:52,760 --> 00:00:54,060
最后我们希望

34
00:00:54,060 --> 00:00:55,240
把替换好的结果

35
00:00:55,240 --> 00:00:55,900
怎么再生成

36
00:00:55,900 --> 00:00:58,160
肯定需要babel-traverse

37
00:00:58,180 --> 00:00:59,100
我们的generator

38
00:00:59,100 --> 00:01:01,260
这三个模块

39
00:01:01,260 --> 00:01:01,980
那好了

40
00:01:01,980 --> 00:01:03,560
我们把这三个模块安装一下

41
00:01:03,560 --> 00:01:04,180
这里

42
00:01:04,180 --> 00:01:05,740
也一样

43
00:01:05,740 --> 00:01:07,180
应该需要安装到这里来

44
00:01:07,180 --> 00:01:07,360
是吧

45
00:01:07,360 --> 00:01:08,460
这是我们自己写的包

46
00:01:08,460 --> 00:01:09,480
那我们可以在这

47
00:01:09,480 --> 00:01:10,320
压压的

48
00:01:10,320 --> 00:01:12,860
装我们这样一个叫

49
00:01:12,860 --> 00:01:13,580
babel

50
00:01:13,580 --> 00:01:15,300
叫babellone

51
00:01:15,300 --> 00:01:15,980
是吧

52
00:01:15,980 --> 00:01:17,260
还有我们的babel

53
00:01:17,260 --> 00:01:17,900
go

54
00:01:17,900 --> 00:01:19,900
atbabel

55
00:01:19,900 --> 00:01:21,760
go

56
00:01:21,760 --> 00:01:22,280
trivers

57
00:01:22,280 --> 00:01:25,200
还有我们的atbabel

58
00:01:25,200 --> 00:01:26,300
但是名字是go

59
00:01:26,300 --> 00:01:28,720
同样这个叫

60
00:01:28,720 --> 00:01:29,480
atbabel

61
00:01:29,480 --> 00:01:30,400
generator

62
00:01:30,400 --> 00:01:33,820
还有我们的atbabel

63
00:01:33,820 --> 00:01:34,460
taps

64
00:01:34,460 --> 00:01:38,600
ok了

65
00:01:38,600 --> 00:01:40,140
回车

66
00:01:40,140 --> 00:01:42,260
这样的话

67
00:01:42,260 --> 00:01:43,980
我们就可以直接怎么样去用了

68
00:01:43,980 --> 00:01:45,620
我们第一把它引进来

69
00:01:45,620 --> 00:01:46,680
第一个叫babel

70
00:01:46,680 --> 00:01:48,560
babel

71
00:01:48,560 --> 00:01:49,840
完了

72
00:01:49,840 --> 00:01:50,800
等于request

73
00:01:50,800 --> 00:01:52,740
它里面有个方法叫pass

74
00:01:52,740 --> 00:01:53,860
可以去解析

75
00:01:53,860 --> 00:01:55,460
这里来个字不串

76
00:01:55,460 --> 00:01:58,800
完了还需要我们这样一个叫Trivers

77
00:01:58,800 --> 00:02:00,880
Trivers

78
00:02:00,880 --> 00:02:03,940
等于Require这样一个东西叫babel

79
00:02:03,940 --> 00:02:04,940
At babel

80
00:02:04,940 --> 00:02:06,020
有了

81
00:02:06,020 --> 00:02:08,240
完了还有呢我们需要这样一个T

82
00:02:08,240 --> 00:02:09,800
等于我们的Require

83
00:02:09,800 --> 00:02:11,500
但这个Trivers要注意一下

84
00:02:11,500 --> 00:02:12,700
它是个ES用模块

85
00:02:12,700 --> 00:02:14,700
我们引用的时候要去再去点Difort

86
00:02:14,700 --> 00:02:16,440
完了这里呢我们再去Require

87
00:02:16,440 --> 00:02:18,800
完了引用的时候呢这个叫

88
00:02:18,800 --> 00:02:20,480
At babel-Tabs

89
00:02:20,480 --> 00:02:24,600
同样呢我们还需要再往下走是吧

90
00:02:24,600 --> 00:02:25,600
hug叫generator

91
00:02:25,600 --> 00:02:28,700
let generator等于require

92
00:02:28,700 --> 00:02:32,980
比如说叫atbabel-generator

93
00:02:32,980 --> 00:02:33,960
OK

94
00:02:33,960 --> 00:02:35,340
完了这里面也一样

95
00:02:35,340 --> 00:02:36,460
它也是个ESU模块

96
00:02:36,460 --> 00:02:37,540
所以也可以去default

97
00:02:37,540 --> 00:02:39,200
你要不default的话

98
00:02:39,200 --> 00:02:40,460
拿到的就不是这样一个东西了

99
00:02:40,460 --> 00:02:41,560
而是默认导出的是个对象

100
00:02:41,560 --> 00:02:42,700
完了

101
00:02:42,700 --> 00:02:44,160
现在有了这样一个东西以后

102
00:02:44,160 --> 00:02:45,580
我们就开始来解析了

103
00:02:45,580 --> 00:02:47,420
那我下找找找找找找

104
00:02:47,420 --> 00:02:47,760
找到这

105
00:02:47,760 --> 00:02:50,140
完了里面我们要进行原码解析

106
00:02:50,140 --> 00:02:51,800
那肯定需要这个babel loan

107
00:02:51,800 --> 00:02:53,880
第二什么的叫pass解析

108
00:02:53,880 --> 00:02:55,660
把我们这样一个圆码

109
00:02:55,660 --> 00:02:57,420
解析成我们所谓的ST

110
00:02:57,420 --> 00:02:59,100
完了这个ST

111
00:02:59,100 --> 00:03:00,260
我们就需要干嘛呢

112
00:03:00,260 --> 00:03:01,220
是不是需要去便利

113
00:03:01,220 --> 00:03:02,100
那便利的话

114
00:03:02,100 --> 00:03:03,640
肯定离不开那个叫穿Wars

115
00:03:03,640 --> 00:03:05,020
完了把ST往里放

116
00:03:05,020 --> 00:03:07,020
那这里面我们去看看吧

117
00:03:07,020 --> 00:03:08,320
这个对应的结构啊

118
00:03:08,320 --> 00:03:09,820
ST长的什么样子

119
00:03:09,820 --> 00:03:10,040
好

120
00:03:10,040 --> 00:03:10,580
这里呢

121
00:03:10,580 --> 00:03:11,400
我们直接引用一下

122
00:03:11,400 --> 00:03:12,540
在这里呢

123
00:03:12,540 --> 00:03:14,840
3w.dst

124
00:03:14,840 --> 00:03:16,660
叫eSplore

125
00:03:16,660 --> 00:03:16,940
对吧

126
00:03:16,940 --> 00:03:18,740
pror.net

127
00:03:18,740 --> 00:03:20,320
这个网址是吧

128
00:03:20,320 --> 00:03:21,620
哦不对是吧

129
00:03:21,620 --> 00:03:22,460
看看哪写错了

130
00:03:22,460 --> 00:03:23,440
不是看百度吧

131
00:03:23,440 --> 00:03:26,500
搜一下这个博博是吧

132
00:03:26,500 --> 00:03:27,960
这样一个路径就在这

133
00:03:27,960 --> 00:03:29,320
这是它的官网

134
00:03:29,320 --> 00:03:30,940
我那里面呢

135
00:03:30,940 --> 00:03:32,900
我在这里面写好了一个叫require.a

136
00:03:32,900 --> 00:03:35,160
这个意思就是相当于我们要知道什么呢

137
00:03:35,160 --> 00:03:36,700
是不是要匹配到require方法

138
00:03:36,700 --> 00:03:37,580
把路径改掉

139
00:03:37,580 --> 00:03:38,660
把这名字改掉

140
00:03:38,660 --> 00:03:40,320
那我们要匹配什么呢

141
00:03:40,320 --> 00:03:42,120
是不是就要匹配这个call expression

142
00:03:42,120 --> 00:03:43,400
这就是我们的IT

143
00:03:43,400 --> 00:03:44,560
咱都说过了

144
00:03:44,560 --> 00:03:45,140
那这里呢

145
00:03:45,140 --> 00:03:47,140
我可以直接放上这样一个方法

146
00:03:47,140 --> 00:03:49,240
这个什么意思呢

147
00:03:49,240 --> 00:03:50,380
叫调用表达式

148
00:03:50,380 --> 00:03:51,640
比如什么叫调用表达式

149
00:03:51,640 --> 00:03:53,220
就是直接比如写A执行

150
00:03:53,220 --> 00:03:53,960
对吧

151
00:03:53,960 --> 00:03:54,620
或者什么呢

152
00:03:54,620 --> 00:03:55,780
或者这个require执行

153
00:03:55,780 --> 00:03:57,200
它都叫对用表达制

154
00:03:57,200 --> 00:03:58,720
那我们要看一看

155
00:03:58,720 --> 00:04:00,020
是不是只有require的时候

156
00:04:00,020 --> 00:04:00,920
我才需要管

157
00:04:00,920 --> 00:04:02,700
所以这里面我要判断

158
00:04:02,700 --> 00:04:03,920
require怎么看呢

159
00:04:03,920 --> 00:04:06,220
你看这里面是不是有一个叫call1

160
00:04:06,220 --> 00:04:07,680
有个点name吧

161
00:04:07,680 --> 00:04:09,260
如果这个name是require

162
00:04:09,260 --> 00:04:10,340
那我说明怎么样

163
00:04:10,340 --> 00:04:11,420
是不是就找到它了

164
00:04:11,420 --> 00:04:11,960
那好了

165
00:04:11,960 --> 00:04:13,120
那这里面呢

166
00:04:13,120 --> 00:04:14,760
我们需要这样一个路径叫p

167
00:04:14,760 --> 00:04:17,820
我们需要拿到当前这样一个节点

168
00:04:17,820 --> 00:04:19,060
那节点的话呢

169
00:04:19,060 --> 00:04:20,620
是p点什么node

170
00:04:20,620 --> 00:04:21,900
OK

171
00:04:21,900 --> 00:04:23,500
你可以认为拿到什么呢

172
00:04:23,500 --> 00:04:24,160
对应的节点

173
00:04:24,160 --> 00:04:26,060
对应的节点

174
00:04:26,060 --> 00:04:27,880
那我们可以怎么样呢

175
00:04:27,880 --> 00:04:29,360
说通过这个节点

176
00:04:29,360 --> 00:04:30,680
往来去点call1

177
00:04:30,680 --> 00:04:32,040
刚才看到了

178
00:04:32,040 --> 00:04:33,860
它里面有个name属性

179
00:04:33,860 --> 00:04:34,640
在这里是吧

180
00:04:34,640 --> 00:04:35,660
叫call1

181
00:04:35,660 --> 00:04:36,960
往来点name

182
00:04:36,960 --> 00:04:37,540
说在这

183
00:04:37,540 --> 00:04:38,340
那好了

184
00:04:38,340 --> 00:04:39,780
如果它要是requare的话

185
00:04:39,780 --> 00:04:41,420
那说明我们确实怎么样

186
00:04:41,420 --> 00:04:43,180
找到了这样一个requare方法

187
00:04:43,180 --> 00:04:44,980
那如果是requare的话

188
00:04:44,980 --> 00:04:46,720
我说了是不是要把这名字进行改造

189
00:04:46,720 --> 00:04:48,260
那改造成什么呢

190
00:04:48,260 --> 00:04:49,400
我们来看一眼

191
00:04:49,400 --> 00:04:51,080
那改造的话是就这么长

192
00:04:51,080 --> 00:04:53,080
叫webpack require这个方法

193
00:04:53,080 --> 00:04:54,240
挺长的

194
00:04:54,240 --> 00:04:56,280
完了还需要干嘛呢

195
00:04:56,280 --> 00:04:57,980
是不是还需要把我们当前

196
00:04:57,980 --> 00:04:59,160
那个引用的路径也改了

197
00:04:59,160 --> 00:05:02,040
那我们是不是需要一个东西叫moduleid

198
00:05:02,040 --> 00:05:03,880
那这个moduleid呢

199
00:05:03,880 --> 00:05:05,680
我希望稍微稍微改一下吧

200
00:05:05,680 --> 00:05:06,960
或者叫modulename都行啊

201
00:05:06,960 --> 00:05:07,400
modulename

202
00:05:07,400 --> 00:05:09,540
这里面呢我们希望怎么样

203
00:05:09,540 --> 00:05:11,660
是不是拿到他这里有的参数

204
00:05:11,660 --> 00:05:12,980
叫arguments

205
00:05:12,980 --> 00:05:16,260
是不是有这样一个叫

206
00:05:16,260 --> 00:05:17,180
delvalue是a

207
00:05:17,180 --> 00:05:18,340
而且它是个数组

208
00:05:18,340 --> 00:05:19,600
所以说我们拿的时候

209
00:05:19,600 --> 00:05:20,440
应该怎么去拿呢

210
00:05:20,440 --> 00:05:22,080
是不是叫Value

211
00:05:22,080 --> 00:05:22,860
不叫Value

212
00:05:22,860 --> 00:05:23,860
应该叫Node

213
00:05:23,860 --> 00:05:24,760
第2什么呢

214
00:05:24,760 --> 00:05:25,700
是不是一层层找

215
00:05:25,700 --> 00:05:26,520
根据关系

216
00:05:26,520 --> 00:05:27,580
是不是第2Arguments

217
00:05:27,580 --> 00:05:29,840
完了再去找我们这样一个Value

218
00:05:29,840 --> 00:05:30,120
是吧

219
00:05:30,120 --> 00:05:31,420
那我可以在这里呢

220
00:05:31,420 --> 00:05:33,120
直接去取Arguments

221
00:05:33,120 --> 00:05:34,560
完了方过号第0个

222
00:05:34,560 --> 00:05:35,380
第2Value

223
00:05:35,380 --> 00:05:36,980
这里取到的就是什么

224
00:05:36,980 --> 00:05:37,700
取到的

225
00:05:37,700 --> 00:05:39,060
就是对吧

226
00:05:39,060 --> 00:05:41,440
模块的引用名字

227
00:05:41,440 --> 00:05:44,420
那有了这个名字以后啊

228
00:05:44,420 --> 00:05:45,340
我就方便多了

229
00:05:45,340 --> 00:05:46,260
那这个名字啊

230
00:05:46,260 --> 00:05:46,860
但是有个特点

231
00:05:46,860 --> 00:05:48,760
你看我以模块的时候

232
00:05:48,760 --> 00:05:50,140
是不是这里面没有加后准啊

233
00:05:50,140 --> 00:05:51,420
所以它这里面也没后准

234
00:05:51,420 --> 00:05:53,460
那我需要先把这后准怎么样

235
00:05:53,460 --> 00:05:54,580
给它补上是吧

236
00:05:54,580 --> 00:05:55,820
那怎么补这后准呢

237
00:05:55,820 --> 00:05:56,480
也非常方便

238
00:05:56,480 --> 00:05:57,380
那我可以在这

239
00:05:57,380 --> 00:05:59,500
其实都是这个pass模块的功能

240
00:05:59,500 --> 00:06:01,440
等于它加上

241
00:06:01,440 --> 00:06:02,900
比如说我就看看吧

242
00:06:02,900 --> 00:06:05,680
当前这样一个什么叫pass.eht

243
00:06:05,680 --> 00:06:07,120
就看看这个东西

244
00:06:07,120 --> 00:06:08,440
它有没有扩展名

245
00:06:08,440 --> 00:06:09,480
我把它放上去

246
00:06:09,480 --> 00:06:12,280
如果没有的话好

247
00:06:12,280 --> 00:06:12,860
给它个空

248
00:06:12,860 --> 00:06:13,660
对吧

249
00:06:13,660 --> 00:06:14,060
完了

250
00:06:14,060 --> 00:06:15,260
如果有的话给个空是吧

251
00:06:15,260 --> 00:06:16,140
如果没有的话

252
00:06:16,140 --> 00:06:17,320
给个叫.js

253
00:06:17,320 --> 00:06:19,800
完了之后我就可以怎么样了

254
00:06:19,800 --> 00:06:21,140
是不是就可以拿到这样一个名字了

255
00:06:21,140 --> 00:06:23,620
但这个名字是不是相当于拼出来一个什么叫

256
00:06:23,620 --> 00:06:25,500
叫dr-a.js

257
00:06:25,500 --> 00:06:27,980
那我是不是还需要载在前面怎么样

258
00:06:27,980 --> 00:06:29,700
是不是加上这样一个副路径

259
00:06:29,700 --> 00:06:31,520
叫src下的a.js

260
00:06:31,520 --> 00:06:32,380
那好了

261
00:06:32,380 --> 00:06:33,380
那这里呢

262
00:06:33,380 --> 00:06:34,220
我就再去拼

263
00:06:34,220 --> 00:06:34,560
是吧

264
00:06:34,560 --> 00:06:36,180
叫pass点什么呢

265
00:06:36,180 --> 00:06:36,400
转

266
00:06:36,400 --> 00:06:38,280
你看其实主要是路径的处理

267
00:06:38,280 --> 00:06:39,560
那拼的时候呢

268
00:06:39,560 --> 00:06:40,560
是不是副路径

269
00:06:40,560 --> 00:06:43,460
我来加上我们这样一个模块的名字

270
00:06:43,460 --> 00:06:44,360
叫module内

271
00:06:44,360 --> 00:06:47,540
那这回拼出来就是什么

272
00:06:47,540 --> 00:06:48,420
是不是就是一个

273
00:06:48,420 --> 00:06:49,640
还是一个module内

274
00:06:49,640 --> 00:06:51,620
这主要现在变成了一个什么样子

275
00:06:51,620 --> 00:06:52,780
是不是这样子了

276
00:06:52,780 --> 00:06:54,880
叫SRC一下的

277
00:06:54,880 --> 00:06:56,540
叫SRC一下的

278
00:06:56,540 --> 00:06:57,620
比如说A.js

279
00:06:57,620 --> 00:06:59,080
这样拿到以后

280
00:06:59,080 --> 00:07:00,780
我们还是前面还是没有点杠

281
00:07:00,780 --> 00:07:01,700
我好了

282
00:07:01,700 --> 00:07:02,480
我还是补上一个

283
00:07:02,480 --> 00:07:03,340
点杠

284
00:07:03,340 --> 00:07:06,200
现在有了以后

285
00:07:06,200 --> 00:07:07,020
我说了

286
00:07:07,020 --> 00:07:08,580
是不是你匹配到require了

287
00:07:08,580 --> 00:07:09,800
说相当于这个路径

288
00:07:09,800 --> 00:07:10,860
就是我们的依赖

289
00:07:10,860 --> 00:07:13,020
我就需要把它存到一个依赖

290
00:07:13,020 --> 00:07:13,540
变量里

291
00:07:13,540 --> 00:07:14,200
依赖数组里

292
00:07:14,200 --> 00:07:16,300
我就在这来个叫depend

293
00:07:16,300 --> 00:07:16,860
sys

294
00:07:16,860 --> 00:07:18,080
它是个数组

295
00:07:18,080 --> 00:07:20,360
这是一个依赖的数组

296
00:07:20,360 --> 00:07:23,920
那我就可以把这个名字放到依赖里

297
00:07:23,920 --> 00:07:24,980
那非常简单了

298
00:07:24,980 --> 00:07:26,640
叫Depensys点什么呢

299
00:07:26,640 --> 00:07:28,240
点push往里塞

300
00:07:28,240 --> 00:07:29,940
那塞进去以后

301
00:07:29,940 --> 00:07:31,880
你想一个文件是不是可能有很多个依赖

302
00:07:31,880 --> 00:07:32,460
那好

303
00:07:32,460 --> 00:07:34,140
我把贸用name放进去

304
00:07:34,140 --> 00:07:34,440
OK

305
00:07:34,440 --> 00:07:36,340
那放进完以后

306
00:07:36,340 --> 00:07:38,220
我们是不是还要把它这个

307
00:07:38,220 --> 00:07:40,300
默认这个一个st

308
00:07:40,300 --> 00:07:41,500
是不是也转换一下

309
00:07:41,500 --> 00:07:42,360
把这value也改掉

310
00:07:42,360 --> 00:07:42,620
是吧

311
00:07:42,620 --> 00:07:43,500
那怎么改呢

312
00:07:43,500 --> 00:07:44,020
是不是就这样

313
00:07:44,020 --> 00:07:45,480
叫改一下来

314
00:07:45,480 --> 00:07:46,860
叫node.arguments

315
00:07:46,860 --> 00:07:47,420
完了

316
00:07:47,420 --> 00:07:48,400
那他呢应该是个数组

317
00:07:48,400 --> 00:07:49,840
那数组里面呢

318
00:07:49,840 --> 00:07:50,560
它是个什么东西

319
00:07:50,560 --> 00:07:50,980
你看啊

320
00:07:50,980 --> 00:07:52,380
叫string letter是吧

321
00:07:52,380 --> 00:07:52,880
那好

322
00:07:52,880 --> 00:07:53,520
我在这里呢

323
00:07:53,520 --> 00:07:55,300
就可以通过这个T来生成一个

324
00:07:55,300 --> 00:07:55,620
对吧

325
00:07:55,620 --> 00:07:56,960
叫string letter

326
00:07:56,960 --> 00:07:58,380
我里面呢

327
00:07:58,380 --> 00:07:59,720
我就可以直接放上

328
00:07:59,720 --> 00:08:00,940
我们当前这样一个冒险内

329
00:08:00,940 --> 00:08:03,340
是不是相当于把这个原码给改掉了

330
00:08:03,340 --> 00:08:05,120
那原码改完以后啊

331
00:08:05,120 --> 00:08:06,260
我们还需要干嘛呢

332
00:08:06,260 --> 00:08:07,980
是不是重新生成一下

333
00:08:07,980 --> 00:08:08,560
generator

334
00:08:08,560 --> 00:08:09,700
第二什么呢

335
00:08:09,700 --> 00:08:10,860
第二这个叫

336
00:08:10,860 --> 00:08:13,020
执行是吧

337
00:08:13,020 --> 00:08:14,640
完了我把这个代码传给就好了

338
00:08:14,640 --> 00:08:17,040
把我们这样一个转化后的代码

339
00:08:17,040 --> 00:08:18,180
叫Lite

340
00:08:18,180 --> 00:08:19,600
把IT放进来就行了

341
00:08:19,600 --> 00:08:20,360
ST

342
00:08:20,360 --> 00:08:21,460
完了可以怎么样

343
00:08:21,460 --> 00:08:22,380
转发出来以后

344
00:08:22,380 --> 00:08:23,420
直接去.code

345
00:08:23,420 --> 00:08:24,500
完了最后

346
00:08:24,500 --> 00:08:25,760
这肯定就是我们所谓的

347
00:08:25,760 --> 00:08:27,060
这样一个圆码了

348
00:08:27,060 --> 00:08:29,000
那圆码有了

349
00:08:29,000 --> 00:08:30,480
是不是我就可以把这圆码怎么样

350
00:08:30,480 --> 00:08:31,880
返回source code

351
00:08:31,880 --> 00:08:33,520
还有刚才我们所谓的

352
00:08:33,520 --> 00:08:34,540
这个依赖关系

353
00:08:34,540 --> 00:08:35,260
也返回

354
00:08:35,260 --> 00:08:37,180
那现在我们来看看吧

355
00:08:37,180 --> 00:08:38,920
能不能把我们的圆码

356
00:08:38,920 --> 00:08:40,020
还有dependencies

357
00:08:40,020 --> 00:08:40,440
完了

358
00:08:40,440 --> 00:08:41,640
速度拿到

359
00:08:41,640 --> 00:08:43,180
那我们再往下找吧

360
00:08:43,180 --> 00:08:44,740
找到我们这样一个pass方法

361
00:08:44,740 --> 00:08:45,860
pass pass

362
00:08:45,860 --> 00:08:47,460
在哪呢

363
00:08:47,460 --> 00:08:48,320
找一下就在这吧

364
00:08:48,320 --> 00:08:50,840
那我现在是不是应该就名字不太对是吧

365
00:08:50,840 --> 00:08:51,780
Depensis

366
00:08:51,780 --> 00:08:52,860
改个名字

367
00:08:52,860 --> 00:08:54,540
那现在我们来打印一下

368
00:08:54,540 --> 00:08:55,200
看看这有没有

369
00:08:55,200 --> 00:08:56,460
Costlog

370
00:08:56,460 --> 00:08:57,980
Costlog

371
00:08:57,980 --> 00:08:59,560
比如说叫Source Code

372
00:08:59,560 --> 00:09:01,880
还有我们这样一个Depensis

373
00:09:01,880 --> 00:09:03,520
这是所有的依赖

374
00:09:03,520 --> 00:09:04,380
那好了

375
00:09:04,380 --> 00:09:05,900
我再把它运行一下

376
00:09:05,900 --> 00:09:07,200
不是这个文件

377
00:09:07,200 --> 00:09:07,840
这里

378
00:09:07,840 --> 00:09:09,440
跑一下

379
00:09:09,440 --> 00:09:12,020
告诉require又拼错了是吧

380
00:09:12,020 --> 00:09:12,880
把上面改改

381
00:09:12,880 --> 00:09:14,860
require这里面是吧

382
00:09:14,860 --> 00:09:15,380
最后一个

383
00:09:15,380 --> 00:09:16,460
我再来一次

384
00:09:16,460 --> 00:09:19,520
现在好了

385
00:09:19,520 --> 00:09:20,080
告诉我

386
00:09:20,080 --> 00:09:21,560
现在这东西它没定义

387
00:09:21,560 --> 00:09:23,660
我看看是不是没有加双一号

388
00:09:23,660 --> 00:09:25,660
写的时候果然没有加

389
00:09:25,660 --> 00:09:27,080
加个双一号

390
00:09:27,080 --> 00:09:27,760
我再来一次

391
00:09:27,760 --> 00:09:29,060
主要就是干嘛

392
00:09:29,060 --> 00:09:29,960
改名字

393
00:09:29,960 --> 00:09:30,540
我加上改缘

394
00:09:30,540 --> 00:09:33,340
看现在我的名字是不是改掉了

395
00:09:33,340 --> 00:09:35,020
并且我的路径也改掉了

396
00:09:35,020 --> 00:09:35,900
完了之后

397
00:09:35,900 --> 00:09:38,640
我的依赖关系是src下的a.js

398
00:09:38,640 --> 00:09:39,860
说明怎么样

399
00:09:39,860 --> 00:09:42,020
是不是我已经把当前

400
00:09:42,020 --> 00:09:43,340
这样一个模块的依赖怎么样

401
00:09:43,340 --> 00:09:44,080
解析好了

402
00:09:44,080 --> 00:09:45,760
但是现在只是什么

403
00:09:45,760 --> 00:09:47,880
是不是你不能光解析这个A

404
00:09:47,880 --> 00:09:49,740
你现在解析的是什么

405
00:09:49,740 --> 00:09:50,980
是这个文件看一下

406
00:09:50,980 --> 00:09:51,900
是不是index

407
00:09:51,900 --> 00:09:53,300
index里面解析出来是A

408
00:09:53,300 --> 00:09:55,020
但是A里面是不是还有东西

409
00:09:55,020 --> 00:09:56,720
那我是不是要根据这路径怎么样

410
00:09:56,720 --> 00:09:57,840
接着解析吧

411
00:09:57,840 --> 00:10:01,500
那这个过程肯定是一个低规的过程了

412
00:10:01,500 --> 00:10:01,800
那好

413
00:10:01,800 --> 00:10:03,040
那也就是说在这里面

414
00:10:03,040 --> 00:10:04,280
我把这个东西放进好

415
00:10:04,280 --> 00:10:05,120
放进来以后

416
00:10:05,120 --> 00:10:06,300
我还需要干嘛呢

417
00:10:06,300 --> 00:10:07,620
还需要再去干这事

418
00:10:07,620 --> 00:10:07,940
对吧

419
00:10:07,940 --> 00:10:09,120
把所有的依赖项

420
00:10:09,120 --> 00:10:10,480
即行低规

421
00:10:10,480 --> 00:10:11,420
第二什么呢

422
00:10:11,420 --> 00:10:11,820
for each

423
00:10:11,820 --> 00:10:13,080
完了这里面呢

424
00:10:13,080 --> 00:10:14,280
我就可以拿到DP

425
00:10:14,280 --> 00:10:15,560
那同样啊

426
00:10:15,560 --> 00:10:18,500
那我是不是应该再去调这个build module方法

427
00:10:18,500 --> 00:10:20,060
实现递规了

428
00:10:20,060 --> 00:10:20,900
build module

429
00:10:20,900 --> 00:10:22,060
完了里面呢

430
00:10:22,060 --> 00:10:22,780
我把这个DP

431
00:10:22,780 --> 00:10:23,820
当然这里面

432
00:10:23,820 --> 00:10:26,320
第一个参数肯定是一个模块的路径

433
00:10:26,320 --> 00:10:28,160
它应该是个绝对路径

434
00:10:28,160 --> 00:10:29,000
那绝对路径的话

435
00:10:29,000 --> 00:10:30,320
我们就不客气了

436
00:10:30,320 --> 00:10:30,780
拼上吧

437
00:10:30,780 --> 00:10:33,860
叫z4.cld

438
00:10:33,860 --> 00:10:35,860
不叫cld

439
00:10:35,860 --> 00:10:36,560
叫root是吧

440
00:10:36,560 --> 00:10:37,880
完了和谁拼呢

441
00:10:37,880 --> 00:10:38,480
和DP

442
00:10:38,480 --> 00:10:40,460
那这样拼完以后啊

443
00:10:40,460 --> 00:10:42,340
它就是一个完整的绝对路径了

444
00:10:42,340 --> 00:10:43,540
完了并且你要跟人家说

445
00:10:43,540 --> 00:10:45,300
这玩意儿不是一个主模块的

446
00:10:45,300 --> 00:10:46,820
因为是依赖关系加载

447
00:10:46,820 --> 00:10:48,500
那肯定是个副模块

448
00:10:48,500 --> 00:10:49,100
自模块

449
00:10:49,100 --> 00:10:50,320
那好了

450
00:10:50,320 --> 00:10:52,060
那这时候我们就可以怎么样

451
00:10:52,060 --> 00:10:53,400
拿到这样的结果了

452
00:10:53,400 --> 00:10:54,320
写上对吧

453
00:10:54,320 --> 00:10:55,040
这是对吧

454
00:10:55,040 --> 00:10:56,680
就是副模块的对吧

455
00:10:56,680 --> 00:10:57,340
这个副啊

456
00:10:57,340 --> 00:10:58,140
副模块的

457
00:10:58,140 --> 00:10:59,780
副属品对吧

458
00:10:59,780 --> 00:11:00,260
副属

459
00:11:00,260 --> 00:11:02,340
副

460
00:11:02,340 --> 00:11:04,140
副模块的对吧

461
00:11:04,140 --> 00:11:05,700
副属

462
00:11:05,700 --> 00:11:07,520
副模块的

463
00:11:07,520 --> 00:11:08,120
对吧

464
00:11:08,120 --> 00:11:08,480
加载

465
00:11:08,480 --> 00:11:10,040
也是个地规对吧

466
00:11:10,040 --> 00:11:13,440
现在我们就可以怎么样

467
00:11:13,440 --> 00:11:14,360
这样一循环

468
00:11:14,360 --> 00:11:16,540
此时我们这个模块加载完以后

469
00:11:16,540 --> 00:11:18,080
就会把所有的模块怎么样

470
00:11:18,080 --> 00:11:20,260
全都塞到这个this.modules里

471
00:11:20,260 --> 00:11:21,940
好了我网上找找找

472
00:11:21,940 --> 00:11:23,700
找到我们的this.modules

473
00:11:23,700 --> 00:11:25,520
在哪找一下

474
00:11:25,520 --> 00:11:27,500
叫build.module

475
00:11:27,500 --> 00:11:28,120
在这里是吧

476
00:11:28,120 --> 00:11:29,140
我在这打印了

477
00:11:29,140 --> 00:11:29,780
Costlog

478
00:11:29,780 --> 00:11:33,780
我来看看这里的modules是不是个对象

479
00:11:33,780 --> 00:11:36,820
这里的entryID有没有拿到

480
00:11:36,820 --> 00:11:39,280
如果拿到了modules和entryID

481
00:11:39,280 --> 00:11:40,220
那说明怎么样

482
00:11:40,220 --> 00:11:41,200
这事就成了

483
00:11:41,200 --> 00:11:42,060
我在这里呢

484
00:11:42,060 --> 00:11:42,700
把它跑一下

485
00:11:42,700 --> 00:11:44,180
回到这里

486
00:11:44,180 --> 00:11:45,880
来运行一下

487
00:11:45,880 --> 00:11:48,780
里面好像有点乱是吧

488
00:11:48,780 --> 00:11:50,360
我把上面的console我删掉

489
00:11:50,360 --> 00:11:51,800
没有这还有一个

490
00:11:51,800 --> 00:11:52,900
把它删掉

491
00:11:52,900 --> 00:11:54,800
完了我再去写一遍是吧

492
00:11:54,800 --> 00:11:55,420
再执行

493
00:11:55,420 --> 00:11:58,680
这里面是不是srcindex

494
00:11:58,680 --> 00:11:59,880
对应的是这个文件

495
00:11:59,880 --> 00:12:01,660
完了里面又有个a对吧

496
00:12:01,660 --> 00:12:02,800
a是不是这样一个路径

497
00:12:02,800 --> 00:12:03,720
没问题吧

498
00:12:03,720 --> 00:12:04,460
完了同样

499
00:12:04,460 --> 00:12:06,360
我这里面还有个base下的b

500
00:12:06,360 --> 00:12:07,720
完了导出的是这样一个东西

501
00:12:07,720 --> 00:12:08,700
完了最后呢

502
00:12:08,700 --> 00:12:09,680
我们的主入口是它

503
00:12:09,680 --> 00:12:12,940
现在我们就已经通过AST怎么样

504
00:12:12,940 --> 00:12:14,740
把这样一个关系都找到了

505
00:12:14,740 --> 00:12:16,340
最后一步还需要干嘛

506
00:12:16,340 --> 00:12:18,000
是不是有了这些依赖关系

507
00:12:18,000 --> 00:12:20,020
我们就需要走到这个里面去了

508
00:12:20,020 --> 00:12:20,420
叫什么

509
00:12:20,420 --> 00:12:22,300
把用对象

510
00:12:22,300 --> 00:12:23,300
用数据

511
00:12:23,300 --> 00:12:26,340
渲染我们的

512
00:12:26,340 --> 00:12:29,400
比如说我们可以把刚才那一坨东西

513
00:12:29,400 --> 00:12:30,780
就这样一个东西

514
00:12:30,780 --> 00:12:31,620
作为一个什么东西

515
00:12:31,620 --> 00:12:32,380
模板

516
00:12:32,380 --> 00:12:34,020
完了把这个地方怎么样

517
00:12:34,020 --> 00:12:34,680
替换掉

518
00:12:34,680 --> 00:12:36,320
这时候打包锤的结果

519
00:12:36,320 --> 00:12:38,120
是不是就是我们想要的结果了

