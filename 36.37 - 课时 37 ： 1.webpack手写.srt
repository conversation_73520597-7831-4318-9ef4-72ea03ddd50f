1
00:00:00,000 --> 00:00:02,900
这一章我们来讲一下受写WipePack

2
00:00:02,900 --> 00:00:06,640
我们肯定是要先需要通过WipePack打包出来

3
00:00:06,640 --> 00:00:08,020
看看这是打包后的结果

4
00:00:08,020 --> 00:00:09,380
根据它打包后的结果

5
00:00:09,380 --> 00:00:11,740
我们来自己实现一套这样一个WipePack

6
00:00:11,740 --> 00:00:12,500
好了

7
00:00:12,500 --> 00:00:15,280
这里我们就先去写这样一个WipePack环境

8
00:00:15,280 --> 00:00:16,620
来写我们的代码

9
00:00:16,620 --> 00:00:19,440
通过这样一个东西来去编程我们自己这样一个

10
00:00:19,440 --> 00:00:21,020
我自己的一个Pack打包工具

11
00:00:21,020 --> 00:00:24,320
这里我们就直接新建个文件

12
00:00:24,320 --> 00:00:28,840
我们叫它WipePack-DV

13
00:00:28,840 --> 00:00:31,540
这是我们的一个用原生

14
00:00:31,540 --> 00:00:33,740
webpack来打包的一个项目

15
00:00:33,740 --> 00:00:35,080
这里直接进去

16
00:00:35,080 --> 00:00:36,880
完了我们来简单写一下

17
00:00:36,880 --> 00:00:37,580
看一下

18
00:00:37,580 --> 00:00:40,160
这里我们建上一个SRC原文件目录

19
00:00:40,160 --> 00:00:42,220
完了我们写的稍微复杂一点

20
00:00:42,220 --> 00:00:43,860
这里来个index.js

21
00:00:43,860 --> 00:00:46,620
它里面肯定是我们的主入口

22
00:00:46,620 --> 00:00:47,200
那好

23
00:00:47,200 --> 00:00:49,820
我在这里就直接去引用一个模块

24
00:00:49,820 --> 00:00:53,340
比如说require.a.js

25
00:00:53,340 --> 00:00:55,100
完了我在SRC下

26
00:00:55,100 --> 00:00:56,960
直接建上这样一个a.js文件

27
00:00:56,960 --> 00:00:59,800
并且在这里去导出一个模块

28
00:00:59,800 --> 00:01:00,920
module is pause

29
00:01:00,920 --> 00:01:02,500
比如我写一个A

30
00:01:02,500 --> 00:01:05,040
后来在这里面先写个B

31
00:01:05,040 --> 00:01:08,020
完了并且我们在A里面再去引一个文件

32
00:01:08,020 --> 00:01:09,260
稍微写的复杂一点

33
00:01:09,260 --> 00:01:11,160
我在这里来个base.js

34
00:01:11,160 --> 00:01:12,660
base文件夹

35
00:01:12,660 --> 00:01:15,240
完了里面我再放一个b.js

36
00:01:15,240 --> 00:01:17,220
b.js

37
00:01:17,220 --> 00:01:20,760
并且我在这里直接去导出module is pause

38
00:01:20,760 --> 00:01:22,720
完了这里我就直接写上一个B

39
00:01:22,720 --> 00:01:23,640
OK

40
00:01:23,640 --> 00:01:25,680
我在A里去引用这个base

41
00:01:25,680 --> 00:01:26,920
那我应该会这样写

42
00:01:26,920 --> 00:01:27,280
对吧

43
00:01:27,280 --> 00:01:28,320
我直接require

44
00:01:28,320 --> 00:01:29,400
require

45
00:01:29,400 --> 00:01:31,020
完了去第二杠

46
00:01:31,020 --> 00:01:33,580
完了base下的b.js

47
00:01:33,580 --> 00:01:35,880
完了里面拿到的呢

48
00:01:35,880 --> 00:01:36,920
就是这样一个b的变量

49
00:01:36,920 --> 00:01:37,520
OK

50
00:01:37,520 --> 00:01:38,980
完了我们在这里呢

51
00:01:38,980 --> 00:01:40,300
再同样把这个b呢

52
00:01:40,300 --> 00:01:41,440
和这个a呢加上是吧

53
00:01:41,440 --> 00:01:41,900
这样a

54
00:01:41,900 --> 00:01:43,160
完了加上一个b

55
00:01:43,160 --> 00:01:44,680
那此时啊

56
00:01:44,680 --> 00:01:45,680
我们在index里面

57
00:01:45,680 --> 00:01:46,740
进来的这个文件呀

58
00:01:46,740 --> 00:01:48,880
其实就是我们整个的这个a和b

59
00:01:48,880 --> 00:01:50,260
那我们可以打印一下

60
00:01:50,260 --> 00:01:51,340
这里面我返回的

61
00:01:51,340 --> 00:01:52,160
肯定是一个字无串

62
00:01:52,160 --> 00:01:53,300
ledstr

63
00:01:53,300 --> 00:01:54,380
这里呢

64
00:01:54,380 --> 00:01:55,840
我们直接打印一下

65
00:01:55,840 --> 00:01:57,280
Costlog STM

66
00:01:57,280 --> 00:01:59,040
完了我们来运行一下

67
00:01:59,040 --> 00:02:01,220
通过Node的环境运行肯定是没问题的

68
00:02:01,220 --> 00:02:05,220
但是我们希望能把这样一个文件夹通过WivePack进行打包

69
00:02:05,220 --> 00:02:08,080
我们就可以在SRC的根目录下

70
00:02:08,080 --> 00:02:09,380
完了处置化下文件

71
00:02:09,380 --> 00:02:12,080
并且在键上这样一个WivePackConfigure.js

72
00:02:12,080 --> 00:02:14,480
完了我在这里直接点开

73
00:02:14,480 --> 00:02:18,340
完了在这里直接运行一下CMD

74
00:02:18,340 --> 00:02:20,780
我先处置化一下是吧

75
00:02:20,780 --> 00:02:21,940
叫亚安IIT

76
00:02:23,220 --> 00:02:24,760
压IIT-Y

77
00:02:24,760 --> 00:02:30,560
出售完以后

78
00:02:30,560 --> 00:02:32,580
我们就先把需要用的包拿上

79
00:02:32,580 --> 00:02:33,520
一个叫Webpack

80
00:02:33,520 --> 00:02:35,720
还有个叫Webpack CLI-D

81
00:02:35,720 --> 00:02:38,680
往这里我们就直接去打包

82
00:02:38,680 --> 00:02:39,660
打包我们的index

83
00:02:39,660 --> 00:02:41,160
往最后我们希望怎么样

84
00:02:41,160 --> 00:02:43,680
可以把这个代码运行在路线器上

85
00:02:43,680 --> 00:02:46,520
在这里我们就直接新建一个叫Webpack

86
00:02:46,520 --> 00:02:50,020
Webpack.config.js

87
00:02:50,020 --> 00:02:52,220
完了里面我可以直接都知道

88
00:02:52,220 --> 00:02:53,520
它要导出一个模块

89
00:02:53,520 --> 00:02:54,520
modus pods

90
00:02:54,520 --> 00:02:56,520
完了里面需要一个entry

91
00:02:56,520 --> 00:03:00,520
比如说我就在这里去引入src下的index.js

92
00:03:00,520 --> 00:03:03,120
并且给它一个输出目录output

93
00:03:03,120 --> 00:03:06,120
完了这里面肯定我们需要一个模块

94
00:03:06,120 --> 00:03:07,320
就是我们的pass模块

95
00:03:07,320 --> 00:03:08,820
我们把它快速写出来

96
00:03:08,820 --> 00:03:10,020
let一个pass

97
00:03:10,020 --> 00:03:14,120
等于我们的require完了一个pass

98
00:03:14,120 --> 00:03:19,620
完了这里面我们可以放一个路径叫fuelname

99
00:03:19,620 --> 00:03:21,820
完了比如说打包出来的文件叫

100
00:03:22,220 --> 00:03:24,260
bunbundle.js

101
00:03:24,260 --> 00:03:26,880
并且我们可以给这样一个pass路径

102
00:03:26,880 --> 00:03:27,980
它是一个绝对路径

103
00:03:27,980 --> 00:03:30,720
所以说这里我们可以直接放一个resolve

104
00:03:30,720 --> 00:03:32,060
完了放到第四目路线

105
00:03:32,060 --> 00:03:36,680
完了并且我们为了希望它能可以产生一个能看到动作代码

106
00:03:36,680 --> 00:03:39,460
这里我可以直接放上一个开发模式

107
00:03:39,460 --> 00:03:41,260
dv-lop-min-t

108
00:03:41,260 --> 00:03:42,260
OK

109
00:03:42,260 --> 00:03:45,020
我们现在就把这样一个文件打包

110
00:03:45,020 --> 00:03:47,580
打包出来的就应该是一个bundle.js

111
00:03:47,580 --> 00:03:48,580
我们来看一下

112
00:03:48,580 --> 00:03:50,780
我们可以直接通过webpack

113
00:03:50,780 --> 00:03:54,020
这个npm这个8.5的功能可以直接npx

114
00:03:54,020 --> 00:03:56,420
往来直接执行我们的webpack

115
00:03:56,420 --> 00:03:58,320
webpack

116
00:03:58,320 --> 00:04:03,020
这样就可以把我们的文件打包

117
00:04:03,020 --> 00:04:05,420
打包完以后产生的就是这样一个文件

118
00:04:05,420 --> 00:04:07,340
这个文件看似有点多

119
00:04:07,340 --> 00:04:09,940
但是我们等会肯定是要基于这个代码的

120
00:04:09,940 --> 00:04:12,480
我们就把这个代码稍微删一删

121
00:04:12,480 --> 00:04:14,420
就是没用的东西我先给删掉

122
00:04:14,420 --> 00:04:16,020
这里我们可以把它替换掉

123
00:04:16,020 --> 00:04:17,720
清空

124
00:04:17,720 --> 00:04:20,140
往来里面我把这个注释也删掉

125
00:04:20,140 --> 00:04:21,040
为了看着方便

126
00:04:21,040 --> 00:04:23,420
其实我们以前也看过这个代码

127
00:04:23,420 --> 00:04:26,340
它主要就是自己实现了一个require方法

128
00:04:26,340 --> 00:04:27,960
这里我就不要了

129
00:04:27,960 --> 00:04:28,640
为了方便

130
00:04:28,640 --> 00:04:30,740
这里我们把没用的都删掉

131
00:04:30,740 --> 00:04:31,640
都删掉

132
00:04:31,640 --> 00:04:33,380
看着有点多

133
00:04:33,380 --> 00:04:34,560
把它删掉

134
00:04:34,560 --> 00:04:36,440
完了到这

135
00:04:36,440 --> 00:04:38,020
这都是没有用的

136
00:04:38,020 --> 00:04:39,240
这里一样

137
00:04:39,240 --> 00:04:40,480
我把注释都删掉

138
00:04:40,480 --> 00:04:42,540
这都删掉

139
00:04:42,540 --> 00:04:44,220
看着就好一点

140
00:04:44,220 --> 00:04:45,460
它也删掉

141
00:04:45,460 --> 00:04:46,340
这也删掉

142
00:04:46,340 --> 00:04:50,080
OK

143
00:04:50,080 --> 00:04:51,420
清空是吧

144
00:04:51,420 --> 00:04:53,440
网上里面呢一样

145
00:04:53,440 --> 00:04:54,460
我把它的也删掉

146
00:04:54,460 --> 00:04:56,660
那看啊

147
00:04:56,660 --> 00:04:57,480
现在啊

148
00:04:57,480 --> 00:04:59,740
我们就把这个webpack打包后的文件呀

149
00:04:59,740 --> 00:05:00,680
简单的删了一下

150
00:05:00,680 --> 00:05:02,100
其实很简单

151
00:05:02,100 --> 00:05:03,860
它呢主要是怎么样做的

152
00:05:03,860 --> 00:05:04,720
就是我们呀

153
00:05:04,720 --> 00:05:06,580
它自己实现了一个require方法

154
00:05:06,580 --> 00:05:07,600
叫webpackrequire

155
00:05:07,600 --> 00:05:08,800
网上里面呢

156
00:05:08,800 --> 00:05:09,480
它会默认呢

157
00:05:09,480 --> 00:05:10,920
去引用我们这样一个主文件

158
00:05:10,920 --> 00:05:11,680
主文件呢

159
00:05:11,680 --> 00:05:13,500
就是我们的src下的index.js

160
00:05:13,500 --> 00:05:15,280
就是我们这个打包后的这个index

161
00:05:15,280 --> 00:05:16,420
并且呢

162
00:05:16,420 --> 00:05:18,500
我们会在这个对象之行的时候啊

163
00:05:18,500 --> 00:05:19,440
传入一个参数

164
00:05:19,440 --> 00:05:22,140
这个参数分别就对应的是key

165
00:05:22,140 --> 00:05:22,780
value

166
00:05:22,780 --> 00:05:26,200
这个key就是当前我们文件的相对路径

167
00:05:26,200 --> 00:05:28,020
完了value就是它当前的什么

168
00:05:28,020 --> 00:05:29,420
一个代码块

169
00:05:29,420 --> 00:05:30,620
那好了

170
00:05:30,620 --> 00:05:31,400
那这样的话

171
00:05:31,400 --> 00:05:33,420
我们后面可能会引用N个模块

172
00:05:33,420 --> 00:05:33,740
对吧

173
00:05:33,740 --> 00:05:35,940
那这时候我们就需要手动的

174
00:05:35,940 --> 00:05:37,620
把这些依赖关系配到这里

175
00:05:37,620 --> 00:05:39,620
并且把入口文件这个地方

176
00:05:39,620 --> 00:05:40,760
确定下来

177
00:05:40,760 --> 00:05:42,420
完了同样生成这样一个文件

178
00:05:42,420 --> 00:05:44,380
那它应该就可以在6w7中运行了

179
00:05:44,380 --> 00:05:46,200
就这个就可以当做成一个模板

180
00:05:46,200 --> 00:05:46,980
那好

181
00:05:46,980 --> 00:05:49,940
那我们把这个东西稍微保存一下是吧

182
00:05:49,940 --> 00:05:51,560
比如把它改成man.js

183
00:05:51,560 --> 00:05:53,560
我在这里呢一样

184
00:05:53,560 --> 00:05:55,540
我可以键上这样一个index.mail

185
00:05:55,540 --> 00:05:57,980
看看这东西能不能跑在乱七上是吧

186
00:05:57,980 --> 00:05:58,500
OK

187
00:05:58,500 --> 00:06:01,620
完了我在这里呢直接去应用script

188
00:06:01,620 --> 00:06:03,060
比如说src

189
00:06:03,060 --> 00:06:05,380
去应用这个man.js

190
00:06:05,380 --> 00:06:09,240
那好了我们来看看效果是吧

191
00:06:09,240 --> 00:06:09,780
在这里

192
00:06:09,780 --> 00:06:12,080
完了直接运行

193
00:06:12,080 --> 00:06:15,000
完了console一下是吧

194
00:06:15,000 --> 00:06:15,640
看看效果

195
00:06:15,640 --> 00:06:20,400
确实出来了AB

196
00:06:20,400 --> 00:06:22,440
也就是说我们打包的这个东西

197
00:06:22,440 --> 00:06:23,720
其实刚才我们删的那些东西

198
00:06:23,720 --> 00:06:24,800
没有什么实际意义

199
00:06:24,800 --> 00:06:25,840
主要核心就在这

200
00:06:25,840 --> 00:06:27,500
就是自己实现了有点方法

201
00:06:27,500 --> 00:06:30,060
默认先会去加载这样一个首页

202
00:06:30,060 --> 00:06:31,680
通过EVL把这代码执行

203
00:06:31,680 --> 00:06:32,780
执行完以后

204
00:06:32,780 --> 00:06:34,200
如果里面再有require的话

205
00:06:34,200 --> 00:06:35,640
它会再去引用别人的路径

206
00:06:35,640 --> 00:06:37,200
一次加载

207
00:06:37,200 --> 00:06:37,500
好了

208
00:06:37,500 --> 00:06:39,020
我们知道这样一个过程

209
00:06:39,020 --> 00:06:41,100
我们就可以先不管这个过程

210
00:06:41,100 --> 00:06:42,800
我来先去自己写这样一个打包工具

211
00:06:42,800 --> 00:06:44,100
来看看这样一个功能

212
00:06:44,100 --> 00:06:45,040
好

213
00:06:45,040 --> 00:06:47,240
那我在这里再去新建个文件夹

214
00:06:47,240 --> 00:06:50,160
这个文件是专门用来打包用的

215
00:06:50,160 --> 00:06:52,020
那我们再写一个我们自己的打包库

216
00:06:52,020 --> 00:06:54,120
我在这里直接新建一个

217
00:06:54,120 --> 00:06:56,000
我们就叫他对吧

218
00:06:56,000 --> 00:06:57,360
比如说叫珠峰-pack

219
00:06:57,360 --> 00:06:59,580
OK

220
00:06:59,580 --> 00:07:02,440
完了我把这个代码也拉进来

221
00:07:02,440 --> 00:07:02,900
跑一下

222
00:07:02,900 --> 00:07:07,840
那这里面我们可以直接去先处理化一下

223
00:07:07,840 --> 00:07:09,260
我在这再新建一个

224
00:07:09,260 --> 00:07:10,600
比如说在这里

225
00:07:10,600 --> 00:07:12,260
完了cmd

226
00:07:12,260 --> 00:07:16,940
完了cd.珠峰-pack

227
00:07:16,940 --> 00:07:19,260
我在这里直接出始化

228
00:07:19,260 --> 00:07:20,400
it-y

229
00:07:20,400 --> 00:07:22,700
完了我们就可以在这里面

230
00:07:22,700 --> 00:07:23,480
系我们代码了

231
00:07:23,480 --> 00:07:24,860
但是我们写的时候

232
00:07:24,860 --> 00:07:26,320
肯定也要有个这样的执行命令

233
00:07:26,320 --> 00:07:27,760
所以说以前我们也说过

234
00:07:27,760 --> 00:07:29,100
我们如果在node里

235
00:07:29,100 --> 00:07:30,720
想配一个面上工具

236
00:07:30,720 --> 00:07:33,160
那这里我们可以去加一个bin属性

237
00:07:33,160 --> 00:07:36,720
这个bin相当于就是运行哪个文件

238
00:07:36,720 --> 00:07:37,100
对吧

239
00:07:37,100 --> 00:07:38,280
我们可以运行哪个命令

240
00:07:38,280 --> 00:07:39,160
运行哪个命令

241
00:07:39,160 --> 00:07:39,900
执行哪个文件

242
00:07:39,900 --> 00:07:42,020
那我们就可以在这个bin下

243
00:07:42,020 --> 00:07:43,320
建这样一个快捷方式

244
00:07:43,320 --> 00:07:46,320
完了里面专门放一个核心文件

245
00:07:46,320 --> 00:07:47,680
叫珠峰pack.js

246
00:07:47,680 --> 00:07:50,600
比如说我们希望干一件事

247
00:07:50,600 --> 00:07:52,020
执行我们的代码

248
00:07:52,020 --> 00:07:54,460
比如说叫珠峰-pack

249
00:07:54,460 --> 00:07:55,660
完了我就希望

250
00:07:55,660 --> 00:07:57,860
它能运行当前B下的文件

251
00:07:57,860 --> 00:07:59,600
好了我可以在这里

252
00:07:59,600 --> 00:08:03,720
直接通过-B下的珠峰-pack.js

253
00:08:03,720 --> 00:08:06,920
现在这样一个文件产生了以后

254
00:08:06,920 --> 00:08:08,620
我们就希望在命令行里面

255
00:08:08,620 --> 00:08:09,880
能执行这个文件

256
00:08:09,880 --> 00:08:10,580
就运行它

257
00:08:10,580 --> 00:08:11,880
但是运行的时候

258
00:08:11,880 --> 00:08:12,800
你要告诉人家

259
00:08:12,800 --> 00:08:15,520
这个文件需要通过什么方式来执行

260
00:08:15,520 --> 00:08:16,440
比如说告诉他

261
00:08:16,440 --> 00:08:18,400
要通过node来执行我们这个文件

262
00:08:18,400 --> 00:08:19,140
那好了

263
00:08:19,140 --> 00:08:20,740
我就需要在这个文件上

264
00:08:20,740 --> 00:08:23,220
加上这样一个代码叫惊叹号

265
00:08:23,220 --> 00:08:24,560
我来告诉他user

266
00:08:24,560 --> 00:08:26,020
user b

267
00:08:26,020 --> 00:08:28,280
我来下载环境的什么node

268
00:08:28,280 --> 00:08:29,320
就是告诉人家

269
00:08:29,320 --> 00:08:30,460
当前这个代码

270
00:08:30,460 --> 00:08:33,080
需要使用我们的node的环境来执行

271
00:08:33,080 --> 00:08:34,180
那这时候呢

272
00:08:34,180 --> 00:08:35,960
我就可以在这里直接打印一下

273
00:08:35,960 --> 00:08:36,560
cotal log

274
00:08:36,560 --> 00:08:37,760
比如来一个hello

275
00:08:37,760 --> 00:08:40,340
就叫start吧

276
00:08:40,340 --> 00:08:42,780
现在我们就需要把这个包

277
00:08:42,780 --> 00:08:44,160
练到我们的全局上

278
00:08:44,160 --> 00:08:46,040
完了在我们当前这个项目里

279
00:08:46,040 --> 00:08:47,340
去应用那个打包工具

280
00:08:47,340 --> 00:08:49,060
这时候我们就可以

281
00:08:49,060 --> 00:08:50,800
把这个项目去链接一下

282
00:08:50,800 --> 00:08:52,500
好我在这里可以直接

283
00:08:52,500 --> 00:08:53,740
来操作一下

284
00:08:53,740 --> 00:08:55,240
叫NPM叫Link

285
00:08:55,240 --> 00:08:56,560
Link的作用

286
00:08:56,560 --> 00:08:58,820
就是它会把当前这样一个包

287
00:08:58,820 --> 00:09:00,720
去链接到我们这个全局下

288
00:09:00,720 --> 00:09:02,720
并且会在全局下

289
00:09:02,720 --> 00:09:03,660
生成这个命令

290
00:09:03,660 --> 00:09:04,620
执行这个命令

291
00:09:04,620 --> 00:09:05,560
就可以执行这个文件

292
00:09:05,560 --> 00:09:06,980
那好了我在这里

293
00:09:06,980 --> 00:09:07,820
直接跑一下

294
00:09:07,820 --> 00:09:08,680
叫NPMLink

295
00:09:08,680 --> 00:09:09,980
OK

296
00:09:09,980 --> 00:09:11,620
那link的时候

297
00:09:11,620 --> 00:09:12,380
他会告诉我

298
00:09:12,380 --> 00:09:12,580
对吧

299
00:09:12,580 --> 00:09:14,240
怎么去连接过去的

300
00:09:14,240 --> 00:09:15,220
我重新断开

301
00:09:15,220 --> 00:09:16,580
跑一下npmlink

302
00:09:16,580 --> 00:09:17,480
好了

303
00:09:17,480 --> 00:09:18,520
那我们看到了

304
00:09:18,520 --> 00:09:19,840
现在已经link成功了

305
00:09:19,840 --> 00:09:22,920
现在在我们的c盘下的npm目录下

306
00:09:22,920 --> 00:09:24,480
已经有了这样一个珠峰缸pack

307
00:09:24,480 --> 00:09:26,200
完了他引用的就是什么

308
00:09:26,200 --> 00:09:28,860
就是当前我们这样一个珠峰pack.js

309
00:09:28,860 --> 00:09:29,780
你也看到了

310
00:09:29,780 --> 00:09:32,500
他会默认找到我们当前项目下的文件

311
00:09:32,500 --> 00:09:33,420
那好了

312
00:09:33,420 --> 00:09:34,980
有了这样一个文件以后

313
00:09:34,980 --> 00:09:36,100
那我们希望

314
00:09:36,100 --> 00:09:37,960
这个模块我们边边写

315
00:09:37,960 --> 00:09:39,460
是不是在这边可以边测试

316
00:09:39,460 --> 00:09:40,660
那现在好了

317
00:09:40,660 --> 00:09:41,820
那我们就可以怎么样

318
00:09:41,820 --> 00:09:43,220
在当前项目下

319
00:09:43,220 --> 00:09:45,200
把这个模块链接进来

320
00:09:45,200 --> 00:09:45,620
OK

321
00:09:45,620 --> 00:09:46,900
我在这里呢

322
00:09:46,900 --> 00:09:47,820
这个项目下是吧

323
00:09:47,820 --> 00:09:48,700
可以通过NPM

324
00:09:48,700 --> 00:09:49,280
Link

325
00:09:49,280 --> 00:09:50,720
我来写上一个叫

326
00:09:50,720 --> 00:09:51,120
珠峰

327
00:09:51,120 --> 00:09:52,000
告Pack

328
00:09:52,000 --> 00:09:54,240
相当于把我们全局下的那个包啊

329
00:09:54,240 --> 00:09:55,400
再映射到本地上

330
00:09:55,400 --> 00:09:56,160
那这时候呢

331
00:09:56,160 --> 00:09:57,400
我们可以通过NPX

332
00:09:57,400 --> 00:09:58,180
珠峰Pack

333
00:09:58,180 --> 00:09:59,560
来执行当前这个命令

334
00:09:59,560 --> 00:10:00,200
OK

335
00:10:00,200 --> 00:10:00,900
运行一下

336
00:10:00,900 --> 00:10:03,560
这时候你发现了吗

337
00:10:03,560 --> 00:10:05,360
是不是已经在当前目录下呢

338
00:10:05,360 --> 00:10:06,520
装了这样一个珠峰Pack

339
00:10:06,520 --> 00:10:07,580
那我可以怎么样

340
00:10:07,580 --> 00:10:08,460
直接NPX

341
00:10:08,460 --> 00:10:09,740
叫珠峰-pack

342
00:10:09,740 --> 00:10:10,860
OK

343
00:10:10,860 --> 00:10:11,640
你会看到

344
00:10:11,640 --> 00:10:13,460
是不是在我们YPAC打包中

345
00:10:13,460 --> 00:10:15,580
已经应用了我们当前这样一个模块

346
00:10:15,580 --> 00:10:17,400
那现在我们就可以怎么样

347
00:10:17,400 --> 00:10:19,300
就是放心写我们这个代码了

348
00:10:19,300 --> 00:10:19,820
每次一

349
00:10:19,820 --> 00:10:21,040
我们一去更新这个代码

350
00:10:21,040 --> 00:10:22,020
比如写个start1

351
00:10:22,020 --> 00:10:23,680
在我们这个项目打包的时候

352
00:10:23,680 --> 00:10:24,720
执行这个命令

353
00:10:24,720 --> 00:10:26,860
就会打出来我们对应的这个结果了

354
00:10:26,860 --> 00:10:27,860
不用在link了

355
00:10:27,860 --> 00:10:28,460
这里面

356
00:10:28,460 --> 00:10:31,100
我们可以直接去执行mpx

357
00:10:31,100 --> 00:10:32,640
或者珠峰-pack

358
00:10:32,640 --> 00:10:34,700
OK

359
00:10:34,700 --> 00:10:36,040
你看start1就更新过来了

360
00:10:36,040 --> 00:10:37,020
这是一个实时操作

361
00:10:37,020 --> 00:10:37,720
那好了

362
00:10:37,720 --> 00:10:40,800
那我们接下来就开始实现我们这个WePAC

