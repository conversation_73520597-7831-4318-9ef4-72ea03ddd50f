1
00:00:00,000 --> 00:00:01,920
我们呢接着来说

2
00:00:01,920 --> 00:00:03,120
就是webpack中呢

3
00:00:03,120 --> 00:00:03,960
比较重要的一个东西

4
00:00:03,960 --> 00:00:04,580
叫sauce map

5
00:00:04,580 --> 00:00:06,720
我们其实以前也提过这个东西

6
00:00:06,720 --> 00:00:08,440
但是现在我们来详细说一下

7
00:00:08,440 --> 00:00:08,980
这个sauce map

8
00:00:08,980 --> 00:00:09,860
就说呀

9
00:00:09,860 --> 00:00:11,960
我们可能在解析GS的过程中啊

10
00:00:11,960 --> 00:00:13,840
可能会把这个高级预法呢

11
00:00:13,840 --> 00:00:14,740
转成低级预法

12
00:00:14,740 --> 00:00:15,760
那这里呢

13
00:00:15,760 --> 00:00:16,920
我们就要配一些什么

14
00:00:16,920 --> 00:00:18,520
是不是babel相关的内容了

15
00:00:18,520 --> 00:00:19,440
那这里呢

16
00:00:19,440 --> 00:00:21,200
我就把这个babel需要动作包呢

17
00:00:21,200 --> 00:00:21,800
都按一下

18
00:00:21,800 --> 00:00:23,800
我们需要这样一个babel的核心包

19
00:00:23,800 --> 00:00:25,520
叫babel-core

20
00:00:25,520 --> 00:00:27,320
同样呢

21
00:00:27,320 --> 00:00:28,240
还有我们的babel

22
00:00:28,240 --> 00:00:30,580
啊杠杠这个预设是吧

23
00:00:30,580 --> 00:00:32,280
Preset的因为那同样

24
00:00:32,280 --> 00:00:33,120
他们的桥梁呢

25
00:00:33,120 --> 00:00:35,680
是通过这个babel杠什么loader来进行处理的

26
00:00:35,680 --> 00:00:37,460
这个东西没有爱的服务啊

27
00:00:37,460 --> 00:00:38,820
叫babel杠loader

28
00:00:38,820 --> 00:00:39,960
那为了方便啊

29
00:00:39,960 --> 00:00:41,260
等会我们可能运行打包

30
00:00:41,260 --> 00:00:43,360
我们用一下这个webpack对吧

31
00:00:43,360 --> 00:00:46,020
dv server把他们都安装上啊

32
00:00:46,020 --> 00:00:47,540
杠大地

33
00:00:47,540 --> 00:00:48,820
那这里呢

34
00:00:48,820 --> 00:00:50,800
我们就先写下这个配置啊

35
00:00:50,800 --> 00:00:52,240
这配置呢还是比较简单的

36
00:00:52,240 --> 00:00:53,700
我们呢先把这里呢

37
00:00:53,700 --> 00:00:55,840
先都先建个历史文件吧

38
00:00:55,840 --> 00:00:56,880
是吧叫history

39
00:00:56,880 --> 00:00:58,540
完了我把这个配置呢

40
00:00:58,540 --> 00:00:59,000
保存一份

41
00:00:59,000 --> 00:01:00,340
放这里

42
00:01:00,340 --> 00:01:02,140
完了这里呢

43
00:01:02,140 --> 00:01:03,240
我把没用的删掉啊

44
00:01:03,240 --> 00:01:04,260
像这句话没有用

45
00:01:04,260 --> 00:01:05,300
完了这个地方呢

46
00:01:05,300 --> 00:01:06,260
other呢也没用

47
00:01:06,260 --> 00:01:07,420
完了他呢不要了

48
00:01:07,420 --> 00:01:09,720
我们现在只需要一个入口

49
00:01:09,720 --> 00:01:11,140
完了文件的名字呢

50
00:01:11,140 --> 00:01:12,320
我就默认改成indice

51
00:01:12,320 --> 00:01:13,100
要找不到

52
00:01:13,100 --> 00:01:15,620
这就是一个单页面应用

53
00:01:15,620 --> 00:01:15,900
是吧

54
00:01:15,900 --> 00:01:17,160
那我们同样呢

55
00:01:17,160 --> 00:01:17,900
需要在这里呢

56
00:01:17,900 --> 00:01:18,800
配上一些规则

57
00:01:18,800 --> 00:01:19,600
比如说module

58
00:01:19,600 --> 00:01:21,260
我们来一个东西叫什么呢

59
00:01:21,260 --> 00:01:22,180
叫rules对吧

60
00:01:22,180 --> 00:01:22,500
规则

61
00:01:22,500 --> 00:01:23,780
rules

62
00:01:23,780 --> 00:01:24,960
规则有很多个

63
00:01:24,960 --> 00:01:25,460
是书组

64
00:01:25,460 --> 00:01:26,600
完了里面呢

65
00:01:26,600 --> 00:01:27,800
我就可以配置

66
00:01:27,800 --> 00:01:28,400
比如说呢

67
00:01:28,400 --> 00:01:30,600
当前是以这个GS结尾的

68
00:01:30,600 --> 00:01:32,400
我们要用

69
00:01:32,400 --> 00:01:34,300
那用谁呢

70
00:01:34,300 --> 00:01:36,100
我们需要用到这个loader是吧

71
00:01:36,100 --> 00:01:36,200
哦

72
00:01:36,200 --> 00:01:38,100
这里面少了一个写缩地儿吧

73
00:01:38,100 --> 00:01:39,100
放在这

74
00:01:39,100 --> 00:01:40,600
这是个没错

75
00:01:40,600 --> 00:01:42,100
我们应该用这个loader是吧

76
00:01:42,100 --> 00:01:42,900
loader

77
00:01:42,900 --> 00:01:44,900
我们用这个bibble gotloader

78
00:01:44,900 --> 00:01:47,600
完了同样呢

79
00:01:47,600 --> 00:01:51,000
我们应该在这来个叫叫什么叫options参数是吧

80
00:01:51,000 --> 00:01:54,300
我们需要把那些预设要配上去叫precess

81
00:01:54,300 --> 00:01:55,000
完了里面呢

82
00:01:55,000 --> 00:02:01,480
我就直接配这个叫@babel-preset-env

83
00:02:01,480 --> 00:02:03,200
就这个以前都配过了

84
00:02:03,200 --> 00:02:04,900
这个应该怎么样配的很熟练了

85
00:02:04,900 --> 00:02:06,800
那好我这下行

86
00:02:06,800 --> 00:02:09,600
那现在我们配了这样一个babel以后呢

87
00:02:09,600 --> 00:02:12,320
我们就把这个代码在我把other删掉吧

88
00:02:12,320 --> 00:02:13,100
other用不到

89
00:02:13,100 --> 00:02:13,900
干掉

90
00:02:13,900 --> 00:02:15,900
完了里面呢我在这个index里面

91
00:02:15,900 --> 00:02:18,700
我再写一个吧写一个我们的es入语法

92
00:02:18,700 --> 00:02:19,800
classlog

93
00:02:19,800 --> 00:02:21,600
完了这里面呢

94
00:02:21,600 --> 00:02:25,040
我们就直接LiteLog等于NewLog

95
00:02:25,040 --> 00:02:26,240
NewLog

96
00:02:26,240 --> 00:02:26,920
完了里面呢

97
00:02:26,920 --> 00:02:28,400
我就在这可能插摊里面

98
00:02:28,400 --> 00:02:29,520
接下来东西是吧

99
00:02:29,520 --> 00:02:30,800
我就打个日志

100
00:02:30,800 --> 00:02:32,640
叫console对吧

101
00:02:32,640 --> 00:02:34,000
Log我就不写

102
00:02:34,000 --> 00:02:34,920
记了啊

103
00:02:34,920 --> 00:02:35,640
不要

104
00:02:35,640 --> 00:02:36,240
我这里呢

105
00:02:36,240 --> 00:02:38,440
我就写上出错了

106
00:02:38,440 --> 00:02:38,920
哎

107
00:02:38,920 --> 00:02:40,520
那这样一个代码明显

108
00:02:40,520 --> 00:02:42,120
我们的ES6肯定会怎么样

109
00:02:42,120 --> 00:02:43,400
把它编译成ES5

110
00:02:43,400 --> 00:02:44,320
那ES5的话

111
00:02:44,320 --> 00:02:45,520
是不是变成了什么

112
00:02:45,520 --> 00:02:46,640
勾动函数了

113
00:02:46,640 --> 00:02:47,200
而且啊

114
00:02:47,200 --> 00:02:48,480
你想我这里面呢

115
00:02:48,480 --> 00:02:49,760
我再给他配个选项

116
00:02:49,760 --> 00:02:50,000
哎

117
00:02:50,000 --> 00:02:51,240
他现在是开发模式

118
00:02:51,240 --> 00:02:53,200
我给它变成生产模式

119
00:02:53,200 --> 00:02:54,160
那它会怎么样

120
00:02:54,160 --> 00:02:56,840
是不是还会把我们这个文件进行压缩呀

121
00:02:56,840 --> 00:02:58,200
那压缩后的结果呀

122
00:02:58,200 --> 00:02:58,840
肯定怎么样

123
00:02:58,840 --> 00:03:00,120
是不是只有一行

124
00:03:00,120 --> 00:03:00,360
哎

125
00:03:00,360 --> 00:03:01,320
那只有一行的话

126
00:03:01,320 --> 00:03:01,960
那报错了

127
00:03:01,960 --> 00:03:03,240
是不是就没法调试了

128
00:03:03,240 --> 00:03:04,840
那再来看看是不是这样啊

129
00:03:04,840 --> 00:03:05,360
那这里呢

130
00:03:05,360 --> 00:03:08,360
我就直接npm run dv

131
00:03:08,360 --> 00:03:10,640
这里没有dv啊

132
00:03:10,640 --> 00:03:11,800
我们还没有配

133
00:03:11,800 --> 00:03:12,520
我在这里面呢

134
00:03:12,520 --> 00:03:13,720
需要找到派尔杰森

135
00:03:13,720 --> 00:03:15,080
我在这里面加上一个脚本

136
00:03:15,080 --> 00:03:15,360
对吧

137
00:03:15,360 --> 00:03:16,360
scripts

138
00:03:16,360 --> 00:03:17,400
我那里面呢

139
00:03:17,400 --> 00:03:18,360
应该来个dv

140
00:03:18,360 --> 00:03:20,760
完了叫webpack dv server

141
00:03:20,760 --> 00:03:22,880
那同样还应该有个东西叫build 是吧

142
00:03:22,880 --> 00:03:23,760
打包

143
00:03:23,760 --> 00:03:24,840
那打包的话呀

144
00:03:24,840 --> 00:03:27,840
我们应该用的就是那个web pack

145
00:03:27,840 --> 00:03:28,400
那现在呀

146
00:03:28,400 --> 00:03:30,440
我们运行第一位就可以帮我们起个服务了

147
00:03:30,440 --> 00:03:31,040
是吧

148
00:03:31,040 --> 00:03:33,880
那这里一样npm软bv

149
00:03:33,880 --> 00:03:36,840
完了里面呢

150
00:03:36,840 --> 00:03:38,440
我看看他跑跑了是吧

151
00:03:38,440 --> 00:03:39,520
388080啊

152
00:03:39,520 --> 00:03:40,320
顿口

153
00:03:40,320 --> 00:03:40,800
我这里呢

154
00:03:40,800 --> 00:03:41,720
我起个服务

155
00:03:41,720 --> 00:03:43,240
看看效果啊

156
00:03:43,240 --> 00:03:43,960
稍等

157
00:03:43,960 --> 00:03:46,360
刷新

158
00:03:46,360 --> 00:03:49,000
那这里面应该就会报了个错是吧

159
00:03:49,000 --> 00:03:50,660
他说没有解析到这个other

160
00:03:50,660 --> 00:03:52,400
肯定是配置文件怎么样

161
00:03:52,400 --> 00:03:53,320
删干净

162
00:03:53,320 --> 00:03:54,420
我把这配置文件

163
00:03:54,420 --> 00:03:55,500
这里是吧删掉

164
00:03:55,500 --> 00:03:57,200
这是个纯单页

165
00:03:57,200 --> 00:03:58,540
这个注释又不要了

166
00:03:58,540 --> 00:03:59,040
完了

167
00:03:59,040 --> 00:04:01,380
怎么好像又回来了呢

168
00:04:01,380 --> 00:04:03,180
明明把它删掉了

169
00:04:03,180 --> 00:04:04,140
这里面要把它删掉

170
00:04:04,140 --> 00:04:05,440
other我是不是又删错了

171
00:04:05,440 --> 00:04:05,960
我看看

172
00:04:05,960 --> 00:04:07,640
哎呦

173
00:04:07,640 --> 00:04:08,720
很尴尬的一件事

174
00:04:08,720 --> 00:04:09,800
那我把这个东西

175
00:04:09,800 --> 00:04:10,900
推回来

176
00:04:10,900 --> 00:04:12,500
把它删错文件了

177
00:04:12,500 --> 00:04:13,780
那我把这个配置文件

178
00:04:13,780 --> 00:04:15,440
口碑一份

179
00:04:15,440 --> 00:04:16,440
我在这里呢

180
00:04:16,440 --> 00:04:17,480
把它拿过来

181
00:04:17,480 --> 00:04:17,920
完了

182
00:04:17,920 --> 00:04:19,620
把那个要粘到历史里去是吧

183
00:04:19,620 --> 00:04:19,980
哎

184
00:04:19,980 --> 00:04:21,080
这是粘错了

185
00:04:21,080 --> 00:04:22,280
他捡切到这里

186
00:04:22,280 --> 00:04:23,820
老干这种事是吧

187
00:04:23,820 --> 00:04:24,880
我要把它们关掉啊

188
00:04:24,880 --> 00:04:26,360
一定关掉都关掉吧

189
00:04:26,360 --> 00:04:27,180
关于其他

190
00:04:27,180 --> 00:04:28,660
完这个就是我们的配置文件

191
00:04:28,660 --> 00:04:29,520
看一眼啊

192
00:04:29,520 --> 00:04:30,320
这个模式呢

193
00:04:30,320 --> 00:04:30,920
我也没有改

194
00:04:30,920 --> 00:04:32,320
应该它是pro

195
00:04:32,320 --> 00:04:33,680
大生是吧

196
00:04:33,680 --> 00:04:34,880
完了这是入口

197
00:04:34,880 --> 00:04:35,860
我这规则

198
00:04:35,860 --> 00:04:37,120
规则没配错是吧

199
00:04:37,120 --> 00:04:38,720
我们同样呢有出口

200
00:04:38,720 --> 00:04:39,720
还有我们的plugin

201
00:04:39,720 --> 00:04:40,620
哎 ok

202
00:04:40,620 --> 00:04:42,120
这时候已经没错了

203
00:04:42,120 --> 00:04:43,720
我说怎么又报错了

204
00:04:43,720 --> 00:04:46,820
那这里面我们来试一试啊

205
00:04:46,820 --> 00:04:47,320
看看

206
00:04:47,320 --> 00:04:49,580
现在呢就出来了一个8080是吧

207
00:04:49,580 --> 00:04:51,160
完了我在这刷新

208
00:04:51,160 --> 00:04:53,480
起码应该会报错是吧

209
00:04:53,480 --> 00:04:55,140
看看报错了

210
00:04:55,140 --> 00:04:57,160
哎明确啊

211
00:04:57,160 --> 00:04:58,020
他告诉我什么呢

212
00:04:58,020 --> 00:04:59,320
说这个东西啊现在有问题

213
00:04:59,320 --> 00:05:00,400
说cost log

214
00:05:00,400 --> 00:05:01,420
他说不是一个函数

215
00:05:01,420 --> 00:05:01,800
一点

216
00:05:01,800 --> 00:05:03,480
你发现了吗

217
00:05:03,480 --> 00:05:04,400
是不是出来的是

218
00:05:04,400 --> 00:05:05,860
这是啥东西对吧

219
00:05:05,860 --> 00:05:07,500
是不是打包后的结果呀

220
00:05:07,500 --> 00:05:09,000
而且告诉我这是第二号

221
00:05:09,000 --> 00:05:10,300
这上哪找去是吧

222
00:05:10,300 --> 00:05:11,880
我们肯定希望怎么样

223
00:05:11,880 --> 00:05:13,980
是不是应该有一个映射文件

224
00:05:13,980 --> 00:05:15,040
就是我一点呀

225
00:05:15,040 --> 00:05:16,200
我查看的错误

226
00:05:16,200 --> 00:05:17,040
应该是圆满

227
00:05:17,040 --> 00:05:19,520
而不是这里面出了一个这个错误是吧

228
00:05:19,520 --> 00:05:21,400
那这时候我要怎么处理呢

229
00:05:21,400 --> 00:05:22,360
哎你需要怎么样

230
00:05:22,360 --> 00:05:23,580
稍微改造一下了

231
00:05:23,580 --> 00:05:24,520
我要跟他说

232
00:05:24,520 --> 00:05:26,700
哎我要怎么样去调试这个代码

233
00:05:26,700 --> 00:05:29,080
而且呢需要一个什么叫原码映射

234
00:05:29,080 --> 00:05:31,660
那这里面我们就可以给他加个叫什么叫

235
00:05:31,660 --> 00:05:33,300
DV2的这样的选项

236
00:05:33,300 --> 00:05:34,840
这里面明确啊

237
00:05:34,840 --> 00:05:38,040
就是增加对映射文件

238
00:05:38,040 --> 00:05:39,040
映射文件

239
00:05:39,040 --> 00:05:40,180
好了可以怎么样

240
00:05:40,180 --> 00:05:43,400
可以帮我们调试原代码

241
00:05:43,400 --> 00:05:45,420
原代码

242
00:05:45,420 --> 00:05:47,060
OK啊

243
00:05:47,060 --> 00:05:48,220
这里面非常方便

244
00:05:48,220 --> 00:05:50,000
我们最暴力的叫sauce map

245
00:05:50,000 --> 00:05:51,340
明确对吧

246
00:05:51,340 --> 00:05:52,300
这个叫sauce map

247
00:05:52,300 --> 00:05:52,600
啥意思

248
00:05:52,600 --> 00:05:54,360
名字就叫原码什么

249
00:05:54,360 --> 00:05:55,180
原码印射

250
00:05:55,180 --> 00:05:56,780
它会怎么样呢

251
00:05:56,780 --> 00:05:58,640
会单独对吧

252
00:05:58,640 --> 00:06:01,560
生成一个叫sauce map文件

253
00:06:01,560 --> 00:06:04,540
sauce map文件

254
00:06:04,540 --> 00:06:06,680
那好了

255
00:06:06,680 --> 00:06:07,520
我们来试一试

256
00:06:07,520 --> 00:06:08,980
生成这样一个文件以后

257
00:06:08,980 --> 00:06:10,680
而且出错对吧

258
00:06:10,680 --> 00:06:11,400
出错了

259
00:06:11,400 --> 00:06:12,380
会怎么样

260
00:06:12,380 --> 00:06:13,540
会标识

261
00:06:13,540 --> 00:06:15,240
我们的标识对吧

262
00:06:15,240 --> 00:06:17,640
标识当前报错的什么

263
00:06:17,640 --> 00:06:19,440
当前报错的

264
00:06:19,440 --> 00:06:22,440
裂和什么裂和行

265
00:06:22,440 --> 00:06:24,640
哎这是特点啊

266
00:06:24,640 --> 00:06:25,740
那咱来试试吧

267
00:06:25,740 --> 00:06:26,640
看看行不行

268
00:06:26,640 --> 00:06:27,840
这么棒的东西是吧

269
00:06:27,840 --> 00:06:28,740
叫npm

270
00:06:28,740 --> 00:06:29,640
叫什么呢

271
00:06:29,640 --> 00:06:30,640
就rumbu的了

272
00:06:30,640 --> 00:06:32,040
打包打包出来

273
00:06:32,040 --> 00:06:34,940
再看看是不是有这样的映射文件

274
00:06:34,940 --> 00:06:35,440
是吧

275
00:06:35,440 --> 00:06:37,540
这个文件应该叫哦

276
00:06:37,540 --> 00:06:38,540
好像还是有了吧

277
00:06:38,540 --> 00:06:40,140
叫home.js.map

278
00:06:40,140 --> 00:06:42,240
这就是我们所谓的那个叫saucemap

279
00:06:42,240 --> 00:06:43,540
你看这个文件挺大的

280
00:06:43,540 --> 00:06:44,640
它是圆码文件

281
00:06:45,140 --> 00:06:46,500
这里面今天不管他是吧

282
00:06:46,500 --> 00:06:47,100
有什么东西

283
00:06:47,100 --> 00:06:49,040
我先把这个index刨起来

284
00:06:49,040 --> 00:06:50,300
刨起来

285
00:06:50,300 --> 00:06:50,880
关键器中

286
00:06:50,880 --> 00:06:52,940
完了c到这里来

287
00:06:52,940 --> 00:06:54,320
看看效果

288
00:06:54,320 --> 00:06:56,120
这时候包错了

289
00:06:56,120 --> 00:06:58,360
你看他描述的不再是以前那个了

290
00:06:58,360 --> 00:06:59,540
而是index第六号

291
00:06:59,540 --> 00:07:00,460
我点一下

292
00:07:00,460 --> 00:07:03,560
你看是不是直接定位到了当前第六号

293
00:07:03,560 --> 00:07:05,920
而且列是不是就是log这个位置

294
00:07:05,920 --> 00:07:07,280
那说明怎么样

295
00:07:07,280 --> 00:07:08,560
这个映射成功了

296
00:07:08,560 --> 00:07:10,260
你也可以看一下sauce

297
00:07:10,260 --> 00:07:11,660
里面有src是吧

298
00:07:11,660 --> 00:07:12,020
index

299
00:07:12,020 --> 00:07:13,480
这就是我们的圆码映射

300
00:07:13,480 --> 00:07:15,820
但是这个东西你看

301
00:07:15,820 --> 00:07:16,540
它就是什么

302
00:07:16,540 --> 00:07:18,220
相当于就是大而全

303
00:07:18,220 --> 00:07:18,580
对吧

304
00:07:18,580 --> 00:07:19,120
而全

305
00:07:19,120 --> 00:07:20,000
而且是什么

306
00:07:20,000 --> 00:07:20,600
是独立的

307
00:07:20,600 --> 00:07:21,080
对吧

308
00:07:21,080 --> 00:07:21,760
而全

309
00:07:21,760 --> 00:07:23,880
大和全是吧

310
00:07:23,880 --> 00:07:24,580
两个特点

311
00:07:24,580 --> 00:07:27,600
那同样的还有一些其他的

312
00:07:27,600 --> 00:07:27,920
对吧

313
00:07:27,920 --> 00:07:29,280
比如有一些其他的小配置

314
00:07:29,280 --> 00:07:29,780
这里呢

315
00:07:29,780 --> 00:07:30,580
我把它去掉

316
00:07:30,580 --> 00:07:31,880
那还有什么呢

317
00:07:31,880 --> 00:07:32,520
还有啊

318
00:07:32,520 --> 00:07:33,740
可以这样配叫dv

319
00:07:33,740 --> 00:07:34,480
这样吧

320
00:07:34,480 --> 00:07:36,460
我把它写到下面来好点

321
00:07:36,460 --> 00:07:38,380
第二个叫什么呢

322
00:07:38,380 --> 00:07:40,020
叫dv-to

323
00:07:40,020 --> 00:07:41,540
叫还是一样啊

324
00:07:41,540 --> 00:07:42,100
还是dv-to

325
00:07:42,100 --> 00:07:42,440
是吧

326
00:07:42,440 --> 00:07:43,880
这里写dvto

327
00:07:43,880 --> 00:07:45,620
这里面有个叫什么

328
00:07:45,620 --> 00:07:46,320
叫evl

329
00:07:46,320 --> 00:07:47,480
叫什么sauce map

330
00:07:47,480 --> 00:07:50,120
这个的区别什么意思

331
00:07:50,120 --> 00:07:52,020
这个就不会产生什么

332
00:07:52,020 --> 00:07:54,080
就不会产生单独的文件

333
00:07:54,080 --> 00:07:58,300
不会产生单独的文件

334
00:07:58,300 --> 00:07:59,920
但是可以怎么样

335
00:07:59,920 --> 00:08:01,960
但是可以显示什么

336
00:08:01,960 --> 00:08:03,420
显示行和力

337
00:08:03,420 --> 00:08:06,600
比如说它一样和sauce map

338
00:08:06,600 --> 00:08:07,460
唯一的区别就是

339
00:08:07,460 --> 00:08:09,560
它会把sauce map

340
00:08:09,560 --> 00:08:12,160
放到当前打包后的文件里

341
00:08:12,160 --> 00:08:13,180
就是后面的js里

342
00:08:13,180 --> 00:08:15,100
我把这d色的删掉太多

343
00:08:15,100 --> 00:08:16,560
再来试一试

344
00:08:16,560 --> 00:08:18,280
看看区别就懂了

345
00:08:18,280 --> 00:08:19,160
npm run

346
00:08:19,160 --> 00:08:24,300
OK稍等

347
00:08:24,300 --> 00:08:26,560
我们在这里再刷新一下

348
00:08:26,560 --> 00:08:27,280
还是文件

349
00:08:27,280 --> 00:08:27,920
刷新

350
00:08:27,920 --> 00:08:29,300
完了这里面

351
00:08:29,300 --> 00:08:30,800
好像没成功

352
00:08:30,800 --> 00:08:32,740
看看src

353
00:08:32,740 --> 00:08:34,320
有这样一个东西

354
00:08:34,320 --> 00:08:35,860
我在这里面再刷新一下

355
00:08:35,860 --> 00:08:36,580
看看报错

356
00:08:36,580 --> 00:08:37,420
我点过去

357
00:08:37,420 --> 00:08:39,160
完了点过去以后

358
00:08:39,160 --> 00:08:40,400
是不是发现它也可以怎么样

359
00:08:40,400 --> 00:08:41,380
定位到这个路径

360
00:08:41,380 --> 00:08:42,780
而且你看看

361
00:08:42,780 --> 00:08:44,240
是不是并没有显示出

362
00:08:44,240 --> 00:08:45,760
我们有什么south map

363
00:08:45,760 --> 00:08:47,320
那就是说他是怎么样

364
00:08:47,320 --> 00:08:49,100
是集成到文件中的

365
00:08:49,100 --> 00:08:51,500
同样其实我这几个还有

366
00:08:51,500 --> 00:08:52,940
经常面试的时候会问

367
00:08:52,940 --> 00:08:54,460
这几个选项有什么区别

368
00:08:54,460 --> 00:08:55,160
好

369
00:08:55,160 --> 00:08:55,980
这个我除掉

370
00:08:55,980 --> 00:08:56,700
再来一个

371
00:08:56,700 --> 00:08:57,700
这个叫什么

372
00:08:57,700 --> 00:08:58,260
还是一样

373
00:08:58,260 --> 00:08:59,960
有一个叫不会什么

374
00:08:59,960 --> 00:09:01,460
不会产生

375
00:09:01,460 --> 00:09:03,360
产生力

376
00:09:03,360 --> 00:09:03,960
对吧

377
00:09:03,960 --> 00:09:04,520
产生力

378
00:09:04,520 --> 00:09:06,640
但是是一个

379
00:09:06,640 --> 00:09:09,560
是一个单独的映射文件

380
00:09:09,560 --> 00:09:11,820
那这时候呢

381
00:09:11,820 --> 00:09:12,880
我们一样可以这样配

382
00:09:12,880 --> 00:09:13,720
叫dv2

383
00:09:13,720 --> 00:09:15,120
完了这个名字叫什么

384
00:09:15,120 --> 00:09:15,740
叫cheap

385
00:09:15,740 --> 00:09:17,060
cheap就是简化的

386
00:09:17,060 --> 00:09:17,280
对吧

387
00:09:17,280 --> 00:09:17,900
cheap module

388
00:09:17,900 --> 00:09:19,260
完了叫什么呢

389
00:09:19,260 --> 00:09:20,560
叫这个sauce map

390
00:09:20,560 --> 00:09:22,060
你看看啊

391
00:09:22,060 --> 00:09:23,520
它呢相当于不会显示列

392
00:09:23,520 --> 00:09:24,720
但是而且呢什么

393
00:09:24,720 --> 00:09:26,160
它是一个单独的文件

394
00:09:26,160 --> 00:09:27,580
如果这个文件啊

395
00:09:27,580 --> 00:09:28,880
和我们当前这个圆满啊

396
00:09:28,880 --> 00:09:30,000
它没有调试的功能

397
00:09:30,000 --> 00:09:30,780
只相当于怎么样

398
00:09:30,780 --> 00:09:32,060
就是产生后

399
00:09:32,060 --> 00:09:32,340
对吧

400
00:09:32,340 --> 00:09:32,820
产生后

401
00:09:32,820 --> 00:09:33,480
对吧

402
00:09:33,480 --> 00:09:34,000
你可以怎么样

403
00:09:34,000 --> 00:09:34,540
你可以

404
00:09:34,540 --> 00:09:35,980
你可以对吧

405
00:09:35,980 --> 00:09:37,560
可以保留起来

406
00:09:37,560 --> 00:09:39,500
用来什么

407
00:09:39,500 --> 00:09:39,960
调试

408
00:09:39,960 --> 00:09:41,560
后面用来调试

409
00:09:41,560 --> 00:09:42,460
比如说他呀

410
00:09:42,460 --> 00:09:43,820
现在肯定看不出来什么效果

411
00:09:43,820 --> 00:09:44,660
那咱再试一试

412
00:09:44,660 --> 00:09:45,460
看是不是这样

413
00:09:45,460 --> 00:09:47,040
RUNB的

414
00:09:47,040 --> 00:09:48,380
他呢

415
00:09:48,380 --> 00:09:49,800
并不会产生一个什么

416
00:09:49,800 --> 00:09:50,560
Source Map

417
00:09:50,560 --> 00:09:52,420
而且他会产生一个Map

418
00:09:52,420 --> 00:09:53,040
但是呢

419
00:09:53,040 --> 00:09:53,720
是个独立文件

420
00:09:53,720 --> 00:09:55,780
他并不会和我们这个代码了

421
00:09:55,780 --> 00:09:56,520
观点起来

422
00:09:56,520 --> 00:09:57,060
你看效果

423
00:09:57,060 --> 00:09:57,600
我刷新

424
00:09:57,600 --> 00:09:59,300
同样

425
00:09:59,300 --> 00:10:00,240
应该有保错了

426
00:10:00,240 --> 00:10:00,460
是吧

427
00:10:00,460 --> 00:10:01,680
这里面告诉我什么

428
00:10:01,680 --> 00:10:02,640
启动第二行

429
00:10:02,640 --> 00:10:04,320
好像这东西

430
00:10:04,320 --> 00:10:05,100
是不是

431
00:10:05,100 --> 00:10:06,960
没有变化呀

432
00:10:06,960 --> 00:10:09,280
但是看没看是不是产生了这样一个sosmire

433
00:10:09,280 --> 00:10:11,580
比如说到时候我们做这种其他监控

434
00:10:11,580 --> 00:10:13,380
我们需要这样个圆码映射

435
00:10:13,380 --> 00:10:14,500
那我可以怎么样

436
00:10:14,500 --> 00:10:16,040
通过它爆出的第几行

437
00:10:16,040 --> 00:10:17,060
我来找到什么

438
00:10:17,060 --> 00:10:19,140
找到它这个相应的位置

439
00:10:19,140 --> 00:10:20,160
这样一个功能

440
00:10:20,160 --> 00:10:22,460
现在我们其实用的也不是很多

441
00:10:22,460 --> 00:10:23,600
我们就把它放在这

442
00:10:23,600 --> 00:10:25,760
同样最后还有一种

443
00:10:25,760 --> 00:10:27,000
它就是什么样的

444
00:10:27,000 --> 00:10:27,880
是这样的

445
00:10:27,880 --> 00:10:28,280
叫什么

446
00:10:28,280 --> 00:10:32,620
叫不会产生文件

447
00:10:32,620 --> 00:10:34,880
不会产生文件

448
00:10:34,880 --> 00:10:35,800
相当于怎么样

449
00:10:35,800 --> 00:10:37,200
是不是集成在什么

450
00:10:37,200 --> 00:10:39,080
集成在文件中对吧

451
00:10:39,080 --> 00:10:43,900
集成在打包后的文件中

452
00:10:43,900 --> 00:10:45,120
完了而且呢

453
00:10:45,120 --> 00:10:45,780
括号对吧

454
00:10:45,780 --> 00:10:46,860
也不会怎么样

455
00:10:46,860 --> 00:10:47,720
产生力

456
00:10:47,720 --> 00:10:49,800
完了这个写法呢

457
00:10:49,800 --> 00:10:50,400
也是一样的

458
00:10:50,400 --> 00:10:51,280
有个DV2

459
00:10:51,280 --> 00:10:52,940
完了它的写法是什么样的

460
00:10:52,940 --> 00:10:56,220
叫chip-module-evl

461
00:10:56,220 --> 00:10:56,800
对吧

462
00:10:56,800 --> 00:10:58,480
-source-map

463
00:10:58,480 --> 00:11:00,860
ok啊

464
00:11:00,860 --> 00:11:01,680
咱来试一试

465
00:11:01,680 --> 00:11:03,500
挺长的

466
00:11:03,500 --> 00:11:03,720
是不是

467
00:11:03,720 --> 00:11:04,340
相当于

468
00:11:04,340 --> 00:11:05,520
就是一个什么

469
00:11:05,520 --> 00:11:06,800
是不是简洁的

470
00:11:06,800 --> 00:11:08,220
就是不加我们那个列的

471
00:11:08,220 --> 00:11:08,780
而且呢

472
00:11:08,780 --> 00:11:09,720
要放在文件中

473
00:11:09,720 --> 00:11:10,240
而且呢

474
00:11:10,240 --> 00:11:11,080
也要产生sauce map

475
00:11:11,080 --> 00:11:12,140
就是就是什么

476
00:11:12,140 --> 00:11:12,880
就是没有列

477
00:11:12,880 --> 00:11:13,380
对吧

478
00:11:13,380 --> 00:11:14,340
那好了啊

479
00:11:14,340 --> 00:11:15,280
那咱来用一下

480
00:11:15,280 --> 00:11:16,300
run build

481
00:11:16,300 --> 00:11:19,160
这都是他的一些配置啊

482
00:11:19,160 --> 00:11:19,760
当然了

483
00:11:19,760 --> 00:11:20,360
这个东西

484
00:11:20,360 --> 00:11:21,360
我们只是演示一下

485
00:11:21,360 --> 00:11:22,580
那现在啊

486
00:11:22,580 --> 00:11:23,200
我们再刷新

487
00:11:23,200 --> 00:11:24,660
看又报错了

488
00:11:24,660 --> 00:11:24,920
是吧

489
00:11:24,920 --> 00:11:25,900
这时候报的时候

490
00:11:25,900 --> 00:11:27,820
又是那个index gsd6行

491
00:11:27,820 --> 00:11:28,540
而且呢

492
00:11:28,540 --> 00:11:29,420
打包出来的文件

493
00:11:29,420 --> 00:11:30,820
确实没有那个sauce map

494
00:11:30,820 --> 00:11:32,060
我这里呢

495
00:11:32,060 --> 00:11:32,580
我点一下

496
00:11:32,580 --> 00:11:33,620
你看啊

497
00:11:33,620 --> 00:11:35,580
它是不是只定位到第六行了

498
00:11:35,580 --> 00:11:37,540
并没有告诉我是落荷出错了

499
00:11:37,540 --> 00:11:38,840
这样的话

500
00:11:38,840 --> 00:11:39,820
我们就知道了

501
00:11:39,820 --> 00:11:41,320
原来这些东西可以干什么事

502
00:11:41,320 --> 00:11:43,000
是不是可以干调试的功能

503
00:11:43,000 --> 00:11:45,140
同样我们还可以怎么样

504
00:11:45,140 --> 00:11:47,420
到时候通过这个sauce map来反解一下

505
00:11:47,420 --> 00:11:47,700
对吧

506
00:11:47,700 --> 00:11:49,680
比如说哪行原码什么样

507
00:11:49,680 --> 00:11:50,360
哪包错了

508
00:11:50,360 --> 00:11:50,900
也可以

509
00:11:50,900 --> 00:11:53,800
我们就把这个sauce map先说在这

