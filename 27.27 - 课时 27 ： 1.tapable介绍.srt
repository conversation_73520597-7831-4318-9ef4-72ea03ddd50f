1
00:00:00,000 --> 00:00:04,280
本节我们就来讲一下webpack中的Typeable

2
00:00:04,280 --> 00:00:07,180
webpack本质上是一种实践流的机制

3
00:00:07,180 --> 00:00:11,160
它的工作流程就是将各个插件的串联起来

4
00:00:11,160 --> 00:00:12,780
这么多插件我们怎么来维护

5
00:00:12,780 --> 00:00:15,260
主要靠的就是我们的webpack中的Typeable

6
00:00:15,260 --> 00:00:18,660
Typeable其实它也是依赖于我们一种实践的机制

7
00:00:18,660 --> 00:00:21,260
其实很像我们Node中的Evance Cool

8
00:00:21,260 --> 00:00:24,980
它里面其实主要包含了我们的发布内容模式

9
00:00:24,980 --> 00:00:26,280
我们可以来看一下

10
00:00:26,280 --> 00:00:29,680
webpack里面集成了很多这样一个Typeable插件

11
00:00:29,680 --> 00:00:30,700
这个插件里面呢

12
00:00:30,700 --> 00:00:31,620
有很多方法

13
00:00:31,620 --> 00:00:33,520
比如包括它的同步的和异步的

14
00:00:33,520 --> 00:00:34,660
我们可以简单看一下

15
00:00:34,660 --> 00:00:35,500
这个Webpack的圆满

16
00:00:35,500 --> 00:00:36,620
这里呢

17
00:00:36,620 --> 00:00:38,420
我们下载了一个Webpack

18
00:00:38,420 --> 00:00:40,160
我们点看一下

19
00:00:40,160 --> 00:00:41,040
这里呢

20
00:00:41,040 --> 00:00:42,800
就包含着我们这样一个Webpack

21
00:00:42,800 --> 00:00:43,620
我找一下

22
00:00:43,620 --> 00:00:45,460
Webpack

23
00:00:45,460 --> 00:00:48,900
我点开以后呢

24
00:00:48,900 --> 00:00:49,400
我们可以看到

25
00:00:49,400 --> 00:00:50,420
往来这里呢

26
00:00:50,420 --> 00:00:52,220
我们可以点开我们的这个Lab库

27
00:00:52,220 --> 00:00:53,080
往来里面呢

28
00:00:53,080 --> 00:00:53,960
有个变异模块

29
00:00:53,960 --> 00:00:54,520
是吧

30
00:00:54,520 --> 00:00:55,720
这是它的主合性模块

31
00:00:55,720 --> 00:00:56,620
这里面啊

32
00:00:56,620 --> 00:00:58,160
它主要靠的就是我们的Typeable

33
00:00:58,160 --> 00:00:59,120
往来里面呢

34
00:00:59,120 --> 00:01:01,020
引用了我们所谓的一些钩子

35
00:01:01,020 --> 00:01:01,940
叫Sync Hook

36
00:01:01,940 --> 00:01:03,540
还有我们的这些A-Sync Hook

37
00:01:03,540 --> 00:01:05,560
就说我们这个插件

38
00:01:05,560 --> 00:01:07,780
里面包含着异步方法和同步方法

39
00:01:07,780 --> 00:01:09,600
这时候我们就要来区分开

40
00:01:09,600 --> 00:01:12,020
比如同步的使用同步钩子来创建起来

41
00:01:12,020 --> 00:01:14,300
异步我们使用异步的钩子创建起来

42
00:01:14,300 --> 00:01:15,660
这里我们就来看一下

43
00:01:15,660 --> 00:01:17,980
咱们就围照这些方法

44
00:01:17,980 --> 00:01:19,460
我们依次来应用一下

45
00:01:19,460 --> 00:01:20,680
并且实现它们的原理

46
00:01:20,680 --> 00:01:24,300
这样的话我们为后面的YPAC手写做准备

47
00:01:24,300 --> 00:01:25,380
我们就来看一下

48
00:01:25,380 --> 00:01:27,080
先去安装一下这个包

49
00:01:27,080 --> 00:01:28,120
它是一个第三方模块

50
00:01:28,120 --> 00:01:30,800
这里我们就直接安装

51
00:01:30,800 --> 00:01:32,640
进到模块里

52
00:01:32,640 --> 00:01:40,280
按好以后

53
00:01:40,280 --> 00:01:41,800
我们就来写一个小例子

54
00:01:41,800 --> 00:01:43,320
通过一个例子来看一下

55
00:01:43,320 --> 00:01:44,260
这东西怎么实现

56
00:01:44,260 --> 00:01:45,300
并且怎么应用

57
00:01:45,300 --> 00:01:47,260
里面我们可以来一个

58
00:01:47,260 --> 00:01:50,900
比如说叫start.js

59
00:01:50,900 --> 00:01:54,440
这里面我们就先说一下同步的勾子

60
00:01:54,440 --> 00:01:56,020
比如说我们有写一个应用

61
00:01:56,020 --> 00:01:56,900
或者写个功能

62
00:01:56,900 --> 00:01:58,840
那这功能呢里面可能有很多步骤

63
00:01:58,840 --> 00:01:59,800
那我们可以怎么样

64
00:01:59,800 --> 00:02:00,660
意思把它拆分开

65
00:02:00,660 --> 00:02:03,020
我们呢可以先引入我们的这个typebook

66
00:02:03,020 --> 00:02:04,680
完了里面呢

67
00:02:04,680 --> 00:02:05,580
它有一些同步的

68
00:02:05,580 --> 00:02:06,160
那这里面呢

69
00:02:06,160 --> 00:02:07,280
我先把它引进来啊

70
00:02:07,280 --> 00:02:08,520
require一个typebook

71
00:02:08,520 --> 00:02:10,060
并且呢

72
00:02:10,060 --> 00:02:11,720
它呢可以返回一个里面

73
00:02:11,720 --> 00:02:13,460
可以结构出来一个同步的勾子

74
00:02:13,460 --> 00:02:14,060
叫singhook

75
00:02:14,060 --> 00:02:15,520
完了比如说呀

76
00:02:15,520 --> 00:02:16,580
我们这里呢写个类

77
00:02:16,580 --> 00:02:17,880
比如叫class叫less

78
00:02:17,880 --> 00:02:19,900
而这里呢

79
00:02:19,900 --> 00:02:22,000
我们可以给它赋予一个contractor

80
00:02:22,000 --> 00:02:23,040
就是它自己的勾动函数

81
00:02:23,040 --> 00:02:24,240
那这里呢

82
00:02:24,240 --> 00:02:26,860
我们也可以给它再加一些自己所谓的勾子啊

83
00:02:26,900 --> 00:02:30,520
这是一个相当于自定义的对象

84
00:02:30,520 --> 00:02:32,420
我们用的时候大致是这样的

85
00:02:32,420 --> 00:02:34,340
我们可以在这里面订阅一些钩子

86
00:02:34,340 --> 00:02:35,520
订阅好以后

87
00:02:35,520 --> 00:02:38,260
完了我们可以通过new这个实例来启动这些钩子

88
00:02:38,260 --> 00:02:41,440
比如说我在这里写一个关于在国客的一个钩子

89
00:02:41,440 --> 00:02:42,880
叫rch

90
00:02:42,880 --> 00:02:45,320
往里面我可以直接用这样一个钩子

91
00:02:45,320 --> 00:02:48,280
叫new new single hook

92
00:02:48,280 --> 00:02:51,440
这里面当我们new这个钩子的时候

93
00:02:51,440 --> 00:02:53,640
到时候钩子之行肯定会传及一些参数

94
00:02:53,640 --> 00:02:55,660
这个参数它可以是可选的

95
00:02:55,660 --> 00:02:55,820
对吧

96
00:02:55,820 --> 00:02:56,260
可以不添

97
00:02:56,260 --> 00:02:58,380
但是我们可以给一个数组

98
00:02:58,380 --> 00:02:59,180
往里面告诉他

99
00:02:59,180 --> 00:03:01,600
我这里面可能到时候会穿一个参数

100
00:03:01,600 --> 00:03:02,580
这里这样

101
00:03:02,580 --> 00:03:05,120
当然了你也可以挂N个钩子

102
00:03:05,120 --> 00:03:06,120
比如说这一个不够

103
00:03:06,120 --> 00:03:06,780
我也在写

104
00:03:06,780 --> 00:03:07,620
比如说GS的

105
00:03:07,620 --> 00:03:08,680
我这里面先写一个

106
00:03:08,680 --> 00:03:10,060
完了之后

107
00:03:10,060 --> 00:03:12,000
我们肯定要有一个启动钩子的方法

108
00:03:12,000 --> 00:03:14,320
这里面我们给他写个名字叫start

109
00:03:14,320 --> 00:03:19,180
并且我们可以在这里面去创建一个L的实例

110
00:03:19,180 --> 00:03:19,460
是吧

111
00:03:19,460 --> 00:03:20,240
new lesson

112
00:03:20,240 --> 00:03:21,480
 lesson

113
00:03:21,480 --> 00:03:24,460
并且我可以调用L的start的方法

114
00:03:24,460 --> 00:03:27,700
就是启动我们的勾子对吧

115
00:03:27,700 --> 00:03:28,540
启动勾子

116
00:03:28,540 --> 00:03:29,960
但是在启动勾子之前

117
00:03:29,960 --> 00:03:31,760
你肯定要先注册一些事件

118
00:03:31,760 --> 00:03:33,080
比如说你来学习加工课

119
00:03:33,080 --> 00:03:34,440
那我们肯定需要怎么样

120
00:03:34,440 --> 00:03:36,060
是不是先放上我们的

121
00:03:36,060 --> 00:03:37,160
比如说课程的一些大概

122
00:03:37,160 --> 00:03:38,420
有node的react

123
00:03:38,420 --> 00:03:39,500
那到时候呢

124
00:03:39,500 --> 00:03:40,800
当我们启动这勾子的时候呢

125
00:03:40,800 --> 00:03:42,620
可以让这些这些方法呢

126
00:03:42,620 --> 00:03:43,060
一丝之行

127
00:03:43,060 --> 00:03:44,880
其实就是我们发布定位的模式

128
00:03:44,880 --> 00:03:45,900
那我们呢

129
00:03:45,900 --> 00:03:46,540
可以在这里呢

130
00:03:46,540 --> 00:03:47,240
有一个方法

131
00:03:47,240 --> 00:03:48,260
专门用来注册

132
00:03:48,260 --> 00:03:48,980
叫type

133
00:03:48,980 --> 00:03:50,760
可以认为对吧

134
00:03:50,760 --> 00:03:52,260
这是注册

135
00:03:52,260 --> 00:03:54,000
监听函数对吧

136
00:03:54,000 --> 00:03:54,980
减击函数

137
00:03:54,980 --> 00:03:58,160
就说我们可以在钩子上挂上一些函数

138
00:03:58,160 --> 00:03:59,760
当我们启动的时候

139
00:03:59,760 --> 00:04:02,120
可以让这些函数一次之行

140
00:04:02,120 --> 00:04:03,640
这里我们可以直接来做

141
00:04:03,640 --> 00:04:06,040
叫this.hooks

142
00:04:06,040 --> 00:04:08,000
它里面有一个.sh

143
00:04:08,000 --> 00:04:10,180
可以拿到当前类的实例

144
00:04:10,180 --> 00:04:12,760
这个实例上就有一个方法

145
00:04:12,760 --> 00:04:14,000
叫注册事件叫type

146
00:04:14,000 --> 00:04:16,740
并且它里面的参数有两个

147
00:04:16,740 --> 00:04:17,800
第一个是个名字

148
00:04:17,800 --> 00:04:18,680
第二个是回调

149
00:04:18,680 --> 00:04:20,860
在这里面可以直接写一个

150
00:04:20,860 --> 00:04:22,580
这个名字没有什么时机

151
00:04:22,580 --> 00:04:23,840
只是说它是个标识

152
00:04:23,840 --> 00:04:25,980
比如说架构课里面有很多课程部分

153
00:04:25,980 --> 00:04:28,680
那我怎么知道这个函数对应的是哪一个

154
00:04:28,680 --> 00:04:30,360
我们所谓的这个执行的名字呢

155
00:04:30,360 --> 00:04:30,580
是吧

156
00:04:30,580 --> 00:04:32,860
所以这个名字就是一个没有意义的名字

157
00:04:32,860 --> 00:04:33,460
我给大家写

158
00:04:33,460 --> 00:04:35,500
比如说珠峰里面有node课程

159
00:04:35,500 --> 00:04:37,760
并且给个回调

160
00:04:37,760 --> 00:04:39,380
完了此时呢

161
00:04:39,380 --> 00:04:41,260
这里面呢就会有一个我们所谓的事件

162
00:04:41,260 --> 00:04:41,900
一个参数

163
00:04:41,900 --> 00:04:43,080
因为我们已经规定了

164
00:04:43,080 --> 00:04:44,020
它的参数呢有一个

165
00:04:44,020 --> 00:04:45,640
那这里呢我就写上

166
00:04:45,640 --> 00:04:46,400
比如叫内

167
00:04:46,400 --> 00:04:49,080
那同样我可以再绑定一个是吧

168
00:04:49,080 --> 00:04:50,260
这里面再加一个

169
00:04:50,260 --> 00:04:52,600
那我说这个名字可以一样可以不一样

170
00:04:52,600 --> 00:04:54,140
只是用来给我们开发者

171
00:04:54,140 --> 00:04:55,900
来提供一个方便阅读

172
00:04:55,900 --> 00:04:57,260
这里我再来一个叫RACT

173
00:04:57,260 --> 00:04:59,340
现在我们就相当于

174
00:04:59,340 --> 00:05:00,880
我们调用这个方法

175
00:05:00,880 --> 00:05:02,600
叫L.type

176
00:05:02,600 --> 00:05:04,920
相当于就是注册了这两个世界

177
00:05:04,920 --> 00:05:07,000
注册这两个世界

178
00:05:07,000 --> 00:05:11,380
当我们调用START的方法的时候

179
00:05:11,380 --> 00:05:13,080
就会把对应的这两个世界怎么样

180
00:05:13,080 --> 00:05:13,780
进行执行

181
00:05:13,780 --> 00:05:15,640
其实就是一个先是什么

182
00:05:15,640 --> 00:05:16,600
先听的过程

183
00:05:16,600 --> 00:05:18,140
再是一个出发的过程

184
00:05:18,140 --> 00:05:20,060
这里我们可以一样写个参数

185
00:05:20,060 --> 00:05:21,760
Costload答应一下

186
00:05:21,760 --> 00:05:23,100
比如这个叫start

187
00:05:23,100 --> 00:05:25,760
完了里面我们就加上这样一个名字

188
00:05:25,760 --> 00:05:27,580
放在这

189
00:05:27,580 --> 00:05:28,880
比如这还写上node

190
00:05:28,880 --> 00:05:30,000
看着标识

191
00:05:30,000 --> 00:05:31,460
这加一个

192
00:05:31,460 --> 00:05:32,700
这个叫我们的名字

193
00:05:32,700 --> 00:05:33,260
这叫react

194
00:05:33,260 --> 00:05:34,740
好了

195
00:05:34,740 --> 00:05:36,000
我们start的时候干嘛

196
00:05:36,000 --> 00:05:37,780
是不是要触发这样件事执行

197
00:05:37,780 --> 00:05:38,920
非常简单

198
00:05:38,920 --> 00:05:40,440
我们可以通过这个钩子上面

199
00:05:40,440 --> 00:05:42,280
有个方法叫什么叫call

200
00:05:42,280 --> 00:05:45,820
call的意思就是让我们函数执行

201
00:05:45,820 --> 00:05:47,940
这里面少了一个叫drash

202
00:05:47,940 --> 00:05:49,980
相当于就是一个发布订阅

203
00:05:49,980 --> 00:05:50,300
对吧

204
00:05:50,300 --> 00:05:51,940
那它的原理大概是这样的

205
00:05:51,940 --> 00:05:52,860
就是它呢

206
00:05:52,860 --> 00:05:54,440
调type方法的时候呢

207
00:05:54,440 --> 00:05:57,040
会先把这两个方法注册到一个数族里

208
00:05:57,040 --> 00:05:58,900
当我们调用code的时候呢

209
00:05:58,900 --> 00:06:00,280
会让这两个方法一次之行

210
00:06:00,280 --> 00:06:01,100
那好了

211
00:06:01,100 --> 00:06:01,680
那这里呢

212
00:06:01,680 --> 00:06:02,040
我们可以

213
00:06:02,040 --> 00:06:02,300
哎

214
00:06:02,300 --> 00:06:03,920
调code的时候是不是还要传个参数啊

215
00:06:03,920 --> 00:06:04,480
叫名字

216
00:06:04,480 --> 00:06:05,180
那这里呢

217
00:06:05,180 --> 00:06:05,880
比如说谁讲的

218
00:06:05,880 --> 00:06:06,920
那可能是我讲的是吧

219
00:06:06,920 --> 00:06:07,880
然后填上我的名字

220
00:06:07,880 --> 00:06:08,840
那到时候一调用

221
00:06:08,840 --> 00:06:09,780
它会把这参数呢

222
00:06:09,780 --> 00:06:10,300
传到这里来

223
00:06:10,300 --> 00:06:11,360
那好了啊

224
00:06:11,360 --> 00:06:12,080
那这个方法呢

225
00:06:12,080 --> 00:06:12,980
其实实现很简单

226
00:06:12,980 --> 00:06:13,940
那再来看一下

227
00:06:13,940 --> 00:06:14,700
右键软

228
00:06:14,700 --> 00:06:16,400
哎

229
00:06:16,400 --> 00:06:17,080
这里呢

230
00:06:17,080 --> 00:06:17,960
我们拉上去

231
00:06:17,960 --> 00:06:19,360
这就可以看到两个结果呀

232
00:06:19,360 --> 00:06:21,080
那有了这样一个基础以后

233
00:06:21,080 --> 00:06:23,540
我们就可以很方便的实现这样的一个原理

234
00:06:23,540 --> 00:06:25,220
我在这里来个例子是吧

235
00:06:25,220 --> 00:06:27,180
来写它的对应的这个圆码怎么实现

236
00:06:27,180 --> 00:06:29,220
然后来个2.kiss

237
00:06:29,220 --> 00:06:32,400
在这里我们一样引一个

238
00:06:32,400 --> 00:06:34,240
不用引导自己来写个这样的类

239
00:06:34,240 --> 00:06:35,880
叫class叫thinkhook

240
00:06:35,880 --> 00:06:37,420
一个同步的钩子

241
00:06:37,420 --> 00:06:39,260
我们也看到了它就是同步的

242
00:06:39,260 --> 00:06:40,440
钩子

243
00:06:40,440 --> 00:06:44,400
钩子是同步的

244
00:06:44,400 --> 00:06:46,840
那好了

245
00:06:46,840 --> 00:06:47,860
那这里面的一样

246
00:06:47,860 --> 00:06:49,080
我们有几个方法

247
00:06:49,080 --> 00:06:50,160
刚才我们看到了

248
00:06:50,160 --> 00:06:51,480
创建完这个实例上

249
00:06:51,480 --> 00:06:52,400
一共有两个

250
00:06:52,400 --> 00:06:53,160
一个叫type

251
00:06:53,160 --> 00:06:54,060
还有个叫call

252
00:06:54,060 --> 00:06:56,020
我在这里就加上两个方法

253
00:06:56,020 --> 00:06:56,800
一个叫call

254
00:06:56,800 --> 00:06:59,280
一个叫type

255
00:06:59,280 --> 00:07:01,680
比如说type的方式

256
00:07:01,680 --> 00:07:03,020
就叫同步注册

257
00:07:03,020 --> 00:07:05,000
我在这里切过去

258
00:07:05,000 --> 00:07:06,460
并且我们可以怎么做

259
00:07:06,460 --> 00:07:08,260
是不是创建这样一个hook

260
00:07:08,260 --> 00:07:10,300
等于newsynchook

261
00:07:10,300 --> 00:07:12,740
它里面参数

262
00:07:12,740 --> 00:07:14,560
是一个数组里面有参数

263
00:07:14,560 --> 00:07:15,940
它可以传多个参数

264
00:07:15,940 --> 00:07:17,820
但是如果你在这里面不写

265
00:07:17,820 --> 00:07:19,580
那表示它就是没有传参数

266
00:07:19,580 --> 00:07:21,260
就是说这它是一个限制作用

267
00:07:21,260 --> 00:07:22,440
并没有什么实际意义

268
00:07:22,440 --> 00:07:23,260
那好我就放在这

269
00:07:23,260 --> 00:07:24,520
完了这里面呢

270
00:07:24,520 --> 00:07:25,880
我们在nute的时候呢

271
00:07:25,880 --> 00:07:27,280
可能会有一个过度参数

272
00:07:27,280 --> 00:07:28,680
完了这里呢

273
00:07:28,680 --> 00:07:29,960
同样会放上这个参数

274
00:07:29,960 --> 00:07:31,020
这个参数呢

275
00:07:31,020 --> 00:07:31,840
就是这数组啊

276
00:07:31,840 --> 00:07:32,340
标一下

277
00:07:32,340 --> 00:07:35,260
args就是我们这样一个数组

278
00:07:35,260 --> 00:07:37,680
里面放上一个name

279
00:07:37,680 --> 00:07:39,700
ok各式化一下

280
00:07:39,700 --> 00:07:41,560
完了当我们调type的时候

281
00:07:41,560 --> 00:07:42,240
非常简单

282
00:07:42,240 --> 00:07:43,380
就相当于是绑定实践

283
00:07:43,380 --> 00:07:43,720
是吧

284
00:07:43,720 --> 00:07:44,700
就相当于订阅嘛

285
00:07:44,700 --> 00:07:45,240
对吧

286
00:07:45,240 --> 00:07:45,840
那type呢

287
00:07:45,840 --> 00:07:46,340
我们可以在这

288
00:07:46,340 --> 00:07:47,360
我说这个名字啊

289
00:07:47,360 --> 00:07:48,420
没有任何意义

290
00:07:48,420 --> 00:07:50,260
比如说这是我们刚才写的react

291
00:07:50,260 --> 00:07:52,160
后面也有一个回调

292
00:07:52,160 --> 00:07:52,920
完了此时

293
00:07:52,920 --> 00:07:55,220
我们这里面接收到参数就两个

294
00:07:55,220 --> 00:07:57,260
第一个就是我们的一个name

295
00:07:57,260 --> 00:07:58,220
在世界名字

296
00:07:58,220 --> 00:07:59,600
第二个就是我们的一个任务

297
00:07:59,600 --> 00:07:59,820
对吧

298
00:07:59,820 --> 00:08:01,700
要做的事叫task

299
00:08:01,700 --> 00:08:05,100
那同样我在这里也写上total log

300
00:08:05,100 --> 00:08:07,160
这里面我就写上一个叫react

301
00:08:07,160 --> 00:08:07,800
完了

302
00:08:07,800 --> 00:08:09,360
并且把名字放在这

303
00:08:09,360 --> 00:08:11,360
这名字是这传的

304
00:08:11,360 --> 00:08:12,500
因为我注册的时候告诉他了

305
00:08:12,500 --> 00:08:13,920
要必须传个参数

306
00:08:13,920 --> 00:08:17,120
这里一样我们可以写两个

307
00:08:17,120 --> 00:08:17,680
注册两个

308
00:08:17,680 --> 00:08:18,580
往这里的名字

309
00:08:18,580 --> 00:08:19,420
我也可以改一下

310
00:08:19,420 --> 00:08:20,320
看到区别

311
00:08:20,320 --> 00:08:21,920
那默认情况下

312
00:08:21,920 --> 00:08:22,720
我注册的时候

313
00:08:22,720 --> 00:08:24,020
其实就是把这两个函数

314
00:08:24,020 --> 00:08:24,800
怎么样存起来

315
00:08:24,800 --> 00:08:25,680
所以说呢

316
00:08:25,680 --> 00:08:27,000
我要在这个构造函数上

317
00:08:27,000 --> 00:08:28,300
加上一个什么呢

318
00:08:28,300 --> 00:08:29,420
加上一个数组

319
00:08:29,420 --> 00:08:30,000
tasks

320
00:08:30,000 --> 00:08:30,740
完了呢

321
00:08:30,740 --> 00:08:31,340
并且呢

322
00:08:31,340 --> 00:08:32,560
我们每次注册的时候

323
00:08:32,560 --> 00:08:33,920
都放到这个数组中

324
00:08:33,920 --> 00:08:35,000
我们可以通过

325
00:08:35,000 --> 00:08:36,860
this.tas.push

326
00:08:36,860 --> 00:08:38,400
把它一个丢进去

327
00:08:38,400 --> 00:08:40,360
相当于就订阅

328
00:08:40,360 --> 00:08:42,220
那我们都ok了

329
00:08:42,220 --> 00:08:42,880
到时候呢

330
00:08:42,880 --> 00:08:43,340
我们怎么样

331
00:08:43,340 --> 00:08:44,480
是不是来真正的去开始

332
00:08:44,480 --> 00:08:45,560
学习这些东西了

333
00:08:45,560 --> 00:08:45,940
那好

334
00:08:45,940 --> 00:08:46,860
肯定是一样一样

335
00:08:46,860 --> 00:08:47,220
来学

336
00:08:47,220 --> 00:08:49,360
那我们可以通过这个hook的一个方法

337
00:08:49,360 --> 00:08:51,240
叫这个call

338
00:08:51,240 --> 00:08:53,260
那执行的时候还要告诉他

339
00:08:53,260 --> 00:08:53,440
对吧

340
00:08:53,440 --> 00:08:54,460
当前呢是谁讲的

341
00:08:54,460 --> 00:08:55,020
那这时候呢

342
00:08:55,020 --> 00:08:56,060
我需要签上我的名字

343
00:08:56,060 --> 00:08:56,300
是吧

344
00:08:56,300 --> 00:08:57,060
OK

345
00:08:57,060 --> 00:08:58,000
参数就过来了

346
00:08:58,000 --> 00:08:59,960
但这个参数我说了

347
00:08:59,960 --> 00:09:01,200
可能不止只是一个

348
00:09:01,200 --> 00:09:02,260
可能有两个有三个

349
00:09:02,260 --> 00:09:02,500
对吧

350
00:09:02,500 --> 00:09:04,080
但是如果我们要穿多个的话

351
00:09:04,080 --> 00:09:05,200
这里面你要怎么样

352
00:09:05,200 --> 00:09:05,800
配上

353
00:09:05,800 --> 00:09:06,660
就是name age

354
00:09:06,660 --> 00:09:06,920
对吧

355
00:09:06,920 --> 00:09:08,000
当然你要写上A和B

356
00:09:08,000 --> 00:09:09,420
可能不方便阅读

357
00:09:09,420 --> 00:09:10,200
所以这一般呢

358
00:09:10,200 --> 00:09:11,140
我们就会有个标识作用

359
00:09:11,140 --> 00:09:12,560
这里呢会有参数

360
00:09:12,560 --> 00:09:13,520
拿过来以后呢

361
00:09:13,520 --> 00:09:14,820
多个可能会这样写

362
00:09:14,820 --> 00:09:16,600
那我们非常简单了

363
00:09:16,600 --> 00:09:17,520
一定要这个方法

364
00:09:17,520 --> 00:09:18,920
就是让这个数租里的函数怎么样

365
00:09:18,920 --> 00:09:19,860
一次执行

366
00:09:19,860 --> 00:09:21,740
那我们就可以得到这样一个结论

367
00:09:21,740 --> 00:09:22,080
是吧

368
00:09:22,080 --> 00:09:25,000
叫this.task.什么呢

369
00:09:25,000 --> 00:09:26,280
这个for each

370
00:09:26,280 --> 00:09:28,160
我料里面呢

371
00:09:28,160 --> 00:09:30,660
拿到我们每一个所谓的任务

372
00:09:30,660 --> 00:09:31,060
task

373
00:09:31,060 --> 00:09:32,900
也就是我们这个每一个函数

374
00:09:32,900 --> 00:09:34,520
我让这个函数干嘛呢

375
00:09:34,520 --> 00:09:35,420
直接执行就好了

376
00:09:35,420 --> 00:09:37,920
执行把我们这个参数传去

377
00:09:37,920 --> 00:09:39,300
这样呢

378
00:09:39,300 --> 00:09:41,000
我们就实现了这样一个同步的钩子

379
00:09:41,000 --> 00:09:42,720
那我们来看一下效果啊

380
00:09:42,720 --> 00:09:43,560
邮件揉摆一下

381
00:09:43,560 --> 00:09:44,520
好了

382
00:09:44,520 --> 00:09:44,960
此时呢

383
00:09:44,960 --> 00:09:45,680
出来的结果就是

384
00:09:45,680 --> 00:09:46,740
OK了

385
00:09:46,740 --> 00:09:49,580
我们这样一个方法就实现了

