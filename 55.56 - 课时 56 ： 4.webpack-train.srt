1
00:00:00,000 --> 00:00:05,600
这个MD可以给我吗

2
00:00:05,600 --> 00:00:07,420
可以可以可以啊

3
00:00:07,420 --> 00:00:09,360
这个对

4
00:00:09,360 --> 00:00:10,300
录屏了已经

5
00:00:10,300 --> 00:00:12,360
放化生油

6
00:00:12,360 --> 00:00:14,860
你不放这个咖啡可乐呢

7
00:00:14,860 --> 00:00:15,540
是吧

8
00:00:15,540 --> 00:00:17,860
录屏了啊

9
00:00:17,860 --> 00:00:18,800
MD给啊对

10
00:00:18,800 --> 00:00:20,960
下个给大家下个会给的啊

11
00:00:20,960 --> 00:00:21,360
会给的

12
00:00:21,360 --> 00:00:24,200
今天可能是到11点半左右吧

13
00:00:24,200 --> 00:00:26,300
现在很幸福啊

14
00:00:26,300 --> 00:00:27,640
一个男生一个女生啊

15
00:00:27,640 --> 00:00:30,840
4-5个小时

16
00:00:30,840 --> 00:00:32,160
12点啊

17
00:00:32,160 --> 00:00:34,500
这个我尽量快点啊

18
00:00:34,500 --> 00:00:35,620
尽量快点啊

19
00:00:35,620 --> 00:00:38,120
好咱们来看啊

20
00:00:38,120 --> 00:00:39,880
咱们先今天要讲第一个环节

21
00:00:39,880 --> 00:00:41,640
讲一下WiPAC的个圆满流程

22
00:00:41,640 --> 00:00:44,960
首先啊咱们看一下这个文章啊

23
00:00:44,960 --> 00:00:46,500
他写的这个很经典对吧

24
00:00:46,500 --> 00:00:46,980
我们来看一下

25
00:00:46,980 --> 00:00:48,440
就是WiPAC的编写流程这样的

26
00:00:48,440 --> 00:00:49,000
第一步呢

27
00:00:49,000 --> 00:00:50,760
叫出发参数

28
00:00:50,760 --> 00:00:53,420
就是从配置文件

29
00:00:53,420 --> 00:00:54,500
什么要配置文件啊

30
00:00:54,500 --> 00:00:55,680
我们建个配置文件啊

31
00:00:55,680 --> 00:00:57,100
你让我们建个配置文件叫WiPAC

32
00:00:57,100 --> 00:01:00,720
config.js

33
00:01:00,720 --> 00:01:02,860
然后里面呢我们建一个

34
00:01:02,860 --> 00:01:05,260
先导入我们的pass模块

35
00:01:05,260 --> 00:01:12,240
好 然后adport

36
00:01:12,240 --> 00:01:14,280
导出一个对象吧

37
00:01:14,280 --> 00:01:15,680
module.adport

38
00:01:15,680 --> 00:01:19,200
等于一个对象

39
00:01:19,200 --> 00:01:21,200
里面有什么入口entry

40
00:01:21,200 --> 00:01:24,480
我们的.src.intern.js

41
00:01:24,480 --> 00:01:24,820
是吧

42
00:01:24,820 --> 00:01:27,020
好

43
00:01:27,020 --> 00:01:28,300
然后我们的输出output

44
00:01:28,300 --> 00:01:33,680
还有pass 指的输入入境 对吧 就是我们这个pass.resolve

45
00:01:33,680 --> 00:01:36,740
我们的blname 下边的什么呀

46
00:01:36,740 --> 00:01:38,020
list目录

47
00:01:38,020 --> 00:01:42,640
还有我们这个file name 文件名

48
00:01:42,640 --> 00:01:44,940
咱们可以给个写死的bundle.js

49
00:01:44,940 --> 00:01:48,780
bundle.js

50
00:01:48,780 --> 00:01:54,420
然后呢 还有什么module 就是模块的配置 是吧 里面有rules 我们的规则

51
00:01:55,180 --> 00:01:58,220
另外还有 plug-ins 数组

52
00:01:58,220 --> 00:01:59,420
对吧 是个拆新数组

53
00:01:59,420 --> 00:02:01,880
好 这叫啥

54
00:02:01,880 --> 00:02:04,720
配置文件 对吧 一般会给mode 等于什么 development

55
00:02:04,720 --> 00:02:07,420
开发模式

56
00:02:07,420 --> 00:02:10,020
还有什么 还有一个叫

57
00:02:10,020 --> 00:02:11,980
mode 开发模式

58
00:02:11,980 --> 00:02:13,620
还有一个什么 dv2

59
00:02:13,620 --> 00:02:18,220
叫什么叫 none 默认给个none

60
00:02:18,220 --> 00:02:23,620
他默认的EVL 如果没有加none的话

61
00:02:23,620 --> 00:02:24,420
它是EVL

62
00:02:24,420 --> 00:02:26,260
它会把原码用EVL包起来

63
00:02:26,260 --> 00:02:27,000
是不是啊

64
00:02:27,000 --> 00:02:27,560
不太好看

65
00:02:27,560 --> 00:02:28,620
所以我把它的文章用开了

66
00:02:28,620 --> 00:02:29,520
好

67
00:02:29,520 --> 00:02:31,520
这叫什么叫配置文件啊

68
00:02:31,520 --> 00:02:33,980
另外什么叫这个面量参数呢

69
00:02:33,980 --> 00:02:34,320
你看啊

70
00:02:34,320 --> 00:02:34,680
是这样的

71
00:02:34,680 --> 00:02:36,380
就说我们在执行命令的时候啊

72
00:02:36,380 --> 00:02:38,060
比如说你看我写个build的命令

73
00:02:38,060 --> 00:02:39,980
这可以执行wifi的命令

74
00:02:39,980 --> 00:02:41,560
对吧

75
00:02:41,560 --> 00:02:45,220
那么执行他的时候你会发现我可以怎么样

76
00:02:45,220 --> 00:02:46,720
我是不是可以去这个

77
00:02:46,720 --> 00:02:48,120
执行参数啊

78
00:02:48,120 --> 00:02:49,560
比如刚刚因为对吧

79
00:02:49,560 --> 00:02:50,560
执行我们的这个

80
00:02:50,560 --> 00:02:51,880
什么呀

81
00:02:51,880 --> 00:02:53,260
我们在这面量参数是不是啊

82
00:02:53,260 --> 00:02:53,940
开发模式

83
00:02:53,940 --> 00:02:54,780
它也通过什么

84
00:02:54,780 --> 00:02:55,620
通过我们的杠杠

85
00:02:55,620 --> 00:02:57,180
config指定什么

86
00:02:57,180 --> 00:02:58,100
我们这个配置文件

87
00:02:58,100 --> 00:02:58,660
对不对

88
00:02:58,660 --> 00:02:59,660
那么这叫什么

89
00:02:59,660 --> 00:03:01,100
这叫命量参数啊

90
00:03:01,100 --> 00:03:01,580
这个叫什么

91
00:03:01,580 --> 00:03:03,700
叫配置的参数啊

92
00:03:03,700 --> 00:03:04,500
在外部会怎么样

93
00:03:04,500 --> 00:03:05,380
会合并它吗

94
00:03:05,380 --> 00:03:06,500
会把它进行合并

95
00:03:06,500 --> 00:03:08,580
得到最终参数啊

96
00:03:08,580 --> 00:03:09,340
然后第二步呢

97
00:03:09,340 --> 00:03:10,580
开始编译啊

98
00:03:10,580 --> 00:03:12,300
从上一步得到的这个参数啊

99
00:03:12,300 --> 00:03:14,700
触手化我们可能拍到对象啊

100
00:03:14,700 --> 00:03:17,100
然后加在所有的配置的插件啊

101
00:03:17,100 --> 00:03:18,180
就是说你看

102
00:03:18,180 --> 00:03:19,020
是不是这个插件啊

103
00:03:19,020 --> 00:03:21,260
这 plugin吧

104
00:03:21,260 --> 00:03:21,540
对吧

105
00:03:21,540 --> 00:03:23,260
你可以逆很多插件过来啊

106
00:03:23,260 --> 00:03:25,420
然后呢

107
00:03:25,420 --> 00:03:26,660
执行对象的run方法

108
00:03:26,660 --> 00:03:28,900
执行我们compiler对象的run方法

109
00:03:28,900 --> 00:03:29,600
开始编译

110
00:03:29,600 --> 00:03:30,880
然后找到入口

111
00:03:30,880 --> 00:03:32,380
然后再找到入口文件

112
00:03:32,380 --> 00:03:33,880
然后呢

113
00:03:33,880 --> 00:03:34,860
从入口文件出发

114
00:03:34,860 --> 00:03:36,300
读取入口文件内容

115
00:03:36,300 --> 00:03:37,020
比如找到谁

116
00:03:37,020 --> 00:03:37,860
找到我们这个

117
00:03:37,860 --> 00:03:40,920
isdm下面的index

118
00:03:40,920 --> 00:03:42,440
这是啥

119
00:03:42,440 --> 00:03:44,000
是不是入口文件啊

120
00:03:44,000 --> 00:03:44,340
对不对

121
00:03:44,340 --> 00:03:45,300
然后呢

122
00:03:45,300 --> 00:03:47,440
可以用可以去引入下其他模块吧

123
00:03:47,440 --> 00:03:49,380
ledtitle等于一个require

124
00:03:49,380 --> 00:03:51,340
叫.title

125
00:03:53,260 --> 00:03:56,920
好 然后把这个拿过来 对吧 只要键下这个文件啊

126
00:03:56,920 --> 00:04:06,060
它是yes 是吧 好 你看啊 把你进来吧 来个module.atpost

127
00:04:06,060 --> 00:04:10,460
等于什么 等于一个叫tattle

128
00:04:10,460 --> 00:04:16,760
然后你看我这儿啊 我这儿导入 title 是不是 打印一下吧 跟昨天一样啊

129
00:04:16,760 --> 00:04:17,380
 打印一下就可以了

130
00:04:17,380 --> 00:04:22,480
就说你看 先去读学什么呀 入口文件 谁 应该yes

131
00:04:23,080 --> 00:04:28,840
然后呢 从里边进行分析 找到他依赖的模块 谁 title.js 找到他之后怎么样

132
00:04:28,840 --> 00:04:30,240
 去加载他的title

133
00:04:30,240 --> 00:04:33,960
然后那地规编译title的模块 然后最后就完成了 是不是

134
00:04:33,960 --> 00:04:37,800
所以你看这个编译模块

135
00:04:37,800 --> 00:04:44,030
然后那个从入文件出发 地规定用所有pate loader进行对宝宝课影什么 进行编译

136
00:04:44,030 --> 00:04:44,240
 是吧

137
00:04:44,240 --> 00:04:50,680
然后找到模块的依赖的模块 在地规本步骤 直到所有依赖模块呢 都得到了处理

138
00:04:50,680 --> 00:04:50,880
 是吧

139
00:04:52,480 --> 00:04:53,480
然后最后完成编译

140
00:04:53,480 --> 00:04:56,320
然后得到了每个模块谁依赖关系

141
00:04:56,320 --> 00:04:57,440
就说谁依赖谁就知道了

142
00:04:57,440 --> 00:05:00,120
知道之后呢 我们要根据什么样

143
00:05:00,120 --> 00:05:01,880
如果我们的入口模块的关系

144
00:05:01,880 --> 00:05:03,360
去组装一个一个的tunk

145
00:05:03,360 --> 00:05:05,480
什么叫tunk呀 叫代码块

146
00:05:05,480 --> 00:05:09,000
就是一个代码块呢 可能会包含多个模块

147
00:05:09,000 --> 00:05:11,880
然后把tunk转成一个个文件

148
00:05:11,880 --> 00:05:13,440
写到我们的输出列表里面去

149
00:05:13,440 --> 00:05:15,600
最后呢 我们可以输出内容 是吧

150
00:05:15,600 --> 00:05:17,760
那刚确定输入内容之后呢

151
00:05:17,760 --> 00:05:20,320
可以根据什么我们配置的这个路径和文件名

152
00:05:20,320 --> 00:05:22,480
把文件内容拿起到文件系统里面去就可以了

153
00:05:22,480 --> 00:05:25,380
另外在这整个过程中呢

154
00:05:25,380 --> 00:05:26,180
我们可能会什么

155
00:05:26,180 --> 00:05:27,320
在特定时间点

156
00:05:27,320 --> 00:05:28,780
监定监控什么样

157
00:05:28,780 --> 00:05:30,120
监定特定事件啊

158
00:05:30,120 --> 00:05:31,520
发生特定事情啊

159
00:05:31,520 --> 00:05:32,820
执行什么特定逻辑

160
00:05:32,820 --> 00:05:33,960
就这样的

161
00:05:33,960 --> 00:05:35,260
好这张图我们来看一下

162
00:05:35,260 --> 00:05:38,380
看这个图你看

163
00:05:38,380 --> 00:05:40,320
是一个第一步要处理化是不是啊

164
00:05:40,320 --> 00:05:41,920
第二个单开始编译

165
00:05:41,920 --> 00:05:42,460
然后呢

166
00:05:42,460 --> 00:05:43,460
编译这个模块

167
00:05:43,460 --> 00:05:44,120
什么时候化

168
00:05:44,120 --> 00:05:45,280
入口模块

169
00:05:45,280 --> 00:05:45,680
然后呢

170
00:05:45,680 --> 00:05:48,320
找到他依赖的模块进行编译啊

171
00:05:48,320 --> 00:05:50,080
就是他依赖谁谁也要编译

172
00:05:50,320 --> 00:05:51,680
那么层体变成之后呢

173
00:05:51,680 --> 00:05:52,760
会完成编译

174
00:05:52,760 --> 00:05:53,760
然后你编译完成

175
00:05:53,760 --> 00:05:55,200
然后呢

176
00:05:55,200 --> 00:05:56,540
经过什么上述的步骤

177
00:05:56,540 --> 00:05:58,320
得到了loader之间的依赖关系

178
00:05:58,320 --> 00:06:00,340
会用loader经处理

179
00:06:00,340 --> 00:06:01,340
得到它依赖关系

180
00:06:01,340 --> 00:06:02,180
然后呢

181
00:06:02,180 --> 00:06:02,940
输入我们的资源

182
00:06:02,940 --> 00:06:03,720
最后写入文件

183
00:06:03,720 --> 00:06:04,820
这是它的整体流程

184
00:06:04,820 --> 00:06:07,960
这个我们没关系

185
00:06:07,960 --> 00:06:09,700
我们后面会讲它的这个

186
00:06:09,700 --> 00:06:10,680
收写实现

187
00:06:10,680 --> 00:06:11,920
所以说我们先这样

188
00:06:11,920 --> 00:06:13,220
我们先给大家说一下

189
00:06:13,220 --> 00:06:14,400
怎么调试我们的wapack

190
00:06:14,400 --> 00:06:16,500
因为比如说你要学习圆码的话

191
00:06:16,500 --> 00:06:17,780
肯定要学习调试圆码

192
00:06:17,780 --> 00:06:18,100
对不对

193
00:06:18,100 --> 00:06:19,220
那怎么调试呢

194
00:06:19,220 --> 00:06:19,920
我们可以这样做

195
00:06:19,920 --> 00:06:20,440
比如说

196
00:06:20,440 --> 00:06:23,380
你看我们刚才我们去

197
00:06:23,380 --> 00:06:25,240
我们现在啊装一些模块

198
00:06:25,240 --> 00:06:26,360
装什么呢

199
00:06:26,360 --> 00:06:27,260
我们装一下啊

200
00:06:27,260 --> 00:06:29,300
比如装一下我们这个cpi install

201
00:06:29,300 --> 00:06:30,200
我们的webpack

202
00:06:30,200 --> 00:06:33,500
webpack-4y-d

203
00:06:33,500 --> 00:06:35,020
装一下依赖的模块

204
00:06:35,020 --> 00:06:45,940
好那怎么调试呢

205
00:06:45,940 --> 00:06:46,860
你看这样做啊

206
00:06:46,860 --> 00:06:47,760
就是说我们来说一下

207
00:06:47,760 --> 00:06:49,680
咱们这个webpack的入口面在哪

208
00:06:49,680 --> 00:06:51,680
在这,你看,打开NodeModules

209
00:06:51,680 --> 00:06:55,680
然后找到什么,找到咱们的WebHackCy

210
00:06:55,680 --> 00:06:57,680
WebHackCy

211
00:06:57,680 --> 00:07:01,680
是这个吧,然后打开它的bin目录

212
00:07:01,680 --> 00:07:03,680
找到它上面,cly.js,是不是

213
00:07:03,680 --> 00:07:07,680
那打开之后呢,你在这写个debug

214
00:07:07,680 --> 00:07:09,680
刚刚这写个debug就可以了

215
00:07:09,680 --> 00:07:11,680
或者写个debug

216
00:07:11,680 --> 00:07:13,680
就通过它又可以什么

217
00:07:13,680 --> 00:07:15,680
可以进行调试了,是不是

218
00:07:15,680 --> 00:07:17,680
我们先来执行命令,比方我们执行一个什么

219
00:07:17,680 --> 00:07:19,380
npm

220
00:07:19,680 --> 00:07:24,000
npm wrong build

221
00:07:24,000 --> 00:07:35,300
他说什么呀 没有参数 是不是 我们要给参数啊 比如说找到我们这个排阶层

222
00:07:35,300 --> 00:07:39,640
知道吧 然后呢 把这个参数 哪儿

223
00:07:39,640 --> 00:07:46,920
应该找我们自己的这个脚本啊 你看 这是没有给啊 去掉就可以了 是吧

224
00:07:49,680 --> 00:07:53,780
你们有问题随时提啊

225
00:07:53,780 --> 00:08:01,460
你看打包出来了吧 打包出来了一个帮助的解释是吧

226
00:08:01,460 --> 00:08:05,040
好那在这在这啊在地点模下

227
00:08:05,040 --> 00:08:11,960
那么怎么调试他呢 你看啊 我刚才说了入口语言谁入口语言就是我们这个cr的解释是不是啊

228
00:08:11,960 --> 00:08:14,520
cr的解释

229
00:08:14,520 --> 00:08:15,800
就他吧

230
00:08:15,800 --> 00:08:17,320
在这写的第八个

231
00:08:17,580 --> 00:08:20,680
然后呢 我可以怎么样 你看 选中这个文件啊 选中文件

232
00:08:20,680 --> 00:08:26,760
然后呢 我左边呢 选这个 叫什么叫 debug 然后呢 点这个什么 启动 点它

233
00:08:26,760 --> 00:08:29,140
看 选了nodejs 就可以启动了

234
00:08:29,140 --> 00:08:37,980
看什么进来了 今天终于可以一步步调试了 看是不是卡卡卡被调试了

235
00:08:37,980 --> 00:08:39,260
 对不对就可以调试了

236
00:08:39,260 --> 00:08:44,340
当然我们现在呀 我们先不管啊 咱们走咱们等到后边的话 咱们一边调试

237
00:08:44,340 --> 00:08:45,740
 一边写咱们自己的wip

238
00:08:46,140 --> 00:08:48,240
跟他一步自然对上对不对好吧

239
00:08:48,240 --> 00:08:50,980
不我们现在呀我们先知道怎么调试就可以了是不是

240
00:08:50,980 --> 00:08:56,740
好另外呢我给他说一个东西啊叫state对象

241
00:08:56,740 --> 00:08:59,200
这个对象叫什么叫状态对象啊

242
00:08:59,200 --> 00:09:01,540
就是你在这官方编辑之后啊会得到这样一个对象

243
00:09:01,540 --> 00:09:04,580
啊在哪呢你看啊给他看一下这个对象啊

244
00:09:04,580 --> 00:09:06,880
找一下

245
00:09:06,880 --> 00:09:10,840
然后我们这没有啊他在这个文件里边

246
00:09:10,840 --> 00:09:15,440
注意啊我给大家看原本的时候大家可能会这个

247
00:09:15,700 --> 00:09:20,020
看不懂 没关系 没关系 不是让你看懂的 是不是 只要你有印象就开了

248
00:09:20,020 --> 00:09:21,840
 是吧 就有一个印象 就说知道我在

249
00:09:21,840 --> 00:09:26,310
我这个证明什么 证明我写的原本跟它是一样的 是不是 就这个意思啊

250
00:09:26,310 --> 00:09:29,020
 不是说让大家能看到你看懂啊 别着急啊

251
00:09:29,020 --> 00:09:32,340
好 比如说你看我这个如果说我进来之后

252
00:09:32,340 --> 00:09:41,560
咱们进去看看

253
00:09:41,560 --> 00:09:43,980
这有一个state

254
00:09:45,700 --> 00:09:47,960
看这是不是有个sled对象啊

255
00:09:47,960 --> 00:09:49,020
它是什么东西呢

256
00:09:49,020 --> 00:09:49,920
它是一个对象

257
00:09:49,920 --> 00:09:51,240
出来描述什么呀

258
00:09:51,240 --> 00:09:54,020
描述咱们这个产出后的资源的一个内容

259
00:09:54,020 --> 00:09:55,480
就是我到底我自己打包

260
00:09:55,480 --> 00:09:56,540
打包出来什么内容

261
00:09:56,540 --> 00:09:57,740
是不是啊

262
00:09:57,740 --> 00:09:58,560
又哪些模块

263
00:09:58,560 --> 00:09:59,980
又哪些代码块

264
00:09:59,980 --> 00:10:01,200
又哪些什么资源

265
00:10:01,200 --> 00:10:02,480
又哪些文件啊

266
00:10:02,480 --> 00:10:03,360
有这些东西啊

267
00:10:03,360 --> 00:10:04,680
呃

268
00:10:04,680 --> 00:10:07,120
好

269
00:10:07,120 --> 00:10:08,420
咱们怎么看这个对象呢

270
00:10:08,420 --> 00:10:09,080
你看我这样看

271
00:10:09,080 --> 00:10:09,740
你看啊

272
00:10:09,740 --> 00:10:10,740
它启动这个wafi啊

273
00:10:10,740 --> 00:10:11,320
还有种方式

274
00:10:11,320 --> 00:10:11,920
这样启动的

275
00:10:11,920 --> 00:10:12,720
你看啊

276
00:10:12,720 --> 00:10:14,740
回到这个目录里边来

277
00:10:14,740 --> 00:10:15,200
你看啊

278
00:10:15,200 --> 00:10:15,920
我建一个文件

279
00:10:15,920 --> 00:10:17,480
我建一个文件

280
00:10:17,480 --> 00:10:20,040
我在这建个文件

281
00:10:20,040 --> 00:10:23,060
怎么叫ci.js

282
00:10:23,060 --> 00:10:24,880
然后我希望什么

283
00:10:24,880 --> 00:10:26,080
希望通过它什么样

284
00:10:26,080 --> 00:10:27,560
去进行启动我们的打包命令

285
00:10:27,560 --> 00:10:28,960
是不是通过它来打包

286
00:10:28,960 --> 00:10:30,060
怎么做呢

287
00:10:30,060 --> 00:10:30,900
你看我建一个什么

288
00:10:30,900 --> 00:10:32,600
建一个这个先去引入一个模块

289
00:10:32,600 --> 00:10:32,960
ypack

290
00:10:32,960 --> 00:10:34,740
requireypack

291
00:10:34,740 --> 00:10:38,800
这个呢是我们原生的模块

292
00:10:38,800 --> 00:10:39,780
系统模块

293
00:10:39,780 --> 00:10:42,160
然后呢我们去拿到我们的这个选项

294
00:10:42,160 --> 00:10:43,540
叫ypack option

295
00:10:43,540 --> 00:10:45,440
等一个require

296
00:10:45,440 --> 00:10:48,580
叫.webpackoption

297
00:10:48,580 --> 00:10:50,360
拿到咱们的选项

298
00:10:50,360 --> 00:10:52,040
然后呢我们拿到一个什么

299
00:10:52,040 --> 00:10:54,040
compiler是变运对象啊

300
00:10:54,040 --> 00:10:54,480
compiler

301
00:10:54,480 --> 00:10:57,900
等于什么等于这个函数之应的结果

302
00:10:57,900 --> 00:10:59,740
它反复的是个函数啊可以执行

303
00:10:59,740 --> 00:11:02,560
然后往里边传入我们的webpackoptions

304
00:11:02,560 --> 00:11:04,220
这个同学说了

305
00:11:04,220 --> 00:11:05,840
说你这个怎么知道的呀

306
00:11:05,840 --> 00:11:07,820
对吧不是看原本看的是不是

307
00:11:07,820 --> 00:11:09,380
那怎么看原本看的呀

308
00:11:09,380 --> 00:11:11,640
咱们回顾一下啊你看比如刚才那个地方

309
00:11:11,640 --> 00:11:16,220
还是回到这个CY的椰子啊

310
00:11:16,220 --> 00:11:17,020
回到CY的椰子

311
00:11:17,020 --> 00:11:19,360
拿过来

312
00:11:19,360 --> 00:11:20,780
找到5IPACCY

313
00:11:20,780 --> 00:11:23,820
5IPACCY

314
00:11:23,820 --> 00:11:26,120
这个吧

315
00:11:26,120 --> 00:11:27,580
然后呢

316
00:11:27,580 --> 00:11:28,860
你看什么建筑物件啊

317
00:11:28,860 --> 00:11:29,460
看我在这

318
00:11:29,460 --> 00:11:31,280
我这启动要调试模式啊

319
00:11:31,280 --> 00:11:32,500
你看他怎么走的

320
00:11:32,500 --> 00:11:32,980
我们看一看

321
00:11:32,980 --> 00:11:36,400
我先简单说一说啊

322
00:11:36,400 --> 00:11:37,320
你看你上来之后他在干嘛

323
00:11:37,320 --> 00:11:37,920
第一步怎么样

324
00:11:37,920 --> 00:11:38,780
他去先什么

325
00:11:38,780 --> 00:11:40,600
先去一步走啊

326
00:11:40,600 --> 00:11:46,300
这是 这是优先起 优先使用内部 我们这个内 本地安装的什么样

327
00:11:46,300 --> 00:11:47,100
WiPAC CY

328
00:11:47,100 --> 00:11:49,500
就是这个意思 不过我们先不看它了啊

329
00:11:49,500 --> 00:11:51,300
我们先往下看 看重点啊

330
00:11:51,300 --> 00:11:53,300
这刚才改 这都在处理参数啊

331
00:11:53,300 --> 00:11:56,800
改 都在处理参数

332
00:11:56,800 --> 00:11:59,800
那处理完成之后啊 它会走到哪 走到这啊

333
00:11:59,800 --> 00:12:00,800
你看这有一个

334
00:12:00,800 --> 00:12:04,900
嗯 往下翻

335
00:12:04,900 --> 00:12:07,000
这都是处理参数的是不是啊

336
00:12:07,000 --> 00:12:08,800
不用管 不用管 走 不用管

337
00:12:08,800 --> 00:12:10,500
重点看这你看

338
00:12:10,500 --> 00:12:12,280
这是不是引入一个WIPAC

339
00:12:12,280 --> 00:12:13,640
Require WIPAC

340
00:12:13,640 --> 00:12:14,900
WIPAC是我们的核心模块

341
00:12:14,900 --> 00:12:16,840
WIPAC会引入WIPAC

342
00:12:16,840 --> 00:12:18,040
我们走过来

343
00:12:18,040 --> 00:12:22,860
进来了吧

344
00:12:22,860 --> 00:12:23,740
你看是不是进来了

345
00:12:23,740 --> 00:12:25,680
你看我第一步怎么要引入WIPAC

346
00:12:25,680 --> 00:12:26,940
第二步怎么样

347
00:12:26,940 --> 00:12:28,660
你看我执行这个函数

348
00:12:28,660 --> 00:12:30,780
然后把option传进去

349
00:12:30,780 --> 00:12:32,160
option什么意思

350
00:12:32,160 --> 00:12:33,820
就咱们参数传进去

351
00:12:33,820 --> 00:12:35,280
传进去之后拿到反回什么

352
00:12:35,280 --> 00:12:35,940
compiler

353
00:12:35,940 --> 00:12:37,040
什么叫compiler

354
00:12:37,040 --> 00:12:38,780
代表本次编译对象

355
00:12:38,780 --> 00:12:40,440
就是一个一次这个编译

356
00:12:40,440 --> 00:12:41,700
是会有一个compiler

357
00:12:41,700 --> 00:12:43,260
就像大管家一样

358
00:12:43,260 --> 00:12:43,860
它只有一个

359
00:12:43,860 --> 00:12:45,640
好 你看往下走

360
00:12:45,640 --> 00:12:47,020
往下走它会干嘛呢

361
00:12:47,020 --> 00:12:48,520
它会其实它会开始什么

362
00:12:48,520 --> 00:12:49,860
开始run 运行

363
00:12:49,860 --> 00:12:53,180
看是不是开始启动

364
00:12:53,180 --> 00:12:54,680
我们总结一下

365
00:12:54,680 --> 00:12:55,820
总结一下它的核心逻辑

366
00:12:55,820 --> 00:12:56,720
第一步呢

367
00:12:56,720 --> 00:12:57,440
我们引入什么

368
00:12:57,440 --> 00:12:58,600
引入我们这个WiPad模块

369
00:12:58,600 --> 00:12:59,400
第二步呢

370
00:12:59,400 --> 00:12:59,940
执行的模块

371
00:12:59,940 --> 00:13:00,800
传入我们的对象

372
00:13:00,800 --> 00:13:02,080
对吧 得到一个compiler

373
00:13:02,080 --> 00:13:03,580
然后第三步怎么样

374
00:13:03,580 --> 00:13:05,080
是不是执行它的run方法

375
00:13:05,080 --> 00:13:05,640
就已经启动

376
00:13:05,640 --> 00:13:07,280
是不是启动

377
00:13:07,280 --> 00:13:09,420
然后这里面传个回调

378
00:13:09,420 --> 00:13:10,240
error state

379
00:13:10,240 --> 00:13:11,640
I don't want this错误对象

380
00:13:11,640 --> 00:13:12,900
发生错误的话

381
00:13:12,900 --> 00:13:13,400
它有个值

382
00:13:13,400 --> 00:13:14,500
C呢是什么

383
00:13:14,500 --> 00:13:15,740
是一个描述对象

384
00:13:15,740 --> 00:13:17,240
描述这是第一次什么

385
00:13:17,240 --> 00:13:18,020
编译的结果的

386
00:13:18,020 --> 00:13:18,200
是不是

387
00:13:18,200 --> 00:13:18,880
好

388
00:13:18,880 --> 00:13:19,460
我们按照这个流程

389
00:13:19,460 --> 00:13:19,980
自己写一下

390
00:13:19,980 --> 00:13:21,420
你看我第一步怎么样

391
00:13:21,420 --> 00:13:22,260
我们引入webpack

392
00:13:22,260 --> 00:13:23,440
第二步怎么样

393
00:13:23,440 --> 00:13:24,040
我引入什么

394
00:13:24,040 --> 00:13:24,980
这个config文件

395
00:13:24,980 --> 00:13:26,380
是不是这个配置文件

396
00:13:26,380 --> 00:13:27,020
就它呀

397
00:13:27,020 --> 00:13:27,580
对不对

398
00:13:27,580 --> 00:13:29,060
这应该是

399
00:13:29,060 --> 00:13:29,260
对

400
00:13:29,260 --> 00:13:29,860
点杠吧

401
00:13:29,860 --> 00:13:31,000
然后呢

402
00:13:31,000 --> 00:13:32,100
把这个配置文件

403
00:13:32,100 --> 00:13:33,440
传给了我们这个webpack

404
00:13:33,440 --> 00:13:34,940
然后返回compiler

405
00:13:34,940 --> 00:13:35,600
对吧

406
00:13:35,600 --> 00:13:37,420
然后调compiler的什么呀

407
00:13:37,420 --> 00:13:38,200
入网方法

408
00:13:38,200 --> 00:13:38,680
开始编译

409
00:13:38,680 --> 00:13:42,540
是不是开始执行编译

410
00:13:42,540 --> 00:13:44,140
然后里面传出回调

411
00:13:44,140 --> 00:13:45,720
拿到一个error和state

412
00:13:45,720 --> 00:13:48,560
我们打一下state

413
00:13:48,560 --> 00:13:50,960
看它里面是什么东西

414
00:13:50,960 --> 00:13:53,140
把这个注掉了

415
00:13:53,140 --> 00:13:53,820
关掉了

416
00:13:53,820 --> 00:13:54,640
然后我们启动它

417
00:13:54,640 --> 00:13:54,900
是不是

418
00:13:54,900 --> 00:13:55,980
邮件运行

419
00:13:55,980 --> 00:13:59,000
看到了吧

420
00:13:59,000 --> 00:14:01,080
这个地方感觉

421
00:14:01,080 --> 00:14:02,260
你看东西很多很多

422
00:14:02,260 --> 00:14:02,680
看到了吧

423
00:14:02,680 --> 00:14:03,240
是不是很多

424
00:14:03,240 --> 00:14:05,740
看

425
00:14:05,740 --> 00:14:06,940
我再也考出来

426
00:14:06,940 --> 00:14:08,560
给它看一下

427
00:14:08,680 --> 00:14:17,160
我新建一个杰森文件

428
00:14:17,160 --> 00:14:18,600
杰森文件

429
00:14:18,600 --> 00:14:20,520
杰森

430
00:14:20,520 --> 00:14:22,460
没有是吧

431
00:14:22,460 --> 00:14:24,540
没有的话就算

432
00:14:24,540 --> 00:14:26,060
就他吧

433
00:14:26,060 --> 00:14:27,680
你看我建一个杰森文件就可以了

434
00:14:27,680 --> 00:14:29,200
你看他是一个对象

435
00:14:29,200 --> 00:14:30,340
他里面有很多属性

436
00:14:30,340 --> 00:14:32,280
有什么有比如说有completion

437
00:14:32,280 --> 00:14:34,220
代表本次的编译

438
00:14:34,220 --> 00:14:36,140
还有一堆什么

439
00:14:36,140 --> 00:14:37,400
host一堆勾子

440
00:14:37,400 --> 00:14:37,760
是吧

441
00:14:37,760 --> 00:14:39,060
还有一个钩子

442
00:14:39,060 --> 00:14:40,040
还有什么呀

443
00:14:40,040 --> 00:14:41,280
还有它的有

444
00:14:41,280 --> 00:14:42,760
还有compare是吧

445
00:14:42,760 --> 00:14:44,440
这才一个总的变运对象

446
00:14:44,440 --> 00:14:49,140
还有什么呀

447
00:14:49,140 --> 00:14:50,100
input file system

448
00:14:50,100 --> 00:14:51,820
就是我们写入的文件的一个文件系统

449
00:14:51,820 --> 00:14:52,240
对吧

450
00:14:52,240 --> 00:14:55,060
还有我们的选项

451
00:14:55,060 --> 00:14:55,880
option的选项

452
00:14:55,880 --> 00:14:56,180
对吧

453
00:14:56,180 --> 00:14:58,060
这就是我们给的那个option的选项

454
00:14:58,060 --> 00:14:58,660
对不对

455
00:14:58,660 --> 00:15:01,060
还有我们output的输入选项

456
00:15:01,060 --> 00:15:01,940
对吧

457
00:15:01,940 --> 00:15:04,740
东西很多啊

458
00:15:04,740 --> 00:15:05,940
我们我们看几个重点

459
00:15:05,940 --> 00:15:07,060
我们第一个什么

460
00:15:07,060 --> 00:15:08,340
这叫什么entries

461
00:15:08,340 --> 00:15:08,900
什么意思

462
00:15:08,900 --> 00:15:10,100
挑出来啊

463
00:15:10,100 --> 00:15:10,720
重点挑出来

464
00:15:10,720 --> 00:15:12,040
entries的意思

465
00:15:12,040 --> 00:15:12,520
入口

466
00:15:12,520 --> 00:15:15,000
就是说这是一个入口

467
00:15:15,000 --> 00:15:19,520
这里放的是什么

468
00:15:19,520 --> 00:15:20,700
是入口模块

469
00:15:20,700 --> 00:15:24,440
主要现在还是处于那种

470
00:15:24,440 --> 00:15:25,320
大家处于那种

471
00:15:25,320 --> 00:15:26,800
有概念阶段啊

472
00:15:26,800 --> 00:15:27,640
还没开始讲了

473
00:15:27,640 --> 00:15:27,800
是吧

474
00:15:27,800 --> 00:15:28,360
别着急啊

475
00:15:28,360 --> 00:15:29,620
我们现在有个概念就可以了

476
00:15:29,620 --> 00:15:31,440
知道我现在在干什么都就可以了

477
00:15:31,440 --> 00:15:31,920
别着急

478
00:15:31,920 --> 00:15:33,640
后面我会讲一横写

479
00:15:33,640 --> 00:15:34,140
对吧

480
00:15:34,140 --> 00:15:36,560
然后你还有什么

481
00:15:36,560 --> 00:15:38,560
还有我们这个preparedentrypoint

482
00:15:38,560 --> 00:15:39,360
这是我们的入口点

483
00:15:39,360 --> 00:15:40,940
它有什么trunks

484
00:15:40,940 --> 00:15:41,380
什么意思

485
00:15:41,380 --> 00:15:43,200
trunks是代码块的意思

486
00:15:43,200 --> 00:15:46,640
就是我们这次编译出来几个代码块

487
00:15:46,640 --> 00:15:48,580
就是编译出来的几个代码块

488
00:15:48,580 --> 00:15:51,560
几个代码块

489
00:15:51,560 --> 00:15:57,200
再找个常用的

490
00:15:57,200 --> 00:15:58,460
比如这个叫modules

491
00:15:58,460 --> 00:15:58,880
什么意思

492
00:15:58,880 --> 00:16:03,460
就是我们编译出来了几个模块

493
00:16:03,460 --> 00:16:07,500
咱们只看最常用的

494
00:16:07,500 --> 00:16:08,280
最有用的

495
00:16:08,280 --> 00:16:09,500
其他的我们先忽略点

496
00:16:09,500 --> 00:16:11,240
这个模块

497
00:16:11,240 --> 00:16:12,320
还有什么

498
00:16:12,320 --> 00:16:12,780
还有这个

499
00:16:12,780 --> 00:16:13,400
这是什么

500
00:16:13,400 --> 00:16:14,400
这是个map看到了吧

501
00:16:14,400 --> 00:16:15,160
是不是map呀

502
00:16:15,160 --> 00:16:16,540
这是个map

503
00:16:16,540 --> 00:16:17,340
你看啊

504
00:16:17,340 --> 00:16:18,100
它的key呢

505
00:16:18,100 --> 00:16:19,580
这是什么呀

506
00:16:19,580 --> 00:16:21,700
它的map它的下完线module字吧

507
00:16:21,700 --> 00:16:23,600
key是模块的什么呀

508
00:16:23,600 --> 00:16:24,640
绝对入境

509
00:16:24,640 --> 00:16:26,880
模块的绝对入境

510
00:16:26,880 --> 00:16:28,640
它的值呢

511
00:16:28,640 --> 00:16:30,840
是对应的模块位相

512
00:16:30,840 --> 00:16:34,740
OK吧

513
00:16:34,740 --> 00:16:36,780
好 往下走

514
00:16:36,780 --> 00:16:39,080
你看这有个什么assess

515
00:16:39,080 --> 00:16:41,220
什么意思啊 资源资产的意思

516
00:16:41,220 --> 00:16:42,740
对吧 就是说我们这次打拨出来

517
00:16:42,740 --> 00:16:44,140
打拨出来哪个资源 你看

518
00:16:44,140 --> 00:16:46,120
它是一个对象

519
00:16:46,120 --> 00:16:47,580
有什么 有K和Value

520
00:16:47,580 --> 00:16:50,900
K呢 是文件的名字 是不是打拨出来帮多杰斯啊

521
00:16:50,900 --> 00:16:52,580
K呢 是文件的名字

522
00:16:52,580 --> 00:16:56,800
那么值呢 就是原代码呗

523
00:16:56,800 --> 00:16:59,880
就是这个文件里边的内容

524
00:17:00,840 --> 00:17:02,200
就是文件的圆码那种

525
00:17:02,200 --> 00:17:03,000
是吧

526
00:17:03,000 --> 00:17:04,580
好 再往下看

527
00:17:04,580 --> 00:17:07,620
其他的我们先忽略掉啊

528
00:17:07,620 --> 00:17:09,620
就是基本上就先不用看了

529
00:17:09,620 --> 00:17:11,360
咱们看还有没有用的

530
00:17:11,360 --> 00:17:14,720
好 其他的我们暂时先忽略掉

531
00:17:14,720 --> 00:17:16,340
是吧 我们就先不再说了

532
00:17:16,340 --> 00:17:18,500
这几个属性非常重要啊

533
00:17:18,500 --> 00:17:19,460
我们所以说我们要

534
00:17:19,460 --> 00:17:21,460
单处给大家讲一下是吧

535
00:17:21,460 --> 00:17:22,940
好 把这个拿出来

536
00:17:22,940 --> 00:17:23,980
发给大家啊

537
00:17:23,980 --> 00:17:25,600
记住的几个变量啊

538
00:17:25,600 --> 00:17:26,200
记住的几个变量

539
00:17:30,840 --> 00:17:33,620
你看没有 是吧 我把这保存一下 零存维下

540
00:17:33,620 --> 00:17:37,640
图面啊 我们这个

541
00:17:37,640 --> 00:17:41,280
叫核心

542
00:17:41,280 --> 00:17:43,440
核心变量

543
00:17:43,440 --> 00:17:46,000
先把它记下来 是吧

544
00:17:46,000 --> 00:17:49,600
到时候我们写外拍的时候吧 我们也会生成这一堆这样的文件 对吧

545
00:17:49,600 --> 00:17:53,420
然后看它是怎么一步生成的 是不是啊 简单了解一下啊

546
00:17:53,420 --> 00:17:57,620
好 那现在呢 你看 比如说我们想看它们那种话怎么看呢 你看啊

547
00:17:57,620 --> 00:18:00,820
这样是不是东西很多啊 那怎么小一点呢 可以的啊

548
00:18:00,820 --> 00:18:02,420
你可以定用c的什么呀

549
00:18:02,420 --> 00:18:03,340
它的方法叫什么

550
00:18:03,340 --> 00:18:04,500
叫to jason

551
00:18:04,500 --> 00:18:07,160
里边传个对象

552
00:18:07,160 --> 00:18:08,820
传什么

553
00:18:08,820 --> 00:18:11,940
比如说我们比如说我们只要自己的属性怎么写

554
00:18:11,940 --> 00:18:13,140
可以定用写啊写个对象

555
00:18:13,140 --> 00:18:14,880
里边写上什么

556
00:18:14,880 --> 00:18:16,220
写上叫比如说

557
00:18:16,220 --> 00:18:17,900
我要写什么entries

558
00:18:17,900 --> 00:18:19,220
那给它个处是吧

559
00:18:19,220 --> 00:18:20,500
可以过滤啊

560
00:18:20,500 --> 00:18:21,460
我要写的trunks

561
00:18:21,460 --> 00:18:22,780
把它也给它为处

562
00:18:22,780 --> 00:18:24,680
我要写是modules

563
00:18:24,680 --> 00:18:25,380
也这为处

564
00:18:25,380 --> 00:18:30,000
我要写什么呀

565
00:18:30,000 --> 00:18:30,460
assess

566
00:18:30,460 --> 00:18:32,300
就是我们打包后的资源

567
00:18:32,300 --> 00:18:32,940
也是处

568
00:18:32,940 --> 00:18:35,140
是不是就可以了

569
00:18:35,140 --> 00:18:37,620
把它怎么样拿过来放到这个地方

570
00:18:37,620 --> 00:18:39,480
放到我们这个地方

571
00:18:39,480 --> 00:18:46,740
看了吗

572
00:18:46,740 --> 00:18:47,160
就是你看

573
00:18:47,160 --> 00:18:49,200
开始编译是不是

574
00:18:49,200 --> 00:18:51,580
然后呢有个回调打印出来它的内容

575
00:18:51,580 --> 00:18:53,280
你看我现在是不是过滤一下呀

576
00:18:53,280 --> 00:18:55,240
我只要的四个像其他都不要了

577
00:18:55,240 --> 00:18:56,440
那你看一下效果

578
00:18:56,440 --> 00:18:57,740
我现在右边撞一下

579
00:18:57,740 --> 00:19:02,920
你看东西是不是少多了

580
00:19:02,920 --> 00:19:03,860
清晰多了

581
00:19:03,860 --> 00:19:04,980
对吧

582
00:19:04,980 --> 00:19:05,900
给大家演示一下

583
00:19:05,900 --> 00:19:07,500
拿过来

584
00:19:07,500 --> 00:19:10,640
看这个文件是不是好看多了

585
00:19:10,640 --> 00:19:11,760
拿过来看一下

586
00:19:11,760 --> 00:19:18,000
是不是很清晰啊

587
00:19:18,000 --> 00:19:18,820
你看有什么

588
00:19:18,820 --> 00:19:20,500
首先有errors

589
00:19:20,500 --> 00:19:23,000
错误消息

590
00:19:23,000 --> 00:19:25,000
警告消息

591
00:19:25,000 --> 00:19:25,920
Version

592
00:19:25,920 --> 00:19:27,160
就是WiPi打包办法号

593
00:19:27,160 --> 00:19:28,220
Hashi呢

594
00:19:28,220 --> 00:19:29,880
就是本次打包的Hashi值

595
00:19:29,880 --> 00:19:30,440
大家知道

596
00:19:30,440 --> 00:19:32,080
每次打包都会升级Hashi值

597
00:19:32,080 --> 00:19:32,920
这个大家知道吧

598
00:19:32,920 --> 00:19:33,980
Hashi值

599
00:19:33,980 --> 00:19:35,820
然后Time花了多少时间

600
00:19:35,820 --> 00:19:37,180
还有什么Buildat

601
00:19:37,180 --> 00:19:38,280
是构建的来于

602
00:19:38,280 --> 00:19:39,500
这是时间戳吧

603
00:19:39,500 --> 00:19:40,280
就什么是构建的

604
00:19:40,280 --> 00:19:40,460
是不是

605
00:19:40,460 --> 00:19:41,820
PublicPath

606
00:19:41,820 --> 00:19:42,660
公开访问路径

607
00:19:42,660 --> 00:19:43,680
就是那个什么

608
00:19:43,680 --> 00:19:45,160
就是那output里面的PublicPath

609
00:19:45,160 --> 00:19:45,900
对吧

610
00:19:45,900 --> 00:19:47,000
outputPath

611
00:19:47,000 --> 00:19:48,040
就是输出的目录

612
00:19:48,040 --> 00:19:49,360
是不是Date的目录啊

613
00:19:49,360 --> 00:19:51,720
Assets by TrunkName

614
00:19:51,720 --> 00:19:52,800
就是我们这个

615
00:19:52,800 --> 00:19:54,900
代码块名字

616
00:19:54,900 --> 00:19:56,040
对应的资源

617
00:19:56,040 --> 00:19:58,320
就是may这个代码块的名字

618
00:19:58,320 --> 00:19:59,560
对应一个什么样

619
00:19:59,560 --> 00:20:00,720
帮助结子资源

620
00:20:00,720 --> 00:20:01,980
就是may这个代码块

621
00:20:01,980 --> 00:20:03,760
就是入口文件的代码块

622
00:20:03,760 --> 00:20:04,800
它是不是都会打破说

623
00:20:04,800 --> 00:20:05,480
给帮助结子的文件

624
00:20:05,480 --> 00:20:06,440
对不对

625
00:20:06,440 --> 00:20:07,480
是不是这样的

626
00:20:07,480 --> 00:20:07,620
你看

627
00:20:07,620 --> 00:20:09,420
你看我现在我写的时候

628
00:20:09,420 --> 00:20:09,640
你看

629
00:20:09,640 --> 00:20:11,040
我这是不是相当于

630
00:20:11,040 --> 00:20:13,920
给了个entry

631
00:20:13,920 --> 00:20:17,200
那么它其实跟什么意思一样呢

632
00:20:17,200 --> 00:20:17,860
它就相当于这个

633
00:20:17,860 --> 00:20:19,220
entry等于对象

634
00:20:19,220 --> 00:20:21,640
k是代码块的名字

635
00:20:21,640 --> 00:20:22,060
叫may

636
00:20:22,060 --> 00:20:23,880
值是我们这个入口文件

637
00:20:23,880 --> 00:20:24,380
就是它吧

638
00:20:24,380 --> 00:20:27,480
这两个是一模一样的

639
00:20:27,480 --> 00:20:27,920
明白了吧

640
00:20:27,920 --> 00:20:30,540
如果说你给对象的话

641
00:20:30,540 --> 00:20:31,080
给多个的话

642
00:20:31,080 --> 00:20:32,240
是不是很多KVU啊

643
00:20:32,240 --> 00:20:33,240
如果你只给ZoShot的话

644
00:20:33,240 --> 00:20:34,060
那么它就是一个

645
00:20:34,060 --> 00:20:35,200
它的这个名字叫什么

646
00:20:35,200 --> 00:20:35,520
叫妹

647
00:20:35,520 --> 00:20:36,120
这写死

648
00:20:36,120 --> 00:20:36,640
令死

649
00:20:36,640 --> 00:20:37,340
就要妹

650
00:20:37,340 --> 00:20:39,000
是这样的啊

651
00:20:39,000 --> 00:20:41,260
还有Asset的名字

652
00:20:41,260 --> 00:20:42,380
W或者资源

653
00:20:42,380 --> 00:20:42,820
对吧

654
00:20:42,820 --> 00:20:43,380
你看有什么

655
00:20:43,380 --> 00:20:44,800
名字叫帮助杰斯

656
00:20:44,800 --> 00:20:46,500
是不是打不出这样的文件啊

657
00:20:46,500 --> 00:20:48,980
然后大小为4126个字节

658
00:20:48,980 --> 00:20:50,880
它是

659
00:20:50,880 --> 00:20:52,480
它包含那代码块呢

660
00:20:52,480 --> 00:20:54,040
它里面肯定包含哪个代码块

661
00:20:54,040 --> 00:20:55,320
包含我们这个叫什么

662
00:20:55,320 --> 00:20:56,140
叫May

663
00:20:56,140 --> 00:20:57,000
对吧

664
00:20:57,000 --> 00:20:59,740
TrunkNames它其实也是个速度

665
00:20:59,740 --> 00:21:00,280
也是个May

666
00:21:00,280 --> 00:21:01,380
对吧

667
00:21:01,380 --> 00:21:04,380
这个又

668
00:21:04,380 --> 00:21:06,400
没有用啊

669
00:21:06,400 --> 00:21:07,180
好晚上看

670
00:21:07,180 --> 00:21:08,520
这过滤的资源

671
00:21:08,520 --> 00:21:09,260
我们已经删掉

672
00:21:09,260 --> 00:21:10,100
没有用

673
00:21:10,100 --> 00:21:10,940
这是什么呀

674
00:21:10,940 --> 00:21:11,700
这是入口点

675
00:21:11,700 --> 00:21:12,340
是不是啊

676
00:21:12,340 --> 00:21:12,940
就是说我们入口

677
00:21:12,940 --> 00:21:13,500
我们研设哪个

678
00:21:13,500 --> 00:21:14,080
哪个模块

679
00:21:14,080 --> 00:21:14,880
是不是啊

680
00:21:14,880 --> 00:21:15,920
是May啊

681
00:21:15,920 --> 00:21:16,560
然后Trunks

682
00:21:16,560 --> 00:21:17,240
就是这个

683
00:21:17,240 --> 00:21:18,400
它有哪些代码块

684
00:21:18,400 --> 00:21:19,220
有哪些资源

685
00:21:19,220 --> 00:21:20,640
这是它的儿子

686
00:21:20,640 --> 00:21:21,380
你看都是空的

687
00:21:21,380 --> 00:21:21,620
是吧

688
00:21:21,620 --> 00:21:22,300
我们把它干掉

689
00:21:22,300 --> 00:21:25,040
精简一下

690
00:21:25,040 --> 00:21:26,120
把没用删掉

691
00:21:26,120 --> 00:21:27,280
这个也没用删掉

692
00:21:27,280 --> 00:21:28,680
大小也没用删掉

693
00:21:28,680 --> 00:21:29,640
好

694
00:21:29,640 --> 00:21:32,100
然后还有我们叫named trunk group

695
00:21:32,100 --> 00:21:35,300
就是一个名字的代码块的一个分组

696
00:21:35,300 --> 00:21:35,600
是吧

697
00:21:35,600 --> 00:21:35,960
may

698
00:21:35,960 --> 00:21:37,340
名字对应什么

699
00:21:37,340 --> 00:21:38,040
对应这个代码块

700
00:21:38,040 --> 00:21:38,940
是吧

701
00:21:38,940 --> 00:21:39,940
这个也不要了

702
00:21:39,940 --> 00:21:42,760
现在听不懂

703
00:21:42,760 --> 00:21:43,220
别着急

704
00:21:43,220 --> 00:21:43,720
别着急

705
00:21:43,720 --> 00:21:44,740
还没到讲的时候

706
00:21:44,740 --> 00:21:46,200
我们先简单了解就可以了

707
00:21:46,200 --> 00:21:46,680
是吧

708
00:21:46,680 --> 00:21:47,000
好

709
00:21:47,000 --> 00:21:47,400
这是什么

710
00:21:47,400 --> 00:21:48,280
我们打不出代码块

711
00:21:48,280 --> 00:21:48,540
是不是

712
00:21:48,540 --> 00:21:49,780
ID叫什么

713
00:21:49,780 --> 00:21:50,160
叫may

714
00:21:50,160 --> 00:21:52,220
然后呢

715
00:21:52,220 --> 00:21:53,280
哈希指是这个是吧

716
00:21:53,280 --> 00:21:55,420
它的包含哪个模块呢

717
00:21:55,420 --> 00:21:55,840
对吧

718
00:21:55,840 --> 00:21:56,960
包肯定包含两个模块呗

719
00:21:56,960 --> 00:21:57,640
包含我们的

720
00:21:57,640 --> 00:21:59,340
Innelges和我们的Telos

721
00:21:59,340 --> 00:21:59,840
是不是

722
00:21:59,840 --> 00:22:04,000
这它里面所有模块吧

723
00:22:04,000 --> 00:22:04,920
是第一个什么

724
00:22:04,920 --> 00:22:06,440
我们的SLInnelges

725
00:22:06,440 --> 00:22:07,700
第二个什么

726
00:22:07,700 --> 00:22:08,820
是不是我们的Telos

727
00:22:08,820 --> 00:22:09,740
对吧

728
00:22:09,740 --> 00:22:10,480
包含这两个模块

729
00:22:10,480 --> 00:22:11,840
就没了

730
00:22:11,840 --> 00:22:13,600
你看其他都没什么用啊

731
00:22:13,600 --> 00:22:14,420
删掉就可以了

732
00:22:14,420 --> 00:22:16,700
最终我们也会打播出

733
00:22:16,700 --> 00:22:17,600
这样的内容

734
00:22:17,600 --> 00:22:17,820
是不是

735
00:22:17,820 --> 00:22:18,660
我们对俗写书

736
00:22:18,660 --> 00:22:19,380
也会这样打播出

737
00:22:19,380 --> 00:22:19,860
这样的内容

738
00:22:19,860 --> 00:22:20,900
把它记住一下

739
00:22:20,900 --> 00:22:21,880
我们叫它什么

740
00:22:21,880 --> 00:22:24,340
我们叫他state.json就可以了

741
00:22:24,340 --> 00:22:26,460
好 别分一下

742
00:22:26,460 --> 00:22:31,540
好 看大家的问题

743
00:22:31,540 --> 00:22:41,200
对 大家尽量讨论技术

744
00:22:41,200 --> 00:22:44,740
加个debugger不就可以进了

745
00:22:44,740 --> 00:22:46,080
对呀 加debugger可以进

746
00:22:46,080 --> 00:22:48,860
大家哪不懂呢

747
00:22:48,860 --> 00:22:49,520
可以随着提

748
00:22:49,520 --> 00:22:51,620
我们现在还处于理论阶段

749
00:22:51,620 --> 00:22:51,860
是不是

750
00:22:51,860 --> 00:22:53,980
我们重点要搞清楚三个对象

751
00:22:53,980 --> 00:22:55,220
第一个是modules

752
00:22:55,220 --> 00:22:56,380
代表所有的模块

753
00:22:56,380 --> 00:22:57,500
不会打包几个模块

754
00:22:57,500 --> 00:22:59,760
我们这个项目里边用几个模块

755
00:22:59,760 --> 00:23:00,260
两个

756
00:23:00,260 --> 00:23:00,920
对不对

757
00:23:00,920 --> 00:23:02,080
一个innajj的一个什么

758
00:23:02,080 --> 00:23:02,680
tidalgets

759
00:23:02,680 --> 00:23:04,000
第二个呢

760
00:23:04,000 --> 00:23:04,440
trunks

761
00:23:04,440 --> 00:23:05,640
代码块

762
00:23:05,640 --> 00:23:07,040
在我们这个例子里边

763
00:23:07,040 --> 00:23:07,900
我们只有一个代码块

764
00:23:07,900 --> 00:23:08,440
叫什么

765
00:23:08,440 --> 00:23:08,900
就是may

766
00:23:08,900 --> 00:23:11,000
它会入口文件是它

767
00:23:11,000 --> 00:23:11,240
是不是

768
00:23:11,240 --> 00:23:13,360
然后它有一来另外一个什么呀

769
00:23:13,360 --> 00:23:13,900
tidalgets

770
00:23:13,900 --> 00:23:15,460
那么相当于这一个代码块

771
00:23:15,460 --> 00:23:16,260
包含两个模块

772
00:23:16,260 --> 00:23:16,680
是不是

773
00:23:16,680 --> 00:23:18,860
innajj和我们tidalgets

774
00:23:18,860 --> 00:23:19,280
对不对

775
00:23:19,280 --> 00:23:20,560
是这样的

776
00:23:20,560 --> 00:23:21,260
第三个叫什么

777
00:23:21,260 --> 00:23:21,600
assess

778
00:23:21,600 --> 00:23:23,540
记住所有要生存的文件

779
00:23:23,540 --> 00:23:25,220
assess什么意思

780
00:23:25,220 --> 00:23:26,820
就是说要生存哪些文件

781
00:23:26,820 --> 00:23:29,520
它的k呢就是文件的名字

782
00:23:29,520 --> 00:23:30,620
直到就是文件的内容

783
00:23:30,620 --> 00:23:33,120
那你想想我们这个项目

784
00:23:33,120 --> 00:23:35,840
那它的assess有几个属性

785
00:23:35,840 --> 00:23:37,080
它这个对象有几个属性

786
00:23:37,080 --> 00:23:37,880
一个

787
00:23:37,880 --> 00:23:38,960
哪个属性

788
00:23:38,960 --> 00:23:40,000
哪个叫什么名字

789
00:23:40,000 --> 00:23:40,780
帮助js

790
00:23:40,780 --> 00:23:42,220
对吧

791
00:23:42,220 --> 00:23:42,760
是不是

792
00:23:42,760 --> 00:23:44,460
一定要记住三个属性

793
00:23:44,460 --> 00:23:46,820
module的创作词和我们的assess

794
00:23:46,820 --> 00:23:47,820
对吧

795
00:23:47,820 --> 00:23:48,540
牢牢记住

796
00:23:51,600 --> 00:23:55,180
这个to string方法是哪得到的

797
00:23:55,180 --> 00:23:56,440
to string方法呀

798
00:23:56,440 --> 00:23:56,860
是什么

799
00:23:56,860 --> 00:23:58,000
是咱们这个

800
00:23:58,000 --> 00:24:01,180
这个c对象自己去创建的

801
00:24:01,180 --> 00:24:02,540
自己去声明的一个对象

802
00:24:02,540 --> 00:24:03,040
对吧

803
00:24:03,040 --> 00:24:03,540
一个属性

804
00:24:03,540 --> 00:24:05,040
是这样的啊

805
00:24:05,040 --> 00:24:06,060
好

806
00:24:06,060 --> 00:24:07,100
那么抓紧时间了啊

807
00:24:07,100 --> 00:24:08,120
咱们讲下一个环节

808
00:24:08,120 --> 00:24:09,860
留守我们了解了吧

809
00:24:09,860 --> 00:24:11,660
我再强调一遍啊

810
00:24:11,660 --> 00:24:12,560
第一步怎么样

811
00:24:12,560 --> 00:24:13,800
数法参数

812
00:24:13,800 --> 00:24:14,820
第二步怎么样

813
00:24:14,820 --> 00:24:15,780
开始编译

814
00:24:15,780 --> 00:24:18,080
编译入口文件和它一类的模块

815
00:24:18,080 --> 00:24:18,540
对不对

816
00:24:18,540 --> 00:24:19,580
然后编译

817
00:24:19,580 --> 00:24:20,640
编译完了以后呢

818
00:24:20,640 --> 00:24:22,100
是不是把这个关系搞清楚了

819
00:24:22,100 --> 00:24:23,720
然后根据这个模块怎么样

820
00:24:23,720 --> 00:24:25,300
去生成我们的代码块

821
00:24:25,300 --> 00:24:27,100
那么一个代码块呢

822
00:24:27,100 --> 00:24:28,160
可能会包含很多模块

823
00:24:28,160 --> 00:24:28,680
对不对

824
00:24:28,680 --> 00:24:29,660
然后呢

825
00:24:29,660 --> 00:24:30,520
根据代码块怎么样

826
00:24:30,520 --> 00:24:31,360
生成文件

827
00:24:31,360 --> 00:24:32,260
泄露文件系统

828
00:24:32,260 --> 00:24:32,580
那是

829
00:24:32,580 --> 00:24:33,840
这是我才得读题

830
00:24:33,840 --> 00:24:35,160
OK吧

831
00:24:35,160 --> 00:24:38,340
当然这个真正环节会比较复杂

832
00:24:38,340 --> 00:24:39,360
我们一步一步来

833
00:24:39,360 --> 00:24:41,240
在讲这个圆码之前啊

834
00:24:41,240 --> 00:24:41,840
我们首先啊

835
00:24:41,840 --> 00:24:44,160
我们先来讲讲两个库啊

836
00:24:44,160 --> 00:24:44,920
第一个库叫什么

837
00:24:44,920 --> 00:24:45,600
叫table

838
00:24:45,600 --> 00:24:48,400
我们因为我们要讲插件的话

839
00:24:48,400 --> 00:24:50,440
其实主要是要考对库叫table

840
00:24:50,440 --> 00:24:51,940
所以我们要好好说一下

841
00:24:51,940 --> 00:24:53,560
面试也不经常问

842
00:24:53,560 --> 00:24:55,920
这个WPAC本身啊

843
00:24:55,920 --> 00:24:58,400
它其实是一个世界流的机制

844
00:24:58,400 --> 00:24:59,600
它就是通过什么

845
00:24:59,600 --> 00:25:01,040
通过我们这个工作流

846
00:25:01,040 --> 00:25:02,940
把各传线串联起来

847
00:25:02,940 --> 00:25:05,320
而实验一切的核心核心就是我们的table

848
00:25:05,320 --> 00:25:08,340
咱们这个WPAC里面有两个核心的项

849
00:25:08,340 --> 00:25:08,940
一个叫什么

850
00:25:08,940 --> 00:25:09,540
Compiler

851
00:25:09,540 --> 00:25:10,720
一个叫Completion

852
00:25:10,720 --> 00:25:12,400
它们都是table的实力

853
00:25:12,400 --> 00:25:14,520
就它们都会进入table

854
00:25:14,520 --> 00:25:17,820
那么有哪些table呢

855
00:25:17,820 --> 00:25:18,280
你看有几个

856
00:25:18,280 --> 00:25:21,980
一二三四五六七八九

857
00:25:21,980 --> 00:25:22,980
一共有九个table

858
00:25:22,980 --> 00:25:23,500
是不是

859
00:25:23,500 --> 00:25:24,520
有九个hook

860
00:25:24,520 --> 00:25:26,220
那九个hook呢

861
00:25:26,220 --> 00:25:27,240
你看根据关心资料

862
00:25:27,240 --> 00:25:28,180
我们可以已经组合

863
00:25:28,180 --> 00:25:29,140
怎么组合呢

864
00:25:29,140 --> 00:25:29,940
你看啊

865
00:25:29,940 --> 00:25:30,240
首先

866
00:25:30,240 --> 00:25:32,120
第一个叫什么叫basic

867
00:25:32,120 --> 00:25:33,160
叫基本类型

868
00:25:33,160 --> 00:25:35,400
比如说你看我们这个叫singhook

869
00:25:35,400 --> 00:25:36,200
它是有基本类型

870
00:25:36,200 --> 00:25:37,280
对吧

871
00:25:37,280 --> 00:25:37,880
还有什么

872
00:25:37,880 --> 00:25:38,760
还有这个bill

873
00:25:38,760 --> 00:25:40,040
是不是有些理由

874
00:25:40,040 --> 00:25:40,420
bill

875
00:25:40,420 --> 00:25:41,980
你看这边有singbill

876
00:25:41,980 --> 00:25:43,200
这有什么

877
00:25:43,200 --> 00:25:45,080
有个叫什么什么bill啊

878
00:25:45,080 --> 00:25:45,920
什么叫bill啊

879
00:25:45,920 --> 00:25:46,780
保险室

880
00:25:46,780 --> 00:25:49,100
就说只要接定函数的反复值

881
00:25:49,100 --> 00:25:50,020
不为andifind

882
00:25:50,020 --> 00:25:51,680
只会跳过后面的接定函数

883
00:25:51,680 --> 00:25:54,060
还有个什么叫word4

884
00:25:54,060 --> 00:25:54,980
叫poo式

885
00:25:54,980 --> 00:25:56,040
什么叫poo式

886
00:25:56,040 --> 00:25:58,200
上一个的结果是下一个的参数

887
00:25:58,200 --> 00:26:00,900
还有什么loop叫循环式

888
00:26:00,900 --> 00:26:02,960
就说它会反复执行

889
00:26:02,960 --> 00:26:04,180
直到什么

890
00:26:04,180 --> 00:26:05,780
直到反wandifind会退出循环

891
00:26:05,780 --> 00:26:07,720
这个你看

892
00:26:07,720 --> 00:26:08,860
它这些hook的

893
00:26:08,860 --> 00:26:10,920
都是由这些关系组成的

894
00:26:10,920 --> 00:26:11,400
能看到了吧

895
00:26:11,400 --> 00:26:12,360
bill啊

896
00:26:12,360 --> 00:26:13,100
word4啊

897
00:26:13,100 --> 00:26:13,800
loop啊组成的

898
00:26:13,800 --> 00:26:13,940
你看

899
00:26:13,940 --> 00:26:15,680
另外还有两个类型

900
00:26:15,680 --> 00:26:16,620
同步和异步

901
00:26:16,620 --> 00:26:17,400
分别什么

902
00:26:17,400 --> 00:26:18,600
think和async

903
00:26:18,600 --> 00:26:20,080
那同步呢

904
00:26:20,080 --> 00:26:20,640
就是同步呗

905
00:26:20,640 --> 00:26:21,960
一步就是一步呗

906
00:26:21,960 --> 00:26:22,320
对不对

907
00:26:22,320 --> 00:26:24,160
一步就分别什么

908
00:26:24,160 --> 00:26:24,980
并行和串行

909
00:26:24,980 --> 00:26:26,260
那么一步你看

910
00:26:26,260 --> 00:26:27,060
async有什么

911
00:26:27,060 --> 00:26:28,140
是不是有parallel

912
00:26:28,140 --> 00:26:28,800
什么意思啊

913
00:26:28,800 --> 00:26:29,220
并行

914
00:26:29,220 --> 00:26:30,980
serious串行啊

915
00:26:30,980 --> 00:26:31,340
对吧

916
00:26:31,340 --> 00:26:33,400
有的几种组合啊

917
00:26:33,400 --> 00:26:34,300
我们今天我们大家

918
00:26:34,300 --> 00:26:35,920
今天因为实现有限啊

919
00:26:35,920 --> 00:26:37,480
我们大家讲两种最典型的

920
00:26:37,480 --> 00:26:38,760
第一种呢

921
00:26:38,760 --> 00:26:39,400
我们讲最简单

922
00:26:39,400 --> 00:26:40,140
叫thinkhook

923
00:26:40,140 --> 00:26:41,380
我们先讲它的用法

924
00:26:41,380 --> 00:26:42,100
然后再讲实现

925
00:26:42,100 --> 00:26:42,420
好吧

926
00:26:42,420 --> 00:26:46,280
看下到底是怎么用的啊

927
00:26:46,280 --> 00:26:52,280
我们建一个文件夹

928
00:26:52,280 --> 00:26:58,280
叫1.table

929
00:26:58,280 --> 00:27:03,280
我们建一个文件叫1.thinkhook

930
00:27:03,280 --> 00:27:06,280
think什么意思?同步

931
00:27:06,280 --> 00:27:09,280
首先我们要引入这个模块

932
00:27:09,280 --> 00:27:13,280
等于require

933
00:27:13,280 --> 00:27:16,780
然后也有我们的这个Thinkhook

934
00:27:16,780 --> 00:27:19,280
那它是个什么东西呢

935
00:27:19,280 --> 00:27:20,280
我们看一下

936
00:27:20,280 --> 00:27:21,280
打开看一下

937
00:27:21,280 --> 00:27:25,280
这个Thinkhook其实它是一个类

938
00:27:25,280 --> 00:27:26,280
对吧

939
00:27:26,280 --> 00:27:27,280
它是个类

940
00:27:27,280 --> 00:27:28,280
我们打印一下吧

941
00:27:28,280 --> 00:27:38,280
看到了吧

942
00:27:38,280 --> 00:27:39,280
它其实是个类型

943
00:27:39,280 --> 00:27:40,280
看到了吧

944
00:27:40,280 --> 00:27:42,280
是不是一个Function

945
00:27:42,280 --> 00:27:44,280
所以我们可以通过它的实力吧

946
00:27:44,280 --> 00:27:45,280
怎么用呢

947
00:27:45,280 --> 00:27:46,280
你看我可以这样写啊

948
00:27:46,280 --> 00:27:48,280
比如我来个Lite

949
00:27:48,280 --> 00:27:50,280
SyncHook

950
00:27:50,280 --> 00:27:52,280
等于另外一个SyncHook

951
00:27:52,280 --> 00:27:54,280
这个名字可以随便请

952
00:27:54,280 --> 00:27:56,280
比如我写上

953
00:27:56,280 --> 00:27:58,280
Sync或者Hook都行

954
00:27:58,280 --> 00:28:00,280
或者写上一个Queen

955
00:28:00,280 --> 00:28:00,280
这个名字啊

956
00:28:00,280 --> 00:28:02,280
其实教授都可以

957
00:28:02,280 --> 00:28:03,280
这个没有关系啊

958
00:28:03,280 --> 00:28:04,280
教授都行

959
00:28:04,280 --> 00:28:06,280
SyncHook

960
00:28:06,280 --> 00:28:07,280
那么我们可以怎么样

961
00:28:07,280 --> 00:28:08,280
我们可以去执行它

962
00:28:08,280 --> 00:28:10,280
对不对

963
00:28:12,280 --> 00:28:13,900
它其实很有两个方法

964
00:28:13,900 --> 00:28:15,040
一个叫type

965
00:28:15,040 --> 00:28:16,180
用来什么

966
00:28:16,180 --> 00:28:17,560
用来去注册实践

967
00:28:17,560 --> 00:28:20,860
一个叫type

968
00:28:20,860 --> 00:28:22,300
一个叫什么

969
00:28:22,300 --> 00:28:24,100
靠触发实践

970
00:28:24,100 --> 00:28:25,380
触发实践

971
00:28:25,380 --> 00:28:27,260
那么因为它什么

972
00:28:27,260 --> 00:28:27,960
它是同步的

973
00:28:27,960 --> 00:28:28,120
是不是

974
00:28:28,120 --> 00:28:29,360
它是同步的

975
00:28:29,360 --> 00:28:30,620
所以说我们看看怎么用

976
00:28:30,620 --> 00:28:31,360
首先你看

977
00:28:31,360 --> 00:28:33,620
第一步我们去通过new这个购栏数

978
00:28:33,620 --> 00:28:34,300
得到什么

979
00:28:34,300 --> 00:28:34,920
得到一个实例

980
00:28:34,920 --> 00:28:35,400
对吧

981
00:28:35,400 --> 00:28:37,280
然后我们来注册吧

982
00:28:37,280 --> 00:28:37,660
queen

983
00:28:37,660 --> 00:28:39,120
第二什么

984
00:28:39,120 --> 00:28:39,920
比较type

985
00:28:39,920 --> 00:28:41,720
第一步参数呢

986
00:28:41,720 --> 00:28:42,260
给个名字

987
00:28:42,260 --> 00:28:42,700
给个什么

988
00:28:42,700 --> 00:28:43,360
给个1吧

989
00:28:43,360 --> 00:28:44,920
然后写一个什么

990
00:28:44,920 --> 00:28:46,620
写个接定参数吧

991
00:28:46,620 --> 00:28:50,780
当你创建SyncHook的时候

992
00:28:50,780 --> 00:28:51,740
可以传个参数

993
00:28:51,740 --> 00:28:52,580
参数呢

994
00:28:52,580 --> 00:28:52,820
是什么

995
00:28:52,820 --> 00:28:53,400
是一个数组

996
00:28:53,400 --> 00:28:53,720
对吧

997
00:28:53,720 --> 00:28:54,460
比如我们可以这样写

998
00:28:54,460 --> 00:28:55,040
然后写个数组

999
00:28:55,040 --> 00:28:55,820
里面写什么

1000
00:28:55,820 --> 00:28:56,400
写个name

1001
00:28:56,400 --> 00:28:57,580
什么意思

1002
00:28:57,580 --> 00:28:57,940
就是说

1003
00:28:57,940 --> 00:28:58,600
表示啊

1004
00:28:58,600 --> 00:28:59,620
我这个Hook出发的时候

1005
00:28:59,620 --> 00:29:00,600
要传一个name参数

1006
00:29:00,600 --> 00:29:01,320
对吧

1007
00:29:01,320 --> 00:29:03,040
那么你看

1008
00:29:03,040 --> 00:29:03,760
我接定的时候

1009
00:29:03,760 --> 00:29:04,640
可以拿到的name参数

1010
00:29:04,640 --> 00:29:04,860
是不是

1011
00:29:04,860 --> 00:29:06,160
然后打印一下

1012
00:29:06,160 --> 00:29:07,780
我们打印一下

1013
00:29:07,780 --> 00:29:09,540
打印我们这个1和name

1014
00:29:09,540 --> 00:29:10,000
对吧

1015
00:29:10,000 --> 00:29:11,200
我们再来一次

1016
00:29:11,200 --> 00:29:14,260
2

1017
00:29:14,260 --> 00:29:15,880
name 对吧

1018
00:29:15,880 --> 00:29:19,080
好 这要我们监听啊

1019
00:29:19,080 --> 00:29:21,100
就是说通过我们的type方法

1020
00:29:21,100 --> 00:29:22,940
可以怎么可以先注册一个时间

1021
00:29:22,940 --> 00:29:25,580
然后呢 当时间出发的时候呢

1022
00:29:25,580 --> 00:29:27,080
会执行什么 这个回答数

1023
00:29:27,080 --> 00:29:29,480
然后它会得到一个参数 叫name

1024
00:29:29,480 --> 00:29:32,340
为什么有name呀 因为这我什么定义的name了

1025
00:29:32,340 --> 00:29:32,860
对不对

1026
00:29:32,860 --> 00:29:35,000
好 然后你看我怎么做 我可以出发一下

1027
00:29:35,000 --> 00:29:35,640
怎么突发呢

1028
00:29:35,640 --> 00:29:36,580
quick 叫什么

1029
00:29:36,580 --> 00:29:40,500
kou

1030
00:29:40,500 --> 00:29:43,160
eoe.call

1031
00:29:43,160 --> 00:29:44,800
一个珠峰

1032
00:29:44,800 --> 00:29:52,780
就可以了吧

1033
00:29:52,780 --> 00:29:53,640
你看啊

1034
00:29:53,640 --> 00:29:54,960
看我们来运行一下

1035
00:29:54,960 --> 00:29:55,520
看看效果

1036
00:29:55,520 --> 00:29:59,000
看是不是打印出来了

1037
00:29:59,000 --> 00:29:59,980
说明什么

1038
00:29:59,980 --> 00:30:00,760
说明你看啊

1039
00:30:00,760 --> 00:30:01,660
这个type是什么样

1040
00:30:01,660 --> 00:30:02,760
就是注册实键

1041
00:30:02,760 --> 00:30:04,160
call呢是什么呀

1042
00:30:04,160 --> 00:30:05,520
触发实键

1043
00:30:05,520 --> 00:30:07,100
触发实键

1044
00:30:07,100 --> 00:30:07,760
就这样的

1045
00:30:07,760 --> 00:30:08,480
好

1046
00:30:08,480 --> 00:30:09,900
那我们看它怎么实现的

1047
00:30:09,900 --> 00:30:11,760
它实现学历其实感觉很简单

1048
00:30:11,760 --> 00:30:12,440
其实不简单

1049
00:30:12,440 --> 00:30:12,780
对吧

1050
00:30:12,780 --> 00:30:13,940
不信你看啊

1051
00:30:13,940 --> 00:30:15,220
我给它看一下这个debug一下

1052
00:30:15,220 --> 00:30:18,420
好打开它

1053
00:30:18,420 --> 00:30:19,800
然后你看啊

1054
00:30:19,800 --> 00:30:20,940
我现在了我点它

1055
00:30:20,940 --> 00:30:21,980
然后呢

1056
00:30:21,980 --> 00:30:22,900
node.js执行

1057
00:30:22,900 --> 00:30:25,680
看进来了吧

1058
00:30:25,680 --> 00:30:26,800
我往下走

1059
00:30:26,800 --> 00:30:28,340
然后你看

1060
00:30:28,340 --> 00:30:29,380
tab这个意思

1061
00:30:29,380 --> 00:30:29,920
注册

1062
00:30:29,920 --> 00:30:30,600
它怎么注册

1063
00:30:30,600 --> 00:30:31,640
我们看一下圆满啊

1064
00:30:31,640 --> 00:30:31,980
进去

1065
00:30:31,980 --> 00:30:34,380
它会有个什么tab方法

1066
00:30:34,380 --> 00:30:35,600
那里边呢

1067
00:30:35,600 --> 00:30:37,740
你看它的合影逻辑其实在干嘛

1068
00:30:37,740 --> 00:30:39,440
第一步怎么样

1069
00:30:39,440 --> 00:30:40,500
我去构建了一个对象

1070
00:30:40,500 --> 00:30:41,440
option等于什么

1071
00:30:41,440 --> 00:30:44,040
等于一个这个option是一个什么

1072
00:30:44,040 --> 00:30:45,400
name等于option是不是

1073
00:30:45,400 --> 00:30:47,580
好走

1074
00:30:47,580 --> 00:30:51,540
然后呢

1075
00:30:51,540 --> 00:30:52,620
你看是不是给他复了一个

1076
00:30:52,620 --> 00:30:53,740
给option的复了一个属性

1077
00:30:53,740 --> 00:30:54,960
他压了type属性啊

1078
00:30:54,960 --> 00:30:56,280
叫sync对吧

1079
00:30:56,280 --> 00:30:59,780
然后这个

1080
00:30:59,780 --> 00:31:01,600
这个栏接器我们先不管的啊

1081
00:31:01,600 --> 00:31:02,500
从我眼里看走了谁

1082
00:31:02,500 --> 00:31:03,680
走了Z的叫什么呀

1083
00:31:03,680 --> 00:31:04,120
sync

1084
00:31:04,120 --> 00:31:05,140
insert的方法

1085
00:31:05,140 --> 00:31:05,760
好

1086
00:31:05,760 --> 00:31:06,680
咱们按了它圆满

1087
00:31:06,680 --> 00:31:07,680
给大家写啊

1088
00:31:07,680 --> 00:31:08,180
给大家写

1089
00:31:08,180 --> 00:31:10,420
我们建一个文件

1090
00:31:10,420 --> 00:31:11,760
叫什么叫这个JavaScript

1091
00:31:11,760 --> 00:31:14,760
首先你看它hook是个啥

1092
00:31:14,760 --> 00:31:15,200
是一个

1093
00:31:15,200 --> 00:31:17,360
是个类吧

1094
00:31:17,360 --> 00:31:18,380
是个类

1095
00:31:18,380 --> 00:31:22,320
是个类

1096
00:31:22,320 --> 00:31:24,580
然后呢类里边有什么方法呀

1097
00:31:24,580 --> 00:31:26,720
叫什么叫这个type方法

1098
00:31:26,720 --> 00:31:28,380
是不是好有个type方法

1099
00:31:28,380 --> 00:31:30,900
type有两个参数

1100
00:31:30,900 --> 00:31:32,780
第一个呢是一个叫options

1101
00:31:32,780 --> 00:31:36,120
什么意思选项

1102
00:31:36,120 --> 00:31:37,040
第二个FN

1103
00:31:37,040 --> 00:31:39,020
Option你看我们给了什么

1104
00:31:39,020 --> 00:31:40,100
是不是给了一个

1105
00:31:40,100 --> 00:31:42,640
给了个名字

1106
00:31:42,640 --> 00:31:43,260
是不是

1107
00:31:43,260 --> 00:31:44,500
给了个读画

1108
00:31:44,500 --> 00:31:44,860
是不是

1109
00:31:44,860 --> 00:31:46,360
里边怎么多的

1110
00:31:46,360 --> 00:31:47,960
看如果它是个读画的话

1111
00:31:47,960 --> 00:31:49,080
把它转成个对象

1112
00:31:49,080 --> 00:31:49,380
是不是

1113
00:31:49,380 --> 00:31:50,620
什么意思

1114
00:31:50,620 --> 00:31:51,040
就说

1115
00:31:51,040 --> 00:31:52,520
这里写啊

1116
00:31:52,520 --> 00:31:52,920
就说

1117
00:31:52,920 --> 00:31:55,400
如果说什么呀

1118
00:31:55,400 --> 00:31:57,180
if

1119
00:31:57,180 --> 00:31:58,380
type of

1120
00:31:58,380 --> 00:32:00,400
当然我们经常简化吧

1121
00:32:00,400 --> 00:32:01,480
因为我们认为它就是读画

1122
00:32:01,480 --> 00:32:01,720
是不是

1123
00:32:01,720 --> 00:32:02,620
那直接怎么样

1124
00:32:02,620 --> 00:32:03,440
Options

1125
00:32:03,440 --> 00:32:04,520
等于什么

1126
00:32:04,520 --> 00:32:05,500
一个对象

1127
00:32:05,500 --> 00:32:07,500
name=options

1128
00:32:07,500 --> 00:32:09,500
就可以了

1129
00:32:09,500 --> 00:32:14,500
这个who它的原理就是发布对面模式

1130
00:32:14,500 --> 00:32:16,500
监听和发布

1131
00:32:16,500 --> 00:32:18,500
name=options

1132
00:32:18,500 --> 00:32:20,500
可以了吧

1133
00:32:20,500 --> 00:32:22,500
第一步

1134
00:32:22,500 --> 00:32:25,500
第二步执行了我们的中间我们可以忽略了

1135
00:32:25,500 --> 00:32:26,500
没什么用

1136
00:32:26,500 --> 00:32:28,500
直接执行这个

1137
00:32:28,500 --> 00:32:30,500
下滑线insert

1138
00:32:30,500 --> 00:32:32,500
插入吧

1139
00:32:32,500 --> 00:32:34,500
插入options

1140
00:32:34,500 --> 00:32:36,540
当然还有这个话要加上

1141
00:32:36,540 --> 00:32:40,640
是不是附了一个type等于syncfn

1142
00:32:40,640 --> 00:32:41,100
对不对

1143
00:32:41,100 --> 00:32:42,940
把这加上就是options

1144
00:32:42,940 --> 00:32:44,560
第二什么

1145
00:32:44,560 --> 00:32:48,560
dr这个fn等于fn

1146
00:32:48,560 --> 00:32:50,560
这样的话

1147
00:32:50,560 --> 00:32:52,160
你看我的option有两个属性了

1148
00:32:52,160 --> 00:32:54,160
一个是name就是这个名字吧

1149
00:32:54,160 --> 00:32:56,040
要是fn是不是函数啊

1150
00:32:56,040 --> 00:32:57,360
对不对

1151
00:32:57,360 --> 00:32:58,460
然后最后怎么样

1152
00:32:58,460 --> 00:32:59,200
调了哪个方法

1153
00:32:59,200 --> 00:33:00,040
insert

1154
00:33:00,040 --> 00:33:00,760
插入一次

1155
00:33:00,760 --> 00:33:02,100
就是把这个函数怎么样

1156
00:33:02,100 --> 00:33:02,940
把这个option怎么样

1157
00:33:02,940 --> 00:33:03,960
先把这个存起来

1158
00:33:03,960 --> 00:33:04,460
对吧

1159
00:33:04,500 --> 00:33:06,500
那怎么存呢

1160
00:33:06,500 --> 00:33:08,500
我们进去看看

1161
00:33:08,500 --> 00:33:10,500
你看到这已经说了了吧

1162
00:33:10,500 --> 00:33:12,500
好我们看啊

1163
00:33:12,500 --> 00:33:14,500
跟他写insert

1164
00:33:14,500 --> 00:33:16,500
然后呢放个什么

1165
00:33:16,500 --> 00:33:18,500
放函数

1166
00:33:18,500 --> 00:33:19,500
然后这放个什么itom

1167
00:33:19,500 --> 00:33:20,500
是不是参数啊

1168
00:33:20,500 --> 00:33:22,500
itom谁是不是这个options啊

1169
00:33:22,500 --> 00:33:24,500
ok吧

1170
00:33:24,500 --> 00:33:25,500
那一步来

1171
00:33:25,500 --> 00:33:26,500
有问题没问啊

1172
00:33:26,500 --> 00:33:27,500
有问题急着举手

1173
00:33:27,500 --> 00:33:29,500
这个能看明白吗

1174
00:33:29,500 --> 00:33:30,500
就是说你看就是个类

1175
00:33:30,500 --> 00:33:31,500
有type方法

1176
00:33:31,500 --> 00:33:33,500
传计一个字符串和一个fn

1177
00:33:33,500 --> 00:33:36,080
然后你看我构建了一个对象

1178
00:33:36,080 --> 00:33:36,840
是不是

1179
00:33:36,840 --> 00:33:38,000
内面的就是这个走创

1180
00:33:38,000 --> 00:33:39,840
然后给他付了个FN属性

1181
00:33:39,840 --> 00:33:41,780
下面把这个对象怎么样

1182
00:33:41,780 --> 00:33:42,640
是不是就搞定了

1183
00:33:42,640 --> 00:33:44,520
然后传给我们的Instagram的方法

1184
00:33:44,520 --> 00:33:45,520
传到itom里边来了

1185
00:33:45,520 --> 00:33:47,580
他在干嘛

1186
00:33:47,580 --> 00:33:48,620
你看他在干嘛

1187
00:33:48,620 --> 00:33:50,080
没用了

1188
00:33:50,080 --> 00:33:51,500
我给大家忽略掉了

1189
00:33:51,500 --> 00:33:52,520
直接看有用的

1190
00:33:52,520 --> 00:33:54,600
他的核心就一行代码

1191
00:33:54,600 --> 00:33:57,420
Z.typesi等于itom

1192
00:33:57,420 --> 00:33:59,000
i是谁呢

1193
00:33:59,000 --> 00:34:00,180
你看i是什么

1194
00:34:00,180 --> 00:34:01,880
是这个types的lens

1195
00:34:01,880 --> 00:34:02,480
是不是

1196
00:34:02,480 --> 00:34:04,120
Types哪来的啊

1197
00:34:04,120 --> 00:34:04,340
你看

1198
00:34:04,340 --> 00:34:08,200
刚开始我设计是不是有一个Types啊

1199
00:34:08,200 --> 00:34:10,140
各难书里面是不是有个Types啊

1200
00:34:10,140 --> 00:34:11,960
是不是一个空什么呀

1201
00:34:11,960 --> 00:34:13,780
是不是一个空的一个数组啊

1202
00:34:13,780 --> 00:34:14,500
所以这样来

1203
00:34:14,500 --> 00:34:15,660
你看这有个难数

1204
00:34:15,660 --> 00:34:16,640
Condructor

1205
00:34:16,640 --> 00:34:19,660
对吧

1206
00:34:19,660 --> 00:34:21,960
Condructor

1207
00:34:21,960 --> 00:34:23,140
你看啊

1208
00:34:23,140 --> 00:34:24,800
它有参数ARGS是吧

1209
00:34:24,800 --> 00:34:27,080
这个速度哪来

1210
00:34:27,080 --> 00:34:27,920
速度不就是

1211
00:34:27,920 --> 00:34:28,880
这个

1212
00:34:28,880 --> 00:34:30,280
是你看啊

1213
00:34:30,280 --> 00:34:31,140
是不是这个速度啊

1214
00:34:31,140 --> 00:34:32,540
是不是

1215
00:34:32,540 --> 00:34:34,760
这那个速度吧

1216
00:34:34,760 --> 00:34:36,700
这速度用来表示什么意思

1217
00:34:36,700 --> 00:34:38,540
它是表示什么呀

1218
00:34:38,540 --> 00:34:42,100
表示我这个方法

1219
00:34:42,100 --> 00:34:42,880
或者什么

1220
00:34:42,880 --> 00:34:44,960
我这个实践出发的时候

1221
00:34:44,960 --> 00:34:46,660
要什么呀

1222
00:34:46,660 --> 00:34:48,060
要传递哪些参数

1223
00:34:48,060 --> 00:34:52,240
那我这是不是写了什么

1224
00:34:52,240 --> 00:34:53,980
写了一个速度里面放个name呀

1225
00:34:53,980 --> 00:34:55,100
说明我在call的时候

1226
00:34:55,100 --> 00:34:55,820
我传个name过来

1227
00:34:55,820 --> 00:34:56,960
那如果写两个

1228
00:34:56,960 --> 00:34:57,880
写个name和什么

1229
00:34:57,880 --> 00:34:58,540
Age呢

1230
00:34:58,540 --> 00:34:59,620
我要传个什么

1231
00:34:59,620 --> 00:35:01,260
试过来

1232
00:35:01,260 --> 00:35:03,400
我这写两个

1233
00:35:03,400 --> 00:35:04,380
他就要写两个

1234
00:35:04,380 --> 00:35:06,160
然后他们两个会传给谁

1235
00:35:06,160 --> 00:35:06,840
传给你监督章数

1236
00:35:06,840 --> 00:35:07,260
传给他

1237
00:35:07,260 --> 00:35:08,520
我这也要监督两个

1238
00:35:08,520 --> 00:35:10,900
可以监督到

1239
00:35:10,900 --> 00:35:13,180
是不是可以了

1240
00:35:13,180 --> 00:35:13,400
好

1241
00:35:13,400 --> 00:35:14,380
回来

1242
00:35:14,380 --> 00:35:16,240
看

1243
00:35:16,240 --> 00:35:18,460
这个Condractor

1244
00:35:18,460 --> 00:35:19,120
ARGS

1245
00:35:19,120 --> 00:35:20,040
进来了

1246
00:35:20,040 --> 00:35:21,080
现在之后你看

1247
00:35:21,080 --> 00:35:21,700
是不是有一个

1248
00:35:21,700 --> 00:35:22,800
你看做了判断

1249
00:35:22,800 --> 00:35:25,080
如果他不是数字的话

1250
00:35:25,080 --> 00:35:25,920
把他搞成数字

1251
00:35:25,920 --> 00:35:26,660
搞成空数字

1252
00:35:26,660 --> 00:35:29,480
但是我这是不是传了数字过来

1253
00:35:29,480 --> 00:35:30,640
那第一步就可以进不来了

1254
00:35:30,640 --> 00:35:31,820
过掉就可以了

1255
00:35:31,820 --> 00:35:33,260
然后存起来

1256
00:35:33,260 --> 00:35:33,560
怎么存

1257
00:35:33,560 --> 00:35:35,660
就Z这点什么

1258
00:35:35,660 --> 00:35:37,640
下关键ARGS等于ARGS

1259
00:35:37,640 --> 00:35:40,020
是把速度存到这个ARGS上面去了

1260
00:35:40,020 --> 00:35:40,740
存到上面去了

1261
00:35:40,740 --> 00:35:42,100
这第一步

1262
00:35:42,100 --> 00:35:43,680
保存我们的什么

1263
00:35:43,680 --> 00:35:45,200
保存参数

1264
00:35:45,200 --> 00:35:47,860
然后第二步

1265
00:35:47,860 --> 00:35:49,100
生命了一个TAPS

1266
00:35:49,100 --> 00:35:51,940
Z的TAPS等于一个空速度

1267
00:35:51,940 --> 00:35:52,280
什么意思

1268
00:35:52,280 --> 00:35:54,840
保存我们所有的接定函数

1269
00:35:54,840 --> 00:35:57,000
保存所有的接定函数

1270
00:35:57,000 --> 00:35:58,700
你看这个

1271
00:35:58,700 --> 00:36:00,740
跟这个不就对上了吗

1272
00:36:00,740 --> 00:36:02,260
你看这个insert怎么写的

1273
00:36:02,260 --> 00:36:04,080
怎么写的

1274
00:36:04,080 --> 00:36:05,020
是不是这么写的呀

1275
00:36:05,020 --> 00:36:05,840
是不是一个

1276
00:36:05,840 --> 00:36:08,640
就是一个z.types

1277
00:36:08,640 --> 00:36:10,020
方块

1278
00:36:10,020 --> 00:36:12,840
z.types.lance

1279
00:36:12,840 --> 00:36:15,500
等于一个atom

1280
00:36:15,500 --> 00:36:16,240
是不是

1281
00:36:16,240 --> 00:36:18,280
你看第一次的时候

1282
00:36:18,280 --> 00:36:19,500
它的常用几

1283
00:36:19,500 --> 00:36:20,020
0

1284
00:36:20,020 --> 00:36:23,720
0的话是不是给它所为0复制

1285
00:36:23,720 --> 00:36:24,760
就有一个元素了

1286
00:36:24,760 --> 00:36:25,420
是不是

1287
00:36:25,420 --> 00:36:26,820
那第二次的时候

1288
00:36:26,820 --> 00:36:27,540
再插住的时候

1289
00:36:27,540 --> 00:36:28,360
是不是插入1

1290
00:36:28,360 --> 00:36:31,120
就每次加1

1291
00:36:31,120 --> 00:36:32,880
跟什么是一模一样的

1292
00:36:32,880 --> 00:36:34,080
跟他的什么样

1293
00:36:34,080 --> 00:36:35,380
点push是一模一样

1294
00:36:35,380 --> 00:36:38,240
跟这个是一样的效果

1295
00:36:38,240 --> 00:36:39,340
是不是

1296
00:36:39,340 --> 00:36:41,540
想想

1297
00:36:41,540 --> 00:36:42,640
最好哪个电话

1298
00:36:42,640 --> 00:36:43,280
是不是一个意思

1299
00:36:43,280 --> 00:36:45,440
你看网数里边什么

1300
00:36:45,440 --> 00:36:46,200
放一个元素

1301
00:36:46,200 --> 00:36:49,100
为啥不用

1302
00:36:49,100 --> 00:36:50,620
就这么写的

1303
00:36:50,620 --> 00:36:52,280
觉得这样好吧

1304
00:36:52,280 --> 00:36:53,760
其实跟push的一样的

1305
00:36:53,760 --> 00:36:54,780
如果觉得这种写

1306
00:36:54,780 --> 00:36:55,300
你可以这样写

1307
00:36:55,300 --> 00:36:55,560
是不是

1308
00:36:55,560 --> 00:36:56,660
咱们有这种写法

1309
00:36:56,660 --> 00:36:57,020
对吧

1310
00:36:57,020 --> 00:36:59,760
好

1311
00:36:59,760 --> 00:37:02,520
那你完事了吗

1312
00:37:02,520 --> 00:37:04,400
你到了这一步

1313
00:37:04,400 --> 00:37:05,360
你看到了insert

1314
00:37:05,360 --> 00:37:06,820
这个就结束了

1315
00:37:06,820 --> 00:37:07,440
看到吧

1316
00:37:07,440 --> 00:37:08,460
是不是type结束了

1317
00:37:08,460 --> 00:37:09,540
所以type很简单

1318
00:37:09,540 --> 00:37:11,040
就是把一个对象

1319
00:37:11,040 --> 00:37:11,800
对吧

1320
00:37:11,800 --> 00:37:13,760
放到我们的type的输入里边去

1321
00:37:13,760 --> 00:37:14,400
结束

1322
00:37:14,400 --> 00:37:15,420
是不是ok

1323
00:37:15,420 --> 00:37:16,880
好我们看第二个

1324
00:37:16,880 --> 00:37:19,160
第二个是一样的

1325
00:37:19,160 --> 00:37:19,840
所以不再看了

1326
00:37:19,840 --> 00:37:21,460
咱们看这call怎么干嘛

1327
00:37:21,460 --> 00:37:22,500
call比较复杂

1328
00:37:22,500 --> 00:37:24,180
你看进去看看

1329
00:37:24,180 --> 00:37:25,840
你看我这个

1330
00:37:25,840 --> 00:37:28,000
我得重新进来

1331
00:37:28,000 --> 00:37:29,620
走

1332
00:37:29,620 --> 00:37:32,080
看我怎么call的

1333
00:37:32,080 --> 00:37:32,960
进去

1334
00:37:32,960 --> 00:37:34,660
这就比较复杂了

1335
00:37:34,660 --> 00:37:36,220
他在干嘛

1336
00:37:36,220 --> 00:37:39,240
他会走到这个方法里边来

1337
00:37:39,240 --> 00:37:40,420
他会走什么

1338
00:37:40,420 --> 00:37:42,480
lazy compare hook

1339
00:37:42,480 --> 00:37:44,200
就是编编译

1340
00:37:44,200 --> 00:37:46,580
然后这是什么

1341
00:37:46,580 --> 00:37:47,260
这是他的参数

1342
00:37:47,260 --> 00:37:50,920
然后他会调

1343
00:37:50,920 --> 00:37:52,660
Z的下方键create call

1344
00:37:52,660 --> 00:37:54,280
这是什么意思

1345
00:37:54,280 --> 00:37:55,460
这是什么

1346
00:37:55,460 --> 00:37:55,820
这是什么

1347
00:37:55,840 --> 00:37:58,100
好像是在创建一个函数

1348
00:37:58,100 --> 00:38:00,260
类型是这个

1349
00:38:00,260 --> 00:38:02,040
然后返回给它

1350
00:38:02,040 --> 00:38:03,200
然后它在执行

1351
00:38:03,200 --> 00:38:05,500
为什么要懒编译呢

1352
00:38:05,500 --> 00:38:07,520
写到这我就不再看原码了

1353
00:38:07,520 --> 00:38:08,640
我直接给大家写了

1354
00:38:08,640 --> 00:38:10,800
好 我们把它关掉

1355
00:38:10,800 --> 00:38:14,380
好 我给大家写这个过程

1356
00:38:14,380 --> 00:38:15,780
我们开始写了

1357
00:38:15,780 --> 00:38:18,540
好 我们建一个文件夹

1358
00:38:18,540 --> 00:38:19,180
叫table

1359
00:38:19,180 --> 00:38:22,740
然后里面建一个

1360
00:38:22,740 --> 00:38:23,500
英态的接线文件

1361
00:38:23,500 --> 00:38:25,820
对吧

1362
00:38:25,820 --> 00:38:29,020
然后里边示标导出一个thinkhook的属性

1363
00:38:29,020 --> 00:38:29,720
好

1364
00:38:29,720 --> 00:38:32,340
我们来建个thinkhook这样一个文件

1365
00:38:32,340 --> 00:38:36,840
然后你看这要怎么样引进来吧

1366
00:38:36,840 --> 00:38:39,500
letthinkhook等于一个require

1367
00:38:39,500 --> 00:38:40,500
我们叫什么

1368
00:38:40,500 --> 00:38:42,360
叫第二杠thinkhook的解释

1369
00:38:42,360 --> 00:38:42,700
是吧

1370
00:38:42,700 --> 00:38:43,820
然后呢

1371
00:38:43,820 --> 00:38:46,400
modulatepose等于一个对象

1372
00:38:46,400 --> 00:38:48,300
导出谁

1373
00:38:48,300 --> 00:38:49,440
导出我们thinkhook

1374
00:38:49,440 --> 00:38:49,700
是不是

1375
00:38:49,700 --> 00:38:51,020
导出他就可以了

1376
00:38:51,020 --> 00:38:54,060
那么他是什么呀

1377
00:38:54,060 --> 00:38:54,280
你看

1378
00:38:54,280 --> 00:38:54,740
想想

1379
00:38:54,740 --> 00:38:56,200
它是个hook吧

1380
00:38:56,200 --> 00:38:57,340
是什么

1381
00:38:57,340 --> 00:38:58,100
是一个类吧

1382
00:38:58,100 --> 00:38:58,580
对

1383
00:38:58,580 --> 00:39:00,080
所以我们要找个干嘛

1384
00:39:00,080 --> 00:39:01,200
是要写一个class

1385
00:39:01,200 --> 00:39:02,740
thinkhook

1386
00:39:02,740 --> 00:39:06,300
然后导出吧

1387
00:39:06,300 --> 00:39:08,240
module.adpost

1388
00:39:08,240 --> 00:39:10,300
等于一个thinkhook

1389
00:39:10,300 --> 00:39:12,240
OK

1390
00:39:12,240 --> 00:39:14,800
但是在我们这个例子里边啊

1391
00:39:14,800 --> 00:39:15,420
你会发现

1392
00:39:15,420 --> 00:39:16,020
这个

1393
00:39:16,020 --> 00:39:18,000
这个thinkhook呀

1394
00:39:18,000 --> 00:39:19,140
它是个

1395
00:39:19,140 --> 00:39:19,660
它是什么

1396
00:39:19,660 --> 00:39:20,120
它是一个

1397
00:39:20,120 --> 00:39:21,400
其实它有个继承的关系啊

1398
00:39:21,400 --> 00:39:21,580
你看

1399
00:39:21,580 --> 00:39:22,440
再给它看一下

1400
00:39:22,440 --> 00:39:23,880
给它看一下

1401
00:39:23,880 --> 00:39:24,720
第八个呀

1402
00:39:24,740 --> 00:39:30,820
其实你看

1403
00:39:30,820 --> 00:39:31,840
其实它的里边

1404
00:39:31,840 --> 00:39:32,680
你看

1405
00:39:32,680 --> 00:39:35,360
这个ThinkHook是什么

1406
00:39:35,360 --> 00:39:36,080
是一个子类

1407
00:39:36,080 --> 00:39:37,460
它会计成个负类

1408
00:39:37,460 --> 00:39:38,240
什么负类

1409
00:39:38,240 --> 00:39:38,620
Hook

1410
00:39:38,620 --> 00:39:40,920
然后另外它会里边

1411
00:39:40,920 --> 00:39:41,460
会计个什么

1412
00:39:41,460 --> 00:39:42,360
你有个ThinkFontory

1413
00:39:42,360 --> 00:39:43,580
CodeFontory

1414
00:39:43,580 --> 00:39:45,020
就是叫什么

1415
00:39:45,020 --> 00:39:46,300
叫代码的工程

1416
00:39:46,300 --> 00:39:47,080
对吧

1417
00:39:47,080 --> 00:39:49,980
它你看ThinkHook怎么写的

1418
00:39:49,980 --> 00:39:52,000
它就是计成一些负类

1419
00:39:52,000 --> 00:39:54,720
然后实验了一个compare方法

1420
00:39:54,740 --> 00:39:55,900
confiler里面呢

1421
00:39:55,900 --> 00:39:57,800
是不是用了factory.setup

1422
00:39:57,800 --> 00:39:59,160
先去建立

1423
00:39:59,160 --> 00:40:00,640
然后呢创建啊

1424
00:40:00,640 --> 00:40:01,260
对吧

1425
00:40:01,260 --> 00:40:02,860
它其实这两步啊

1426
00:40:02,860 --> 00:40:04,300
其实是最终会怎么样

1427
00:40:04,300 --> 00:40:05,520
会返回一个函数

1428
00:40:05,520 --> 00:40:06,660
好

1429
00:40:06,660 --> 00:40:08,060
我们先写这个synchook啊

1430
00:40:08,060 --> 00:40:08,700
好

1431
00:40:08,700 --> 00:40:09,120
我们来写

1432
00:40:09,120 --> 00:40:10,280
这synchook啊

1433
00:40:10,280 --> 00:40:11,300
它要继承一个负类

1434
00:40:11,300 --> 00:40:11,880
谁啊

1435
00:40:11,880 --> 00:40:12,200
hook

1436
00:40:12,200 --> 00:40:15,000
好

1437
00:40:15,000 --> 00:40:16,320
hook呢也是个类啊

1438
00:40:16,320 --> 00:40:17,340
我们要继承过来

1439
00:40:17,340 --> 00:40:18,620
好

1440
00:40:18,620 --> 00:40:19,640
我们拉一条hook

1441
00:40:19,640 --> 00:40:21,000
等于一个require

1442
00:40:21,000 --> 00:40:23,860
叫.hook

1443
00:40:23,860 --> 00:40:24,720
是不是啊

1444
00:40:24,720 --> 00:40:28,720
它这里面你看这两个方法都是什么都是没实现啊

1445
00:40:28,720 --> 00:40:31,720
是先这个compiler是编译啊对不对

1446
00:40:31,720 --> 00:40:34,720
它是只实现了吗有compiler方法对不对

1447
00:40:34,720 --> 00:40:37,720
编译啊好写啊compiler

1448
00:40:37,720 --> 00:40:39,720
只实现了compiler

1449
00:40:39,720 --> 00:40:44,720
compiler里面呢有两个有两个东西是不是

1450
00:40:44,720 --> 00:40:46,720
第一个叫什么叫factory

1451
00:40:46,720 --> 00:40:49,720
第二什么叫setup

1452
00:40:49,720 --> 00:40:51,720
这个options

1453
00:40:51,720 --> 00:40:57,440
第二个叫return factory 第二个create options

1454
00:41:21,720 --> 00:41:24,280
是一个hook的代码工厂

1455
00:41:24,280 --> 00:41:26,160
可以通过他的生成什么hook的代码

1456
00:41:26,160 --> 00:41:26,880
是不是factory

1457
00:41:26,880 --> 00:41:28,180
是吧

1458
00:41:28,180 --> 00:41:29,360
另外他就可以了

1459
00:41:29,360 --> 00:41:29,960
好

1460
00:41:29,960 --> 00:41:32,460
那他这么来的肯定也是个类吧

1461
00:41:32,460 --> 00:41:33,720
也要去引入进来

1462
00:41:33,720 --> 00:41:34,000
是不是

1463
00:41:34,000 --> 00:41:34,760
require

1464
00:41:34,760 --> 00:41:38,900
叫点刚什么呀

1465
00:41:38,900 --> 00:41:40,060
hook code factory

1466
00:41:40,060 --> 00:41:40,400
是不是

1467
00:41:40,400 --> 00:41:41,840
你看啊

1468
00:41:41,840 --> 00:41:43,960
我这儿继承了一个负类

1469
00:41:43,960 --> 00:41:45,240
然后他继承下来了

1470
00:41:45,240 --> 00:41:45,420
是不是

1471
00:41:45,420 --> 00:41:46,280
然后呢

1472
00:41:46,280 --> 00:41:47,140
我们去require了什么

1473
00:41:47,140 --> 00:41:48,000
一个代码工厂

1474
00:41:48,000 --> 00:41:48,760
然后呢

1475
00:41:48,760 --> 00:41:49,620
我去require这个工厂

1476
00:41:49,620 --> 00:41:50,680
得到一个工厂的实力

1477
00:41:50,680 --> 00:41:52,280
然后在compiler里边呢

1478
00:41:52,280 --> 00:41:52,920
你看我怎么做的

1479
00:41:52,920 --> 00:41:56,040
我是不是相当于我是setup了

1480
00:41:56,040 --> 00:41:57,040
是不是啊

1481
00:41:57,040 --> 00:41:59,400
然后这个先去setup一下

1482
00:41:59,400 --> 00:42:01,100
然后再去创建啊

1483
00:42:01,100 --> 00:42:02,420
那到底他在干嘛

1484
00:42:02,420 --> 00:42:03,220
他在干嘛呢

1485
00:42:03,220 --> 00:42:04,380
我们写一下是不是

1486
00:42:04,380 --> 00:42:05,000
别着急啊

1487
00:42:05,000 --> 00:42:05,460
我们写一下

1488
00:42:05,460 --> 00:42:08,360
另外这个hook我们要建下吧

1489
00:42:08,360 --> 00:42:09,940
把它两个类建一下啊

1490
00:42:09,940 --> 00:42:10,840
建下我们hook来

1491
00:42:10,840 --> 00:42:13,220
别着急啊

1492
00:42:13,220 --> 00:42:14,700
一共就这四个文件hook

1493
00:42:14,700 --> 00:42:16,420
hook就是我刚才写这个吧

1494
00:42:16,420 --> 00:42:17,200
是不是他呀

1495
00:42:17,200 --> 00:42:18,160
考过来

1496
00:42:18,160 --> 00:42:20,600
写了半天就写这个hook是不是啊

1497
00:42:20,680 --> 00:42:24,280
好 这么能hook

1498
00:42:24,280 --> 00:42:25,600
导出吧

1499
00:42:25,600 --> 00:42:31,000
modu.add post等于hook

1500
00:42:31,000 --> 00:42:31,740
导出

1501
00:42:31,740 --> 00:42:33,320
然后呢

1502
00:42:33,320 --> 00:42:36,480
那么还有一个什么factory吧

1503
00:42:36,480 --> 00:42:39,200
好 建个这个文件

1504
00:42:39,200 --> 00:42:40,720
hook code factory

1505
00:42:40,720 --> 00:42:42,460
建一下这个文件

1506
00:42:42,460 --> 00:42:43,960
叫gs 是吧

1507
00:42:43,960 --> 00:42:46,140
那么它也是个类

1508
00:42:46,140 --> 00:42:46,960
你要留一下吧

1509
00:42:46,960 --> 00:42:47,860
也个class 是吧

1510
00:42:47,860 --> 00:42:48,660
然后导出

1511
00:42:48,660 --> 00:42:49,820
modu

1512
00:42:49,820 --> 00:42:52,280
ad pause等于它

1513
00:42:52,280 --> 00:42:52,500
是不是

1514
00:42:52,500 --> 00:42:54,120
好

1515
00:42:54,120 --> 00:42:55,640
那关键看这个

1516
00:42:55,640 --> 00:42:56,620
你看啊

1517
00:42:56,620 --> 00:42:57,820
刚才我们已经知道了

1518
00:42:57,820 --> 00:42:58,520
在咱们什么

1519
00:42:58,520 --> 00:42:59,840
在咱们执行这个

1520
00:42:59,840 --> 00:43:02,700
你看啊

1521
00:43:02,700 --> 00:43:07,400
刚才我们怎么用的呀

1522
00:43:07,400 --> 00:43:09,000
你看我们怎么用的呀

1523
00:43:09,000 --> 00:43:09,880
得一步来

1524
00:43:09,880 --> 00:43:11,020
咱们先第一步看谁

1525
00:43:11,020 --> 00:43:11,840
看咱们的new

1526
00:43:11,840 --> 00:43:13,460
new这个自行hook

1527
00:43:13,460 --> 00:43:14,580
new它的时候

1528
00:43:14,580 --> 00:43:15,540
你看到这来了吧

1529
00:43:15,540 --> 00:43:16,780
是不是这个呀

1530
00:43:16,780 --> 00:43:18,480
然后它进行了hook这个负类

1531
00:43:18,480 --> 00:43:20,020
然后返回来的实例

1532
00:43:20,020 --> 00:43:20,260
是不是

1533
00:43:20,260 --> 00:43:22,800
那他的各团核里边

1534
00:43:22,800 --> 00:43:23,960
他的各团核谁

1535
00:43:23,960 --> 00:43:24,420
是不是就是

1536
00:43:24,420 --> 00:43:26,680
这个呀

1537
00:43:26,680 --> 00:43:27,560
是不是他呀

1538
00:43:27,560 --> 00:43:28,200
各团核吧

1539
00:43:28,200 --> 00:43:29,440
就会掉他

1540
00:43:29,440 --> 00:43:31,280
然后会把OAG传给他

1541
00:43:31,280 --> 00:43:32,800
然后他会缓存一个什么呀

1542
00:43:32,800 --> 00:43:33,940
他的层路层路数组

1543
00:43:33,940 --> 00:43:34,760
对吧

1544
00:43:34,760 --> 00:43:35,180
数组

1545
00:43:35,180 --> 00:43:38,000
还会怎么样

1546
00:43:38,000 --> 00:43:39,040
声明一个

1547
00:43:39,040 --> 00:43:40,680
鉴定函数数组

1548
00:43:40,680 --> 00:43:41,740
用来保存别的函数

1549
00:43:41,740 --> 00:43:42,260
对吧

1550
00:43:42,260 --> 00:43:44,400
那当你type的时候

1551
00:43:44,400 --> 00:43:45,980
是往那个数字里边放了

1552
00:43:45,980 --> 00:43:47,120
是不是

1553
00:43:47,120 --> 00:43:48,060
你看

1554
00:43:48,060 --> 00:43:50,260
这个thinkhook是不是继续了hook

1555
00:43:50,260 --> 00:43:52,340
那么当你那过难的时候

1556
00:43:52,340 --> 00:43:54,100
当你new这个thinkhook的时候

1557
00:43:54,100 --> 00:43:55,440
它会掉着各档数是不是

1558
00:43:55,440 --> 00:43:56,740
掉它各档数吧

1559
00:43:56,740 --> 00:43:58,580
然后会把这个数字传进来

1560
00:43:58,580 --> 00:43:59,900
然后缓存这两个变量

1561
00:43:59,900 --> 00:44:00,880
记住了吧

1562
00:44:00,880 --> 00:44:02,460
放到这了

1563
00:44:02,460 --> 00:44:03,220
放到这了是不是

1564
00:44:03,220 --> 00:44:05,040
相当于这一步啊

1565
00:44:05,040 --> 00:44:05,860
咱们type就写完了

1566
00:44:05,860 --> 00:44:08,780
关键看第二步什么呀

1567
00:44:08,780 --> 00:44:09,580
咱们的call

1568
00:44:09,580 --> 00:44:10,920
不是咱们这个call是不是

1569
00:44:10,920 --> 00:44:12,160
call怎么写的

1570
00:44:12,160 --> 00:44:14,060
一步来看看call怎么写的啊

1571
00:44:14,060 --> 00:44:16,080
关键看它怎么写的

1572
00:44:16,080 --> 00:44:17,480
call

1573
00:44:17,480 --> 00:44:19,180
这个方法在这时间点

1574
00:44:19,180 --> 00:44:20,500
在这去创建点

1575
00:44:20,500 --> 00:44:22,060
看一下这个圆码里面怎么做的

1576
00:44:22,060 --> 00:44:30,320
看这个cost在哪做的

1577
00:44:30,320 --> 00:44:31,560
咱们回顾一下

1578
00:44:31,560 --> 00:44:33,840
走

1579
00:44:33,840 --> 00:44:35,440
走进来

1580
00:44:35,440 --> 00:44:37,320
是不是在这

1581
00:44:37,320 --> 00:44:38,600
这什么有一个你看

1582
00:44:38,600 --> 00:44:40,460
先去教养我们的z点什么

1583
00:44:40,460 --> 00:44:41,380
create call

1584
00:44:41,380 --> 00:44:42,760
然后返回了一个函数

1585
00:44:42,760 --> 00:44:43,640
那执行啊

1586
00:44:43,640 --> 00:44:43,960
对不对

1587
00:44:43,960 --> 00:44:45,400
它其实在这做的

1588
00:44:45,400 --> 00:44:45,700
对吧

1589
00:44:45,700 --> 00:44:46,380
在这做的

1590
00:44:46,380 --> 00:44:49,560
它其实是先返回的什么

1591
00:44:49,560 --> 00:44:50,220
返回的函数

1592
00:44:50,220 --> 00:44:52,320
所以我们要把它实现一下吧

1593
00:44:52,320 --> 00:44:52,840
对吧

1594
00:44:52,840 --> 00:44:54,360
我们实现这个credit call方法

1595
00:44:54,360 --> 00:44:54,680
对不对

1596
00:44:54,680 --> 00:44:55,540
好

1597
00:44:55,540 --> 00:44:56,660
回到咱们代码里面来

1598
00:44:56,660 --> 00:44:58,760
找到咱们的hook方法

1599
00:44:58,760 --> 00:45:00,480
咱们在这

1600
00:45:00,480 --> 00:45:01,380
夹语方法叫什么

1601
00:45:01,380 --> 00:45:01,900
就叫什么

1602
00:45:01,900 --> 00:45:02,320
叫call

1603
00:45:02,320 --> 00:45:04,700
我在实现

1604
00:45:04,700 --> 00:45:05,220
我现在在干嘛

1605
00:45:05,220 --> 00:45:06,220
我在实现这个call方法

1606
00:45:06,220 --> 00:45:07,940
call方法谁

1607
00:45:07,940 --> 00:45:08,920
call方法就是这里边的什么

1608
00:45:08,920 --> 00:45:09,460
这个call方法

1609
00:45:09,460 --> 00:45:09,880
看到了吧

1610
00:45:09,880 --> 00:45:11,000
是不是实现这个方法呀

1611
00:45:11,000 --> 00:45:12,860
是不是要出发的呀

1612
00:45:12,860 --> 00:45:13,940
那么它的参数啥

1613
00:45:13,940 --> 00:45:14,540
是不是一个

1614
00:45:14,540 --> 00:45:15,760
这是它的参数吧

1615
00:45:15,760 --> 00:45:17,040
有几个不一定

1616
00:45:17,040 --> 00:45:18,780
对吧

1617
00:45:18,780 --> 00:45:19,500
因为你看

1618
00:45:19,500 --> 00:45:20,480
你知道定义几个

1619
00:45:20,480 --> 00:45:21,320
他就传几个是不是

1620
00:45:21,320 --> 00:45:22,000
对吧

1621
00:45:22,000 --> 00:45:22,620
就不一定

1622
00:45:22,620 --> 00:45:23,940
所以我们就来什么

1623
00:45:23,940 --> 00:45:25,620
我们这就来执行就可以了

1624
00:45:25,620 --> 00:45:28,080
靠这么多点

1625
00:45:28,080 --> 00:45:29,100
第一步怎么样

1626
00:45:29,100 --> 00:45:29,580
我们 return

1627
00:45:29,580 --> 00:45:31,040
他会

1628
00:45:31,040 --> 00:45:31,400
你看

1629
00:45:31,400 --> 00:45:32,680
他会返回什么

1630
00:45:32,680 --> 00:45:34,440
Let him

1631
00:45:34,440 --> 00:45:35,420
Let him call什么呀

1632
00:45:35,420 --> 00:45:36,040
Method

1633
00:45:36,040 --> 00:45:38,220
等于什么

1634
00:45:38,220 --> 00:45:39,620
等于一个Z的点下回线

1635
00:45:39,620 --> 00:45:40,240
Create call

1636
00:45:40,240 --> 00:45:42,720
对吧

1637
00:45:42,720 --> 00:45:43,680
什么意思

1638
00:45:43,680 --> 00:45:45,120
就这样我们的Z的点

1639
00:45:45,120 --> 00:45:46,060
credit call方法

1640
00:45:46,060 --> 00:45:47,780
然后返回一个什么

1641
00:45:47,780 --> 00:45:49,300
返回一个call的一个函数

1642
00:45:49,300 --> 00:45:51,420
这函数不是写死的

1643
00:45:51,420 --> 00:45:52,620
是动态生成的

1644
00:45:52,620 --> 00:45:53,280
就是说这个

1645
00:45:53,280 --> 00:45:55,600
要执行的什么

1646
00:45:55,600 --> 00:45:57,920
执行的函数

1647
00:45:57,920 --> 00:46:01,000
是什么

1648
00:46:01,000 --> 00:46:02,580
不是写死的

1649
00:46:02,580 --> 00:46:04,700
而是什么

1650
00:46:04,700 --> 00:46:06,600
动态生成的

1651
00:46:06,600 --> 00:46:07,660
你看

1652
00:46:07,660 --> 00:46:09,280
第二种credit call方法

1653
00:46:09,280 --> 00:46:10,980
它会返回一个call的函数

1654
00:46:10,980 --> 00:46:12,620
然后再执行它吧

1655
00:46:12,620 --> 00:46:13,480
这么执行啊

1656
00:46:13,480 --> 00:46:13,960
你得想写

1657
00:46:13,960 --> 00:46:14,400
 return

1658
00:46:14,400 --> 00:46:17,160
靠点什么

1659
00:46:17,160 --> 00:46:17,560
apply

1660
00:46:17,560 --> 00:46:19,440
然后this

1661
00:46:19,440 --> 00:46:20,440
然后呢

1662
00:46:20,440 --> 00:46:20,940
点

1663
00:46:20,940 --> 00:46:22,260
lgs

1664
00:46:22,260 --> 00:46:30,720
当你call的时候

1665
00:46:30,720 --> 00:46:32,340
你是不是可以传参数过来啊

1666
00:46:32,340 --> 00:46:32,520
你看

1667
00:46:32,520 --> 00:46:34,400
这是不是传了两个参数啊

1668
00:46:34,400 --> 00:46:35,100
把它拿过来

1669
00:46:35,100 --> 00:46:36,000
放到这

1670
00:46:36,000 --> 00:46:36,360
你看

1671
00:46:36,360 --> 00:46:38,140
我这接受怎么接受

1672
00:46:38,140 --> 00:46:39,180
lg等于速度

1673
00:46:39,180 --> 00:46:41,280
是比他呀

1674
00:46:41,280 --> 00:46:41,540
看

1675
00:46:41,540 --> 00:46:42,900
我先创业一个

1676
00:46:42,900 --> 00:46:44,600
创业一个临时的一个函数

1677
00:46:44,600 --> 00:46:45,860
然后呢让它怎么样

1678
00:46:45,860 --> 00:46:46,660
apply什么意思

1679
00:46:46,660 --> 00:46:47,340
让它执行

1680
00:46:47,340 --> 00:46:48,620
类的指向

1681
00:46:48,620 --> 00:46:50,460
类的指向当前这个户口的实例

1682
00:46:50,460 --> 00:46:53,200
然后IGS呢会传给它作为参数

1683
00:46:53,200 --> 00:46:56,080
这个明白吧

1684
00:46:56,080 --> 00:46:57,960
就说当地要call的时候

1685
00:46:57,960 --> 00:46:58,980
我会创业一个函数

1686
00:46:58,980 --> 00:47:00,280
然后呢那执行

1687
00:47:00,280 --> 00:47:01,620
传入我干传的参数就可以了

1688
00:47:01,620 --> 00:47:01,820
是不是

1689
00:47:01,820 --> 00:47:04,640
那么这个函数到底在干嘛呢

1690
00:47:04,640 --> 00:47:05,820
它在干嘛

1691
00:47:05,820 --> 00:47:07,820
它是不是要执行我们的

1692
00:47:07,820 --> 00:47:10,200
这个函数和这个函数啊

1693
00:47:10,200 --> 00:47:11,480
所以说它要干嘛

1694
00:47:11,480 --> 00:47:12,820
它要动态的一个组合

1695
00:47:12,820 --> 00:47:13,920
怎么能在祖国呢

1696
00:47:13,920 --> 00:47:14,440
他会把什么

1697
00:47:14,440 --> 00:47:17,080
把这个函数和这个函数

1698
00:47:17,080 --> 00:47:17,740
对吧

1699
00:47:17,740 --> 00:47:18,820
变成一个函数

1700
00:47:18,820 --> 00:47:19,760
让他一次执行

1701
00:47:19,760 --> 00:47:21,320
这才要干的事情

1702
00:47:21,320 --> 00:47:22,920
想想是不是

1703
00:47:22,920 --> 00:47:24,800
这是个监听啊

1704
00:47:24,800 --> 00:47:26,440
然后他是他是比较出发

1705
00:47:26,440 --> 00:47:29,240
他出发这叫监听函数要执行啊

1706
00:47:29,240 --> 00:47:29,520
是不是

1707
00:47:29,520 --> 00:47:31,000
所以他要把什么

1708
00:47:31,000 --> 00:47:32,540
把这两个方式怎么样

1709
00:47:32,540 --> 00:47:33,820
都要执行

1710
00:47:33,820 --> 00:47:34,820
对吧

1711
00:47:34,820 --> 00:47:35,540
所以我会怎么样

1712
00:47:35,540 --> 00:47:37,140
我会构建一个大的一个方式

1713
00:47:37,140 --> 00:47:38,360
然后把他们怎么样

1714
00:47:38,360 --> 00:47:40,020
作为他的函数体的一部分

1715
00:47:40,020 --> 00:47:41,240
一次调用

1716
00:47:41,240 --> 00:47:42,840
然后并且把他参数怎么样

1717
00:47:42,840 --> 00:47:44,200
把他参数传给他

1718
00:47:44,200 --> 00:47:44,520
是不是

1719
00:47:44,520 --> 00:47:46,200
就这样写

1720
00:47:46,200 --> 00:47:49,720
ok吗

1721
00:47:49,720 --> 00:47:51,980
就说他他在干嘛用

1722
00:47:51,980 --> 00:47:53,960
这在动态创新函数啊

1723
00:47:53,960 --> 00:47:54,400
创新函数

1724
00:47:54,400 --> 00:47:55,420
那他在干嘛

1725
00:47:55,420 --> 00:47:56,340
你看怎么是行的

1726
00:47:56,340 --> 00:47:57,460
写方法啊

1727
00:47:57,460 --> 00:47:57,960
cretical

1728
00:47:57,960 --> 00:47:59,760
然后怎么写

1729
00:47:59,760 --> 00:48:00,880
return什么

1730
00:48:00,880 --> 00:48:01,400
一个什么

1731
00:48:01,400 --> 00:48:01,960
一个这个

1732
00:48:01,960 --> 00:48:04,800
compile

1733
00:48:04,800 --> 00:48:05,680
编译吧

1734
00:48:05,680 --> 00:48:07,360
然后第一个参数呢

1735
00:48:07,360 --> 00:48:08,080
是咱们什么

1736
00:48:08,080 --> 00:48:08,920
咱们这个

1737
00:48:08,920 --> 00:48:10,060
他会传入一个什么

1738
00:48:10,060 --> 00:48:10,560
一个对象

1739
00:48:10,560 --> 00:48:11,940
第一个是types

1740
00:48:11,940 --> 00:48:14,480
types是由我们这个速度吧

1741
00:48:14,480 --> 00:48:15,240
就是z的types

1742
00:48:15,240 --> 00:48:16,280
把它拿过来放到这

1743
00:48:16,280 --> 00:48:16,780
对吧

1744
00:48:16,780 --> 00:48:20,100
还要传入什么

1745
00:48:20,100 --> 00:48:21,100
传入一个algs

1746
00:48:21,100 --> 00:48:23,780
谁是不是参数啊

1747
00:48:23,780 --> 00:48:25,820
z的algs

1748
00:48:25,820 --> 00:48:26,660
对不对

1749
00:48:26,660 --> 00:48:27,400
你看

1750
00:48:27,400 --> 00:48:30,140
我现在我要通过compile方法

1751
00:48:30,140 --> 00:48:31,380
动态的拿到什么

1752
00:48:31,380 --> 00:48:32,680
拿到一个函数

1753
00:48:32,680 --> 00:48:34,700
动态编出来一个函数

1754
00:48:34,700 --> 00:48:36,240
它里面参数两个

1755
00:48:36,240 --> 00:48:36,600
一个是什么

1756
00:48:36,600 --> 00:48:39,040
一个是要实行的函数的那个速度

1757
00:48:39,040 --> 00:48:39,540
对吧

1758
00:48:39,540 --> 00:48:41,080
但是它是个对象

1759
00:48:41,080 --> 00:48:41,760
它现在是个对象

1760
00:48:41,760 --> 00:48:43,560
是不是对象数组

1761
00:48:43,560 --> 00:48:44,760
是不是option数组

1762
00:48:44,760 --> 00:48:45,660
对吧

1763
00:48:45,660 --> 00:48:46,520
另外什么

1764
00:48:46,520 --> 00:48:46,980
参数

1765
00:48:46,980 --> 00:48:49,240
是不是参数数组

1766
00:48:49,240 --> 00:48:50,220
就它呀

1767
00:48:50,220 --> 00:48:50,580
对不对

1768
00:48:50,580 --> 00:48:51,920
好

1769
00:48:51,920 --> 00:48:53,480
下面来看

1770
00:48:53,480 --> 00:48:55,240
看这个compiler吧

1771
00:48:55,240 --> 00:48:57,340
是不是看到编译方法怎么实现的呀

1772
00:48:57,340 --> 00:48:57,840
对不对

1773
00:48:57,840 --> 00:48:59,340
好

1774
00:48:59,340 --> 00:49:01,080
编译方法咱们写在子类里边

1775
00:49:01,080 --> 00:49:03,160
我知道刚才是子类里边写好了

1776
00:49:03,160 --> 00:49:04,960
是不是在这写了compiler方法呀

1777
00:49:04,960 --> 00:49:05,840
编译啊

1778
00:49:05,840 --> 00:49:06,080
对吧

1779
00:49:06,080 --> 00:49:06,840
它这是编译的

1780
00:49:06,840 --> 00:49:08,940
这编译第一步是怎么样

1781
00:49:08,940 --> 00:49:09,640
setup

1782
00:49:09,640 --> 00:49:10,440
第二步怎么样

1783
00:49:10,440 --> 00:49:10,920
create

1784
00:49:10,920 --> 00:49:12,300
它为怎么样

1785
00:49:12,300 --> 00:49:13,860
让factory来怎么样

1786
00:49:13,860 --> 00:49:14,900
来于创建和什么

1787
00:49:14,900 --> 00:49:16,020
和创建函数

1788
00:49:16,020 --> 00:49:18,040
那factory怎么实践呢

1789
00:49:18,040 --> 00:49:18,400
你看

1790
00:49:18,400 --> 00:49:19,560
它是不是有两方法

1791
00:49:19,560 --> 00:49:19,980
一个要setup

1792
00:49:19,980 --> 00:49:20,500
一个要create

1793
00:49:20,500 --> 00:49:21,720
是不是

1794
00:49:21,720 --> 00:49:23,900
这个codefactory有两个方法

1795
00:49:23,900 --> 00:49:24,460
一个要setup

1796
00:49:24,460 --> 00:49:24,980
一个要create

1797
00:49:24,980 --> 00:49:25,640
好

1798
00:49:25,640 --> 00:49:27,900
那回到这个codefactory来

1799
00:49:27,900 --> 00:49:30,280
codefactory里边来

1800
00:49:30,280 --> 00:49:31,240
接两个方法

1801
00:49:31,240 --> 00:49:31,700
一个什么

1802
00:49:31,700 --> 00:49:32,240
一个要setup

1803
00:49:32,240 --> 00:49:32,500
是不是

1804
00:49:32,500 --> 00:49:33,060
setup

1805
00:49:33,060 --> 00:49:34,320
建立

1806
00:49:34,320 --> 00:49:35,140
setup

1807
00:49:35,140 --> 00:49:38,560
一个什么

1808
00:49:38,560 --> 00:49:40,560
我们要create创建

1809
00:49:40,560 --> 00:49:42,560
他有两个方法

1810
00:49:42,560 --> 00:49:45,560
先看我们的setup

1811
00:49:45,560 --> 00:49:47,560
setup里面

1812
00:49:47,560 --> 00:49:49,560
是不是有两个参数

1813
00:49:49,560 --> 00:49:51,560
看这个setup里面

1814
00:49:51,560 --> 00:49:54,560
是不是传入了z.options

1815
00:49:54,560 --> 00:49:57,560
z.options是谁

1816
00:49:57,560 --> 00:50:02,560
factory,setup,z和options

1817
00:50:05,560 --> 00:50:08,280
在这 compiler 传来一个options

1818
00:50:08,280 --> 00:50:09,240
把这个拿过来

1819
00:50:09,240 --> 00:50:12,280
这应该是逗号吧

1820
00:50:12,280 --> 00:50:16,880
当我在这编译的时候

1821
00:50:16,880 --> 00:50:18,040
我要把这个东西传过来

1822
00:50:18,040 --> 00:50:18,480
谁呀

1823
00:50:18,480 --> 00:50:20,000
typedalgs传过来

1824
00:50:20,000 --> 00:50:21,400
传给谁传给他是不是

1825
00:50:21,400 --> 00:50:22,640
传给 compiler吧

1826
00:50:22,640 --> 00:50:24,560
传给他之后你看他会传给谁

1827
00:50:24,560 --> 00:50:25,760
我们的factory setup

1828
00:50:25,760 --> 00:50:26,920
对吧

1829
00:50:26,920 --> 00:50:28,800
这会给他传了两个参数啊

1830
00:50:28,800 --> 00:50:31,000
现在就传了两个参数啊

1831
00:50:31,000 --> 00:50:32,560
一个叫this

1832
00:50:32,560 --> 00:50:33,800
this代表谁当写的hook

1833
00:50:33,800 --> 00:50:34,280
是吧

1834
00:50:34,640 --> 00:50:37,080
this代表当前的hook

1835
00:50:37,080 --> 00:50:42,060
对吧

1836
00:50:42,060 --> 00:50:44,120
然后option代表什么

1837
00:50:44,120 --> 00:50:46,920
是不是配置对象

1838
00:50:46,920 --> 00:50:52,820
里面有啥是不是有个对象里面有什么

1839
00:50:52,820 --> 00:50:55,380
小关键aljs和我们的taps

1840
00:50:55,380 --> 00:50:56,140
是不是

1841
00:50:56,140 --> 00:50:57,160
就是说

1842
00:50:57,160 --> 00:50:58,200
现在我们要干嘛

1843
00:50:58,200 --> 00:50:59,220
我们要把他吗

1844
00:50:59,220 --> 00:51:02,800
你看setup在干嘛呢

1845
00:51:03,060 --> 00:51:06,380
setup他其实是什么他其实得到里面的一些什么鉴定的函数

1846
00:51:06,380 --> 00:51:08,440
我们看setup怎么做的啊

1847
00:51:08,440 --> 00:51:09,720
好 拿过来

1848
00:51:09,720 --> 00:51:11,260
这是不是有两个参数啊

1849
00:51:11,260 --> 00:51:13,560
对吧 两个参数 第一个参数是我们的instance

1850
00:51:13,560 --> 00:51:15,340
是不是hook的instance啊

1851
00:51:15,340 --> 00:51:16,620
hook的实力啊

1852
00:51:16,620 --> 00:51:18,940
第二呢是我们这个参数options

1853
00:51:18,940 --> 00:51:23,800
那么他在那嘛他其实有两个动作啊

1854
00:51:23,800 --> 00:51:25,840
一个叫this.options

1855
00:51:25,840 --> 00:51:26,620
等于options

1856
00:51:26,620 --> 00:51:29,940
第一步呢他要保存options

1857
00:51:29,940 --> 00:51:31,980
把这个什么把你传过来的options怎么样

1858
00:51:32,240 --> 00:51:34,660
保存到我们的工厂里边去

1859
00:51:34,660 --> 00:51:35,880
保存到什么

1860
00:51:35,880 --> 00:51:36,920
这个类的实力里边去

1861
00:51:36,920 --> 00:51:38,260
第二步呢

1862
00:51:38,260 --> 00:51:38,900
要做映射

1863
00:51:38,900 --> 00:51:39,920
怎么映射呢

1864
00:51:39,920 --> 00:51:40,640
你看我这样写

1865
00:51:40,640 --> 00:51:41,160
就是一个什么

1866
00:51:41,160 --> 00:51:42,320
就是一个这个

1867
00:51:42,320 --> 00:51:43,540
因子户incidence

1868
00:51:43,540 --> 00:51:44,160
第二什么

1869
00:51:44,160 --> 00:51:45,400
下游戏X

1870
00:51:45,400 --> 00:51:47,040
等于什么

1871
00:51:47,040 --> 00:51:50,260
等于一个这个option.taps.map

1872
00:51:50,260 --> 00:51:52,480
然后返回那边一个atom

1873
00:51:52,480 --> 00:51:54,860
然后返回什么atom.fn

1874
00:51:54,860 --> 00:51:56,080
什么意思

1875
00:51:56,080 --> 00:51:57,160
我会你看

1876
00:51:57,160 --> 00:51:58,860
option.taps.taps.m

1877
00:51:58,860 --> 00:51:59,600
是不是那个数组

1878
00:51:59,600 --> 00:52:01,260
数组放在一个对象

1879
00:52:01,260 --> 00:52:02,880
对象长什么样子

1880
00:52:02,880 --> 00:52:04,480
是不是很多对象

1881
00:52:04,480 --> 00:52:06,160
都有什么都有内幕和什么

1882
00:52:06,160 --> 00:52:07,540
和这个FN

1883
00:52:07,540 --> 00:52:10,140
是不是

1884
00:52:10,140 --> 00:52:12,120
然后你看

1885
00:52:12,120 --> 00:52:13,140
我做了个映射

1886
00:52:13,140 --> 00:52:14,460
拿出来

1887
00:52:14,460 --> 00:52:15,700
把它FN都拿出来了

1888
00:52:15,700 --> 00:52:16,940
现在一向

1889
00:52:16,940 --> 00:52:17,880
把它变成

1890
00:52:17,880 --> 00:52:19,520
变成一个FN数组了

1891
00:52:19,520 --> 00:52:20,640
然后付给雷

1892
00:52:20,640 --> 00:52:21,200
付给了我们

1893
00:52:21,200 --> 00:52:22,660
Who instance的夏威X

1894
00:52:22,660 --> 00:52:24,660
是不是付给了我们的

1895
00:52:24,660 --> 00:52:25,860
Hook实力的夏威X

1896
00:52:25,860 --> 00:52:26,820
谁

1897
00:52:26,820 --> 00:52:28,400
就Thinkhood的夏威X

1898
00:52:28,400 --> 00:52:29,520
付给他了

1899
00:52:29,520 --> 00:52:30,600
就说你看

1900
00:52:30,600 --> 00:52:31,520
z不在干嘛

1901
00:52:31,520 --> 00:52:33,700
z不其实啊

1902
00:52:33,700 --> 00:52:34,680
就是就一句话

1903
00:52:34,680 --> 00:52:37,760
就是z'点下位x等于什么呀

1904
00:52:37,760 --> 00:52:40,680
等于那个fn的数组

1905
00:52:40,680 --> 00:52:44,100
是不是这样的

1906
00:52:44,100 --> 00:52:47,900
让z'点x等于什么fn的数组

1907
00:52:47,900 --> 00:52:49,340
干了这件事情啊

1908
00:52:49,340 --> 00:52:50,020
他在干了这件事情

1909
00:52:50,020 --> 00:52:51,540
就是收集到这个数组了

1910
00:52:51,540 --> 00:52:52,240
是不是啊

1911
00:52:52,240 --> 00:52:52,880
下不干嘛

1912
00:52:52,880 --> 00:52:53,920
是把create给编译了

1913
00:52:53,920 --> 00:52:54,540
对吧

1914
00:52:54,540 --> 00:52:56,300
是把编译我们这个函数了

1915
00:52:56,300 --> 00:52:57,520
怎么编译呢

1916
00:52:57,520 --> 00:52:58,020
你看啊

1917
00:52:58,020 --> 00:52:59,900
把opin的长过来

1918
00:52:59,900 --> 00:53:01,100
开始编译了

1919
00:53:01,100 --> 00:53:02,100
拿过来放到这

1920
00:53:02,100 --> 00:53:04,260
你想想

1921
00:53:04,260 --> 00:53:06,300
我这要编译出来一个函数

1922
00:53:06,300 --> 00:53:07,800
那怎么编出来一个函数呢

1923
00:53:07,800 --> 00:53:08,500
我们要看个支点

1924
00:53:08,500 --> 00:53:09,500
给它讲支点

1925
00:53:09,500 --> 00:53:10,400
就说我们这个

1926
00:53:10,400 --> 00:53:11,000
比如说

1927
00:53:11,000 --> 00:53:14,700
2.f function

1928
00:53:14,700 --> 00:53:15,600
对吧

1929
00:53:15,600 --> 00:53:16,200
你看

1930
00:53:16,200 --> 00:53:17,300
以前我们写函数怎么写

1931
00:53:17,300 --> 00:53:18,300
会这么写 function

1932
00:53:18,300 --> 00:53:19,200
比如说a

1933
00:53:19,200 --> 00:53:22,300
比如说我们function sum吧

1934
00:53:22,300 --> 00:53:24,300
传了一个a和b

1935
00:53:24,300 --> 00:53:25,500
然后反回什么

1936
00:53:25,500 --> 00:53:26,500
a+b

1937
00:53:26,500 --> 00:53:27,600
是不是啊

1938
00:53:27,600 --> 00:53:29,100
这个sum是不是啊

1939
00:53:29,100 --> 00:53:29,740
是这么写的

1940
00:53:29,900 --> 00:53:32,760
还给你那么写let som2等于什么介绍函数

1941
00:53:32,760 --> 00:53:38,080
a和b返回什么一个a+b

1942
00:53:38,080 --> 00:53:39,020
是不是啊

1943
00:53:39,020 --> 00:53:39,880
是这种写法

1944
00:53:39,880 --> 00:53:44,480
还给你那么写这本写let som3等于什么new一个function

1945
00:53:44,480 --> 00:53:46,860
可以new一个函数

1946
00:53:46,860 --> 00:53:48,020
对吧

1947
00:53:48,020 --> 00:53:50,820
第一个参数呢是它的参数就是a和b

1948
00:53:50,820 --> 00:53:51,620
对吧

1949
00:53:51,620 --> 00:53:52,400
对吧

1950
00:53:52,400 --> 00:53:52,900
a和b

1951
00:53:52,900 --> 00:53:55,340
第二个参数呢是函数体我们就是什么

1952
00:53:55,340 --> 00:53:57,860
就是函数体就是我们的return a+b

1953
00:53:57,860 --> 00:53:59,300
这也可以是吧

1954
00:53:59,900 --> 00:54:01,900
你看看啊 执行一下这个SAM3

1955
00:54:01,900 --> 00:54:03,900
然后呢 里边呢 传一个什么

1956
00:54:03,900 --> 00:54:05,900
3和3

1957
00:54:05,900 --> 00:54:07,900
3和4吧

1958
00:54:07,900 --> 00:54:09,900
你有一个函数

1959
00:54:09,900 --> 00:54:11,900
你第一个参数呢 是它的参数

1960
00:54:11,900 --> 00:54:13,900
多少个开

1961
00:54:13,900 --> 00:54:15,900
你看第二层什么 是不是一个是啥

1962
00:54:15,900 --> 00:54:17,900
是不是一个子无串啊 就是一个函数体啊

1963
00:54:17,900 --> 00:54:19,900
你看一样呢

1964
00:54:19,900 --> 00:54:23,900
看看吧 是不是7啊

1965
00:54:23,900 --> 00:54:25,900
这也是创建函数的方式 大家了解啊

1966
00:54:25,900 --> 00:54:27,900
就是通过那个方式的创建函数

1967
00:54:27,900 --> 00:54:28,780
你这样创建啊

1968
00:54:28,780 --> 00:54:29,460
你这样创建

1969
00:54:29,460 --> 00:54:30,380
创建函数啊

1970
00:54:30,380 --> 00:54:32,460
动代创业函数啊

1971
00:54:32,460 --> 00:54:32,840
怎么做呢

1972
00:54:32,840 --> 00:54:33,440
你看这

1973
00:54:33,440 --> 00:54:35,780
create方法要动代创业函数怎么创建啊

1974
00:54:35,780 --> 00:54:36,540
好

1975
00:54:36,540 --> 00:54:37,000
return

1976
00:54:37,000 --> 00:54:38,840
一个他是不是

1977
00:54:38,840 --> 00:54:40,700
那然后你看第一个呢

1978
00:54:40,700 --> 00:54:41,660
他有参数

1979
00:54:41,660 --> 00:54:43,140
他的参数哪来的

1980
00:54:43,140 --> 00:54:44,960
参数是不是就是我们什么呀

1981
00:54:44,960 --> 00:54:46,040
是不是就我们这个

1982
00:54:46,040 --> 00:54:50,400
想想是不是就是个RGS数组啊

1983
00:54:50,400 --> 00:54:51,740
是不是啊

1984
00:54:51,740 --> 00:54:52,740
是不是他呀

1985
00:54:52,740 --> 00:54:55,940
但你看我们把这个RGS的Option怎么样

1986
00:54:55,940 --> 00:54:57,140
你看把这个

1987
00:54:57,140 --> 00:54:59,440
你看我们现在这个数字哪能拿到

1988
00:54:59,440 --> 00:55:01,240
啊

1989
00:55:01,240 --> 00:55:03,820
赛大的时候我传了option过来

1990
00:55:03,820 --> 00:55:04,620
你看啊

1991
00:55:04,620 --> 00:55:05,780
看我们这个户口里边

1992
00:55:05,780 --> 00:55:07,860
你看我这是不是传了option的啊

1993
00:55:07,860 --> 00:55:09,120
是不是啊

1994
00:55:09,120 --> 00:55:10,420
Compiler哪来的

1995
00:55:10,420 --> 00:55:11,620
看这

1996
00:55:11,620 --> 00:55:15,480
看当我去create a call的时候

1997
00:55:15,480 --> 00:55:16,680
调用crate call

1998
00:55:16,680 --> 00:55:18,380
当我调crate call的时候传过来

1999
00:55:18,380 --> 00:55:19,060
调用了吗

2000
00:55:19,060 --> 00:55:19,820
Compiler

2001
00:55:19,820 --> 00:55:21,800
Compiler传过来一个options

2002
00:55:21,800 --> 00:55:24,520
它是个对象

2003
00:55:24,520 --> 00:55:26,040
有两属性分别是taps和什么

2004
00:55:26,040 --> 00:55:27,020
下文LDS

2005
00:55:27,140 --> 00:55:29,780
他们俩哥们

2006
00:55:29,780 --> 00:55:31,740
让我把他给谁了

2007
00:55:31,740 --> 00:55:32,780
给了compiler

2008
00:55:32,780 --> 00:55:34,320
compiler在哪

2009
00:55:34,320 --> 00:55:35,520
compiler是不是就是

2010
00:55:35,520 --> 00:55:38,580
他的compiler到这了

2011
00:55:38,580 --> 00:55:39,720
opin的也是他

2012
00:55:39,720 --> 00:55:42,100
他给谁了

2013
00:55:42,100 --> 00:55:44,680
给了setup和create

2014
00:55:44,680 --> 00:55:46,200
就给他了

2015
00:55:46,200 --> 00:55:48,880
那么create里边是不是有个ARGS

2016
00:55:48,880 --> 00:55:50,100
是不是

2017
00:55:50,100 --> 00:55:51,580
fairy create方法

2018
00:55:51,580 --> 00:55:54,160
这是不是长的样子

2019
00:55:54,160 --> 00:55:56,520
你看那ARB怎么来的

2020
00:55:56,520 --> 00:55:57,640
是不是就是algs

2021
00:55:57,640 --> 00:55:58,920
就是什么

2022
00:55:58,920 --> 00:55:59,600
就是速度吧

2023
00:55:59,600 --> 00:56:01,040
dr join 逗号就可以了

2024
00:56:01,040 --> 00:56:01,200
是不是

2025
00:56:01,200 --> 00:56:02,660
想想是不是这样的

2026
00:56:02,660 --> 00:56:03,620
你看啊

2027
00:56:03,620 --> 00:56:03,940
就这

2028
00:56:03,940 --> 00:56:04,600
你看啊

2029
00:56:04,600 --> 00:56:04,980
这东西

2030
00:56:04,980 --> 00:56:06,380
就是options

2031
00:56:06,380 --> 00:56:06,860
dr 什么

2032
00:56:06,860 --> 00:56:09,740
dr join 逗号

2033
00:56:09,740 --> 00:56:13,400
因为algs在我们的例子里边使啥

2034
00:56:13,400 --> 00:56:15,700
是不是就是一个速度啊

2035
00:56:15,700 --> 00:56:16,960
是分别是name和什么

2036
00:56:16,960 --> 00:56:17,540
和aj

2037
00:56:17,540 --> 00:56:18,860
是不是

2038
00:56:18,860 --> 00:56:20,260
然后我这做了

2039
00:56:20,260 --> 00:56:21,400
做了一个join连接

2040
00:56:21,400 --> 00:56:22,220
变成了什么

2041
00:56:22,220 --> 00:56:23,220
变成一个name

2042
00:56:23,220 --> 00:56:24,440
逗号aj了

2043
00:56:24,440 --> 00:56:25,280
是不是刚好参户列表

2044
00:56:25,280 --> 00:56:26,140
是不是

2045
00:56:26,140 --> 00:56:32,020
一般情况下,我们会把它变成一个方法

2046
00:56:32,020 --> 00:56:34,080
比如说aljs

2047
00:56:34,080 --> 00:56:37,400
会这么做,会变成一个aljs的方法

2048
00:56:37,400 --> 00:56:39,200
然后它返回

2049
00:56:39,200 --> 00:56:42,520
z.options

2050
00:56:42,520 --> 00:56:48,420
aljs.john

2051
00:56:48,420 --> 00:56:51,220
它会这么写

2052
00:56:51,220 --> 00:56:54,040
因为在situp的时候,我是把options传过来了

2053
00:56:54,300 --> 00:56:56,300
给Z选Options了

2054
00:57:24,300 --> 00:57:25,660
这部分怎么写呢

2055
00:57:25,660 --> 00:57:26,600
我们不知道

2056
00:57:26,600 --> 00:57:26,940
这样吧

2057
00:57:26,940 --> 00:57:29,800
咱们看一下到底原生是怎么实现的是不是

2058
00:57:29,800 --> 00:57:31,360
看他编出来代码长什么样子

2059
00:57:31,360 --> 00:57:32,300
我们来模拟啊

2060
00:57:32,300 --> 00:57:33,100
比如说

2061
00:57:33,100 --> 00:57:33,700
看看啊

2062
00:57:33,700 --> 00:57:34,440
看看这儿

2063
00:57:34,440 --> 00:57:37,240
咱们回到咱们第八个里边来啊

2064
00:57:37,240 --> 00:57:38,640
看啊

2065
00:57:38,640 --> 00:57:39,700
你看他怎么编译了

2066
00:57:39,700 --> 00:57:40,900
看

2067
00:57:40,900 --> 00:57:41,540
这个样子

2068
00:57:41,540 --> 00:57:43,260
你看这才就是他编出来代码

2069
00:57:43,260 --> 00:57:45,100
就是我们利用方形就出来代码有这个样子

2070
00:57:45,100 --> 00:57:45,900
是不是

2071
00:57:45,900 --> 00:57:46,600
你看

2072
00:57:46,600 --> 00:57:47,000
先怎么样

2073
00:57:47,000 --> 00:57:48,500
我们先是外面掏了个方形

2074
00:57:48,500 --> 00:57:49,260
是不是

2075
00:57:49,260 --> 00:57:49,900
有两个参数

2076
00:57:49,900 --> 00:57:50,400
内蒙Age

2077
00:57:50,400 --> 00:57:50,800
是不是

2078
00:57:50,800 --> 00:57:52,100
参数列表吧

2079
00:57:52,100 --> 00:57:52,600
然后里面呢

2080
00:57:52,600 --> 00:57:53,100
我们写了什么

2081
00:57:53,100 --> 00:57:53,300
写了

2082
00:57:53,300 --> 00:57:54,760
就是strict 一个模式

2083
00:57:54,760 --> 00:57:55,860
然后写了什么

2084
00:57:55,860 --> 00:57:58,440
contest 没有用到 是不是

2085
00:57:58,440 --> 00:58:00,600
然后呢你看z的条下方线x等于什么

2086
00:58:00,600 --> 00:58:01,200
等于下方线x

2087
00:58:01,200 --> 00:58:03,240
这是写死的吧

2088
00:58:03,240 --> 00:58:04,960
这个数字还记得吗

2089
00:58:04,960 --> 00:58:06,960
是不是一个数字里面放了所有简单函数啊

2090
00:58:06,960 --> 00:58:07,960
对不对

2091
00:58:07,960 --> 00:58:10,000
什么时候负责值

2092
00:58:10,000 --> 00:58:11,300
是不是在我们setup负责值啊

2093
00:58:11,300 --> 00:58:13,660
翻过去setup的时候在干嘛

2094
00:58:13,660 --> 00:58:16,700
是不是我们我们给x负责了

2095
00:58:16,700 --> 00:58:18,440
给hook的x负责了

2096
00:58:18,440 --> 00:58:19,360
对吧

2097
00:58:19,360 --> 00:58:20,700
然后呢怎么负责值

2098
00:58:20,700 --> 00:58:22,160
是不是把它对样的

2099
00:58:22,160 --> 00:58:23,560
把对象的fn属性取回来

2100
00:58:23,560 --> 00:58:24,200
够一个数度

2101
00:58:24,200 --> 00:58:24,760
是不是

2102
00:58:24,760 --> 00:58:26,500
然后负给下方x吧

2103
00:58:26,500 --> 00:58:27,620
所以你看这

2104
00:58:27,620 --> 00:58:30,140
是不是可以在这下方x拿到这个数度啊

2105
00:58:30,140 --> 00:58:31,400
然后你看

2106
00:58:31,400 --> 00:58:33,960
现在拿到第一个fn0

2107
00:58:33,960 --> 00:58:34,540
对吧

2108
00:58:34,540 --> 00:58:35,160
执行吧

2109
00:58:35,160 --> 00:58:36,300
fn0谁

2110
00:58:36,300 --> 00:58:37,940
是不是第一个建定函数啊

2111
00:58:37,940 --> 00:58:38,440
就是他呀

2112
00:58:38,440 --> 00:58:40,160
是不是第一个方式就是他呀

2113
00:58:40,160 --> 00:58:41,060
好 打印

2114
00:58:41,060 --> 00:58:41,520
走

2115
00:58:41,520 --> 00:58:43,480
再拿到什么

2116
00:58:43,480 --> 00:58:44,200
第二个建定函数

2117
00:58:44,200 --> 00:58:44,460
是不是

2118
00:58:44,460 --> 00:58:44,960
执行他

2119
00:58:44,960 --> 00:58:47,140
诶 你看是不是又执行

2120
00:58:47,140 --> 00:58:48,940
是第二个建定函数吧

2121
00:58:48,940 --> 00:58:50,080
好 走

2122
00:58:50,080 --> 00:58:51,660
诶 可以了

2123
00:58:51,660 --> 00:58:52,120
算是

2124
00:58:52,160 --> 00:58:54,200
所以评论我们编诸代码长什么样

2125
00:58:54,200 --> 00:58:55,120
长得样子看到了吧

2126
00:58:55,120 --> 00:58:57,120
把这考出来模拟一下吧

2127
00:58:57,120 --> 00:58:58,860
好模拟啊

2128
00:58:58,860 --> 00:59:00,320
放到这

2129
00:59:00,320 --> 00:59:04,860
可以了吧

2130
00:59:04,860 --> 00:59:05,900
你看模拟

2131
00:59:05,900 --> 00:59:08,440
那么我们现在西方什么

2132
00:59:08,440 --> 00:59:10,500
你看这个参数写好了

2133
00:59:10,500 --> 00:59:11,720
是不是就这个参数啊

2134
00:59:11,720 --> 00:59:12,000
ok

2135
00:59:12,000 --> 00:59:42,000
关键要想

2136
00:59:42,000 --> 00:59:43,120
模板读丝吧

2137
00:59:43,120 --> 00:59:45,500
谈为啥

2138
00:59:45,500 --> 00:59:47,980
是不是就是这部分啊

2139
00:59:47,980 --> 00:59:48,700
这是hider部分

2140
00:59:48,700 --> 00:59:50,300
因为这个跟我们的

2141
00:59:50,300 --> 00:59:51,960
跟我们的循环体没无关

2142
00:59:51,960 --> 00:59:53,860
把它放到hider里面就可以了

2143
00:59:53,860 --> 00:59:55,720
就这部分先把它写死

2144
00:59:55,720 --> 00:59:58,020
不管什么都要指定它

2145
00:59:58,020 --> 00:59:59,420
用strict

2146
00:59:59,420 --> 01:00:00,940
挖一个context

2147
01:00:00,940 --> 01:00:01,380
没有闹

2148
01:00:01,380 --> 01:00:03,320
还要挖一个小X

2149
01:00:03,320 --> 01:00:05,240
因为

2150
01:00:05,240 --> 01:00:07,280
就是那个数组

2151
01:00:07,280 --> 01:00:08,320
是不是就是那数组

2152
01:00:08,320 --> 01:00:09,420
F的数组

2153
01:00:09,420 --> 01:00:13,180
就是坚定函数组

2154
01:00:13,180 --> 01:00:15,920
对吧

2155
01:00:15,920 --> 01:00:18,100
然后呢还有什么

2156
01:00:18,100 --> 01:00:19,120
Z.context

2157
01:00:19,120 --> 01:00:21,620
Z.context也声明一下吧

2158
01:00:21,620 --> 01:00:23,500
它在干嘛

2159
01:00:23,500 --> 01:00:25,660
是不是拼这一部分啊

2160
01:00:25,660 --> 01:00:27,560
这一部分看有什么规律啊

2161
01:00:27,560 --> 01:00:29,520
你看都什么挖一个什么名字

2162
01:00:29,520 --> 01:00:31,080
等于一个什么它

2163
01:00:31,080 --> 01:00:32,080
然后执行

2164
01:00:32,080 --> 01:00:34,600
看这两个是不是感觉很像的

2165
01:00:34,600 --> 01:00:36,680
先去除第一个函数执行

2166
01:00:36,680 --> 01:00:38,920
再去除什么

2167
01:00:38,920 --> 01:00:39,720
第二个卡通之行

2168
01:00:39,720 --> 01:00:40,400
传入内部Age

2169
01:00:40,400 --> 01:00:41,200
是不是

2170
01:00:41,200 --> 01:00:43,920
所以说我们怎么拼它呀

2171
01:00:43,920 --> 01:00:44,500
循环吧

2172
01:00:44,500 --> 01:00:45,220
好

2173
01:00:45,220 --> 01:00:45,940
这是写循环

2174
01:00:45,940 --> 01:00:47,540
循环谁

2175
01:00:47,540 --> 01:00:50,800
我们来一个let code等于一个空

2176
01:00:50,800 --> 01:00:51,360
对吧

2177
01:00:51,360 --> 01:00:52,380
然后写循环

2178
01:00:52,380 --> 01:00:54,920
好

2179
01:00:54,920 --> 01:00:55,780
let怎么样

2180
01:00:55,780 --> 01:00:56,580
index

2181
01:00:56,580 --> 01:00:57,560
不管这是个0

2182
01:00:57,560 --> 01:00:58,100
对吧

2183
01:00:58,100 --> 01:00:59,080
然后呢

2184
01:00:59,080 --> 01:00:59,760
i小于什么

2185
01:00:59,760 --> 01:01:02,440
z.options.types.lens

2186
01:01:02,440 --> 01:01:03,120
接下

2187
01:01:03,120 --> 01:01:07,120
对吧

2188
01:01:07,120 --> 01:01:07,840
你看啊

2189
01:01:07,840 --> 01:01:08,180
加价

2190
01:01:08,180 --> 01:01:10,600
那么往里边加什么呀

2191
01:01:10,600 --> 01:01:11,320
就是code

2192
01:01:11,320 --> 01:01:12,920
加等于什么

2193
01:01:12,920 --> 01:01:15,140
加等于一个模板图书

2194
01:01:15,140 --> 01:01:17,560
放啥

2195
01:01:17,560 --> 01:01:18,860
就是放这一部分啊

2196
01:01:18,860 --> 01:01:19,540
拿过来吧

2197
01:01:19,540 --> 01:01:20,600
拿过来考到这

2198
01:01:20,600 --> 01:01:20,960
放到这

2199
01:01:20,960 --> 01:01:21,580
有可言了是吧

2200
01:01:21,580 --> 01:01:23,040
好

2201
01:01:23,040 --> 01:01:28,460
你看我这生病病量

2202
01:01:28,460 --> 01:01:30,980
你看发现这个地方不能写死吧

2203
01:01:30,980 --> 01:01:31,980
是不是每个不一样

2204
01:01:31,980 --> 01:01:32,880
你看第一次是零

2205
01:01:32,880 --> 01:01:34,360
第二次是一啊

2206
01:01:34,360 --> 01:01:35,120
每次加一啊

2207
01:01:35,120 --> 01:01:36,140
刚好是锁引啊

2208
01:01:36,140 --> 01:01:37,000
好把它换成锁引

2209
01:01:37,000 --> 01:01:42,340
就是fn0x0

2210
01:01:42,340 --> 01:01:42,920
对吧

2211
01:01:42,920 --> 01:01:44,180
fn0执行

2212
01:01:44,180 --> 01:01:46,000
这个地方不能写

2213
01:01:46,000 --> 01:01:47,200
不能这这样写吧

2214
01:01:47,200 --> 01:01:47,600
应该怎么写

2215
01:01:47,600 --> 01:01:49,180
它是不是刚好是什么

2216
01:01:49,180 --> 01:01:50,740
是我们这个algs

2217
01:01:50,740 --> 01:01:52,020
是不是algs

2218
01:01:52,020 --> 01:01:53,260
这改掉

2219
01:01:53,260 --> 01:01:55,380
把这改成给变量啊

2220
01:01:55,380 --> 01:01:56,540
干什么

2221
01:01:56,540 --> 01:01:57,920
z的点algs

2222
01:01:57,920 --> 01:02:00,220
是不是可以了

2223
01:02:00,220 --> 01:02:01,060
好

2224
01:02:01,060 --> 01:02:02,740
那么经过z的虚幻之后

2225
01:02:02,740 --> 01:02:04,440
我们这有简单的简单数啊

2226
01:02:04,440 --> 01:02:05,460
是不是有两个呀

2227
01:02:05,460 --> 01:02:06,440
是不是有俩呀

2228
01:02:06,440 --> 01:02:06,940
有俩的话

2229
01:02:06,940 --> 01:02:08,800
他会执行两次是拼出来两个

2230
01:02:08,800 --> 01:02:11,680
这个拿到第一个函数执行

2231
01:02:11,680 --> 01:02:12,780
第二函数执行

2232
01:02:12,780 --> 01:02:15,200
拼出两个这样的代码块啊

2233
01:02:15,200 --> 01:02:15,540
对不对

2234
01:02:15,540 --> 01:02:16,480
最后怎么样

2235
01:02:16,480 --> 01:02:17,800
返回吧

2236
01:02:17,800 --> 01:02:19,300
好返回我们的code就可以了

2237
01:02:19,300 --> 01:02:20,220
OK

2238
01:02:20,220 --> 01:02:23,240
比如说经过这么一处理的

2239
01:02:23,240 --> 01:02:25,340
你看我们是不是拥有了一个fn出来了

2240
01:02:25,340 --> 01:02:26,940
然后是不是有个参数

2241
01:02:26,940 --> 01:02:28,080
vdhgs

2242
01:02:28,080 --> 01:02:30,100
vdhader和vdhader content

2243
01:02:30,100 --> 01:02:32,040
对不对就可以了

2244
01:02:32,040 --> 01:02:33,180
别了解啊

2245
01:02:33,180 --> 01:02:34,080
现在大家可能有点懵

2246
01:02:34,080 --> 01:02:35,560
我以为大家再给大家捋

2247
01:02:35,560 --> 01:02:36,620
多捋几遍啊

2248
01:02:36,620 --> 01:02:39,220
这就写完了

2249
01:02:39,220 --> 01:02:41,280
我们看一下我们自己写的效果行不行

2250
01:02:41,280 --> 01:02:48,600
我们先把这个代码改掉

2251
01:02:48,600 --> 01:02:49,480
改成第一写的

2252
01:02:49,480 --> 01:02:50,680
叫.table

2253
01:02:50,680 --> 01:02:50,960
是不是

2254
01:02:50,960 --> 01:02:51,360
你看

2255
01:02:51,360 --> 01:02:53,240
我们调我们的调table

2256
01:02:53,240 --> 01:02:53,580
调谁

2257
01:02:53,580 --> 01:02:54,580
是不是要用这个文件

2258
01:02:54,580 --> 01:02:56,820
table下边的这个应该解释

2259
01:02:56,820 --> 01:02:58,920
结果说里边的think hook

2260
01:02:58,920 --> 01:02:59,800
对吧

2261
01:02:59,800 --> 01:03:00,820
让给大家debug一下

2262
01:03:00,820 --> 01:03:01,420
debug一下

2263
01:03:01,420 --> 01:03:01,800
别着急

2264
01:03:01,800 --> 01:03:03,460
一行扩一行过

2265
01:03:03,460 --> 01:03:04,660
先debug

2266
01:03:04,660 --> 01:03:05,280
对吧

2267
01:03:05,280 --> 01:03:05,820
然后呢

2268
01:03:05,820 --> 01:03:06,040
走

2269
01:03:06,040 --> 01:03:10,600
然后进来之后你看往下左右

2270
01:03:10,600 --> 01:03:11,640
你看现在怎么样

2271
01:03:11,640 --> 01:03:13,080
创建singhook这个函数

2272
01:03:13,080 --> 01:03:13,800
对不对

2273
01:03:13,800 --> 01:03:14,340
进去

2274
01:03:14,340 --> 01:03:17,400
到这来了吧

2275
01:03:17,400 --> 01:03:18,880
他会先做先做他的怎么样

2276
01:03:18,880 --> 01:03:19,940
够函数对吧

2277
01:03:19,940 --> 01:03:20,500
进去

2278
01:03:20,500 --> 01:03:22,140
到这来了

2279
01:03:22,140 --> 01:03:22,740
你看

2280
01:03:22,740 --> 01:03:24,440
我这是不是传了一个数组

2281
01:03:24,440 --> 01:03:25,440
内部ad啊

2282
01:03:25,440 --> 01:03:26,840
他给谁了

2283
01:03:26,840 --> 01:03:27,540
是不是给他了

2284
01:03:27,540 --> 01:03:29,200
给的hook的够函数了

2285
01:03:29,200 --> 01:03:30,780
看是不是那个

2286
01:03:30,780 --> 01:03:32,100
内面数组啊

2287
01:03:32,100 --> 01:03:33,340
给他了吧

2288
01:03:33,340 --> 01:03:34,300
然后他给谁了

2289
01:03:34,300 --> 01:03:35,980
给了hook的内部的

2290
01:03:36,040 --> 01:03:37,240
下面的LGA的属性

2291
01:03:37,240 --> 01:03:39,340
是不是属性内部了

2292
01:03:39,340 --> 01:03:40,480
然后怎么样

2293
01:03:40,480 --> 01:03:41,840
勾建空的速度

2294
01:03:41,840 --> 01:03:43,240
空速度

2295
01:03:43,240 --> 01:03:44,300
现在什么没放

2296
01:03:44,300 --> 01:03:44,600
是不是

2297
01:03:44,600 --> 01:03:45,380
走

2298
01:03:45,380 --> 01:03:48,340
然后你看

2299
01:03:48,340 --> 01:03:49,380
就完事了吧

2300
01:03:49,380 --> 01:03:50,200
勾建完成了

2301
01:03:50,200 --> 01:03:51,160
完事了

2302
01:03:51,160 --> 01:03:52,400
然后type看他们怎么多的

2303
01:03:52,400 --> 01:03:52,920
你看进来

2304
01:03:52,920 --> 01:03:55,120
看先走到这儿了吧

2305
01:03:55,120 --> 01:03:56,840
是不是这个hook的鸡肋

2306
01:03:56,840 --> 01:03:57,620
对吧

2307
01:03:57,620 --> 01:03:58,060
里边怎么多

2308
01:03:58,060 --> 01:03:58,300
你看

2309
01:03:58,300 --> 01:03:59,460
先判断一下

2310
01:03:59,460 --> 01:04:00,880
因为我这传的个读话

2311
01:04:00,880 --> 01:04:02,280
它变成个对象了

2312
01:04:02,280 --> 01:04:03,200
内部一是不是

2313
01:04:03,200 --> 01:04:03,900
变成对象

2314
01:04:03,900 --> 01:04:05,020
付给options

2315
01:04:05,020 --> 01:04:05,420
是吧

2316
01:04:05,420 --> 01:04:08,920
然后你看给Option加了属性要Fn

2317
01:04:08,920 --> 01:04:10,500
是不是等于这个Fn函数啊

2318
01:04:10,500 --> 01:04:13,580
是不是我们的第一个建议函数就这个函数啊

2319
01:04:13,580 --> 01:04:14,320
把它给谁了

2320
01:04:14,320 --> 01:04:15,880
给他了吧给Fn了吧

2321
01:04:15,880 --> 01:04:18,400
Fn给谁了给Option的Fn了

2322
01:04:18,400 --> 01:04:20,780
这样的话它变成一个对象了

2323
01:04:20,780 --> 01:04:22,100
有两个属性分别是名字

2324
01:04:22,100 --> 01:04:24,700
Name和什么和我们的建议函数Fn

2325
01:04:24,700 --> 01:04:25,360
对不对

2326
01:04:25,360 --> 01:04:28,340
然后把它一整块怎么样传给了insert

2327
01:04:28,340 --> 01:04:30,320
而insert呢很简单

2328
01:04:30,320 --> 01:04:31,840
就是什么放到数字里边去办事

2329
01:04:31,840 --> 01:04:33,340
是不是存起来了

2330
01:04:33,340 --> 01:04:35,040
现在我们讲过了啊

2331
01:04:35,040 --> 01:04:35,760
这个很简单

2332
01:04:35,760 --> 01:04:36,800
关键看下一个环节

2333
01:04:36,800 --> 01:04:40,380
关键看我们的call

2334
01:04:40,380 --> 01:04:41,520
看call怎么call的

2335
01:04:41,520 --> 01:04:42,340
你看进去

2336
01:04:42,340 --> 01:04:43,760
是不是到这儿了

2337
01:04:43,760 --> 01:04:44,600
你看第一步怎么样

2338
01:04:44,600 --> 01:04:46,260
他掉了Z找下关键creditcall

2339
01:04:46,260 --> 01:04:48,680
动态创建我们的call函数

2340
01:04:48,680 --> 01:04:49,300
是不是

2341
01:04:49,300 --> 01:04:51,040
然后在那执行吧

2342
01:04:51,040 --> 01:04:52,240
关键你看

2343
01:04:52,240 --> 01:04:53,060
我看第一步

2344
01:04:53,060 --> 01:04:54,480
动态创建call怎么创建的

2345
01:04:54,480 --> 01:04:54,880
进去

2346
01:04:54,880 --> 01:04:56,520
就到这儿了

2347
01:04:56,520 --> 01:04:57,220
然后走了

2348
01:04:57,220 --> 01:04:58,520
走了compare了

2349
01:04:58,520 --> 01:05:00,100
但是compare自己实现了吗

2350
01:05:00,100 --> 01:05:00,340
没有

2351
01:05:00,340 --> 01:05:00,980
自己实现的

2352
01:05:00,980 --> 01:05:02,140
子力实现的

2353
01:05:02,140 --> 01:05:02,740
是不是信呼呼

2354
01:05:02,740 --> 01:05:03,480
子力实现的呀

2355
01:05:03,480 --> 01:05:04,640
看子力怎么做的

2356
01:05:04,640 --> 01:05:05,340
怎么编写的

2357
01:05:05,340 --> 01:05:06,800
这有两个参数

2358
01:05:06,800 --> 01:05:07,740
一个是Types

2359
01:05:07,740 --> 01:05:10,380
是不是对象数组

2360
01:05:10,380 --> 01:05:11,600
第二是LGS

2361
01:05:11,600 --> 01:05:12,920
是不是参数数组

2362
01:05:12,920 --> 01:05:13,720
对吧

2363
01:05:13,720 --> 01:05:14,740
好我们进去

2364
01:05:14,740 --> 01:05:16,640
进去之后

2365
01:05:16,640 --> 01:05:17,280
你看先走什么

2366
01:05:17,280 --> 01:05:17,640
SIDUP

2367
01:05:17,640 --> 01:05:18,640
先建立

2368
01:05:18,640 --> 01:05:19,560
创建

2369
01:05:19,560 --> 01:05:20,260
怎么创建的

2370
01:05:20,260 --> 01:05:21,360
走了工厂创建方法

2371
01:05:21,360 --> 01:05:22,080
怎么创建的

2372
01:05:22,080 --> 01:05:22,700
进去

2373
01:05:22,700 --> 01:05:24,440
第一步怎么样

2374
01:05:24,440 --> 01:05:26,100
我们把options给了

2375
01:05:26,100 --> 01:05:28,180
给了当前工厂的option的属性

2376
01:05:28,180 --> 01:05:29,640
保管园一下

2377
01:05:29,640 --> 01:05:31,020
第二步

2378
01:05:31,020 --> 01:05:31,860
你看我做了个什么

2379
01:05:31,860 --> 01:05:32,440
MAP净设

2380
01:05:32,440 --> 01:05:33,980
这原来是什么

2381
01:05:33,980 --> 01:05:35,280
是不是个对象数组啊

2382
01:05:35,280 --> 01:05:36,460
是不是啊

2383
01:05:36,460 --> 01:05:37,800
里面有内部Fn啊

2384
01:05:37,800 --> 01:05:39,080
映射出来什么

2385
01:05:39,080 --> 01:05:40,060
一个新的数组

2386
01:05:40,060 --> 01:05:41,020
只有什么只有Fn

2387
01:05:41,020 --> 01:05:41,720
看到了吧

2388
01:05:41,720 --> 01:05:42,740
这样的话你看

2389
01:05:42,740 --> 01:05:43,600
那直接完成之后

2390
01:05:43,600 --> 01:05:44,800
这个下边X

2391
01:05:44,800 --> 01:05:45,980
就这个什么hook

2392
01:05:45,980 --> 01:05:47,660
它的下边X是不是有个新的数组

2393
01:05:47,660 --> 01:05:49,600
就这个函数的数组啊

2394
01:05:49,600 --> 01:05:50,920
就是接近函数数组啊

2395
01:05:50,920 --> 01:05:51,680
对不对

2396
01:05:51,680 --> 01:05:53,140
好看是不是这样的

2397
01:05:53,140 --> 01:05:53,960
看到了吧

2398
01:05:53,960 --> 01:05:55,040
你看X是不是一个

2399
01:05:55,040 --> 01:05:57,140
看是不是第一个零一

2400
01:05:57,140 --> 01:05:58,600
是不是第一接近函数啊

2401
01:05:58,600 --> 01:05:59,980
第二接近函数啊

2402
01:05:59,980 --> 01:06:01,100
是不是啊

2403
01:06:01,100 --> 01:06:01,820
好走

2404
01:06:01,820 --> 01:06:02,640
建立好了啊

2405
01:06:02,640 --> 01:06:03,660
再准备这窗间

2406
01:06:03,660 --> 01:06:04,200
怎么创建呢

2407
01:06:04,200 --> 01:06:04,620
进去

2408
01:06:04,620 --> 01:06:07,860
看通过new方式创建的

2409
01:06:07,860 --> 01:06:09,560
先怎么得到参数吧

2410
01:06:09,560 --> 01:06:10,700
怎么得到进去

2411
01:06:10,700 --> 01:06:12,340
是不是这个呀

2412
01:06:12,340 --> 01:06:12,860
他得到个啥

2413
01:06:12,860 --> 01:06:15,140
是不是得到一个name多少0

2414
01:06:15,140 --> 01:06:16,380
多少AD啊

2415
01:06:16,380 --> 01:06:16,720
对不对

2416
01:06:16,720 --> 01:06:18,280
看第二个怎么得到header

2417
01:06:18,280 --> 01:06:19,360
header不是写死的吗

2418
01:06:19,360 --> 01:06:20,320
对吧

2419
01:06:20,320 --> 01:06:20,800
好

2420
01:06:20,800 --> 01:06:23,340
再看看我们的这个content内容

2421
01:06:23,340 --> 01:06:24,200
内容怎么得到

2422
01:06:24,200 --> 01:06:26,000
内容你看我写着循环

2423
01:06:26,000 --> 01:06:27,100
你看

2424
01:06:27,100 --> 01:06:28,940
第一次循环的时候

2425
01:06:28,940 --> 01:06:30,200
哎

2426
01:06:30,200 --> 01:06:32,500
报了个错

2427
01:06:32,500 --> 01:06:33,640
看看哪报错了

2428
01:06:33,640 --> 01:06:46,960
好 再来一次 咱们稍后段点 重新直接走到这

2429
01:06:46,960 --> 01:06:52,080
不带喇叭道 说话也不要费劲 一会儿又哑了

2430
01:06:52,080 --> 01:06:57,200
走 走 循环吧

2431
01:06:57,200 --> 01:07:02,320
你看 两次吧 两个code变成什么样子了

2432
01:07:02,320 --> 01:07:03,200
看了

2433
01:07:03,200 --> 01:07:05,260
YFN0等于X0

2434
01:07:05,260 --> 01:07:06,820
FN0执行

2435
01:07:06,820 --> 01:07:07,860
传入NAMEA

2436
01:07:07,860 --> 01:07:08,460
是吧

2437
01:07:08,460 --> 01:07:10,860
YFN1等于X1

2438
01:07:10,860 --> 01:07:11,860
F1执行

2439
01:07:11,860 --> 01:07:12,940
是不是一样的

2440
01:07:12,940 --> 01:07:13,580
好

2441
01:07:13,580 --> 01:07:14,540
它会返回一个什么

2442
01:07:14,540 --> 01:07:14,820
code

2443
01:07:14,820 --> 01:07:16,580
然后这样我函数一件好了

2444
01:07:16,580 --> 01:07:16,780
是不是

2445
01:07:16,780 --> 01:07:18,380
那函数之后呢

2446
01:07:18,380 --> 01:07:19,660
是不是传选好了

2447
01:07:19,660 --> 01:07:20,240
返回

2448
01:07:20,240 --> 01:07:21,520
该干嘛

2449
01:07:21,520 --> 01:07:22,520
是不是到这来了

2450
01:07:22,520 --> 01:07:23,220
该直接该调用

2451
01:07:23,220 --> 01:07:23,600
该扣了

2452
01:07:23,600 --> 01:07:24,020
该执行了

2453
01:07:24,020 --> 01:07:25,140
这么执行

2454
01:07:25,140 --> 01:07:25,680
进去看看

2455
01:07:25,680 --> 01:07:26,620
你看

2456
01:07:26,620 --> 01:07:27,660
是不是到我们这个

2457
01:07:27,660 --> 01:07:28,660
构建出来的

2458
01:07:28,660 --> 01:07:29,460
这个NAME函数里边来了

2459
01:07:29,460 --> 01:07:30,560
跟他一样

2460
01:07:30,560 --> 01:07:30,820
你看

2461
01:07:30,820 --> 01:07:31,460
Strict

2462
01:07:31,460 --> 01:07:32,280
他看他

2463
01:07:32,280 --> 01:07:33,440
x是不是

2464
01:07:33,440 --> 01:07:33,980
走

2465
01:07:33,980 --> 01:07:36,340
拿到什么呀

2466
01:07:36,340 --> 01:07:37,840
第一个fn0执行

2467
01:07:37,840 --> 01:07:39,460
是吧

2468
01:07:39,460 --> 01:07:40,060
你看打印

2469
01:07:40,060 --> 01:07:42,060
拿到第二分0执行

2470
01:07:42,060 --> 01:07:43,980
打印

2471
01:07:43,980 --> 01:07:44,360
是不是

2472
01:07:44,360 --> 01:07:46,260
好

2473
01:07:46,260 --> 01:07:47,420
完美结束

2474
01:07:47,420 --> 01:07:48,200
是不是

2475
01:07:48,200 --> 01:07:49,280
一样的

2476
01:07:49,280 --> 01:07:50,340
你看结果什么一模一样的

2477
01:07:50,340 --> 01:07:53,280
一二入风就可以了

2478
01:07:53,280 --> 01:07:54,880
这一个hooks的基本原理

2479
01:07:54,880 --> 01:07:56,920
只不过呢

2480
01:07:56,920 --> 01:07:58,220
你看这刚才给他写了一下

2481
01:07:58,220 --> 01:07:59,380
写了一下简单实现

2482
01:07:59,380 --> 01:08:01,440
这大家还有问题吗

2483
01:08:01,440 --> 01:08:09,180
我看下来的问题

2484
01:08:09,180 --> 01:08:23,800
你原码写的真的不太好阅读啊

2485
01:08:23,800 --> 01:08:24,360
对吧

2486
01:08:24,360 --> 01:08:24,640
好

2487
01:08:24,640 --> 01:08:25,720
看他问题啊

2488
01:08:25,720 --> 01:08:27,880
看你

2489
01:08:27,880 --> 01:08:28,480
嗯

2490
01:08:28,480 --> 01:08:33,120
好 第一个这个

2491
01:08:33,120 --> 01:08:37,040
module的和矿石一对应的吗

2492
01:08:37,040 --> 01:08:38,240
这个不是这样的啊

2493
01:08:38,240 --> 01:08:40,680
就一个模块可能会多用多个truck

2494
01:08:40,680 --> 01:08:41,720
一个truck呢

2495
01:08:41,720 --> 01:08:42,680
可能不太多个模块

2496
01:08:42,680 --> 01:08:43,640
是多于多的关系

2497
01:08:43,640 --> 01:08:47,020
type unimit unimit一样的吗

2498
01:08:47,020 --> 01:08:47,660
对一样的

2499
01:08:47,660 --> 01:08:48,820
type相当于unimit

2500
01:08:48,820 --> 01:08:49,720
就是发布的模块

2501
01:08:49,720 --> 01:08:50,680
我说就是infit的模块

2502
01:08:50,680 --> 01:08:51,180
对吧

2503
01:08:51,180 --> 01:08:53,860
对于更高几质熔断啊

2504
01:08:53,860 --> 01:08:54,640
铺铺流等方式

2505
01:08:54,640 --> 01:08:55,300
对 没错

2506
01:08:55,300 --> 01:08:56,820
山下熔断啊

2507
01:08:56,820 --> 01:08:57,580
只要有一个错误

2508
01:08:57,580 --> 01:08:58,240
他就不把他走了

2509
01:08:58,240 --> 01:08:59,240
要熔断的概念

2510
01:08:59,240 --> 01:09:00,420
就跟保险斯一样

2511
01:09:00,420 --> 01:09:01,800
其实是保险斯的意思

2512
01:09:01,800 --> 01:09:04,000
对

2513
01:09:04,000 --> 01:09:06,320
FortianU return不再触发

2514
01:09:06,320 --> 01:09:09,320
为啥不用push

2515
01:09:09,320 --> 01:09:09,940
可以

2516
01:09:09,940 --> 01:09:12,920
靠怎么调指定的type

2517
01:09:12,920 --> 01:09:13,660
刚才讲过了

2518
01:09:13,660 --> 01:09:16,540
type和call属于同一个实例

2519
01:09:16,540 --> 01:09:17,220
对 没错

2520
01:09:17,220 --> 01:09:18,760
都是hook的实例

2521
01:09:18,760 --> 01:09:21,680
对象分类集成

2522
01:09:21,680 --> 01:09:22,360
很厉害

2523
01:09:22,360 --> 01:09:23,580
那当然厉害了

2524
01:09:23,580 --> 01:09:24,380
大神吧

2525
01:09:24,380 --> 01:09:27,720
很突兀是吧

2526
01:09:27,720 --> 01:09:28,720
push对对对push

2527
01:09:28,720 --> 01:09:30,520
嗯

2528
01:09:30,520 --> 01:09:32,520
对动态拼接

2529
01:09:32,520 --> 01:09:34,220
为什么要用这种方式啊

2530
01:09:34,220 --> 01:09:36,420
因为你看为什么要拼接大家想想为什么拼接

2531
01:09:36,420 --> 01:09:38,220
为什么要动态创建这种函数啊

2532
01:09:38,220 --> 01:09:41,220
因为他的执行啊是不固定的是不是啊

2533
01:09:41,220 --> 01:09:42,120
对吧

2534
01:09:42,120 --> 01:09:45,820
你比如说你有几天记得几个鉴定的函数不一定啊是不是啊

2535
01:09:45,820 --> 01:09:46,620
不固定的啊

2536
01:09:46,620 --> 01:09:48,620
所以说他只能动态创建啊

2537
01:09:48,620 --> 01:09:50,220
而且这懒家的懒创建

2538
01:09:50,220 --> 01:09:52,220
他不是说你鉴定完之后立马创建

2539
01:09:52,220 --> 01:09:53,320
而是直接用来创建

2540
01:09:53,320 --> 01:09:55,020
提高性能对不对啊

2541
01:09:56,920 --> 01:10:00,500
有发布订阅需要讲这么复杂吗 原码就这样的啊 对啊

2542
01:10:00,500 --> 01:10:03,060
对 没错啊

2543
01:10:03,060 --> 01:10:07,680
每次反为一个立面函数 对 没错

2544
01:10:07,680 --> 01:10:08,960
对

2545
01:10:08,960 --> 01:10:11,000
对啊

2546
01:10:11,000 --> 01:10:16,380
为什么不用Event 功能比这个强大了 因为功能太弱了啊 这功能很强大

2547
01:10:16,380 --> 01:10:18,940
没用过 对吧 没用过 就见识一下呗

2548
01:10:18,940 --> 01:10:22,780
对 没错啊 没错

2549
01:10:24,320 --> 01:10:28,920
东南双也没看懂没看懂就是一个他就利用这个方讯有参数有什么有很容易体是不是啊

2550
01:10:28,920 --> 01:10:34,040
因为他指的是什么因为他指的是我们的这个

2551
01:10:34,040 --> 01:10:36,860
node里边什么的6node里边的英文字模块啊

2552
01:10:36,860 --> 01:10:40,640
好那我们休息一下吧咱们休息啊

2553
01:10:40,640 --> 01:10:44,540
6分钟啊咱们休息到这个9:20分好吧休息啊

2554
01:10:44,540 --> 01:10:49,520
呃好把代码提了一下再看看吧休息再看看代码啊

2555
01:10:54,320 --> 01:11:00,200
又没有忽略是吧

2556
01:11:00,200 --> 01:11:00,620
忽略一下

2557
01:11:00,620 --> 01:11:04,560
第二

2558
01:11:04,560 --> 01:11:13,920
我们休息10分钟

2559
01:11:13,920 --> 01:11:14,640
休息10分钟

2560
01:11:14,640 --> 01:11:15,640
大家把代码看一下

2561
01:11:15,640 --> 01:11:15,880
对吧

2562
01:11:15,880 --> 01:11:16,420
我提到一下

2563
01:11:16,420 --> 01:11:28,960
好 我也喝水啊

2564
01:11:28,960 --> 01:11:29,720
这个这个

2565
01:11:29,720 --> 01:11:36,300
不带麦克风的话

2566
01:11:36,300 --> 01:11:37,320
说话很费劲

2567
01:11:46,420 --> 01:11:55,320
哎呀 这可坏了

2568
01:11:55,320 --> 01:11:57,220
怎么又提要不了了呢

2569
01:11:57,220 --> 01:12:02,620
非法的

2570
01:12:02,620 --> 01:12:21,520
咦 这个不对啊

2571
01:12:21,520 --> 01:12:22,620
我的天

2572
01:12:22,620 --> 01:12:25,120
这个仓库不对啊

2573
01:12:25,120 --> 01:12:29,620
这个仓库不对啊

2574
01:12:29,620 --> 01:12:33,620
这个仓库不对啊

2575
01:12:33,620 --> 01:12:34,620
第一次

2576
01:12:34,620 --> 01:12:48,620
你拿个这个仓库在这里

2577
01:12:48,620 --> 01:13:10,620
大家可以休息 然后喝水啊

2578
01:13:10,620 --> 01:13:16,620
我可以休息 然后喝水啊

2579
01:13:16,620 --> 01:13:32,620
我可以休息 然后喝水啊

2580
01:13:32,620 --> 01:13:50,620
我可以休息 然后喝水啊

2581
01:13:50,620 --> 01:14:06,620
喝水啊

2582
01:14:06,620 --> 01:14:10,620
喝水啊

2583
01:14:10,620 --> 01:14:26,620
喝水啊

2584
01:14:26,620 --> 01:14:30,620
喝水啊

2585
01:14:30,620 --> 01:14:32,620
喝水啊

2586
01:14:32,620 --> 01:14:34,620
喝水啊

2587
01:14:34,620 --> 01:14:36,620
喝水啊

2588
01:14:36,620 --> 01:14:38,620
喝水啊

2589
01:14:38,620 --> 01:15:08,600
字幕志愿者 杨茜茜

2590
01:15:08,620 --> 01:15:38,600
字幕志愿者 杨茜茜

2591
01:15:38,620 --> 01:16:08,600
字幕志愿者 杨茜茜

2592
01:16:08,600 --> 01:16:38,580
字幕志愿者 杨茜茜

2593
01:16:38,580 --> 01:17:08,560
字幕志愿者 杨茜茜

2594
01:17:08,560 --> 01:17:38,540
字幕志愿者 杨茜茜

2595
01:17:38,540 --> 01:17:44,380
字幕志愿者 杨茜茜

