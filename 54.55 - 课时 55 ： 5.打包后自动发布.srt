1
00:00:00,000 --> 00:00:02,100
这一节呢

2
00:00:02,100 --> 00:00:04,400
我们就来实现一下如何打包后呢

3
00:00:04,400 --> 00:00:05,900
将我们打包后的文件

4
00:00:05,900 --> 00:00:07,800
自动上传到我们的轻牛上

5
00:00:07,800 --> 00:00:08,600
那这里呢

6
00:00:08,600 --> 00:00:09,800
我们可以把它关掉啊

7
00:00:09,800 --> 00:00:10,500
关闭其他

8
00:00:10,500 --> 00:00:13,700
为了一样我们就写上这样一个插件

9
00:00:13,700 --> 00:00:15,400
我给它起个名字叫upload

10
00:00:15,400 --> 00:00:17,800
plugin上传插件

11
00:00:17,800 --> 00:00:20,600
那这里一样我们可以呢

12
00:00:20,600 --> 00:00:21,500
把它拿过来

13
00:00:21,500 --> 00:00:23,100
往来去以入进来啊

14
00:00:23,100 --> 00:00:25,200
在这里加一个叫lite

15
00:00:25,200 --> 00:00:26,300
一个叫upload

16
00:00:26,300 --> 00:00:28,600
plugin等于require

17
00:00:28,600 --> 00:00:31,000
我们就以plugins下的对吧

18
00:00:31,000 --> 00:00:32,800
upload plugin

19
00:00:32,800 --> 00:00:36,900
这时候我们把刚才inline source就干掉了

20
00:00:36,900 --> 00:00:39,600
这样的话我们就不把所有的文件打包出来

21
00:00:39,600 --> 00:00:41,200
所有的文件都去上传

22
00:00:41,200 --> 00:00:43,900
这里一样我们可以去new这样一个插件

23
00:00:43,900 --> 00:00:48,100
这时候我们上传七牛肯定需要一些参数

24
00:00:48,100 --> 00:00:50,000
这些参数我就直接拿过来了

25
00:00:50,000 --> 00:00:51,600
这些都是人家要求的

26
00:00:51,600 --> 00:00:52,800
比如说有这些参数

27
00:00:52,800 --> 00:00:54,900
给大家解释一下这几个参数什么意思

28
00:00:54,900 --> 00:00:56,100
第一个叫捅

29
00:00:56,100 --> 00:00:57,300
就是说我们要上传七牛

30
00:00:57,300 --> 00:00:59,100
是上传哪个资源上

31
00:00:59,100 --> 00:01:02,100
第二个就是上传到哪个育名上

32
00:01:02,100 --> 00:01:04,100
这个就是我们本人的一个叫ak

33
00:01:04,100 --> 00:01:05,460
就是自己的访问的key

34
00:01:05,460 --> 00:01:07,000
还有一个自己的喵的一个key

35
00:01:07,000 --> 00:01:08,400
这里面有4个key

36
00:01:08,400 --> 00:01:10,000
我大家分别找一下漆牛

37
00:01:10,000 --> 00:01:11,500
这几个名字都哪来的

38
00:01:11,500 --> 00:01:14,500
这里一样我给大家看一下

39
00:01:14,500 --> 00:01:16,800
我们打开我们的漆牛网站

40
00:01:16,800 --> 00:01:20,100
这里我们直接打开漆牛.com

41
00:01:20,100 --> 00:01:24,200
这里我们可以直接找到我们的管理控制中心

42
00:01:24,200 --> 00:01:25,300
没有的话可以注册一个

43
00:01:25,300 --> 00:01:26,900
他需要你去生命认一下

44
00:01:26,900 --> 00:01:30,380
认证完以后这里面就有一个叫对象存储看到了吧

45
00:01:30,380 --> 00:01:33,340
我们肯定需要把文件上传到我们的对象里面

46
00:01:33,340 --> 00:01:35,700
默认情况下你看我这有个空间出处列表

47
00:01:35,700 --> 00:01:37,300
我这个叫姜文static

48
00:01:37,300 --> 00:01:38,780
那好这里也是一样的

49
00:01:38,780 --> 00:01:42,060
我的筒的名字就是姜文static

50
00:01:42,060 --> 00:01:44,340
好了就上传到空间里

51
00:01:44,340 --> 00:01:46,580
之后我们就有内容管理看到了吧

52
00:01:46,580 --> 00:01:48,060
内容管理里面告诉我们

53
00:01:48,060 --> 00:01:49,660
上传的时候可以上传到这个域名

54
00:01:49,660 --> 00:01:51,540
这个域名我在这里面就直接写了

55
00:01:51,540 --> 00:01:53,460
这是我自己买的域名

56
00:01:53,460 --> 00:01:56,860
image.fullstack

57
00:01:56,860 --> 00:01:58,160
sour stack对吧

58
00:01:58,160 --> 00:02:00,160
第2javascript的第2CN

59
00:02:00,160 --> 00:02:02,100
这里面没有点啊

60
00:02:02,100 --> 00:02:03,400
这多个点啊

61
00:02:03,400 --> 00:02:04,060
第2CN

62
00:02:04,060 --> 00:02:05,760
那现在我们上传完以后呢

63
00:02:05,760 --> 00:02:06,520
可能怎么样

64
00:02:06,520 --> 00:02:08,020
是不是还需要这两个key啊

65
00:02:08,020 --> 00:02:08,720
因为这两个key呢

66
00:02:08,720 --> 00:02:10,060
表示是上传到哪个人

67
00:02:10,060 --> 00:02:11,600
你不能随便所有的人都能上传

68
00:02:11,600 --> 00:02:12,100
是吧

69
00:02:12,100 --> 00:02:12,600
那这里呢

70
00:02:12,600 --> 00:02:13,720
我就找一下

71
00:02:13,720 --> 00:02:15,600
里面有一个东西叫哦

72
00:02:15,600 --> 00:02:16,720
个人个人面板

73
00:02:16,720 --> 00:02:17,720
里面有个人中心

74
00:02:17,720 --> 00:02:19,420
没点开

75
00:02:19,420 --> 00:02:21,160
完了这里面就有个密钥管理

76
00:02:21,160 --> 00:02:22,020
完了一个叫ak

77
00:02:22,020 --> 00:02:22,720
一个叫sk

78
00:02:22,720 --> 00:02:23,460
就是个size key

79
00:02:23,460 --> 00:02:24,720
还有我们的secret key

80
00:02:24,720 --> 00:02:25,800
这是我们的key啊

81
00:02:25,800 --> 00:02:26,820
就直接粘过来

82
00:02:26,860 --> 00:02:28,960
你生成完以后也会有这样一个key

83
00:02:28,960 --> 00:02:31,320
完了后面底下呢这就是我们的skey是吧

84
00:02:31,320 --> 00:02:32,400
把它直接粘过来

85
00:02:32,400 --> 00:02:36,220
那这样的话我们的page就写好了

86
00:02:36,220 --> 00:02:39,120
那这时候呢我们就回到我们这样一个插件里来

87
00:02:39,120 --> 00:02:41,220
找到我们的upload的这个插件

88
00:02:41,220 --> 00:02:43,100
class

89
00:02:43,100 --> 00:02:45,560
他完了默认呢我们需要导出

90
00:02:45,560 --> 00:02:47,600
冒就有点ispulse 都有他

91
00:02:47,600 --> 00:02:49,620
完了此时呢我们可以在这怎么样呢

92
00:02:49,620 --> 00:02:51,420
是不是弄一个kstractor

93
00:02:51,420 --> 00:02:52,800
过头参数

94
00:02:52,800 --> 00:02:55,160
网里面呢就应该放着我们这些属性是吧

95
00:02:55,160 --> 00:02:56,300
我就叫他options

96
00:02:56,860 --> 00:03:01,460
完了上传图片的功能其实也是奇牛提供的

97
00:03:01,460 --> 00:03:02,620
这边有个文档中心

98
00:03:02,620 --> 00:03:06,100
完了你可以去找这里面有工具有个官方的sdk

99
00:03:06,100 --> 00:03:07,920
因为我们webpack是用node来写的

100
00:03:07,920 --> 00:03:09,860
所以找到的肯定是node的服务端是吧

101
00:03:09,860 --> 00:03:10,620
这里有个文档

102
00:03:10,620 --> 00:03:13,820
文档里面就包含着一个叫往上找是吧

103
00:03:13,820 --> 00:03:15,560
有什么文件上传看到了吧

104
00:03:15,560 --> 00:03:16,760
完了你可以自己去看

105
00:03:16,760 --> 00:03:18,400
他需要的用到你的key

106
00:03:18,400 --> 00:03:21,260
我通过key的生成一个mac地址的一个mac

107
00:03:21,260 --> 00:03:23,100
我通过这个mac可以再去怎么样

108
00:03:23,100 --> 00:03:24,000
是一步步来操作

109
00:03:24,000 --> 00:03:26,160
往上往上再生成一个upload token

110
00:03:26,160 --> 00:03:27,000
往通过这个token呢

111
00:03:27,000 --> 00:03:28,060
我们再去上传

112
00:03:28,060 --> 00:03:29,740
反正这些操作都是默认的

113
00:03:29,740 --> 00:03:30,800
我就不去大家找了

114
00:03:30,800 --> 00:03:32,100
你可以把它拼装一下

115
00:03:32,100 --> 00:03:34,200
这里我有写好的我就直接粘过来了

116
00:03:34,200 --> 00:03:35,000
这几句话

117
00:03:35,000 --> 00:03:38,400
这就是犀牛的API

118
00:03:38,400 --> 00:03:39,900
这几个我们刚才说了

119
00:03:39,900 --> 00:03:40,600
默认情况下呢

120
00:03:40,600 --> 00:03:42,700
需要通过Authions里面结构下这几个参数

121
00:03:42,700 --> 00:03:43,840
先上车mac

122
00:03:43,840 --> 00:03:44,740
往下再通过什么呢

123
00:03:44,740 --> 00:03:45,400
是告诉他哎

124
00:03:45,400 --> 00:03:46,500
上车到哪个综艺下

125
00:03:46,500 --> 00:03:47,000
对吧

126
00:03:47,000 --> 00:03:48,600
之后呢产生一个token

127
00:03:48,600 --> 00:03:50,200
往下有了token以后还有个配置

128
00:03:50,200 --> 00:03:51,000
往下这个方法呢

129
00:03:51,000 --> 00:03:52,800
就用来上传我们什么文件的

130
00:03:52,800 --> 00:03:55,560
网上里面有个叫 put extra 是吧

131
00:03:55,560 --> 00:03:57,560
这里面我们用的时候就大致这样

132
00:03:57,560 --> 00:04:00,000
你看 form uploader 这里面有

133
00:04:00,000 --> 00:04:04,000
你搜一下叫 form uploader 是吧

134
00:04:04,000 --> 00:04:06,240
在下面 uploader

135
00:04:06,240 --> 00:04:11,200
您说了他提交上传文件的时候需要几个参数

136
00:04:11,200 --> 00:04:13,260
第一叫 token 第二就是 key

137
00:04:13,260 --> 00:04:14,760
key 就表示文件的名字

138
00:04:14,760 --> 00:04:16,000
等于看这文件的名字在这

139
00:04:16,000 --> 00:04:18,440
第二个 local file 就是本地文件存在哪

140
00:04:18,440 --> 00:04:20,140
最后就需要这样一个参数

141
00:04:20,140 --> 00:04:20,360
对吧

142
00:04:20,360 --> 00:04:21,640
刚才我们看到那个参数

143
00:04:21,640 --> 00:04:22,940
完之后回调成功以后

144
00:04:22,940 --> 00:04:23,900
第一个参数是错误

145
00:04:23,900 --> 00:04:25,680
第二就是成功的响应体是吧

146
00:04:25,680 --> 00:04:27,340
这里面我们就一样

147
00:04:27,340 --> 00:04:29,840
我在这里是不是还是要写个apply方法

148
00:04:29,840 --> 00:04:31,300
我们需要怎么样

149
00:04:31,300 --> 00:04:33,040
等待文件上传完成后

150
00:04:33,040 --> 00:04:35,080
这时候我们上传完成后

151
00:04:35,080 --> 00:04:37,140
也会有个叫什么叫after emit

152
00:04:37,140 --> 00:04:40,000
以前在用都是上传之前是吧

153
00:04:40,000 --> 00:04:40,840
这又上传后

154
00:04:40,840 --> 00:04:44,940
在这里我们可以直接一样叫compiler

155
00:04:44,940 --> 00:04:46,640
compiler

156
00:04:46,640 --> 00:04:49,000
完了里面挂上这样一个钩子

157
00:04:49,100 --> 00:04:51,960
完了沟字上呢就有一个叫after对吧

158
00:04:51,960 --> 00:04:54,260
after叫compiler

159
00:04:54,260 --> 00:04:58,140
哎不after image 发射文件对吧

160
00:04:58,140 --> 00:05:00,000
哎完了这里呢我们可以绑

161
00:05:00,000 --> 00:05:01,800
我这里面就用type promise吧对吧

162
00:05:01,800 --> 00:05:03,400
因为上传文件肯定是多个

163
00:05:03,400 --> 00:05:05,300
那我们希望都上传完成以后呢

164
00:05:05,300 --> 00:05:06,360
才将打包成功

165
00:05:06,360 --> 00:05:08,460
所以这里面我就type promise了

166
00:05:08,460 --> 00:05:11,960
那里面一样插件的名字呢就叫upload plugin

167
00:05:11,960 --> 00:05:13,900
完了后面呢我们可以给他一个回调

168
00:05:13,900 --> 00:05:17,540
哎这个回调里面的一样有几个参数对吧

169
00:05:17,540 --> 00:05:18,800
完了我就现在就不用了

170
00:05:18,800 --> 00:05:20,960
用不用到忘了我消息也能用得到

171
00:05:20,960 --> 00:05:23,200
因为我们要拿到这个compleation对吧

172
00:05:23,200 --> 00:05:24,800
compleation

173
00:05:24,800 --> 00:05:26,960
他的参数里面就放的是我们所有的什么

174
00:05:26,960 --> 00:05:28,160
那些资源资源的话

175
00:05:28,160 --> 00:05:29,340
我们是不是可以这样去拿

176
00:05:29,340 --> 00:05:31,200
light一个ersize对吧

177
00:05:31,200 --> 00:05:32,100
等于他

178
00:05:32,100 --> 00:05:33,340
第二ersize

179
00:05:33,340 --> 00:05:34,560
因为我要干嘛呢

180
00:05:34,560 --> 00:05:36,400
是不是拿到每一个文件去上传

181
00:05:36,400 --> 00:05:37,640
所以这里面毫无疑问

182
00:05:37,640 --> 00:05:39,660
肯定要循环这样一个东西对吧

183
00:05:39,660 --> 00:05:40,500
object

184
00:05:40,500 --> 00:05:41,400
因为它是个对象

185
00:05:41,400 --> 00:05:42,660
我把它转成一个key

186
00:05:42,660 --> 00:05:44,500
我需要拿到文件名而已

187
00:05:44,500 --> 00:05:47,400
在这里面拿过来第二什么的第二负一池是吧

188
00:05:48,200 --> 00:05:48,960
.for each

189
00:05:48,960 --> 00:05:52,160
往这里面就可以拿到我们所谓的那个叫file name是吧

190
00:05:52,160 --> 00:05:53,000
就上传

191
00:05:53,000 --> 00:05:54,500
那上传的话我就在这里面

192
00:05:54,500 --> 00:05:55,940
因为我就用他 promise 了

193
00:05:55,940 --> 00:05:58,340
那他这个方法应该返回的就得是个什么

194
00:05:58,340 --> 00:05:59,460
是不是 promise

195
00:05:59,460 --> 00:06:00,860
那 promise 应该怎么用呢

196
00:06:00,860 --> 00:06:02,900
是不是等待所有都上传完呀

197
00:06:02,900 --> 00:06:04,400
肯定是个 promise all

198
00:06:04,400 --> 00:06:05,060
哎

199
00:06:05,060 --> 00:06:07,660
那 return 是吧 return promise all

200
00:06:07,660 --> 00:06:10,960
那这时候 promise all 里面应该放上一个 promises 对吧

201
00:06:10,960 --> 00:06:11,900
它是个书组

202
00:06:11,900 --> 00:06:14,540
并且把 promises 怎么样整个都放进来

203
00:06:14,540 --> 00:06:15,700
哎

204
00:06:15,700 --> 00:06:16,940
那这样好了以后啊

205
00:06:16,940 --> 00:06:18,300
我们是不是就应该怎么样

206
00:06:18,300 --> 00:06:20,340
是不是通过一个个文件上传

207
00:06:20,340 --> 00:06:21,940
这时候我就叫一个方法

208
00:06:21,940 --> 00:06:23,180
叫this.upload

209
00:06:23,180 --> 00:06:24,900
upload

210
00:06:24,900 --> 00:06:25,700
完了怎么做

211
00:06:25,700 --> 00:06:27,180
我就把feel那么一传

212
00:06:27,180 --> 00:06:28,580
就表示上传这个文件

213
00:06:28,580 --> 00:06:30,340
它返回的肯定是个promise

214
00:06:30,340 --> 00:06:30,700
对吧

215
00:06:30,700 --> 00:06:33,180
promise的话我就把它怎么样往里一放

216
00:06:33,180 --> 00:06:34,340
这就ok了

217
00:06:34,340 --> 00:06:36,540
到时候我们把这书组里的文件

218
00:06:36,540 --> 00:06:38,300
一次上传都上传完成后

219
00:06:38,300 --> 00:06:38,900
打爆成功

220
00:06:38,900 --> 00:06:39,500
是吧

221
00:06:39,500 --> 00:06:41,700
这里我们再需要写一个方法

222
00:06:41,700 --> 00:06:42,580
叫upload

223
00:06:42,580 --> 00:06:44,580
ok

224
00:06:44,580 --> 00:06:46,380
upload里面应该放上一个就

225
00:06:46,380 --> 00:06:47,820
东西就叫我们的fail name

226
00:06:47,820 --> 00:06:51,440
那同样他应该返回的是return new promise 对吧

227
00:06:51,440 --> 00:06:53,620
我来个reload reject

228
00:06:53,620 --> 00:06:55,980
可以了

229
00:06:55,980 --> 00:06:57,240
那现在我要怎么做呢

230
00:06:57,240 --> 00:06:58,940
我是不是要一个个上传呀

231
00:06:58,940 --> 00:06:59,580
那上传的话

232
00:06:59,580 --> 00:07:01,240
刚才看到了应该有这样一个方法

233
00:07:01,240 --> 00:07:02,380
叫什么form date uploader

234
00:07:02,380 --> 00:07:03,080
就就这个是吧

235
00:07:03,080 --> 00:07:03,880
挺长的

236
00:07:03,880 --> 00:07:04,920
我就粘过来

237
00:07:04,920 --> 00:07:06,180
我当然了这个属性呢

238
00:07:06,180 --> 00:07:07,440
现在是挂掉 this 上了

239
00:07:07,440 --> 00:07:08,220
我就粘一下

240
00:07:08,220 --> 00:07:09,120
往上里面呢

241
00:07:09,120 --> 00:07:10,220
我就这就不用写了

242
00:07:10,220 --> 00:07:11,740
我就判断如果有错误的话

243
00:07:11,740 --> 00:07:12,040
那好

244
00:07:12,040 --> 00:07:13,440
我就直接reject就好了

245
00:07:13,440 --> 00:07:13,880
对吧

246
00:07:13,880 --> 00:07:15,220
reject把它放进来

247
00:07:16,380 --> 00:07:21,680
哎完了如果要是成功的话呢等于200的话那好我就这掉下rezo对吧rezo

248
00:07:21,680 --> 00:07:23,540
rezo

249
00:07:23,540 --> 00:07:29,440
完了else呢我就先不管了对吧你还要看看什么其他的我再把它成打印象就不管了这不要了

250
00:07:29,440 --> 00:07:31,880
如果是200就成功我这就ok了

251
00:07:31,880 --> 00:07:37,380
那现在我们这里面要注意啊那这里面现在他返回的是一个promise这几个参数哪来的对吧

252
00:07:37,380 --> 00:07:42,040
在token刚才能看到了是通过上面来生成的叫z4.upload token

253
00:07:43,020 --> 00:07:45,480
k 呢就是我们的文件名也就是我们的fuel name

254
00:07:45,480 --> 00:07:48,060
那本地文件应该被打不到哪去呢

255
00:07:48,060 --> 00:07:50,180
是不是应该会传到这个this的目录下来

256
00:07:50,180 --> 00:07:52,260
所以这里面我需要怎么样呢是不是

257
00:07:52,260 --> 00:07:53,260
拼一个对吧

258
00:07:53,260 --> 00:07:54,980
那我还需要一下这个pass的模块

259
00:07:54,980 --> 00:07:57,260
light pass

260
00:07:57,260 --> 00:07:58,520
等于require pass

261
00:07:58,520 --> 00:08:00,960
说到引包的时候这里面其实还引了个包啊

262
00:08:00,960 --> 00:08:03,880
我们用七牛的话也需要下载一个七牛的模块

263
00:08:03,880 --> 00:08:05,280
这里面没有去下载啊

264
00:08:05,280 --> 00:08:06,680
那这里我要把它下一下

265
00:08:06,680 --> 00:08:09,720
呀啊的对吧呀啊的不是七牛是吧

266
00:08:09,720 --> 00:08:10,320
七牛

267
00:08:11,620 --> 00:08:14,260
完了这里面呢我们就可以把这七牛引进来

268
00:08:14,260 --> 00:08:17,460
七牛等于require一个叫七牛是吧

269
00:08:17,460 --> 00:08:20,960
ok 完了这时候呢我们就有这样一个七牛有这样一个pass

270
00:08:20,960 --> 00:08:22,760
那本地的路径怎么拿呢

271
00:08:22,760 --> 00:08:25,760
其实就是一个real pass或者叫local fail 对吧

272
00:08:25,760 --> 00:08:26,480
local fail

273
00:08:26,480 --> 00:08:28,620
那它的位置呢就是以什么呢

274
00:08:28,620 --> 00:08:30,360
是不是以当前目录下去哪找呢

275
00:08:30,360 --> 00:08:31,860
是不是pass.result

276
00:08:31,860 --> 00:08:32,760
完了你去哪找呢

277
00:08:32,760 --> 00:08:34,980
是不是--第20当前目录

278
00:08:34,980 --> 00:08:36,020
完了找什么呢

279
00:08:36,020 --> 00:08:38,560
是不是上一级的dist目录下对吧

280
00:08:38,560 --> 00:08:40,660
那就是---dist

281
00:08:40,660 --> 00:08:41,460
完了找什么呢

282
00:08:41,460 --> 00:08:43,420
是不是找当前这样一个文件名就好了

283
00:08:43,420 --> 00:08:44,460
就fuel name

284
00:08:44,460 --> 00:08:46,020
这就是真实的那个文件

285
00:08:46,020 --> 00:08:47,620
他会去把这个文件读到是吧

286
00:08:47,620 --> 00:08:48,420
上传上去

287
00:08:48,420 --> 00:08:49,020
我这个呢

288
00:08:49,020 --> 00:08:49,600
这个属性呢

289
00:08:49,600 --> 00:08:51,820
也看到了也是挂在zc上的是吧

290
00:08:51,820 --> 00:08:53,620
通过表单生成的把它放在这

291
00:08:53,620 --> 00:08:55,660
那这样的话呀

292
00:08:55,660 --> 00:08:57,220
一个结构就差不多了

293
00:08:57,220 --> 00:08:58,300
那都成功以后

294
00:08:58,300 --> 00:09:00,120
是不是都会放到这样一个书读里

295
00:09:00,120 --> 00:09:02,260
完了promise 2都成功完以后怎么样

296
00:09:02,260 --> 00:09:04,820
是不是把我们这个文件都传到青牛上了

297
00:09:04,820 --> 00:09:06,220
那我们来看看效果吧

298
00:09:06,220 --> 00:09:07,060
能不能实现呢

299
00:09:07,060 --> 00:09:07,660
是吧

300
00:09:07,660 --> 00:09:08,100
OK

301
00:09:08,100 --> 00:09:09,420
这个功能很简单啊

302
00:09:09,420 --> 00:09:10,500
那这里面一样

303
00:09:10,500 --> 00:09:12,260
打爆npx webpack

304
00:09:12,260 --> 00:09:14,440
运行一下

305
00:09:14,440 --> 00:09:16,800
完了这时候看着已经打爆成功了

306
00:09:16,800 --> 00:09:18,500
但是好像没有什么变化

307
00:09:18,500 --> 00:09:19,900
早上打个log好了

308
00:09:19,900 --> 00:09:21,900
再来看看这里面没有东西

309
00:09:21,900 --> 00:09:23,000
这是最重要的

310
00:09:23,000 --> 00:09:25,200
我在这控制管理是吧

311
00:09:25,200 --> 00:09:27,400
里面有个对象存储这里

312
00:09:27,400 --> 00:09:30,600
完了里面就有一个内存管理是吧

313
00:09:30,600 --> 00:09:32,700
你看是不是4个文件都传上去了

314
00:09:32,700 --> 00:09:34,100
当然有可能你可以判断一下

315
00:09:34,100 --> 00:09:35,200
比如atml传

316
00:09:35,200 --> 00:09:35,900
gs不传

317
00:09:35,900 --> 00:09:37,300
然后说md也不传是吧

318
00:09:37,300 --> 00:09:37,900
也可以

319
00:09:37,900 --> 00:09:39,700
但是咱来试试看看能不能访问

320
00:09:39,900 --> 00:09:42,140
就通过这个image对吧

321
00:09:42,140 --> 00:09:43,540
完了这个东西是吧

322
00:09:43,540 --> 00:09:45,000
我就直接访问atml

323
00:09:45,000 --> 00:09:47,100
飞车应该是不是变红了

324
00:09:47,100 --> 00:09:48,200
而且有个哈喽

325
00:09:48,200 --> 00:09:49,560
嗯这个哈喽是我的吗

326
00:09:49,560 --> 00:09:50,300
我看看啊

327
00:09:50,300 --> 00:09:52,360
console好像不太像是吧

328
00:09:52,360 --> 00:09:55,140
我因为我这个文件里面好像并没有写东西

329
00:09:55,140 --> 00:09:56,160
没有写是吧

330
00:09:56,160 --> 00:09:57,600
可能是他有这个缓存啊

331
00:09:57,600 --> 00:09:58,760
我这里面有来个问号

332
00:09:58,760 --> 00:10:00,540
哎哎

333
00:10:00,540 --> 00:10:01,240
其实可以的

334
00:10:01,240 --> 00:10:02,340
你看就有点问题啊

335
00:10:02,340 --> 00:10:03,760
这里面应该是不是有bill的

336
00:10:03,760 --> 00:10:05,240
但这个东西没有去识别啊

337
00:10:05,240 --> 00:10:06,540
因为他并不是一个

338
00:10:06,540 --> 00:10:08,300
他没有识别上这个atml类型

339
00:10:08,300 --> 00:10:08,700
那这里呢

340
00:10:08,700 --> 00:10:09,700
我还在这访问吧

341
00:10:09,700 --> 00:10:10,460
打包出来的

342
00:10:10,460 --> 00:10:12,400
我这里面访问的时候你会发现

343
00:10:12,400 --> 00:10:14,660
当前这样一个文件里面装的是啥呢

344
00:10:14,660 --> 00:10:17,900
其实它里面并没有变成什么当前那个东西

345
00:10:17,900 --> 00:10:19,240
那我们还需要干嘛呢

346
00:10:19,240 --> 00:10:20,740
是不是在这里面再配上一个

347
00:10:20,740 --> 00:10:22,240
我们以前配过的东西叫什么

348
00:10:22,240 --> 00:10:25,300
是他应该引用的是上传上去的文件资源

349
00:10:25,300 --> 00:10:28,060
也就需要配个叫 public 什么 pass

350
00:10:28,060 --> 00:10:31,060
因为我们传到cdn 上了是吧

351
00:10:31,060 --> 00:10:32,660
这里面应该怎么办呢

352
00:10:32,660 --> 00:10:34,240
是把我们的路径往上一放

353
00:10:34,240 --> 00:10:36,340
这是最靠谱的翻过来

354
00:10:36,340 --> 00:10:38,000
看到这里

355
00:10:38,000 --> 00:10:39,260
那这样的话是不是每个资源

356
00:10:39,260 --> 00:10:40,760
前面就会加上这样一个资源

357
00:10:40,760 --> 00:10:42,360
那现在我们的atml打包出来

358
00:10:42,360 --> 00:10:44,000
是不是就会是他下载什么

359
00:10:44,000 --> 00:10:45,040
他下载什么什么啊

360
00:10:45,040 --> 00:10:46,040
那这里面为了方便

361
00:10:46,040 --> 00:10:47,460
我在最后补个杠啊

362
00:10:47,460 --> 00:10:48,900
万一出点什么问题就不好了

363
00:10:48,900 --> 00:10:50,200
那这里再加杠

364
00:10:50,200 --> 00:10:52,040
就后面这是吧

365
00:10:52,040 --> 00:10:53,160
完了我再来打包

366
00:10:53,160 --> 00:10:54,360
哎看看行不行

367
00:10:54,360 --> 00:10:55,000
npm

368
00:10:55,000 --> 00:10:56,200
xwipac

369
00:10:56,200 --> 00:10:58,260
因为它的缓存很严重啊

370
00:10:58,260 --> 00:11:00,260
那这里面我就打包了

371
00:11:00,260 --> 00:11:02,660
嗯好像没成功一样是吧

372
00:11:02,660 --> 00:11:04,660
npxwipac再打一次

373
00:11:04,660 --> 00:11:07,920
应该是文件传上去的发言名字

374
00:11:08,000 --> 00:11:08,900
都重了是吧

375
00:11:08,900 --> 00:11:10,640
那最好我把这个文件先删掉啊

376
00:11:10,640 --> 00:11:11,700
我全选一下是吧

377
00:11:11,700 --> 00:11:13,940
呃全选全选是在哪

378
00:11:13,940 --> 00:11:15,300
这就一个个点吧

379
00:11:15,300 --> 00:11:16,340
全三全删掉

380
00:11:16,340 --> 00:11:18,600
你要是失败了是吧

381
00:11:18,600 --> 00:11:20,140
失败的话直接就失败了

382
00:11:20,140 --> 00:11:21,700
那失败的话就没有想走是吧

383
00:11:21,700 --> 00:11:22,840
那这样的话我再传一下

384
00:11:22,840 --> 00:11:23,660
应该就可以了

385
00:11:23,660 --> 00:11:26,460
好成功了是吧

386
00:11:26,460 --> 00:11:28,000
因为就是刚才已经传过了

387
00:11:28,000 --> 00:11:29,900
那这里面我再来刷新一下是吧

388
00:11:29,900 --> 00:11:31,140
ok 刷新

389
00:11:31,140 --> 00:11:31,700
我这里呢

390
00:11:31,700 --> 00:11:33,940
我应该访问的就是本地的这个资源了

391
00:11:33,940 --> 00:11:35,200
那本地资源就是他是吧

392
00:11:35,200 --> 00:11:35,860
转过来

393
00:11:35,860 --> 00:11:37,400
是不是依然可以变红

394
00:11:37,400 --> 00:11:38,900
而且你看看我是不是有引到呢

395
00:11:38,900 --> 00:11:39,140
对吧

396
00:11:39,140 --> 00:11:40,440
network刷新

397
00:11:40,440 --> 00:11:42,540
我要找一下这个all所有的资源是吧

398
00:11:42,540 --> 00:11:44,000
嗯小点

399
00:11:44,000 --> 00:11:48,240
那这个文件是不是就是我当前cd那个慢点css

400
00:11:48,240 --> 00:11:51,440
还有我们慢点的这个blogs打开

401
00:11:51,440 --> 00:11:52,640
是不是没问题啊

402
00:11:52,640 --> 00:11:54,740
那这样的话我们就实现了一个什么

403
00:11:54,740 --> 00:11:57,660
是不是自动打包自动上传的一个功能

