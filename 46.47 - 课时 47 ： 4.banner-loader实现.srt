1
00:00:00,000 --> 00:00:04,620
这里为了大家能掌握更多的loader写法

2
00:00:04,620 --> 00:00:07,420
我们先来介绍一下loader的一些API用法

3
00:00:07,420 --> 00:00:10,360
我们先来自创一个loader来感受一下

4
00:00:10,360 --> 00:00:10,620
是吧

5
00:00:10,620 --> 00:00:13,900
我们在webpack中把loader先锄掉

6
00:00:13,900 --> 00:00:16,580
我们在这里写个对象

7
00:00:16,580 --> 00:00:17,800
比如说来个test

8
00:00:17,800 --> 00:00:19,880
我们希望还是以GS结尾

9
00:00:19,880 --> 00:00:21,740
但这时候用的不太一样了

10
00:00:21,740 --> 00:00:21,980
对吧

11
00:00:21,980 --> 00:00:23,820
我们自己写上这样一个loader

12
00:00:23,820 --> 00:00:24,620
叫bannerloader

13
00:00:24,620 --> 00:00:27,260
也是为了掌握我们的loader的用法

14
00:00:27,260 --> 00:00:28,720
我们写的时候再复杂一点

15
00:00:28,720 --> 00:00:30,220
把这点选项是吧

16
00:00:30,220 --> 00:00:31,420
比如说有一个loader

17
00:00:31,420 --> 00:00:32,320
完了他

18
00:00:32,320 --> 00:00:34,720
写多了啊

19
00:00:34,720 --> 00:00:35,420
loader

20
00:00:35,420 --> 00:00:38,320
还应该有我们的这样一个选项options

21
00:00:38,320 --> 00:00:39,520
什么叫班的loader呢

22
00:00:39,520 --> 00:00:40,220
就是需要啊

23
00:00:40,220 --> 00:00:41,720
给所有匹配的gs呢

24
00:00:41,720 --> 00:00:42,720
都加上这样一个

25
00:00:42,720 --> 00:00:45,220
应该我们所谓的班那班那指的就是一个注释

26
00:00:45,220 --> 00:00:45,520
是吧

27
00:00:45,520 --> 00:00:47,220
比如说我们一般都会写上

28
00:00:47,220 --> 00:00:47,380
对吧

29
00:00:47,380 --> 00:00:48,420
谁写的这个文件

30
00:00:48,420 --> 00:00:49,020
比如说哎

31
00:00:49,020 --> 00:00:50,080
当前谁写的是吧

32
00:00:50,080 --> 00:00:50,720
有个注释

33
00:00:50,720 --> 00:00:51,520
都是珠峰

34
00:00:51,520 --> 00:00:53,120
有这样一个效果啊

35
00:00:53,120 --> 00:00:54,720
珠峰

36
00:00:54,720 --> 00:00:56,720
那好了

37
00:00:56,720 --> 00:00:57,180
比如说呢

38
00:00:57,180 --> 00:00:57,920
我们可以这样

39
00:00:57,920 --> 00:00:59,380
采用文本的方式给他

40
00:00:59,380 --> 00:01:00,420
比如来个泰斯

41
00:01:00,420 --> 00:01:02,120
说我要写个文本

42
00:01:02,120 --> 00:01:03,420
那这里呢我就写个珠峰

43
00:01:03,420 --> 00:01:04,180
那这样的话呢

44
00:01:04,180 --> 00:01:05,780
到时候生成的注释呢就叫珠峰

45
00:01:05,780 --> 00:01:06,560
那同样啊

46
00:01:06,560 --> 00:01:07,360
我为了方便

47
00:01:07,360 --> 00:01:09,060
有可能我有的时候不给珠峰

48
00:01:09,060 --> 00:01:10,560
有可能呢给一个feel name

49
00:01:10,560 --> 00:01:11,580
这个feel name指的什么意思

50
00:01:11,580 --> 00:01:12,880
就是我希望有个模板

51
00:01:12,880 --> 00:01:13,420
对吧

52
00:01:13,420 --> 00:01:14,220
比如这个模板呢

53
00:01:14,220 --> 00:01:15,760
我在这新建一个吧

54
00:01:15,760 --> 00:01:16,880
叫猪背训

55
00:01:16,880 --> 00:01:18,080
叫banner

56
00:01:18,080 --> 00:01:18,380
对吧

57
00:01:18,380 --> 00:01:19,220
.js

58
00:01:19,220 --> 00:01:20,180
就这样写了啊

59
00:01:20,180 --> 00:01:20,780
往这里面呢

60
00:01:20,780 --> 00:01:23,060
我就来个猪峰培训

61
00:01:23,060 --> 00:01:25,980
完了比如说你可以写上日期是吧

62
00:01:25,980 --> 00:01:26,780
我就不写了

63
00:01:26,780 --> 00:01:27,380
那这里呢

64
00:01:27,380 --> 00:01:28,620
我们就可以在这放在这

65
00:01:28,620 --> 00:01:30,320
我给他一个绝对路径

66
00:01:30,320 --> 00:01:33,160
我希望如果他没有给泰斯给了费用内部

67
00:01:33,160 --> 00:01:35,480
我就应该读费用内部中的结果

68
00:01:35,480 --> 00:01:39,320
那pass.resol来个杠杠第20里面

69
00:01:39,320 --> 00:01:41,760
我就可以给他一个叫banner.js

70
00:01:41,760 --> 00:01:44,900
好了现在我们有了这样一个配置以后

71
00:01:44,900 --> 00:01:46,560
我们也需要写这样一个loader

72
00:01:46,560 --> 00:01:48,300
这个loader叫bannerloader

73
00:01:48,300 --> 00:01:53,140
我们同样快速的把它技术结构拿过来

74
00:01:53,140 --> 00:01:55,640
它就叫loader里面有自己的圆满

75
00:01:55,640 --> 00:01:58,940
并且呢我们需要把这样一个loader的导出

76
00:01:58,940 --> 00:02:02,740
那现在我们就来去处理这些事的

77
00:02:02,740 --> 00:02:04,140
那这个sauce呢我们有了

78
00:02:04,140 --> 00:02:05,240
我们有了以后需要怎么样

79
00:02:05,240 --> 00:02:07,540
是不是先要拿到他的参数是吧

80
00:02:07,540 --> 00:02:08,540
那参数的话一样

81
00:02:08,540 --> 00:02:10,640
我们可以用我们刚才学的那个方法对吧

82
00:02:10,640 --> 00:02:12,240
叫youtil对吧

83
00:02:12,240 --> 00:02:13,340
叫loader util

84
00:02:13,340 --> 00:02:16,240
loader utils等于requare

85
00:02:16,240 --> 00:02:18,840
我们就直接引进来叫loader utils

86
00:02:18,840 --> 00:02:19,940
完并且呢

87
00:02:19,940 --> 00:02:23,140
我们可以用他的一个方法叫get options是吧

88
00:02:23,140 --> 00:02:24,540
get options

89
00:02:24,540 --> 00:02:26,180
里面的把我们的扫子放在这

90
00:02:26,180 --> 00:02:26,440
不是扫子是z

91
00:02:26,440 --> 00:02:29,980
也不是sauce 是this this就是当前我们的loader contest

92
00:02:29,980 --> 00:02:33,340
这里呢我们就可以拿到这样一个选项

93
00:02:33,340 --> 00:02:35,340
但是这个选项呢有个问题了

94
00:02:35,340 --> 00:02:38,140
我们一般写代码是不是都会教养一下这个选项

95
00:02:38,140 --> 00:02:39,840
到底符不符合我的规范

96
00:02:39,840 --> 00:02:41,200
是不是需要这样做

97
00:02:41,200 --> 00:02:41,780
对吧

98
00:02:41,780 --> 00:02:43,140
比如说他里面这参数有没有啊

99
00:02:43,140 --> 00:02:44,040
传的对不对啊

100
00:02:44,040 --> 00:02:46,200
那这时候呢我们还需要一个校验的模块

101
00:02:46,200 --> 00:02:47,740
叫股价校验对吧

102
00:02:47,740 --> 00:02:49,440
我们需要一个一二二的

103
00:02:49,440 --> 00:02:49,940
他叫什么呢

104
00:02:49,940 --> 00:02:52,180
叫skammer skammer股价的意思啊

105
00:02:52,180 --> 00:02:53,900
-youtube也是个酷

106
00:02:53,900 --> 00:02:55,380
那用的时候也非常方便

107
00:02:55,380 --> 00:02:56,580
我们可以再把它引进来

108
00:02:56,580 --> 00:02:59,680
叫scammer utils等于requare

109
00:02:59,680 --> 00:03:01,080
我们在这直接拿进来

110
00:03:01,080 --> 00:03:02,580
叫scammer utils

111
00:03:02,580 --> 00:03:05,180
并且我们可以通过它对吧

112
00:03:05,180 --> 00:03:06,580
它里面就是一个胶验的方法

113
00:03:06,580 --> 00:03:07,480
你可以点击去看看

114
00:03:07,480 --> 00:03:09,280
就一个叫validate options

115
00:03:09,280 --> 00:03:11,180
胶验属性的方法就是它是吧

116
00:03:11,180 --> 00:03:11,880
倒出来就是它

117
00:03:11,880 --> 00:03:12,980
我就直接用了

118
00:03:12,980 --> 00:03:14,980
这里面你需要给它一个股价

119
00:03:14,980 --> 00:03:17,280
比如说应该有什么选项参与是什么

120
00:03:17,280 --> 00:03:17,880
那好

121
00:03:17,880 --> 00:03:19,780
我就给它创建这样一个股价

122
00:03:19,780 --> 00:03:20,780
SCHEM

123
00:03:20,780 --> 00:03:22,880
那里面我跟它说

124
00:03:22,880 --> 00:03:24,780
哎当前它的对象它类型对吧

125
00:03:24,780 --> 00:03:26,880
这个loader那类型的应该是对象

126
00:03:26,880 --> 00:03:28,520
那对象呢

127
00:03:28,520 --> 00:03:31,840
他应该有些属性叫proper

128
00:03:31,840 --> 00:03:33,580
proper提示

129
00:03:33,580 --> 00:03:34,540
他的属性呢

130
00:03:34,540 --> 00:03:35,340
比如说应该有什么呢

131
00:03:35,340 --> 00:03:36,980
是不是有我们的文本叫泰斯

132
00:03:36,980 --> 00:03:38,820
那这里面写的是不是叫泰克斯

133
00:03:38,820 --> 00:03:40,420
那同样他应该什么类型的

134
00:03:40,420 --> 00:03:42,180
同样你再写个对象告诉他

135
00:03:42,180 --> 00:03:44,180
他的类型呢是应该是死君是吧

136
00:03:44,180 --> 00:03:45,980
你可以自己去选择怎么去添

137
00:03:45,980 --> 00:03:48,680
那同样里面还有刚才看到看到了是吧

138
00:03:48,680 --> 00:03:49,640
还有个叫feel name

139
00:03:49,640 --> 00:03:51,180
他应该也是个字母串是吧

140
00:03:51,180 --> 00:03:54,280
那好feel name他应该也是一个字母串

141
00:03:54,580 --> 00:03:55,580
type 是4G

142
00:03:55,580 --> 00:04:00,280
4G 自不串4G

143
00:04:00,280 --> 00:04:01,120
那好了

144
00:04:01,120 --> 00:04:03,380
那我们现在就把这股价拿过来

145
00:04:03,380 --> 00:04:04,280
和谁比呢

146
00:04:04,280 --> 00:04:05,980
和我们当前这个参数来比

147
00:04:05,980 --> 00:04:06,880
看看传的对不对

148
00:04:06,880 --> 00:04:08,080
当然你不传没关系

149
00:04:08,080 --> 00:04:09,380
你要传的话类型不对

150
00:04:09,380 --> 00:04:10,280
那就有关系了

151
00:04:10,280 --> 00:04:11,580
我这时候最好怎么样

152
00:04:11,580 --> 00:04:13,380
说如果要是不符合

153
00:04:13,380 --> 00:04:15,680
你说要跟人家说哪个loader报错了

154
00:04:15,680 --> 00:04:16,080
那好

155
00:04:16,080 --> 00:04:17,780
最后一个参数就是给他个名字

156
00:04:17,780 --> 00:04:19,080
是这个loader出问题了

157
00:04:19,080 --> 00:04:19,980
那好了

158
00:04:19,980 --> 00:04:21,280
那有了这样一个参数以后

159
00:04:21,280 --> 00:04:22,180
我们可以判断

160
00:04:22,180 --> 00:04:23,080
比如说如果呀

161
00:04:23,080 --> 00:04:24,880
当前我们这个选项对吧

162
00:04:24,880 --> 00:04:27,080
你非常简单的按有file name

163
00:04:27,080 --> 00:04:29,880
那我该干嘛了是要读取文件了

164
00:04:29,880 --> 00:04:32,880
那读文件的话依然我们需要肯定fs模块

165
00:04:32,880 --> 00:04:37,780
好了我们可以通过这个fs模块的去读取

166
00:04:37,780 --> 00:04:40,380
对吧叫fs.readerfilesync

167
00:04:40,380 --> 00:04:43,380
那为了方便我们还是用一部的

168
00:04:43,380 --> 00:04:47,580
完了里面呢我们就直接来写的都知道一部方便是吧

169
00:04:47,580 --> 00:04:49,780
那这里面我们是不是要读这个文件名啊

170
00:04:49,780 --> 00:04:52,340
完并且呢给人家一个东西叫又跳吧

171
00:04:52,340 --> 00:04:53,620
完了成功以后呢

172
00:04:53,620 --> 00:04:54,740
我们可以怎么做呢

173
00:04:54,740 --> 00:04:56,620
是不是可以给一个回调

174
00:04:56,620 --> 00:04:57,460
那读完以后呢

175
00:04:57,460 --> 00:04:59,100
是不是就有我们所谓的错误

176
00:04:59,100 --> 00:05:00,860
还有我们所谓的这个叫呃

177
00:05:00,860 --> 00:05:01,500
data

178
00:05:01,500 --> 00:05:02,620
那同样一步的话

179
00:05:02,620 --> 00:05:04,140
我们说了不能用return了是不是

180
00:05:04,140 --> 00:05:05,020
那这时候怎么办呢

181
00:05:05,020 --> 00:05:08,140
我们就需要创建这样一个叫呃叫cb

182
00:05:08,140 --> 00:05:09,060
对吧还记得吧

183
00:05:09,060 --> 00:05:12,020
他用要这样一个ersink方法来创建

184
00:05:12,020 --> 00:05:12,780
那同样啊

185
00:05:12,780 --> 00:05:14,620
我们应该在读完以后呢

186
00:05:14,620 --> 00:05:16,260
调一下这个cb方法

187
00:05:16,260 --> 00:05:16,940
完了并且呢

188
00:05:16,940 --> 00:05:18,420
把我们的error传进去

189
00:05:18,420 --> 00:05:19,420
完了第二个参数呢

190
00:05:19,420 --> 00:05:21,580
就应该是我们的读到的数据

191
00:05:21,580 --> 00:05:24,020
但这数据里面我需要干嘛

192
00:05:24,020 --> 00:05:25,520
是不是还要加上我们的圆码

193
00:05:25,520 --> 00:05:27,620
完了把它应该在这加上是吧

194
00:05:27,620 --> 00:05:28,420
那为了方便吧

195
00:05:28,420 --> 00:05:30,220
我们就来个这个东西用过是吧

196
00:05:30,220 --> 00:05:31,020
往往子不串

197
00:05:31,020 --> 00:05:32,120
我们在这

198
00:05:32,120 --> 00:05:34,820
完了前面我们说了他应该也是个变量

199
00:05:34,820 --> 00:05:35,520
但是他呢

200
00:05:35,520 --> 00:05:37,720
是不是应该去放到我们的注释里面去

201
00:05:37,720 --> 00:05:39,920
好我在这来个杠星星对吧

202
00:05:39,920 --> 00:05:41,120
这来个星星杠

203
00:05:41,120 --> 00:05:43,920
当然了如果要是没有这个值的话

204
00:05:43,920 --> 00:05:46,520
那你是不是就应该用我们那个什么options text

205
00:05:46,520 --> 00:05:48,820
那好应该在这来个test

206
00:05:48,920 --> 00:05:49,620
那同样啊

207
00:05:49,620 --> 00:05:50,280
那这时候呢

208
00:05:50,280 --> 00:05:51,420
我不能用return

209
00:05:51,420 --> 00:05:53,480
我就说这不是没有放在一部里面吗

210
00:05:53,480 --> 00:05:54,180
这是一面啊

211
00:05:54,180 --> 00:05:55,580
因为你已经告诉人家了

212
00:05:55,580 --> 00:05:56,880
当前是个一部方法

213
00:05:56,880 --> 00:05:57,520
所以说呀

214
00:05:57,520 --> 00:05:59,080
他已经把这cb传给你了

215
00:05:59,080 --> 00:05:59,920
你需要怎么样

216
00:05:59,920 --> 00:06:01,020
就不管同步一步

217
00:06:01,020 --> 00:06:02,720
你都可以怎么样去这样来调用

218
00:06:02,720 --> 00:06:03,820
这是你的规范啊

219
00:06:03,820 --> 00:06:04,980
都随便下写了

220
00:06:04,980 --> 00:06:06,420
那这里面一样我来个no

221
00:06:06,420 --> 00:06:08,960
那看看效果吧

222
00:06:08,960 --> 00:06:11,380
那现在我们产生的时候应该就没有问题了

223
00:06:11,380 --> 00:06:12,680
我在这里呢运行一下

224
00:06:12,680 --> 00:06:13,560
呃

225
00:06:13,560 --> 00:06:14,320
npx

226
00:06:14,320 --> 00:06:15,220
npx

227
00:06:15,220 --> 00:06:15,920
webpack

228
00:06:15,920 --> 00:06:17,680
就是根据我们的内容呢

229
00:06:17,680 --> 00:06:18,720
写这样一个注释

230
00:06:18,720 --> 00:06:20,220
哦这注释啊有点问题啊

231
00:06:20,220 --> 00:06:22,360
还是这里面我还是用满足串吧

232
00:06:22,360 --> 00:06:24,160
没有加上把它放在这

233
00:06:24,160 --> 00:06:26,960
往这里呢也一样叫奥山泰克斯

234
00:06:26,960 --> 00:06:27,860
放在这是吧

235
00:06:27,860 --> 00:06:29,120
这样就合理一点啊

236
00:06:29,120 --> 00:06:30,960
完了效果也是一样的

237
00:06:30,960 --> 00:06:33,320
现在我们有feel name

238
00:06:33,320 --> 00:06:34,820
所以说肯定用的是上面这个啊

239
00:06:34,820 --> 00:06:35,880
那咱来看看吧

240
00:06:35,880 --> 00:06:37,480
有没有达到我们的预期呢

241
00:06:37,480 --> 00:06:40,480
会不会把我们的文件打包

242
00:06:40,480 --> 00:06:41,220
打包完以后

243
00:06:41,220 --> 00:06:43,220
会不会有这样一个是不是有了

244
00:06:43,220 --> 00:06:45,020
这样一个注释是没问题的

245
00:06:45,020 --> 00:06:46,120
那现在ok了

246
00:06:46,120 --> 00:06:47,780
我们知道这样一个功能以后啊

247
00:06:47,780 --> 00:06:49,220
其实我们要稍微改一改

248
00:06:49,220 --> 00:06:51,480
这时候我们都知道我们打包的时候

249
00:06:51,480 --> 00:06:53,680
一般反正我都会加上这样一个参数

250
00:06:53,680 --> 00:06:54,480
这个参数叫什么来着

251
00:06:54,480 --> 00:06:55,080
还记得吗

252
00:06:55,080 --> 00:06:56,780
叫过持什么处

253
00:06:56,780 --> 00:06:57,720
就说我们希望怎么样

254
00:06:57,720 --> 00:06:59,480
是不是边写边打包啊

255
00:06:59,480 --> 00:07:00,680
那这时候我们来看看效果

256
00:07:00,680 --> 00:07:01,480
走你

257
00:07:01,480 --> 00:07:03,780
是不是就卡在这了

258
00:07:03,780 --> 00:07:06,480
说正在监听我们的文件变化

259
00:07:06,480 --> 00:07:07,080
那此时啊

260
00:07:07,080 --> 00:07:08,180
我们再去干嘛呢

261
00:07:08,180 --> 00:07:10,080
去改一下我们的配置文件改了

262
00:07:10,080 --> 00:07:11,080
你看123

263
00:07:11,080 --> 00:07:12,480
我希望的加个123

264
00:07:12,480 --> 00:07:12,980
那这时候呢

265
00:07:12,980 --> 00:07:13,980
他应该怎么样

266
00:07:13,980 --> 00:07:15,880
发现好像没变异是吧

267
00:07:15,880 --> 00:07:16,880
那为什么没变异呢

268
00:07:16,880 --> 00:07:17,980
我们来看看是不是没变异

269
00:07:17,980 --> 00:07:19,600
确实没变异

270
00:07:19,600 --> 00:07:21,320
一说我们在loader里面

271
00:07:21,320 --> 00:07:22,440
是不是去读了一个文件

272
00:07:22,440 --> 00:07:25,000
但是他觉得这个文件跟webpack没关系

273
00:07:25,000 --> 00:07:26,360
它就是一个文件而已

274
00:07:26,360 --> 00:07:28,200
读完以后怎么样就用呗

275
00:07:28,200 --> 00:07:29,820
这时候我们希望干嘛

276
00:07:29,820 --> 00:07:31,340
是不是希望我们loader

277
00:07:31,340 --> 00:07:34,080
当每次我们依赖的文件变化了

278
00:07:34,080 --> 00:07:35,200
就要重新去打包

279
00:07:35,200 --> 00:07:37,700
这时候我们就要跟webpack说一声

280
00:07:37,700 --> 00:07:39,260
webpack怎么知道要不要打包

281
00:07:39,260 --> 00:07:40,860
我们怎么做

282
00:07:40,860 --> 00:07:42,160
把这事件改一下

283
00:07:42,160 --> 00:07:43,600
当我们读完文件

284
00:07:43,600 --> 00:07:45,140
这个文件是需要干嘛的

285
00:07:45,140 --> 00:07:46,560
是不是需要有依赖关系的

286
00:07:46,560 --> 00:07:47,700
或者在这写就行了

287
00:07:47,700 --> 00:07:49,020
把它怎么样

288
00:07:49,020 --> 00:07:50,600
加到我们的依赖里去

289
00:07:50,600 --> 00:07:53,000
叫addependency

290
00:07:53,000 --> 00:07:53,380
是吧

291
00:07:53,380 --> 00:07:54,380
这样一个方法

292
00:07:54,380 --> 00:07:54,940
是吧

293
00:07:54,940 --> 00:07:56,600
他把这个文件加进去以后

294
00:07:56,600 --> 00:07:57,820
这个文件变化了

295
00:07:57,820 --> 00:07:59,280
也会让我们的wip

296
00:07:59,280 --> 00:07:59,820
怎么样

297
00:07:59,820 --> 00:08:00,740
重新打包

298
00:08:00,740 --> 00:08:01,600
来试试

299
00:08:01,600 --> 00:08:03,440
我在这里一项运行

300
00:08:03,440 --> 00:08:06,240
有点抱错

301
00:08:06,240 --> 00:08:07,840
他说方法不对

302
00:08:07,840 --> 00:08:09,180
看看是拼错了

303
00:08:09,180 --> 00:08:11,640
addependency

304
00:08:11,640 --> 00:08:12,020
是吧

305
00:08:12,020 --> 00:08:12,580
多了个n

306
00:08:12,580 --> 00:08:13,400
再来一次

307
00:08:13,400 --> 00:08:14,380
运行

308
00:08:14,380 --> 00:08:17,140
你看这时候啊

309
00:08:17,140 --> 00:08:18,820
现在不动了是吧

310
00:08:18,820 --> 00:08:19,180
完了

311
00:08:19,180 --> 00:08:20,780
我稍微改一下啊

312
00:08:20,780 --> 00:08:24,020
比如说把这个值删掉保存

313
00:08:24,020 --> 00:08:25,440
你看是不是好像重新打包了

314
00:08:25,440 --> 00:08:27,240
那再来试试看看是不是一样啊

315
00:08:27,240 --> 00:08:28,560
刚才是你看是注明讯了

316
00:08:28,560 --> 00:08:30,140
比如我在这里快速改是吧

317
00:08:30,140 --> 00:08:31,520
来个123一保存

318
00:08:31,520 --> 00:08:33,740
我这里呢是不是就可以了

319
00:08:33,740 --> 00:08:35,900
那我们就知道了这样一个方式啊

320
00:08:35,900 --> 00:08:36,440
可以怎么样

321
00:08:36,440 --> 00:08:38,080
自动的添加文件依赖

322
00:08:38,080 --> 00:08:41,240
自动的添加文件依赖

323
00:08:41,240 --> 00:08:43,280
当然了还有比如添加文件夹的依赖

324
00:08:43,280 --> 00:08:43,620
是吧

325
00:08:43,620 --> 00:08:45,060
你可以自己去看一下API

326
00:08:45,060 --> 00:08:46,240
文件依赖

327
00:08:46,240 --> 00:08:48,160
同样我们WiPAC

328
00:08:48,160 --> 00:08:48,840
它有个功能

329
00:08:48,840 --> 00:08:50,100
就是默认打包的时候

330
00:08:50,100 --> 00:08:51,060
它会启用缓存

331
00:08:51,060 --> 00:08:51,880
什么叫缓存

332
00:08:51,880 --> 00:08:52,900
就是有这样一个属性

333
00:08:52,900 --> 00:08:53,920
叫catchable

334
00:08:53,920 --> 00:08:55,660
catchable

335
00:08:55,660 --> 00:08:57,240
它一定要用

336
00:08:57,240 --> 00:08:58,400
如果你传个false

337
00:08:58,400 --> 00:08:59,980
表示就是每次打包的时候

338
00:08:59,980 --> 00:09:00,980
都需要怎么样

339
00:09:00,980 --> 00:09:02,340
重新的不要缓存

340
00:09:02,340 --> 00:09:03,300
你要给个false

341
00:09:03,300 --> 00:09:04,420
就表示不要缓存了

342
00:09:04,420 --> 00:09:04,960
好

343
00:09:04,960 --> 00:09:06,100
我们来看看

344
00:09:06,100 --> 00:09:06,940
是不是就这样

345
00:09:06,940 --> 00:09:08,560
比如在这里运行

346
00:09:08,560 --> 00:09:10,840
它就会有提示

347
00:09:10,840 --> 00:09:12,300
这东西是没缓存的

348
00:09:12,300 --> 00:09:13,600
但是如果我们loader里面

349
00:09:13,600 --> 00:09:14,680
比如说有大量的计算

350
00:09:14,680 --> 00:09:16,700
我们更希望怎么样缓存

351
00:09:16,700 --> 00:09:17,940
而且YPAC也怎么样

352
00:09:17,940 --> 00:09:19,240
推荐你使用缓存

353
00:09:19,240 --> 00:09:21,620
所以说你会看到一些人的loader里面会这样写

354
00:09:21,620 --> 00:09:23,260
只要他有缓存

355
00:09:23,260 --> 00:09:25,400
但是第一次肯定是没有的

356
00:09:25,400 --> 00:09:26,140
如果有

357
00:09:26,140 --> 00:09:27,720
我就掉下这方法

358
00:09:27,720 --> 00:09:29,560
这里我就来个什么括号

359
00:09:29,560 --> 00:09:30,480
他就不要了

360
00:09:30,480 --> 00:09:31,560
这样来写

361
00:09:31,560 --> 00:09:32,860
这不写也默认的

362
00:09:32,860 --> 00:09:33,420
好

363
00:09:33,420 --> 00:09:34,320
来看看效果

364
00:09:34,320 --> 00:09:36,900
这样的话

365
00:09:36,900 --> 00:09:40,040
我们就实现了一个我们自己的叫bandloader了

366
00:09:40,040 --> 00:09:41,660
我们来看看效果

367
00:09:41,660 --> 00:09:43,040
好像应该也是可以的

368
00:09:43,040 --> 00:09:43,540
是吧

369
00:09:43,540 --> 00:09:44,900
这里面中文讯是有的

370
00:09:44,900 --> 00:09:47,500
同样你可以看看原码也是ok的

371
00:09:47,500 --> 00:09:49,300
每次更改的时候依然会怎么样

372
00:09:49,300 --> 00:09:50,700
让我们的阴面来刷新是吧

373
00:09:50,700 --> 00:09:52,980
保存你看是不是就自动动信了

374
00:09:52,980 --> 00:09:53,540
好

375
00:09:53,540 --> 00:09:55,340
那我们就接着往下来实现

376
00:09:55,340 --> 00:09:57,940
实现我们的URL和FileLoader

