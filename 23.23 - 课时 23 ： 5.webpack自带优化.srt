1
00:00:00,000 --> 00:00:02,280
本节呢

2
00:00:02,280 --> 00:00:05,880
我们来讲一下这个外派个中的自带的一些优化功能

3
00:00:05,880 --> 00:00:06,440
那这里呢

4
00:00:06,440 --> 00:00:07,760
我们先把这个配置文件啊

5
00:00:07,760 --> 00:00:08,640
保存一份

6
00:00:08,640 --> 00:00:09,720
好了

7
00:00:09,720 --> 00:00:11,760
我们简单的把这个文件的新去

8
00:00:11,760 --> 00:00:12,600
好了

9
00:00:12,600 --> 00:00:13,040
并且呢

10
00:00:13,040 --> 00:00:15,080
我们把当前这个目录下的对吧

11
00:00:15,080 --> 00:00:16,840
比如说这个海币派克干掉吧

12
00:00:16,840 --> 00:00:17,880
因为它的速度呢

13
00:00:17,880 --> 00:00:18,680
很慢啊

14
00:00:18,680 --> 00:00:19,560
那这里面一样

15
00:00:19,560 --> 00:00:22,320
我把我们的这个babel再贴回去

16
00:00:22,320 --> 00:00:22,920
是吧

17
00:00:22,920 --> 00:00:24,280
是我的babel配置

18
00:00:24,280 --> 00:00:26,040
那放到我们的这里面来

19
00:00:26,040 --> 00:00:27,360
那同样的css呢

20
00:00:27,360 --> 00:00:29,000
我也把它再粘回去啊

21
00:00:29,000 --> 00:00:29,880
就是用它

22
00:00:30,000 --> 00:00:33,060
那这样的话呢他俩我就不要了

23
00:00:33,060 --> 00:00:35,840
哎就可以了这是一个默认的打包形式啊

24
00:00:35,840 --> 00:00:40,000
那我们把src下来把这两个文件呢也稍微的保存一份吧

25
00:00:40,000 --> 00:00:42,000
哎放到我们的室里

26
00:00:42,000 --> 00:00:45,500
为了方便呢我把这个文件干掉

27
00:00:45,500 --> 00:00:47,400
这个test呢我也把它干掉了

28
00:00:47,400 --> 00:00:48,900
这两个空的文件啊

29
00:00:48,900 --> 00:00:51,700
那现在我要怎么做呢我写这样一个代码吧

30
00:00:51,700 --> 00:00:55,800
比如说我们都知道啊其实在webpack里面它会怎么样根据入口来打包

31
00:00:55,800 --> 00:00:58,600
那这里面我比如说写个非常简易的代码啊

32
00:00:58,600 --> 00:01:00,000
我就在这里嗯

33
00:01:00,000 --> 00:01:00,800
泰斯里吧

34
00:01:00,800 --> 00:01:03,200
我就写上写上一个名字吧

35
00:01:03,200 --> 00:01:04,000
在这 light

36
00:01:04,000 --> 00:01:09,200
我们来个比如说叫C叫萨姆求和

37
00:01:09,200 --> 00:01:10,100
那求和的话呢

38
00:01:10,100 --> 00:01:12,200
可能会有两个参数

39
00:01:12,200 --> 00:01:14,400
那返回的就是A加B

40
00:01:14,400 --> 00:01:15,100
完了后面呢

41
00:01:15,100 --> 00:01:16,300
我就再加一个标识符

42
00:01:16,300 --> 00:01:17,500
比如他叫萨姆

43
00:01:17,500 --> 00:01:18,500
那好除了萨姆呢

44
00:01:18,500 --> 00:01:20,500
肯定有加肯定还有减对吧

45
00:01:20,500 --> 00:01:21,600
那叫minus

46
00:01:21,600 --> 00:01:23,300
这里面一样会有A有B

47
00:01:23,300 --> 00:01:25,300
那我这里面就应该是A减B

48
00:01:25,300 --> 00:01:27,200
里面放上一个minus

49
00:01:27,200 --> 00:01:27,700
最后呢

50
00:01:27,700 --> 00:01:35,700
我把这两个方法同样导出,比如说sum,还有我们的minus,这两个三数导出去,

51
00:01:35,700 --> 00:01:38,500
现在我们是不是可以在index里面去使用了,

52
00:01:38,500 --> 00:01:43,300
index里面可以去这样用对吧,比如说我可以拿到这样一个计算公式对吧,

53
00:01:43,300 --> 00:01:48,200
我可以在这里去拿到叫index.test.js,

54
00:01:48,200 --> 00:01:53,200
往那里面呢,我就可以怎么样,是不是去拿到我们这样一个clc,

55
00:01:53,200 --> 00:01:57,600
那我就可以怎么样,是不是调他的add方法呀,这里面我传个一传个二,

56
00:01:57,600 --> 00:02:00,920
那结果呢应该就是3和sum是吧

57
00:02:00,920 --> 00:02:02,220
那我们来看一看

58
00:02:02,220 --> 00:02:03,060
这里面呢

59
00:02:03,060 --> 00:02:04,760
我把这个配置现在是开发模式

60
00:02:04,760 --> 00:02:05,700
我来运行一下

61
00:02:05,700 --> 00:02:07,720
npm run build

62
00:02:07,720 --> 00:02:09,280
现在的意思是这样的

63
00:02:09,280 --> 00:02:12,260
这里面我虽然导出了两个变量

64
00:02:12,260 --> 00:02:13,800
一个叫我们的sum

65
00:02:13,800 --> 00:02:14,380
一个叫minus

66
00:02:14,380 --> 00:02:16,780
那我在index里面到底能不能

67
00:02:16,780 --> 00:02:18,300
我指引了什么是不是add

68
00:02:18,300 --> 00:02:21,320
那我这个所谓的minus

69
00:02:21,320 --> 00:02:22,220
是不是应该没用到

70
00:02:22,220 --> 00:02:23,280
那没有add

71
00:02:23,280 --> 00:02:24,400
应该叫什么叫sum

72
00:02:24,400 --> 00:02:24,640
是吧

73
00:02:24,640 --> 00:02:25,140
改个名字

74
00:02:25,140 --> 00:02:28,320
这里面是不是应该只能引到什么

75
00:02:28,320 --> 00:02:30,260
是不是sum但是minus没用到

76
00:02:30,260 --> 00:02:31,900
那minus会不会打包呢

77
00:02:31,900 --> 00:02:33,360
这时候我们来见证一下

78
00:02:33,360 --> 00:02:34,620
里面会有个bundle

79
00:02:34,620 --> 00:02:37,560
这里应该会有一个叫sum

80
00:02:37,560 --> 00:02:38,940
确实是有的

81
00:02:38,940 --> 00:02:40,220
往来再看看没有minus

82
00:02:40,220 --> 00:02:41,760
是吧你发现这minus也在

83
00:02:41,760 --> 00:02:43,680
那这时候是不是就我就不高兴了

84
00:02:43,680 --> 00:02:44,720
那我们来看一看吧

85
00:02:44,720 --> 00:02:46,900
最后在打包的时候会不会真的打进去

86
00:02:46,900 --> 00:02:48,820
我把这模式变成produks

87
00:02:48,820 --> 00:02:51,440
那这里呢

88
00:02:51,440 --> 00:02:54,040
用produks

89
00:02:55,140 --> 00:02:56,620
再打包

90
00:02:56,620 --> 00:02:58,840
完了看一看我们生产环境下

91
00:02:58,840 --> 00:03:00,760
会不会有这样一个加和减

92
00:03:00,760 --> 00:03:01,060
是吧

93
00:03:01,060 --> 00:03:02,720
告诉我1001个字节

94
00:03:02,720 --> 00:03:03,040
是吧

95
00:03:03,040 --> 00:03:04,360
这里面我点开

96
00:03:04,360 --> 00:03:06,780
看看有没有这个对吧

97
00:03:06,780 --> 00:03:07,620
menas

98
00:03:07,620 --> 00:03:09,040
发现没有menas了

99
00:03:09,040 --> 00:03:10,320
但是我们的sum有没有

100
00:03:10,320 --> 00:03:10,840
哎

101
00:03:10,840 --> 00:03:11,900
发现sum是有的

102
00:03:11,900 --> 00:03:12,880
那也就是说呀

103
00:03:12,880 --> 00:03:14,040
默认情况下

104
00:03:14,040 --> 00:03:15,460
我们用这种语法的话

105
00:03:15,460 --> 00:03:17,680
import这种语法对吧

106
00:03:17,680 --> 00:03:19,840
语法在打包的时候对吧

107
00:03:19,840 --> 00:03:22,000
在生产环境下

108
00:03:22,000 --> 00:03:24,040
环境下对吧

109
00:03:24,040 --> 00:03:24,840
会自动

110
00:03:24,840 --> 00:03:37,380
对吧,会自动,自动去除掉,去除掉,没有用的代码,没用的代码,那这种模式啊,我们叫在啥呢?其实他有个自己的专业名词,叫tree

111
00:03:37,380 --> 00:03:49,670
 shaking,什么叫shaking呢?这个tree,tree就是树,对吧?那shaking呢?就是树的摇晃,你说他们用的,没用的叶子,对吧?我们树上有很多叶子,有绿的,有黄的,那没用的怎么样?是不是一摇就掉下来了?所以这种方式呢,叫tree

112
00:03:49,670 --> 00:03:54,120
 shaking,就是把什么?把没用的代码,没用到的,

113
00:03:54,840 --> 00:04:23,280
代码

114
00:04:24,840 --> 00:04:26,940
npm run dv

115
00:04:26,940 --> 00:04:28,440
顺便说下这个问题

116
00:04:28,440 --> 00:04:30,240
在这里把它刨起来

117
00:04:30,240 --> 00:04:32,240
3000

118
00:04:32,240 --> 00:04:33,640
你执行了

119
00:04:33,640 --> 00:04:35,340
完了这里面有点慢

120
00:04:35,340 --> 00:04:36,040
稍等

121
00:04:36,040 --> 00:04:37,840
好像是ok的

122
00:04:37,840 --> 00:04:39,440
但是我看看这个代码行不行

123
00:04:39,440 --> 00:04:40,140
console

124
00:04:40,140 --> 00:04:41,240
看他现在报错了

125
00:04:41,240 --> 00:04:42,000
他告诉我

126
00:04:42,000 --> 00:04:43,640
sum他并不是个函数

127
00:04:43,640 --> 00:04:44,740
这是为什么呢

128
00:04:44,740 --> 00:04:45,940
我想再提一下

129
00:04:45,940 --> 00:04:47,340
因为我们这个clc

130
00:04:47,340 --> 00:04:48,640
你看看他是什么东西

131
00:04:48,640 --> 00:04:49,740
他其实是个对象

132
00:04:49,740 --> 00:04:50,740
这个没问题

133
00:04:50,740 --> 00:04:52,640
稍等

134
00:04:52,640 --> 00:04:53,340
有点卡

135
00:04:53,340 --> 00:04:54,340
这里面汇车下

136
00:04:54,840 --> 00:04:56,180
编译的时候有点慢

137
00:04:56,180 --> 00:04:59,240
你看它因为它是个ES6模块导出

138
00:04:59,240 --> 00:05:02,500
所以它会把导出的结果放到这样一个default的数据上

139
00:05:02,500 --> 00:05:04,840
也就是说我们这里面要拿的话怎么拿

140
00:05:04,840 --> 00:05:07,500
你得clc.default

141
00:05:07,500 --> 00:05:10,340
我要再去点什么比如说sum来个一来个二

142
00:05:10,340 --> 00:05:12,500
这就可以了再来试试是吧

143
00:05:12,500 --> 00:05:13,540
编译一下

144
00:05:13,540 --> 00:05:15,940
又卡住了他说正在更新对吧

145
00:05:15,940 --> 00:05:18,100
重新编译你看是不是3sum

146
00:05:18,100 --> 00:05:20,140
所以说这里面我们要再多来一句是吧

147
00:05:20,140 --> 00:05:22,000
就是ES6模块对吧

148
00:05:22,000 --> 00:05:24,240
ES6模块会把

149
00:05:24,840 --> 00:05:26,740
结果放到这个default

150
00:05:26,740 --> 00:05:29,540
default是bfout

151
00:05:29,540 --> 00:05:30,620
那好了

152
00:05:30,620 --> 00:05:31,560
那咱来试一试吧

153
00:05:31,560 --> 00:05:33,880
那看看打包的时候能不能解一样

154
00:05:33,880 --> 00:05:35,500
把这个没用代码删除掉

155
00:05:35,500 --> 00:05:37,020
npm run build

156
00:05:37,020 --> 00:05:39,700
那现在我们这个就是一个生产环境

157
00:05:39,700 --> 00:05:40,820
你看看是不是生产环境

158
00:05:40,820 --> 00:05:41,580
是production

159
00:05:41,580 --> 00:05:43,120
那咱来看看吧

160
00:05:43,120 --> 00:05:44,000
这代码呢会不会在

161
00:05:44,000 --> 00:05:46,700
你看明显好像比刚才这个大了

162
00:05:46,700 --> 00:05:47,940
1.07k了是不是

163
00:05:47,940 --> 00:05:49,540
那在这里面我找一下吧

164
00:05:49,540 --> 00:05:52,820
回到我们的文件在哪呢

165
00:05:52,820 --> 00:05:53,940
是不是在这个叫bundle

166
00:05:53,940 --> 00:05:54,500
是他

167
00:05:54,500 --> 00:05:56,640
看这是sum这是什么这是minus

168
00:05:56,640 --> 00:05:59,740
也说他把sum和minus怎么样都拿过来了

169
00:05:59,740 --> 00:06:01,660
所以说我们require语法

170
00:06:01,660 --> 00:06:03,680
他并不支持我们的treeshaking

171
00:06:03,680 --> 00:06:04,900
这也就是我们前端

172
00:06:04,900 --> 00:06:06,980
为什么要使用这种input的语法

173
00:06:06,980 --> 00:06:09,380
所以这里面我们要注意这样一点

174
00:06:09,380 --> 00:06:11,680
其次还有一个东西叫什么

175
00:06:11,680 --> 00:06:13,300
叫我们这里面还有一个名词

176
00:06:13,300 --> 00:06:14,320
其实也能看到

177
00:06:14,320 --> 00:06:15,880
叫scopehosting

178
00:06:15,880 --> 00:06:17,500
叫什么叫作用欲提升

179
00:06:17,500 --> 00:06:19,780
什么意思

180
00:06:19,780 --> 00:06:21,280
比如说我在这写着代码

181
00:06:21,280 --> 00:06:22,560
这代码写的很low

182
00:06:22,560 --> 00:06:24,900
LetA等于1B等于2

183
00:06:24,900 --> 00:06:26,760
完了LetA一个C等于3

184
00:06:26,760 --> 00:06:30,080
完了LetA一个D等于A加B加C

185
00:06:30,080 --> 00:06:31,300
完了最后呢

186
00:06:31,300 --> 00:06:32,380
我想打印个什么呢

187
00:06:32,380 --> 00:06:33,020
打印个D

188
00:06:33,020 --> 00:06:35,080
你不觉得这代码写的有点啰嗦吗

189
00:06:35,080 --> 00:06:36,640
你直接怎么办就好了

190
00:06:36,640 --> 00:06:38,020
是不是给我打印出来什么

191
00:06:38,020 --> 00:06:39,420
1加2加3就可以了吗

192
00:06:39,420 --> 00:06:41,060
这些变量是不是都应该可以怎么样

193
00:06:41,060 --> 00:06:41,960
省略掉吧

194
00:06:41,960 --> 00:06:43,840
那这个动作如果在路远器里面

195
00:06:43,840 --> 00:06:44,620
是不是还需要怎么样

196
00:06:44,620 --> 00:06:45,920
不停的来重复声明

197
00:06:45,920 --> 00:06:47,100
那你看看吧

198
00:06:47,100 --> 00:06:48,940
我们WiPiC怎么帮我们做的

199
00:06:48,940 --> 00:06:49,920
我在这里面一样

200
00:06:49,920 --> 00:06:50,580
RunBuild

201
00:06:50,580 --> 00:06:55,000
同样它也是生产环境下怎么样才生效的

202
00:06:55,000 --> 00:06:56,580
以前我们把它拍个里面的

203
00:06:56,580 --> 00:06:58,460
从三开始才加了这个功能

204
00:06:58,460 --> 00:07:00,260
这里面我来运行一下

205
00:07:00,260 --> 00:07:03,160
这时候你看结果是1.067K是吧

206
00:07:03,160 --> 00:07:03,960
06K

207
00:07:03,960 --> 00:07:06,060
我把这里把bundle点开

208
00:07:06,060 --> 00:07:07,200
刷新一下

209
00:07:07,200 --> 00:07:10,900
看看我们123是不是ABD哪去了

210
00:07:10,900 --> 00:07:11,600
找一找

211
00:07:11,600 --> 00:07:13,660
为了能看到这个效果

212
00:07:13,660 --> 00:07:16,200
因为打包后它很难看到这个结果

213
00:07:16,200 --> 00:07:17,600
所以这里面我再加个什么

214
00:07:17,600 --> 00:07:19,240
我再加一个杠

215
00:07:19,240 --> 00:07:19,720
好吧

216
00:07:19,720 --> 00:07:21,440
这样要看着明显一点

217
00:07:21,440 --> 00:07:22,720
明确就是变量

218
00:07:22,720 --> 00:07:24,440
再打包

219
00:07:24,440 --> 00:07:26,420
这里我再运行一下

220
00:07:26,420 --> 00:07:27,760
稍等刷新

221
00:07:27,760 --> 00:07:28,800
这里面没看不到

222
00:07:28,800 --> 00:07:29,860
我还盲懂

223
00:07:29,860 --> 00:07:30,900
你看这里面

224
00:07:30,900 --> 00:07:34,360
是不是帮我们自己把分析好了

225
00:07:34,360 --> 00:07:37,020
是把那1加2加3的结果直接变成6了

226
00:07:37,020 --> 00:07:37,660
放到这来了

227
00:07:37,660 --> 00:07:39,020
是不是可以省去什么

228
00:07:39,020 --> 00:07:41,500
我们这些所谓的A加B加C这样一个什么

229
00:07:41,500 --> 00:07:42,180
表达式计算

230
00:07:42,180 --> 00:07:43,460
方便一些

231
00:07:43,460 --> 00:07:44,280
因为它在什么

232
00:07:44,280 --> 00:07:45,900
在WinePack中

233
00:07:45,900 --> 00:07:46,220
对吧

234
00:07:46,220 --> 00:07:47,640
中会自动怎么样

235
00:07:47,640 --> 00:07:48,760
会自动

236
00:07:48,760 --> 00:07:51,000
省略

237
00:07:51,000 --> 00:07:55,060
比如说一些的一些可以简化的代码

238
00:07:55,060 --> 00:07:57,840
一些可以简化的

239
00:07:57,840 --> 00:08:02,920
他说把这个代码怎么自动可以简化

240
00:08:02,920 --> 00:08:04,600
可以简化代码可以自动省略

241
00:08:04,600 --> 00:08:05,820
可以简化你的代码

242
00:08:05,820 --> 00:08:07,060
同样可以看看

243
00:08:07,060 --> 00:08:09,780
我们刚才这个引用其实也是会变的

244
00:08:09,780 --> 00:08:11,680
比如你看我们是不是在这里面会变成什么

245
00:08:11,680 --> 00:08:12,420
是不是哇

246
00:08:12,420 --> 00:08:13,400
R等于什么

247
00:08:13,400 --> 00:08:14,380
SUM是个函数

248
00:08:14,380 --> 00:08:15,620
完了Mainas是个函数

249
00:08:15,620 --> 00:08:16,480
是不是变成对象了

250
00:08:16,480 --> 00:08:16,660
你看

251
00:08:16,660 --> 00:08:18,040
是不是也帮你弄好了

252
00:08:18,040 --> 00:08:20,640
并没有怎么样真正的去导入另一个模块

253
00:08:20,640 --> 00:08:22,240
制作帮你怎么样是不是实现了

254
00:08:22,240 --> 00:08:25,240
相当于我在这里面直接把你这个引用的关系怎么样

255
00:08:25,240 --> 00:08:26,140
把它变成一个对象

256
00:08:26,140 --> 00:08:27,540
对象放到当前作用域下

257
00:08:27,540 --> 00:08:29,740
我掉的时候就是他第二题放到第二次

258
00:08:29,740 --> 00:08:31,540
这就是他的好处

259
00:08:31,540 --> 00:08:34,140
所以说我们webpack自动做了两个功能

260
00:08:34,140 --> 00:08:35,340
第一功能叫tree checking

261
00:08:35,340 --> 00:08:37,240
第二个叫scope hosting

