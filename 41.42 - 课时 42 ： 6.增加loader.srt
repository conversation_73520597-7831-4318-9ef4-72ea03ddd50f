1
00:00:00,000 --> 00:00:04,220
那我们接下来就开始来编写我们这个loader

2
00:00:04,220 --> 00:00:06,400
那loader的话我们就写的非常容易了

3
00:00:06,400 --> 00:00:08,160
其实我们都知道上次已经讲过了

4
00:00:08,160 --> 00:00:09,200
这loader怎么去实现

5
00:00:09,200 --> 00:00:10,380
那我们为了简单

6
00:00:10,380 --> 00:00:11,540
我们就先来写一下

7
00:00:11,540 --> 00:00:12,540
不是在这里呢

8
00:00:12,540 --> 00:00:13,840
我们写两个loader吧

9
00:00:13,840 --> 00:00:16,320
为了方便我就在外拍个配置一下

10
00:00:16,320 --> 00:00:18,000
这里呢我再加两个

11
00:00:18,000 --> 00:00:20,700
比如说一个叫这个应该叫module

12
00:00:20,700 --> 00:00:21,820
完了里面有个什么呢

13
00:00:21,820 --> 00:00:22,920
module对吧

14
00:00:22,920 --> 00:00:24,900
完了里面有个rolls规则

15
00:00:24,900 --> 00:00:27,500
规则里面我就写上一个test

16
00:00:27,500 --> 00:00:28,600
比如写个less吧

17
00:00:28,600 --> 00:00:30,740
我就批以less结尾的

18
00:00:30,740 --> 00:00:32,140
格式化一下

19
00:00:32,140 --> 00:00:33,640
我这里呢

20
00:00:33,640 --> 00:00:35,060
我就使用一下两个loader

21
00:00:35,060 --> 00:00:35,540
use

22
00:00:35,540 --> 00:00:36,300
比如说呢

23
00:00:36,300 --> 00:00:36,740
第一个呢

24
00:00:36,740 --> 00:00:37,460
我们就叫他

25
00:00:37,460 --> 00:00:38,820
在这先建个loader吧

26
00:00:38,820 --> 00:00:39,440
有两loader

27
00:00:39,440 --> 00:00:40,340
我就不去下了

28
00:00:40,340 --> 00:00:41,220
自己来手写一下

29
00:00:41,220 --> 00:00:42,220
loader点js

30
00:00:42,220 --> 00:00:43,060
loader

31
00:00:43,060 --> 00:00:44,320
完了里面呢

32
00:00:44,320 --> 00:00:45,360
我们就分别建两个

33
00:00:45,360 --> 00:00:47,500
一个叫我们的这个叫lessloader

34
00:00:47,500 --> 00:00:48,900
我就不写styleloader了

35
00:00:48,900 --> 00:00:49,820
为了方便

36
00:00:49,820 --> 00:00:51,540
除了less呢

37
00:00:51,540 --> 00:00:53,540
我们可能还有我们这样一个styleloader

38
00:00:53,540 --> 00:00:55,800
那非常简单

39
00:00:55,800 --> 00:00:56,520
那这样的话

40
00:00:56,520 --> 00:00:58,280
我们用的时候肯定需要安装一下

41
00:00:58,280 --> 00:01:01,880
这个Less应该安装我们的代码里去

42
00:01:01,880 --> 00:01:03,480
因为要NoLess

43
00:01:03,480 --> 00:01:05,260
我们LessLoader非常简单

44
00:01:05,260 --> 00:01:07,260
我们说了Loader它就是一个函数

45
00:01:07,260 --> 00:01:09,060
没啥可说的

46
00:01:09,060 --> 00:01:13,640
完了我们还要把这Loader干嘛

47
00:01:13,640 --> 00:01:14,280
导树出去

48
00:01:14,280 --> 00:01:17,880
这样的话我们可以怎么做

49
00:01:17,880 --> 00:01:18,860
非常方便了

50
00:01:18,860 --> 00:01:21,080
我们说了Loader里面它的参数就应该什么

51
00:01:21,080 --> 00:01:21,640
是不是圆码

52
00:01:21,640 --> 00:01:23,280
我们就需要怎么样

53
00:01:23,280 --> 00:01:24,500
把这圆码进行过滤

54
00:01:24,500 --> 00:01:27,980
我们第一步LessLoader肯定需要用Less模块

55
00:01:27,980 --> 00:01:28,280
对吧

56
00:01:28,280 --> 00:01:29,980
把我们这个sauce原码呢

57
00:01:29,980 --> 00:01:30,840
进行render渲染

58
00:01:30,840 --> 00:01:33,180
那好了

59
00:01:33,180 --> 00:01:35,360
我们把这个渲染后的结果呢

60
00:01:35,360 --> 00:01:35,860
返回回去

61
00:01:35,860 --> 00:01:36,780
这里呢

62
00:01:36,780 --> 00:01:37,240
我来个空

63
00:01:37,240 --> 00:01:37,600
是吧

64
00:01:37,600 --> 00:01:39,340
比如说这样写吧

65
00:01:39,340 --> 00:01:40,940
叫sauce叫less

66
00:01:40,940 --> 00:01:42,440
这样render渲染

67
00:01:42,440 --> 00:01:43,960
那渲染谁呢

68
00:01:43,960 --> 00:01:45,340
那渲染的肯定是原码

69
00:01:45,340 --> 00:01:45,640
对吧

70
00:01:45,640 --> 00:01:46,220
这个东西啊

71
00:01:46,220 --> 00:01:47,040
是个同步代码

72
00:01:47,040 --> 00:01:47,920
但它写的时候

73
00:01:47,920 --> 00:01:48,880
需要用回调来写

74
00:01:48,880 --> 00:01:49,620
第一个呢

75
00:01:49,620 --> 00:01:50,120
是error

76
00:01:50,120 --> 00:01:50,840
第二个呢

77
00:01:50,840 --> 00:01:51,480
是我们的c

78
00:01:51,480 --> 00:01:52,660
c里面呢

79
00:01:52,660 --> 00:01:53,200
就有一个东西

80
00:01:53,200 --> 00:01:53,580
我们要

81
00:01:53,580 --> 00:01:55,380
叫c点什么css

82
00:01:55,380 --> 00:01:56,640
这就是我们最终

83
00:01:56,640 --> 00:01:57,640
渲染出来的结果

84
00:01:57,640 --> 00:01:59,040
那我就把这css怎么样

85
00:01:59,040 --> 00:01:59,700
覆盖掉

86
00:01:59,700 --> 00:02:00,960
完了把它怎么样

87
00:02:00,960 --> 00:02:02,520
就是returncss

88
00:02:02,520 --> 00:02:04,540
它的作用就是什么

89
00:02:04,540 --> 00:02:06,060
就是把我们的这个代码

90
00:02:06,060 --> 00:02:07,260
转完成类似代码

91
00:02:07,260 --> 00:02:07,960
完了发回去

92
00:02:07,960 --> 00:02:09,380
那发回去以后

93
00:02:09,380 --> 00:02:11,720
我们在styleloader里面非常简单

94
00:02:11,720 --> 00:02:12,860
可以干什么事呢

95
00:02:12,860 --> 00:02:14,100
是不是创建一个style标签

96
00:02:14,100 --> 00:02:15,320
完了把这内容怎么样

97
00:02:15,320 --> 00:02:16,320
stylestyle标签里

98
00:02:16,320 --> 00:02:17,980
查到我们页面的头部

99
00:02:17,980 --> 00:02:18,780
那好了

100
00:02:18,780 --> 00:02:19,620
那这里面呢

101
00:02:19,620 --> 00:02:20,560
我也再来一个吧

102
00:02:20,560 --> 00:02:21,980
这里面我就叫它

103
00:02:21,980 --> 00:02:22,860
也叫loader

104
00:02:22,860 --> 00:02:23,200
是吧

105
00:02:23,200 --> 00:02:24,040
名字无所谓

106
00:02:24,040 --> 00:02:25,880
module.express

107
00:02:25,880 --> 00:02:27,620
等于这个loader

108
00:02:27,640 --> 00:02:29,460
完了里面呢

109
00:02:29,460 --> 00:02:31,380
我就可以拿到我们这样的同样的圆码

110
00:02:31,380 --> 00:02:32,140
那这时候呢

111
00:02:32,140 --> 00:02:34,420
我们需要弄一个style标签是吧

112
00:02:34,420 --> 00:02:35,000
放在这

113
00:02:35,000 --> 00:02:36,100
完了里面呢

114
00:02:36,100 --> 00:02:40,100
我需要来一个style等于document.createelement

115
00:02:40,100 --> 00:02:42,280
完了来一个style元素

116
00:02:42,280 --> 00:02:43,600
完了并且呢

117
00:02:43,600 --> 00:02:44,920
我们在这style里面呢

118
00:02:44,920 --> 00:02:46,140
去插入这样一个脚本

119
00:02:46,140 --> 00:02:47,200
但是要注意啊

120
00:02:47,200 --> 00:02:48,220
这个source呢

121
00:02:48,220 --> 00:02:49,760
它里面可能啊会包含

122
00:02:49,760 --> 00:02:51,460
比如说我在这写个样式

123
00:02:51,460 --> 00:02:51,700
对吧

124
00:02:51,700 --> 00:02:52,880
叫index.less

125
00:02:52,880 --> 00:02:54,260
为了方便啊

126
00:02:54,260 --> 00:02:55,360
我就来一个body

127
00:02:55,360 --> 00:02:56,840
就background right

128
00:02:56,840 --> 00:02:58,760
很尴尬

129
00:02:58,760 --> 00:03:00,660
因为这东西它是有换行的

130
00:03:00,660 --> 00:03:02,000
那换行的话我们说了

131
00:03:02,000 --> 00:03:02,700
那肯定不支持

132
00:03:02,700 --> 00:03:03,940
那我们需要怎么样

133
00:03:03,940 --> 00:03:05,640
是把它转成一行去

134
00:03:05,640 --> 00:03:06,320
变成这样

135
00:03:06,320 --> 00:03:08,480
那怎么变成这样呢

136
00:03:08,480 --> 00:03:09,320
其实也很简单

137
00:03:09,320 --> 00:03:11,200
我们可以用一个非常方便的方法

138
00:03:11,200 --> 00:03:11,900
咱以前也说过

139
00:03:11,900 --> 00:03:12,840
我可以通过什么呢

140
00:03:12,840 --> 00:03:13,020
是不是

141
00:03:13,020 --> 00:03:14,620
刀着符大瓜

142
00:03:14,620 --> 00:03:16,460
把我们这个原码怎么样

143
00:03:16,460 --> 00:03:17,060
转成一行

144
00:03:17,060 --> 00:03:18,120
叫死军反

145
00:03:18,120 --> 00:03:20,100
完了把这sauce往里一丢

146
00:03:20,100 --> 00:03:21,440
这事就结了

147
00:03:21,440 --> 00:03:23,440
最后我们再retain一个什么

148
00:03:23,440 --> 00:03:24,240
style

149
00:03:24,240 --> 00:03:26,240
style

150
00:03:26,240 --> 00:03:28,120
当然了这里面还插一步

151
00:03:28,120 --> 00:03:28,400
是吧

152
00:03:28,400 --> 00:03:29,280
你这样写完以后

153
00:03:29,280 --> 00:03:30,320
它不会插到页面上

154
00:03:30,320 --> 00:03:31,340
那我们还需要怎么样

155
00:03:31,340 --> 00:03:32,060
是不是document

156
00:03:32,060 --> 00:03:32,900
dir hide

157
00:03:32,900 --> 00:03:33,820
好了

158
00:03:33,820 --> 00:03:35,700
dir open the child

159
00:03:35,700 --> 00:03:36,740
把谁放进去呢

160
00:03:36,740 --> 00:03:38,400
把这个style表先放进去

161
00:03:38,400 --> 00:03:39,760
那这样的话呢

162
00:03:39,760 --> 00:03:41,020
我们这俩loader就写完了

163
00:03:41,020 --> 00:03:42,460
还写的很简练啊

164
00:03:42,460 --> 00:03:43,360
没有就写的很复杂

165
00:03:43,360 --> 00:03:44,180
那好了

166
00:03:44,180 --> 00:03:45,980
这两个loader我们写完以后啊

167
00:03:45,980 --> 00:03:46,880
就需要干嘛呢

168
00:03:46,880 --> 00:03:48,580
是不是在配置文件里去调用啊

169
00:03:48,580 --> 00:03:49,380
这里面一样

170
00:03:49,380 --> 00:03:50,160
use

171
00:03:50,160 --> 00:03:52,100
那use谁呢

172
00:03:52,100 --> 00:03:52,660
第一呢

173
00:03:52,660 --> 00:03:54,540
我们需要在这里面去pass点

174
00:03:54,540 --> 00:03:55,220
reload

175
00:03:55,220 --> 00:03:57,760
为了方便节奏路径

176
00:03:57,760 --> 00:04:00,620
当前路径下的loader

177
00:04:00,620 --> 00:04:03,320
往那里面存的什么

178
00:04:03,320 --> 00:04:05,020
是不是叫less loader

179
00:04:05,020 --> 00:04:05,720
但是顺序什么

180
00:04:05,720 --> 00:04:07,260
是不是先往上再往后

181
00:04:07,260 --> 00:04:08,660
叫pass.result

182
00:04:08,660 --> 00:04:10,780
往再来叫-DNA

183
00:04:10,780 --> 00:04:11,900
叫loader

184
00:04:11,900 --> 00:04:15,260
它里面应该引的叫我们的style-loader

185
00:04:15,260 --> 00:04:16,240
OK

186
00:04:16,240 --> 00:04:19,440
那现在这两个关系我们就有了

187
00:04:19,440 --> 00:04:20,420
那有了以后

188
00:04:20,420 --> 00:04:23,040
我们可以在我们自己手写的webpack里面

189
00:04:23,040 --> 00:04:24,380
是不是要匹配

190
00:04:24,380 --> 00:04:26,640
如果路径是以Less结尾的

191
00:04:26,640 --> 00:04:26,980
那好

192
00:04:26,980 --> 00:04:28,060
就用这两路段怎么样

193
00:04:28,060 --> 00:04:28,900
依次去处理

194
00:04:28,900 --> 00:04:29,640
但是明确

195
00:04:29,640 --> 00:04:31,680
这现在里面放的是两个路径

196
00:04:31,680 --> 00:04:34,640
那我们是不是应该拿到路径里对应的方法怎么样

197
00:04:34,640 --> 00:04:35,800
把圆码传进去

198
00:04:35,800 --> 00:04:36,620
最后怎么样

199
00:04:36,620 --> 00:04:38,180
是放回来就可以了

200
00:04:38,180 --> 00:04:38,480
那好

201
00:04:38,480 --> 00:04:40,420
那这里我就稍微改造一下

202
00:04:40,420 --> 00:04:43,360
那我们当时读圆码的时候

203
00:04:43,360 --> 00:04:45,040
我是不是要看看对吧

204
00:04:45,040 --> 00:04:46,740
那你看哪个是读圆码的方法

205
00:04:46,740 --> 00:04:47,760
那是写了一个

206
00:04:47,760 --> 00:04:49,540
叫什么呢

207
00:04:49,540 --> 00:04:50,860
叫这里

208
00:04:50,860 --> 00:04:54,020
我们是不是通过路径来读这个圆码呀

209
00:04:54,020 --> 00:04:57,820
但是有可能它这个路径是和我们这个less怎么样

210
00:04:57,820 --> 00:04:58,700
匹配上的

211
00:04:58,700 --> 00:05:00,580
比如说我在我的代码里

212
00:05:00,580 --> 00:05:02,020
来这么一句是吧

213
00:05:02,020 --> 00:05:06,340
index我就去require我们当前的这样一个第二杠

214
00:05:06,340 --> 00:05:07,080
对吧

215
00:05:07,080 --> 00:05:08,220
叫indexless

216
00:05:08,220 --> 00:05:10,600
那只要引这个less的话

217
00:05:10,600 --> 00:05:12,020
是不是就跑到这里面来

218
00:05:12,020 --> 00:05:14,560
那这个路径是不是就是可能就有可能是这个什么

219
00:05:14,560 --> 00:05:16,380
第二杠indexless

220
00:05:16,380 --> 00:05:19,020
那我们是不是就应该把我们这样一个

221
00:05:19,020 --> 00:05:19,500
对吧

222
00:05:19,500 --> 00:05:21,860
这个配置文件里面的这个配置

223
00:05:21,860 --> 00:05:23,300
取出这个test

224
00:05:23,300 --> 00:05:24,140
来看一看

225
00:05:24,140 --> 00:05:25,240
它到底能不能匹配到

226
00:05:25,240 --> 00:05:26,400
能匹配到的话怎么样

227
00:05:26,400 --> 00:05:27,660
就用这个loader

228
00:05:27,660 --> 00:05:28,040
那好了

229
00:05:28,040 --> 00:05:29,340
那我们就取吧

230
00:05:29,340 --> 00:05:31,080
叫module的什么rows

231
00:05:31,080 --> 00:05:31,840
OK

232
00:05:31,840 --> 00:05:32,880
那这里面呢

233
00:05:32,880 --> 00:05:33,880
我就可以去拿了

234
00:05:33,880 --> 00:05:34,280
第一

235
00:05:34,280 --> 00:05:36,940
我们要拿到所有的这个规则

236
00:05:36,940 --> 00:05:38,440
那规则怎么拿呢

237
00:05:38,440 --> 00:05:39,940
是不是叫module的rows

238
00:05:39,940 --> 00:05:41,740
module

239
00:05:41,740 --> 00:05:42,560
不叫module

240
00:05:42,560 --> 00:05:46,860
应该叫z4.config中的module的什么rows

241
00:05:46,860 --> 00:05:49,780
拿到的就是当前这样一个数组

242
00:05:49,780 --> 00:05:51,060
那数组的话

243
00:05:51,060 --> 00:05:51,700
我们应该怎么样

244
00:05:51,700 --> 00:05:52,500
是不是循环来写

245
00:05:52,500 --> 00:05:53,380
要一一匹配

246
00:05:53,380 --> 00:05:54,080
那好

247
00:05:54,080 --> 00:05:55,380
那我就来个复运环

248
00:05:55,380 --> 00:05:56,760
Let个I等0

249
00:05:56,760 --> 00:05:58,060
完了

250
00:05:58,060 --> 00:05:59,340
I小于什么呢

251
00:05:59,340 --> 00:06:01,100
I小于Rules的什么

252
00:06:01,100 --> 00:06:01,800
Lence

253
00:06:01,800 --> 00:06:04,100
完了之后呢

254
00:06:04,100 --> 00:06:04,540
I加加

255
00:06:04,540 --> 00:06:05,860
写上个注释

256
00:06:05,860 --> 00:06:06,140
是吧

257
00:06:06,140 --> 00:06:07,260
就是拿到

258
00:06:07,260 --> 00:06:07,720
对吧

259
00:06:07,720 --> 00:06:10,040
拿到每一个规则

260
00:06:10,040 --> 00:06:10,680
每个

261
00:06:10,680 --> 00:06:12,960
每个规则

262
00:06:12,960 --> 00:06:13,280
对吧

263
00:06:13,280 --> 00:06:14,980
每个规则

264
00:06:14,980 --> 00:06:16,420
来处理

265
00:06:16,420 --> 00:06:18,960
OK啊

266
00:06:18,960 --> 00:06:19,640
那这里面

267
00:06:19,640 --> 00:06:21,060
我就可以Let一个Rules

268
00:06:21,060 --> 00:06:22,620
等于我们的row是什么

269
00:06:22,620 --> 00:06:23,140
方括i

270
00:06:23,140 --> 00:06:24,840
row是

271
00:06:24,840 --> 00:06:26,580
完了方括号i

272
00:06:26,580 --> 00:06:29,060
那这时候我们拿到row了

273
00:06:29,060 --> 00:06:30,120
那这row指的是

274
00:06:30,120 --> 00:06:31,880
是不是就是当前这样一个对象

275
00:06:31,880 --> 00:06:33,520
那对象里面是不是有test

276
00:06:33,520 --> 00:06:33,980
有use

277
00:06:33,980 --> 00:06:34,500
那好了

278
00:06:34,500 --> 00:06:35,800
我一想到就可以怎么办

279
00:06:35,800 --> 00:06:36,240
是不是结构

280
00:06:36,240 --> 00:06:37,580
一个要test

281
00:06:37,580 --> 00:06:38,960
还有一个叫use

282
00:06:38,960 --> 00:06:41,580
完了等于我们这样一个row

283
00:06:41,580 --> 00:06:43,260
就是

284
00:06:43,260 --> 00:06:44,760
那我们这个test

285
00:06:44,760 --> 00:06:45,720
是不是就可以去

286
00:06:45,720 --> 00:06:46,340
它是个正责

287
00:06:46,340 --> 00:06:47,680
是不是可以匹配这个model pass

288
00:06:47,680 --> 00:06:48,400
那好了

289
00:06:48,400 --> 00:06:50,140
我就在这里面直接来做一步

290
00:06:50,140 --> 00:06:52,920
如果当前这个test

291
00:06:52,920 --> 00:06:54,140
它能怎么样

292
00:06:54,140 --> 00:06:55,520
能test的是吧

293
00:06:55,520 --> 00:06:56,200
冲名了

294
00:06:56,200 --> 00:06:56,820
无所谓

295
00:06:56,820 --> 00:06:58,900
是不是能匹配到这个路径的话

296
00:06:58,900 --> 00:06:59,760
那说明怎么样

297
00:06:59,760 --> 00:06:59,940
是不是

298
00:06:59,940 --> 00:07:00,980
这个模块

299
00:07:00,980 --> 00:07:02,640
这个模块对吧

300
00:07:02,640 --> 00:07:03,560
需要怎么样

301
00:07:03,560 --> 00:07:07,000
需要通过loader来转化

302
00:07:07,000 --> 00:07:09,920
那怎么转化

303
00:07:09,920 --> 00:07:12,220
我们是不是应该拿到这个数组

304
00:07:12,220 --> 00:07:13,760
取出它的最后一项

305
00:07:13,760 --> 00:07:16,760
那我是不是应该先定位到最后一个loader

306
00:07:16,760 --> 00:07:18,560
那怎么定位到最后一个loader

307
00:07:18,560 --> 00:07:22,460
是不是use的lens减一数组的内向就找到了

308
00:07:22,460 --> 00:07:25,120
那我们就可以在这里面直接拿到

309
00:07:25,120 --> 00:07:26,680
拿到这样一个lens对吧

310
00:07:26,680 --> 00:07:27,540
light一个lens

311
00:07:27,540 --> 00:07:28,920
那怎么办呢

312
00:07:28,920 --> 00:07:30,120
我就直接roll

313
00:07:30,120 --> 00:07:34,380
roll呢指的就是这个数组是吧

314
00:07:34,380 --> 00:07:35,560
点lens减一

315
00:07:35,560 --> 00:07:37,800
那我们要匹配到以后怎么样

316
00:07:37,800 --> 00:07:40,580
我是不是就可以拿到我们这样一个use

317
00:07:40,580 --> 00:07:42,620
但是use是一个数组是吧

318
00:07:42,620 --> 00:07:43,600
我可以取它的lens

319
00:07:43,600 --> 00:07:45,140
lens的话是不是就最后一个呀

320
00:07:45,140 --> 00:07:46,860
那最后一个的话我可以怎么样

321
00:07:46,860 --> 00:07:48,660
是不是他拿到的就是一个loader

322
00:07:48,660 --> 00:07:50,040
但是他其实是个字不串

323
00:07:50,040 --> 00:07:51,280
要明确是个字不串

324
00:07:51,280 --> 00:07:52,080
那好了

325
00:07:52,080 --> 00:07:53,520
那字不串的话应该怎么办

326
00:07:53,520 --> 00:07:56,380
我是不是应该再通过requare去引用这个路径

327
00:07:56,380 --> 00:07:57,320
这是个绝路径

328
00:07:57,320 --> 00:07:59,700
那这样一用的话

329
00:07:59,700 --> 00:08:01,640
我是不是拿到了这个对应的什么

330
00:08:01,640 --> 00:08:03,320
是不是获取对吧

331
00:08:03,320 --> 00:08:07,340
叫获取对应的loader函数

332
00:08:07,340 --> 00:08:09,280
函数函数

333
00:08:09,280 --> 00:08:10,800
是不是就OK了

334
00:08:10,800 --> 00:08:12,760
那现在我们可以通过这函数怎么样

335
00:08:12,760 --> 00:08:14,120
是不是先去引用第一个

336
00:08:14,120 --> 00:08:15,340
那把谁放去呢

337
00:08:15,340 --> 00:08:17,060
是不是把我们当前这个圆码

338
00:08:17,060 --> 00:08:18,000
圆码呢

339
00:08:18,000 --> 00:08:18,640
圆码在下面

340
00:08:18,640 --> 00:08:20,220
把这圆码放到上面去

341
00:08:20,220 --> 00:08:21,760
肯定要读是吧

342
00:08:21,760 --> 00:08:23,480
那把这圆码放过来

343
00:08:23,480 --> 00:08:24,080
放到里面

344
00:08:24,080 --> 00:08:25,560
是不是再把这content的怎么样

345
00:08:25,560 --> 00:08:26,900
转化一下

346
00:08:26,900 --> 00:08:28,120
这就可以了

347
00:08:28,120 --> 00:08:29,760
但是这样一转化有个问题

348
00:08:29,760 --> 00:08:31,660
现在我们只使用了一个loader

349
00:08:31,660 --> 00:08:33,220
那我们是不是要多次使用

350
00:08:33,220 --> 00:08:34,600
直到这loader没有为止

351
00:08:34,600 --> 00:08:36,800
那我们可以在里面来写个方法

352
00:08:36,800 --> 00:08:38,200
它就是个普通的loader

353
00:08:38,200 --> 00:08:39,760
我叫做normalloader

354
00:08:39,760 --> 00:08:40,000
OK

355
00:08:40,000 --> 00:08:41,740
完了我把这个代码呢

356
00:08:41,740 --> 00:08:42,580
塞进来

357
00:08:42,580 --> 00:08:44,260
完了先调一下这个normalloader

358
00:08:44,260 --> 00:08:46,700
应该能看得懂

359
00:08:46,700 --> 00:08:47,960
就是个自知识形

360
00:08:47,960 --> 00:08:49,380
那我就应该怎么样

361
00:08:49,380 --> 00:08:50,780
是不是先取出第一个

362
00:08:50,780 --> 00:08:51,480
我调完以后

363
00:08:51,480 --> 00:08:52,600
完了每次怎么样

364
00:08:52,600 --> 00:08:52,820
是不是

365
00:08:52,820 --> 00:08:53,360
渐渐

366
00:08:53,360 --> 00:08:54,880
完了之后怎么样

367
00:08:54,880 --> 00:08:55,920
是不是如果呀

368
00:08:55,920 --> 00:08:56,960
我再取出来的时候

369
00:08:56,960 --> 00:08:57,620
发现还有

370
00:08:57,620 --> 00:08:58,620
也就是这个论呢

371
00:08:58,620 --> 00:08:59,760
如果这个论

372
00:08:59,760 --> 00:09:01,000
它大于等于零

373
00:09:01,000 --> 00:09:02,940
那说明是不是这次还有

374
00:09:02,940 --> 00:09:04,060
那有的话怎么样

375
00:09:04,060 --> 00:09:05,380
再接着来处理

376
00:09:05,380 --> 00:09:07,660
说这相当于就是一个什么

377
00:09:07,660 --> 00:09:07,880
是不是

378
00:09:07,880 --> 00:09:09,040
地规对吧

379
00:09:09,040 --> 00:09:09,820
就是地规

380
00:09:09,820 --> 00:09:12,400
地规调用

381
00:09:12,400 --> 00:09:12,880
对吧

382
00:09:12,880 --> 00:09:13,760
loader

383
00:09:13,760 --> 00:09:14,520
好了

384
00:09:14,520 --> 00:09:15,280
实现什么

385
00:09:15,280 --> 00:09:16,100
实现这个

386
00:09:16,100 --> 00:09:17,440
叫转化功能

387
00:09:17,440 --> 00:09:17,780
对吧

388
00:09:17,780 --> 00:09:19,640
转化功能

389
00:09:19,640 --> 00:09:21,780
那这时候好了

390
00:09:21,780 --> 00:09:21,960
是不是

391
00:09:21,960 --> 00:09:22,800
如果它达约零

392
00:09:22,800 --> 00:09:23,840
我就不停的掉楼子

393
00:09:23,840 --> 00:09:25,520
如果楼子都执行完以后

394
00:09:25,520 --> 00:09:26,960
是不是返回的content

395
00:09:26,960 --> 00:09:29,280
就是刚才我们这样写的一个什么

396
00:09:29,280 --> 00:09:29,960
document

397
00:09:29,960 --> 00:09:31,080
这东西

398
00:09:31,080 --> 00:09:31,320
是吧

399
00:09:31,320 --> 00:09:32,320
是返回这个脚本

400
00:09:32,320 --> 00:09:34,360
那这脚本是不是就会查到什么

401
00:09:34,360 --> 00:09:36,080
是不是对应模块路径的什么

402
00:09:36,080 --> 00:09:37,180
那个内容去了吧

403
00:09:37,180 --> 00:09:39,220
那到时候我EVL一执行

404
00:09:39,220 --> 00:09:41,740
是不是就把当前这样一个东西执行了

405
00:09:41,740 --> 00:09:43,080
那执行完以后呢

406
00:09:43,080 --> 00:09:44,380
就会把这style标签呢

407
00:09:44,380 --> 00:09:45,560
插到我们的页面上

408
00:09:45,560 --> 00:09:46,200
哎

409
00:09:46,200 --> 00:09:48,720
你看这样一个过程实现完以后啊

410
00:09:48,720 --> 00:09:50,140
我们这功能啊就ok了

411
00:09:50,140 --> 00:09:51,700
get resource是吧

412
00:09:51,700 --> 00:09:52,840
这里呢也有

413
00:09:52,840 --> 00:09:53,500
嗯

414
00:09:53,500 --> 00:09:53,840
ok

415
00:09:53,840 --> 00:09:55,280
那咱来试一试吧

416
00:09:55,280 --> 00:09:57,520
看看呢能不能实现这样一个功能

417
00:09:57,520 --> 00:09:58,240
我把它呢

418
00:09:58,240 --> 00:09:58,880
跑一下

419
00:09:58,880 --> 00:10:00,560
呃

420
00:10:00,560 --> 00:10:01,220
珠峰

421
00:10:01,220 --> 00:10:01,940
pack

422
00:10:01,940 --> 00:10:03,600
完了告诉我了

423
00:10:03,600 --> 00:10:05,460
这东西啊有东西是安定范是吧

424
00:10:05,460 --> 00:10:06,780
那咱来看看啊

425
00:10:06,780 --> 00:10:07,540
那有可能啊

426
00:10:07,540 --> 00:10:09,600
就是这里面的use论没有渠道

427
00:10:09,600 --> 00:10:10,760
我把这个论呢

428
00:10:10,760 --> 00:10:10,940
哦

429
00:10:10,940 --> 00:10:11,720
这里面写错了

430
00:10:11,720 --> 00:10:13,280
应该是useless

431
00:10:13,280 --> 00:10:14,860
因为它是数组

432
00:10:14,860 --> 00:10:15,780
它也是个数组

433
00:10:15,780 --> 00:10:17,900
它是个对象

434
00:10:17,900 --> 00:10:18,900
对象里的数组

435
00:10:18,900 --> 00:10:20,620
us写错了

436
00:10:20,620 --> 00:10:21,100
再来一次

437
00:10:21,100 --> 00:10:22,080
突然就看到了

438
00:10:22,080 --> 00:10:22,780
来一下

439
00:10:22,780 --> 00:10:25,420
他说包错了

440
00:10:25,420 --> 00:10:27,060
说1-5

441
00:10:27,060 --> 00:10:29,120
咱来看看哪的问题

442
00:10:29,120 --> 00:10:31,740
他这个错误还挺隐晦的

443
00:10:31,740 --> 00:10:32,380
1-5

444
00:10:32,380 --> 00:10:34,440
我们在这里面打印一下

445
00:10:34,440 --> 00:10:36,100
console log content

446
00:10:36,100 --> 00:10:38,680
执行一下

447
00:10:38,680 --> 00:10:40,020
来看看效果

448
00:10:40,020 --> 00:10:41,660
这时候我发现

449
00:10:41,660 --> 00:10:43,000
是可以拿到这样一个结果的

450
00:10:43,000 --> 00:10:44,420
可能这样一个原因

451
00:10:44,420 --> 00:10:45,700
所以我们拿到结果以后

452
00:10:45,700 --> 00:10:47,300
它并没有走第二次是吧

453
00:10:47,300 --> 00:10:49,480
那肯定是这里面有点问题

454
00:10:49,480 --> 00:10:51,780
我们需要把这个结果怎么放在上面去

455
00:10:51,780 --> 00:10:53,020
每次转化完以后

456
00:10:53,020 --> 00:10:54,120
第一次转化完

457
00:10:54,120 --> 00:10:55,480
它是不是打出来的CSS

458
00:10:55,480 --> 00:10:56,780
那我们应该怎么样

459
00:10:56,780 --> 00:10:58,440
是不是如果它大于零的话

460
00:10:58,440 --> 00:11:00,460
我需要再调下个loader接着执行

461
00:11:00,460 --> 00:11:03,940
所以这里面应该再去调这个normalloader

462
00:11:03,940 --> 00:11:05,900
OK来看看效果执行

463
00:11:05,900 --> 00:11:08,500
完了这里面

464
00:11:08,500 --> 00:11:10,060
现在没有包错了

465
00:11:10,060 --> 00:11:12,140
那说明这个功能应该是实现了

466
00:11:12,140 --> 00:11:12,940
再来看看代码

467
00:11:12,940 --> 00:11:15,220
完了回到我们的这个bundle里面

468
00:11:15,220 --> 00:11:16,040
看看

469
00:11:16,040 --> 00:11:18,220
这时候你发现是不是好像

470
00:11:18,220 --> 00:11:19,580
哦代码插进来了

471
00:11:19,580 --> 00:11:20,500
这是lice是吧

472
00:11:20,500 --> 00:11:21,020
这是代码

473
00:11:21,020 --> 00:11:22,060
那好看看效果

474
00:11:22,060 --> 00:11:23,220
把这文件打开

475
00:11:23,220 --> 00:11:28,860
你会发现这个文件并没有变红

476
00:11:28,860 --> 00:11:29,640
为什么呢

477
00:11:29,640 --> 00:11:30,380
看这包错了

478
00:11:30,380 --> 00:11:31,480
说这个有异常

479
00:11:31,480 --> 00:11:32,320
那咱点过去

480
00:11:32,320 --> 00:11:34,180
这里面你会发现个问题

481
00:11:34,180 --> 00:11:35,840
执行的时候哪异常

482
00:11:35,840 --> 00:11:36,540
说这有异常

483
00:11:36,540 --> 00:11:37,800
他说因为这里面

484
00:11:37,800 --> 00:11:38,820
你看我们写的是杠恩

485
00:11:38,820 --> 00:11:39,900
这个杠N呢

486
00:11:39,900 --> 00:11:40,960
他会认为这是个转移

487
00:11:40,960 --> 00:11:41,700
对吧

488
00:11:41,700 --> 00:11:43,060
那把这个N给转移了

489
00:11:43,060 --> 00:11:43,960
那肯定会有点问题

490
00:11:43,960 --> 00:11:45,120
就不会时间中换行了

491
00:11:45,120 --> 00:11:46,740
那这时候我也要怎么办呢

492
00:11:46,740 --> 00:11:48,260
我需要把这个东西变成什么呢

493
00:11:48,260 --> 00:11:49,660
是杠杠N就可以了

494
00:11:49,660 --> 00:11:50,780
再来试一下

495
00:11:50,780 --> 00:11:52,400
比如说我就把这loader

496
00:11:52,400 --> 00:11:54,020
loader在这里

497
00:11:54,020 --> 00:11:54,960
liceloader

498
00:11:54,960 --> 00:11:56,740
需要在这里面

499
00:11:56,740 --> 00:11:58,820
css等于css点什么

500
00:11:58,820 --> 00:11:59,360
replace

501
00:11:59,360 --> 00:12:00,000
替换一下

502
00:12:00,000 --> 00:12:01,620
比如说我把这个杠N

503
00:12:01,620 --> 00:12:02,840
用正责吧

504
00:12:02,840 --> 00:12:03,940
所有的杠N

505
00:12:03,940 --> 00:12:05,640
完了

506
00:12:05,640 --> 00:12:06,160
来个G

507
00:12:06,160 --> 00:12:07,400
替换成什么呢

508
00:12:07,400 --> 00:12:08,080
叫杠杠N

509
00:12:08,080 --> 00:12:10,020
这样应该就可以了

510
00:12:10,020 --> 00:12:12,040
我把这代码再跑一次

511
00:12:12,040 --> 00:12:13,600
走你

512
00:12:13,600 --> 00:12:15,860
你看这时候也没有包错

513
00:12:15,860 --> 00:12:17,280
看看这阴面变变红

514
00:12:17,280 --> 00:12:18,120
一刷新

515
00:12:18,120 --> 00:12:19,080
它是不是成了

516
00:12:19,080 --> 00:12:19,860
因为这时候

517
00:12:19,860 --> 00:12:20,840
这个杠恩什么意思

518
00:12:20,840 --> 00:12:22,360
是不是一个杠是代表转易

519
00:12:22,360 --> 00:12:23,940
我两个杠的意思就是杠恩

520
00:12:23,940 --> 00:12:24,660
是不是代表换行

521
00:12:24,660 --> 00:12:26,820
那这时候我们就实现了这样一个功能

522
00:12:26,820 --> 00:12:28,360
这个就是我们当前

523
00:12:28,360 --> 00:12:30,420
在我们自己写的解义webpack中

524
00:12:30,420 --> 00:12:33,280
加了这样一个手写loader的功能

