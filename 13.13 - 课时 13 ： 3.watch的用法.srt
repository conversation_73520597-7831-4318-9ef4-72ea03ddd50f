1
00:00:00,000 --> 00:00:01,800
我们接着来说

2
00:00:01,800 --> 00:00:04,900
来说一下这个webpack中怎么能实时打包

3
00:00:04,900 --> 00:00:06,220
比如说我们在这里

4
00:00:06,220 --> 00:00:10,200
我们并不希望每次启动的时候都通过这样一个NPM run build

5
00:00:10,200 --> 00:00:12,080
完了每次一更新代码都需要怎么样

6
00:00:12,080 --> 00:00:12,860
重新打包

7
00:00:12,860 --> 00:00:15,300
他说那你可以用这个webpack DV server

8
00:00:15,300 --> 00:00:17,780
但是这个webpack DV server它有特点

9
00:00:17,780 --> 00:00:19,420
就是我们不能马上的怎么样

10
00:00:19,420 --> 00:00:21,420
看到我们这样一个实体文件

11
00:00:21,420 --> 00:00:22,760
那我们希望怎么样

12
00:00:22,760 --> 00:00:23,820
是不是改完代码以后

13
00:00:23,820 --> 00:00:25,520
结果重新打包出实体文件来

14
00:00:25,520 --> 00:00:27,180
那这时候可以怎么做呢

15
00:00:27,180 --> 00:00:28,140
也非常简单

16
00:00:28,140 --> 00:00:30,280
我把配置文件依旧保存一份

17
00:00:30,280 --> 00:00:31,940
这是第二个

18
00:00:31,940 --> 00:00:32,740
关掉

19
00:00:32,740 --> 00:00:34,280
这里我把它打开

20
00:00:34,280 --> 00:00:36,780
完了我把sauce map先干掉

21
00:00:36,780 --> 00:00:37,640
因为用的很多

22
00:00:37,640 --> 00:00:41,360
这时候我们在这里把它就删掉

23
00:00:41,360 --> 00:00:42,620
这很多删掉

24
00:00:42,620 --> 00:00:44,160
我们在这里配一个

25
00:00:44,160 --> 00:00:45,540
配个什么叫watch

26
00:00:45,540 --> 00:00:46,520
watch什么意思

27
00:00:46,520 --> 00:00:47,020
就是监控

28
00:00:47,020 --> 00:00:48,520
我可以怎么样

29
00:00:48,520 --> 00:00:50,740
监控当前代码的变化

30
00:00:50,740 --> 00:00:51,940
代码一变化怎么样

31
00:00:51,940 --> 00:00:53,000
就帮我实时打包

32
00:00:53,000 --> 00:00:53,920
什么意思

33
00:00:53,920 --> 00:00:55,100
比如说我在这里

34
00:00:55,100 --> 00:00:55,980
我在run build

35
00:00:55,980 --> 00:00:57,660
npm run build

36
00:00:57,660 --> 00:01:00,740
如果这个握持处啊

37
00:01:00,740 --> 00:01:01,260
它就会怎么样

38
00:01:01,260 --> 00:01:03,020
在这等着就不会打断

39
00:01:03,020 --> 00:01:05,320
你看说正在监控webpack这个文件

40
00:01:05,320 --> 00:01:07,200
那比如说在这来一句代码

41
00:01:07,200 --> 00:01:07,500
是吧

42
00:01:07,500 --> 00:01:08,380
比如给一个

43
00:01:08,380 --> 00:01:09,240
这叫home是吧

44
00:01:09,240 --> 00:01:09,920
我来个home1

45
00:01:09,920 --> 00:01:11,700
我log改成正确的

46
00:01:11,700 --> 00:01:12,620
那这时候呢

47
00:01:12,620 --> 00:01:13,120
我一刷新

48
00:01:13,120 --> 00:01:15,940
你看是不是出来的就是正常的

49
00:01:15,940 --> 00:01:17,940
相当于我只要改代码怎么样

50
00:01:17,940 --> 00:01:19,600
它就会实时编译

51
00:01:19,600 --> 00:01:20,820
而且呀

52
00:01:20,820 --> 00:01:21,740
这个watch呢

53
00:01:21,740 --> 00:01:22,900
也有自己的一些参数

54
00:01:22,900 --> 00:01:23,860
比如说在这里

55
00:01:23,860 --> 00:01:25,300
可以加一个watch的选项

56
00:01:25,300 --> 00:01:25,920
叫options

57
00:01:25,920 --> 00:01:27,240
这个选项啥意思呢

58
00:01:27,240 --> 00:01:28,840
就是监控的选项是吧

59
00:01:28,840 --> 00:01:29,840
监控的选项

60
00:01:29,840 --> 00:01:32,420
监控的

61
00:01:32,420 --> 00:01:34,480
有输入法的问题

62
00:01:34,480 --> 00:01:36,160
监控的选项

63
00:01:36,160 --> 00:01:39,040
而这里我可以给个属性

64
00:01:39,040 --> 00:01:40,360
比如说我希望怎么样

65
00:01:40,360 --> 00:01:42,880
哎他可以怎么样多少秒对吧

66
00:01:42,880 --> 00:01:45,280
监控一次比如说1000这啥意思

67
00:01:45,280 --> 00:01:46,480
就是每秒对吧

68
00:01:46,480 --> 00:01:49,240
每秒问我问我对吧

69
00:01:49,240 --> 00:01:51,040
1000次问问他怎么样

70
00:01:51,040 --> 00:01:52,760
哎我需要更新吗需要更新吗

71
00:01:52,760 --> 00:01:54,760
那当然了这个值你改的越小

72
00:01:54,760 --> 00:01:56,200
那精度怎么样就越低

73
00:01:56,440 --> 00:01:57,280
比如一秒问一次

74
00:01:57,280 --> 00:01:57,840
那可能怎么样

75
00:01:57,840 --> 00:01:58,000
是不是

76
00:01:58,000 --> 00:01:59,020
频率就低了

77
00:01:59,020 --> 00:02:00,240
但是也不能给他很高

78
00:02:00,240 --> 00:02:01,380
你要给他很高的话

79
00:02:01,380 --> 00:02:02,820
你想想一秒问一万次

80
00:02:02,820 --> 00:02:04,000
你不嫌烦吗

81
00:02:04,000 --> 00:02:04,200
是不是

82
00:02:04,200 --> 00:02:05,080
那肯定这样

83
00:02:05,080 --> 00:02:05,360
对吧

84
00:02:05,360 --> 00:02:06,180
那这个值啊

85
00:02:06,180 --> 00:02:07,480
最合理的应该就是1000

86
00:02:07,480 --> 00:02:09,620
当然你也可以根据你自己的需求啊

87
00:02:09,620 --> 00:02:10,060
来更改

88
00:02:10,060 --> 00:02:11,100
网络里面呢

89
00:02:11,100 --> 00:02:11,960
还有一个属性叫什么

90
00:02:11,960 --> 00:02:13,020
叫agreat

91
00:02:13,020 --> 00:02:17,380
这样一个属性

92
00:02:17,380 --> 00:02:18,140
greement是吧

93
00:02:18,140 --> 00:02:18,860
你看这样一个学性

94
00:02:18,860 --> 00:02:19,940
这什么意思呢

95
00:02:19,940 --> 00:02:20,660
就是他呀

96
00:02:20,660 --> 00:02:22,020
其实就是一个防抖的作用

97
00:02:22,020 --> 00:02:22,260
对吧

98
00:02:22,260 --> 00:02:23,220
这里又提到个词

99
00:02:23,220 --> 00:02:23,780
叫防抖

100
00:02:23,780 --> 00:02:25,820
防抖

101
00:02:25,820 --> 00:02:26,980
什么叫防抖

102
00:02:26,980 --> 00:02:30,860
比如说我一直输入袋

103
00:02:30,860 --> 00:02:32,920
这时候你想想

104
00:02:32,920 --> 00:02:34,120
我不能我写个字母

105
00:02:34,120 --> 00:02:35,000
你就给我打包一次

106
00:02:35,000 --> 00:02:36,480
性能也很低

107
00:02:36,480 --> 00:02:38,020
这时候我给个500

108
00:02:38,020 --> 00:02:39,000
什么意思

109
00:02:39,000 --> 00:02:41,080
我就希望500毫秒内

110
00:02:41,080 --> 00:02:42,100
我输的东西怎么样

111
00:02:42,100 --> 00:02:42,980
只打包一次

112
00:02:42,980 --> 00:02:44,060
这就是防抖

113
00:02:44,060 --> 00:02:47,060
比如说当我时间一直出发

114
00:02:47,060 --> 00:02:48,980
什么时候出发完了以后怎么样

115
00:02:48,980 --> 00:02:51,080
比如说我这输框一样

116
00:02:51,080 --> 00:02:52,180
我输框往里写字

117
00:02:52,180 --> 00:02:52,980
123123

118
00:02:52,980 --> 00:02:53,840
我一直输

119
00:02:53,840 --> 00:02:54,540
那就不打包

120
00:02:54,540 --> 00:02:56,200
等我停了以后怎么样

121
00:02:56,200 --> 00:02:57,300
过了500毫秒

122
00:02:57,300 --> 00:02:58,120
完了开始怎么样

123
00:02:58,120 --> 00:02:58,600
打爆

124
00:02:58,600 --> 00:03:00,040
这样一个操作

125
00:03:00,040 --> 00:03:01,420
好了

126
00:03:01,420 --> 00:03:02,780
我把这两个参数配上

127
00:03:02,780 --> 00:03:03,540
当然了

128
00:03:03,540 --> 00:03:05,780
有一个文件加上是不需要监控的

129
00:03:05,780 --> 00:03:06,520
就非常重要

130
00:03:06,520 --> 00:03:07,280
我们要忽略它

131
00:03:07,280 --> 00:03:09,260
比如说你不能说做的文件都监控

132
00:03:09,260 --> 00:03:10,380
这里面还有个属性叫

133
00:03:10,380 --> 00:03:10,800
一个node

134
00:03:10,800 --> 00:03:12,940
它可以忽略我们的什么东西

135
00:03:12,940 --> 00:03:14,300
叫nodemodus

136
00:03:14,300 --> 00:03:16,860
就是不需要监控哪个东西

137
00:03:16,860 --> 00:03:23,140
不需要进行监控哪个文件

138
00:03:23,140 --> 00:03:24,500
这样的话

139
00:03:24,500 --> 00:03:26,100
我们就进行了一个配置

140
00:03:26,100 --> 00:03:27,080
哪个文件

141
00:03:27,080 --> 00:03:28,860
好了

142
00:03:28,860 --> 00:03:29,900
我们来试一试

143
00:03:29,900 --> 00:03:30,580
看看哦不OK

144
00:03:30,580 --> 00:03:32,180
把它运行一下

145
00:03:32,180 --> 00:03:33,700
这里重新跑一下

146
00:03:33,700 --> 00:03:34,700
npm run build

147
00:03:34,700 --> 00:03:37,300
这里也没能看到效果

148
00:03:37,300 --> 00:03:38,620
他告诉我有问题

149
00:03:38,620 --> 00:03:39,740
他说这个属性叫

150
00:03:39,740 --> 00:03:41,680
叫agreement timeout

151
00:03:41,680 --> 00:03:42,460
好

152
00:03:42,460 --> 00:03:43,620
这里面超时

153
00:03:43,620 --> 00:03:44,300
好

154
00:03:44,300 --> 00:03:46,480
这里我再编译

155
00:03:46,480 --> 00:03:47,240
完了

156
00:03:47,240 --> 00:03:48,640
我把代码进行放在这

157
00:03:48,640 --> 00:03:50,180
比如说再看看效果

158
00:03:50,180 --> 00:03:50,920
行不行

159
00:03:50,920 --> 00:03:52,380
现在已经停住了

160
00:03:52,380 --> 00:03:52,800
完了

161
00:03:52,800 --> 00:03:53,920
我来开始洗代码

162
00:03:53,920 --> 00:03:54,960
怎么写呢

163
00:03:54,960 --> 00:03:55,640
比如我在这写

164
00:03:55,640 --> 00:03:58,220
一保存一保存一保存

165
00:03:58,220 --> 00:04:01,240
是不是你看我停下之后的500毫秒怎么样

166
00:04:01,240 --> 00:04:01,880
他在更新

167
00:04:01,880 --> 00:04:04,280
比如说删删删删

168
00:04:04,280 --> 00:04:05,940
你看是停完以后更新一次

169
00:04:05,940 --> 00:04:07,920
这个意思就是什么

170
00:04:07,920 --> 00:04:08,660
就是我们那个防抖

171
00:04:08,660 --> 00:04:10,760
同样他怎么样会实时监控

172
00:04:10,760 --> 00:04:12,000
并且我们怎么样

173
00:04:12,000 --> 00:04:14,360
你看到结果肯定永远是最新的

174
00:04:14,360 --> 00:04:16,080
现在我们就知道了

175
00:04:16,080 --> 00:04:20,260
OK原来我们webpack可以实时去打包我们的文件

176
00:04:20,260 --> 00:04:22,960
如果我们想实时看到我们打包后的结果

177
00:04:22,960 --> 00:04:24,220
那我们就可以选用什么

178
00:04:24,220 --> 00:04:25,460
这个握持参数了

