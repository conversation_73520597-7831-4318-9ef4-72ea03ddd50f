1
00:00:00,000 --> 00:00:05,920
上一节呢我们实现了如何将我们的高级语法呢转换成我们的低级语法

2
00:00:05,920 --> 00:00:07,280
也就是我们的es5语法

3
00:00:07,280 --> 00:00:09,200
那接下来呢我们接着往下看

4
00:00:09,200 --> 00:00:11,440
上一节呢我们写了个class a

5
00:00:11,440 --> 00:00:12,140
这是一个类

6
00:00:12,140 --> 00:00:16,480
那同样呢我们可以在这个a.js里面也加个class叫class b

7
00:00:16,480 --> 00:00:19,020
那这时候呢我们来看一下效果啊

8
00:00:19,020 --> 00:00:20,080
那这里面多了个括号

9
00:00:20,080 --> 00:00:23,480
哎同样我们还是要打包npxwipac

10
00:00:23,480 --> 00:00:26,100
哎稍等一下

11
00:00:26,100 --> 00:00:28,860
那这时候啊他会有一个非常恶心的事啊

12
00:00:28,860 --> 00:00:29,980
我们来看看效果啊

13
00:00:29,980 --> 00:00:31,140
bill得出来的bundle

14
00:00:31,140 --> 00:00:32,020
往那里面呢

15
00:00:32,020 --> 00:00:33,240
我给大家看一下

16
00:00:33,240 --> 00:00:34,320
比如说这里面呢

17
00:00:34,320 --> 00:00:35,480
应该打包完成了

18
00:00:35,480 --> 00:00:37,080
我们先看一下这个A

19
00:00:37,080 --> 00:00:38,720
叫应该叫fn

20
00:00:38,720 --> 00:00:39,180
对吧

21
00:00:39,180 --> 00:00:41,820
你看这里面是不是有一个叫class call check

22
00:00:41,820 --> 00:00:42,640
那这里面呢

23
00:00:42,640 --> 00:00:45,040
我们是不是有这样一个A呀A的类

24
00:00:45,040 --> 00:00:45,880
那同样呢

25
00:00:45,880 --> 00:00:47,080
我们来看看B啊

26
00:00:47,080 --> 00:00:49,780
是不是也有这样一个class call check

27
00:00:49,780 --> 00:00:51,340
那相当于我们这个类呢

28
00:00:51,340 --> 00:00:52,640
是不是教验了两次

29
00:00:52,640 --> 00:00:54,220
这个方法应该是可以公用的吧

30
00:00:54,220 --> 00:00:55,140
他没有提出来

31
00:00:55,140 --> 00:00:56,820
那同样的这里还没完事

32
00:00:56,820 --> 00:00:57,640
比如说呀

33
00:00:57,640 --> 00:00:58,880
我们说了这里面啊

34
00:00:58,880 --> 00:00:59,840
我们还可以写一些

35
00:00:59,840 --> 00:01:01,900
给我们所谓的generator预法对吧

36
00:01:01,900 --> 00:01:02,740
什么generator

37
00:01:02,740 --> 00:01:04,680
是处理我们的一步流程是吧

38
00:01:04,680 --> 00:01:05,440
我们就来个gen

39
00:01:05,440 --> 00:01:07,700
我就为了说什么叫generator是吧

40
00:01:07,700 --> 00:01:09,880
其实它就是有个星函数前面是吧

41
00:01:09,880 --> 00:01:11,580
这里我们可以去调用

42
00:01:11,580 --> 00:01:13,040
比如说conflog

43
00:01:13,040 --> 00:01:15,040
我们打印一下gen执行

44
00:01:15,040 --> 00:01:19,040
完了执行后我们去调一下nest预法

45
00:01:19,040 --> 00:01:20,900
好了

46
00:01:20,900 --> 00:01:22,600
我们在这里就随便产出点东西

47
00:01:22,600 --> 00:01:23,380
dl的1

48
00:01:23,380 --> 00:01:26,680
现在我们再来打包

49
00:01:26,680 --> 00:01:29,200
npx webpack

50
00:01:29,680 --> 00:01:31,120
那这里呢我们同样啊

51
00:01:31,120 --> 00:01:32,660
把一天猫打开过来运行一下

52
00:01:32,660 --> 00:01:33,740
那这时候呢

53
00:01:33,740 --> 00:01:34,340
他会告诉我啊

54
00:01:34,340 --> 00:01:35,140
很奇葩的一件事

55
00:01:35,140 --> 00:01:37,860
我们不是已经把es6转成es5了吗

56
00:01:37,860 --> 00:01:39,080
那这个语法呀

57
00:01:39,080 --> 00:01:39,740
其实是什么呢

58
00:01:39,740 --> 00:01:41,320
是他内置的一个api

59
00:01:41,320 --> 00:01:43,180
他并不会去转化这个api

60
00:01:43,180 --> 00:01:44,660
就算转化了以后啊

61
00:01:44,660 --> 00:01:46,840
他也不会给你内置一下这个转化的方法

62
00:01:46,840 --> 00:01:48,260
这里面其实他并没有

63
00:01:48,260 --> 00:01:48,860
你看啊

64
00:01:48,860 --> 00:01:49,700
我在这里console

65
00:01:49,700 --> 00:01:52,680
是告诉我这东西包错了

66
00:01:52,680 --> 00:01:53,920
可以看一下啊

67
00:01:53,920 --> 00:01:55,100
那这个方法哪来的呢

68
00:01:55,100 --> 00:01:56,400
其实就是打包完以后啊

69
00:01:56,400 --> 00:01:58,140
他把我们generator的进行转化了

70
00:01:58,140 --> 00:01:59,340
但转化的时候啊

71
00:01:59,340 --> 00:02:00,940
并没有加上这样一个帮助

72
00:02:00,940 --> 00:02:02,400
没有帮帮助这条代码

73
00:02:02,400 --> 00:02:04,200
所以说语法就会包错了

74
00:02:04,200 --> 00:02:06,100
同样比如说你这里面用一些

75
00:02:06,100 --> 00:02:07,380
有更高级的像promise

76
00:02:07,380 --> 00:02:08,340
他也不会转化

77
00:02:08,340 --> 00:02:10,000
这时候我们要怎么做

78
00:02:10,000 --> 00:02:11,840
这时候我们就需要一个包了

79
00:02:11,840 --> 00:02:13,440
这个包可以帮我们看这件事

80
00:02:13,440 --> 00:02:16,240
他叫什么叫我们代码运行时的一个包

81
00:02:16,240 --> 00:02:18,440
这里我就用babel给大家看一下

82
00:02:18,440 --> 00:02:21,040
babel好像挂了是吧

83
00:02:21,040 --> 00:02:21,940
我再看一下网

84
00:02:21,940 --> 00:02:23,440
没有OK

85
00:02:23,440 --> 00:02:26,100
这里面就直接找一下叫@babel.com

86
00:02:26,100 --> 00:02:27,780
我们他也是一个plugin

87
00:02:27,780 --> 00:02:29,580
他叫这个你看这些出来了

88
00:02:29,580 --> 00:02:31,540
这个这个包呢经常被用到

89
00:02:31,540 --> 00:02:34,040
只要配置我们的ES6转ES5

90
00:02:34,040 --> 00:02:36,540
或者是转更高级的语法都会用到这个包

91
00:02:36,540 --> 00:02:37,820
他的作用是什么呢

92
00:02:37,820 --> 00:02:39,180
他说了这个里面啊

93
00:02:39,180 --> 00:02:40,720
他不能工作一种代码

94
00:02:40,720 --> 00:02:41,420
就这个语法

95
00:02:41,420 --> 00:02:43,880
这个语法一看是我们的ES7语法

96
00:02:43,880 --> 00:02:44,540
但是呢

97
00:02:44,540 --> 00:02:45,980
他是实力上的方法

98
00:02:45,980 --> 00:02:47,340
所以他并不能实现

99
00:02:47,340 --> 00:02:47,980
那这时候呢

100
00:02:47,980 --> 00:02:48,680
他还告诉我了

101
00:02:48,680 --> 00:02:51,020
你还可以用一个其他的包来解决这件事

102
00:02:51,020 --> 00:02:52,180
那好了

103
00:02:52,180 --> 00:02:53,220
那这里面我们讲下看

104
00:02:53,220 --> 00:02:54,920
那我们可以去安装这个包

105
00:02:54,920 --> 00:02:56,480
这个包呢可以帮我们看这件事

106
00:02:56,480 --> 00:02:56,920
那好了

107
00:02:56,920 --> 00:02:58,060
就安把是不是

108
00:02:58,060 --> 00:02:58,880
压压压的

109
00:02:58,880 --> 00:03:00,060
同样是个插件

110
00:03:00,060 --> 00:03:00,760
钢地

111
00:03:00,760 --> 00:03:03,140
他说了这个东西是一个开发以来

112
00:03:03,140 --> 00:03:04,100
但同样

113
00:03:04,100 --> 00:03:05,640
他会往你的生产

114
00:03:05,640 --> 00:03:06,620
就是输出的代码

115
00:03:06,620 --> 00:03:07,560
注一些脚本

116
00:03:07,560 --> 00:03:09,120
这时候他还需要一个叫

117
00:03:09,120 --> 00:03:10,320
atbibble runtime

118
00:03:10,320 --> 00:03:12,460
他在我们上线的时候也需要

119
00:03:12,460 --> 00:03:14,460
因为帮我们打出这样一个补丁以后

120
00:03:14,460 --> 00:03:15,280
是不是上线的时候

121
00:03:15,280 --> 00:03:16,240
也需要带着这补丁

122
00:03:16,240 --> 00:03:18,540
所以说这里面他告诉我了

123
00:03:18,540 --> 00:03:19,680
这需要加一个刚刚c

124
00:03:19,680 --> 00:03:20,200
是吧

125
00:03:20,200 --> 00:03:21,400
好了压压的

126
00:03:21,400 --> 00:03:24,280
这里面直接就什么都不加就OK了

127
00:03:24,280 --> 00:03:25,320
配合完以后

128
00:03:25,320 --> 00:03:28,360
我们就需要把这东西怎么样配到我们这样一个预法里

129
00:03:28,360 --> 00:03:29,080
你看那是写的

130
00:03:29,080 --> 00:03:29,640
是吧

131
00:03:29,640 --> 00:03:29,880
哎

132
00:03:29,880 --> 00:03:32,040
需要把这东西放到我们的插件里去是吧

133
00:03:32,040 --> 00:03:32,920
那好

134
00:03:32,920 --> 00:03:35,240
找到我们的webpack page

135
00:03:35,240 --> 00:03:35,960
点开

136
00:03:35,960 --> 00:03:39,400
完了这里我们可以随心所欲的再加一个插件就ok了

137
00:03:39,400 --> 00:03:40,200
这里

138
00:03:40,200 --> 00:03:42,440
同样我们再去打包是吧

139
00:03:42,440 --> 00:03:43,880
npx webpack

140
00:03:43,880 --> 00:03:47,440
ok再来一次

141
00:03:47,440 --> 00:03:49,720
xnpx webpack

142
00:03:49,720 --> 00:03:55,280
稍微等一等

143
00:03:55,280 --> 00:03:56,280
看看效果

144
00:03:56,280 --> 00:03:58,080
有点慢

145
00:03:58,080 --> 00:04:00,120
这里面报了一些警告

146
00:04:00,120 --> 00:04:02,120
这个警告人家说了是他跑哪去了

147
00:04:02,120 --> 00:04:03,880
是不是去node的modus下去找了

148
00:04:03,880 --> 00:04:05,320
这个原因是这样的

149
00:04:05,320 --> 00:04:08,040
就是默认情况下我们匹配了所有的gs

150
00:04:08,040 --> 00:04:09,680
这里面一定要再加一句对吧

151
00:04:09,680 --> 00:04:12,520
比如说我匹配gs其实只要src下的

152
00:04:12,520 --> 00:04:14,760
这里面我可以给他加一个incluse

153
00:04:14,760 --> 00:04:16,000
咱说过是吧

154
00:04:16,000 --> 00:04:17,000
你要是不加的话

155
00:04:17,000 --> 00:04:18,320
他可能默认会怎么样

156
00:04:18,320 --> 00:04:19,720
全部找这个gs

157
00:04:19,720 --> 00:04:21,440
我就需要排除掉谁

158
00:04:21,440 --> 00:04:24,880
我排除掉node的modus好了是吧

159
00:04:24,880 --> 00:04:26,980
那同样的其实对应的还有个叫inclue的

160
00:04:26,980 --> 00:04:27,520
就是哎

161
00:04:27,520 --> 00:04:28,880
你想找哪个里面

162
00:04:28,880 --> 00:04:29,880
你这里面可以给一个

163
00:04:29,880 --> 00:04:31,080
比如pass.result

164
00:04:31,080 --> 00:04:34,080
我就希望找DR内容下的src

165
00:04:34,080 --> 00:04:35,180
一个排除

166
00:04:35,180 --> 00:04:35,680
一个怎么样

167
00:04:35,680 --> 00:04:37,780
一个叫包括只要这些东西

168
00:04:37,780 --> 00:04:38,540
那好了

169
00:04:38,540 --> 00:04:39,880
那这里面应该就

170
00:04:39,880 --> 00:04:42,180
偷偷的应该不会出现警告了

171
00:04:42,180 --> 00:04:44,180
然后这个文件也很大

172
00:04:44,180 --> 00:04:44,780
稍等

173
00:04:44,780 --> 00:04:46,280
打包出来了

174
00:04:46,280 --> 00:04:48,080
你看这这回就没有警告了是吧

175
00:04:48,080 --> 00:04:50,880
当然了这个gs的大小也会增大一些

176
00:04:50,880 --> 00:04:52,880
因为他会帮我们去怎么样

177
00:04:52,880 --> 00:04:54,680
看看结果帮走

178
00:04:54,680 --> 00:04:56,580
这里面是不是应该会把我干一下

179
00:04:56,580 --> 00:04:57,120
叫什么

180
00:04:57,120 --> 00:04:58,660
你看是不是有一个圆拐特的语法

181
00:04:58,660 --> 00:04:59,200
一个包了

182
00:04:59,200 --> 00:05:00,700
是不是在babel runtime里面

183
00:05:00,700 --> 00:05:03,260
而且他把所有的class call check

184
00:05:03,260 --> 00:05:04,760
应该都会抽离出去

185
00:05:04,760 --> 00:05:07,340
你看看这里面都是人家写好的

186
00:05:07,340 --> 00:05:08,480
我这代码在最下面

187
00:05:08,480 --> 00:05:09,880
你看看我引的时候

188
00:05:09,880 --> 00:05:11,900
是不是引的什么class call check

189
00:05:11,900 --> 00:05:14,420
都是通过人家babel runtime helper里面

190
00:05:14,420 --> 00:05:15,500
这个方法了

191
00:05:15,500 --> 00:05:16,700
那也相当于怎么样

192
00:05:16,700 --> 00:05:18,640
是不是把公共的地方进行抽离了

193
00:05:18,640 --> 00:05:19,760
就这个文档上有说

194
00:05:19,760 --> 00:05:22,500
说他已经帮我们去做了一些简单的优化

195
00:05:22,500 --> 00:05:23,820
这是好处

196
00:05:23,820 --> 00:05:26,320
现在好了我们知道了怎么去转化

197
00:05:26,320 --> 00:05:27,620
但是刚才也看到了

198
00:05:27,620 --> 00:05:28,420
他也提示我了

199
00:05:28,420 --> 00:05:30,600
说我们如果使用个更高级的语法

200
00:05:30,600 --> 00:05:31,380
他是不理你的

201
00:05:31,380 --> 00:05:32,300
比如来一个

202
00:05:32,300 --> 00:05:34,120
是不是叫AA.include

203
00:05:34,120 --> 00:05:35,920
看看他有没有AA

204
00:05:35,920 --> 00:05:37,840
include

205
00:05:37,840 --> 00:05:39,880
比如说判断一下没有A

206
00:05:39,880 --> 00:05:41,900
我们再来打包一下

207
00:05:41,900 --> 00:05:43,480
npx ypack

208
00:05:43,480 --> 00:05:46,900
这样的话可能也会有一些问题

209
00:05:46,900 --> 00:05:48,740
他也会告诉我这东西有问题

210
00:05:48,740 --> 00:05:50,600
你看打包出来了

211
00:05:50,600 --> 00:05:51,560
我们来看看

212
00:05:51,560 --> 00:05:52,660
include语法

213
00:05:52,660 --> 00:05:53,680
我说他是1s7的

214
00:05:53,680 --> 00:05:54,640
是吧他没有转化

215
00:05:54,640 --> 00:05:56,780
你看这有个英克路的方法哦

216
00:05:56,780 --> 00:05:58,240
可路子啊我拼错了

217
00:05:58,240 --> 00:06:00,340
那这里面应该叫英克路的是吧

218
00:06:00,340 --> 00:06:01,340
英克路子

219
00:06:01,340 --> 00:06:03,180
这里呢我再打包

220
00:06:03,180 --> 00:06:05,840
我说了实力上的方法啊

221
00:06:05,840 --> 00:06:06,640
他默认怎么样

222
00:06:06,640 --> 00:06:07,880
他都不会帮你去解析

223
00:06:07,880 --> 00:06:09,280
那你看看是不是这样啊

224
00:06:09,280 --> 00:06:11,080
叫英克路子你看英克路子吧

225
00:06:11,080 --> 00:06:13,280
我找一下是全屏只有一个呀

226
00:06:13,280 --> 00:06:14,440
那这时候人家说了

227
00:06:14,440 --> 00:06:16,840
你可以用一个模块来解决这件事

228
00:06:16,840 --> 00:06:18,380
这个模块叫艾特白布

229
00:06:18,380 --> 00:06:19,840
搞什么搞泡里发

230
00:06:19,840 --> 00:06:21,680
好里发人说了

231
00:06:21,680 --> 00:06:23,180
这就是一个补丁模块

232
00:06:23,180 --> 00:06:25,620
当然同样他需要引入到我们的代码里

233
00:06:25,620 --> 00:06:26,880
所以不能加力钢大地

234
00:06:26,880 --> 00:06:28,520
好回撤

235
00:06:28,520 --> 00:06:31,740
现在我们可以在我们的配置文件里

236
00:06:31,740 --> 00:06:33,780
比如说他里面需要这样一个方法

237
00:06:33,780 --> 00:06:34,180
好了

238
00:06:34,180 --> 00:06:35,420
我在这里面就一下吧

239
00:06:35,420 --> 00:06:37,780
就是require我们的爱特

240
00:06:37,780 --> 00:06:38,420
白宝

241
00:06:38,420 --> 00:06:40,620
杠我们的叫破利法是吧

242
00:06:40,620 --> 00:06:44,780
我没有爱他提示的时候我把它播错了

243
00:06:44,780 --> 00:06:45,020
是吧

244
00:06:45,020 --> 00:06:46,080
说写写错了

245
00:06:46,080 --> 00:06:48,780
这边应该是斜杠写的时候感觉不太对

246
00:06:48,780 --> 00:06:50,420
这里也一样是吧

247
00:06:50,420 --> 00:06:53,140
爱特白宝杠我们的破利法是吧

248
00:06:53,180 --> 00:06:54,740
HolyFile

249
00:06:54,740 --> 00:06:56,940
现在好了

250
00:06:56,940 --> 00:06:58,220
我们再来试一下

251
00:06:58,220 --> 00:06:59,060
看看这时候

252
00:06:59,060 --> 00:07:00,080
有不OK

253
00:07:00,080 --> 00:07:01,320
有点卡

254
00:07:01,320 --> 00:07:02,180
稍微等一下

255
00:07:02,180 --> 00:07:04,860
OK

256
00:07:04,860 --> 00:07:07,680
马上了

257
00:07:07,680 --> 00:07:09,920
好

258
00:07:09,920 --> 00:07:10,720
应该这就还好了

259
00:07:10,720 --> 00:07:11,460
这包还挺大的

260
00:07:11,460 --> 00:07:11,740
其实

261
00:07:11,740 --> 00:07:13,220
NPX

262
00:07:13,220 --> 00:07:14,020
Pack

263
00:07:14,020 --> 00:07:15,320
可以看到

264
00:07:15,320 --> 00:07:17,320
他会帮你去实现一些

265
00:07:17,320 --> 00:07:17,800
比如说

266
00:07:17,800 --> 00:07:19,500
他不能实现的预法

267
00:07:19,500 --> 00:07:20,700
比如说像Inclose

268
00:07:20,700 --> 00:07:21,780
他会在数组上怎么样

269
00:07:21,780 --> 00:07:22,800
给你自己重写

270
00:07:22,800 --> 00:07:24,660
这个看是不是这样啊

271
00:07:24,660 --> 00:07:26,600
我们可以看到bundle里面

272
00:07:26,600 --> 00:07:27,320
它里面呢

273
00:07:27,320 --> 00:07:28,680
有没有我们所谓的这个叫

274
00:07:28,680 --> 00:07:31,500
你看是不是在array的prototype上

275
00:07:31,500 --> 00:07:34,200
给你加了这样一个叫include的方法呢

276
00:07:34,200 --> 00:07:35,880
是不是有啊

277
00:07:35,880 --> 00:07:37,240
是不是自己去实现了一个呀

278
00:07:37,240 --> 00:07:38,180
那这样的话怎么样

279
00:07:38,180 --> 00:07:39,260
是不是我那个预法

280
00:07:39,260 --> 00:07:41,560
他就可以去打包成低级预法

281
00:07:41,560 --> 00:07:43,300
那这样的话

282
00:07:43,300 --> 00:07:44,020
我们就知道了

283
00:07:44,020 --> 00:07:46,080
怎么去去优化我们的gs

284
00:07:46,080 --> 00:07:47,520
包括一些高级预法

285
00:07:47,520 --> 00:07:48,260
怎么去转化

286
00:07:48,260 --> 00:07:50,540
那我们再来看一个吧

287
00:07:50,540 --> 00:07:51,260
就是怎么样

288
00:07:51,260 --> 00:07:52,780
去给我们的这个gs呢

289
00:07:52,780 --> 00:07:53,460
加一些教验

290
00:07:53,460 --> 00:07:54,380
我们都知道

291
00:07:54,380 --> 00:07:55,860
其实这个GS语法

292
00:07:55,860 --> 00:07:56,940
在我们写代码的时候

293
00:07:56,940 --> 00:07:58,300
都希望一个救援器

294
00:07:58,300 --> 00:07:59,860
去教验一下我这个代码

295
00:07:59,860 --> 00:08:00,820
服务和规范

296
00:08:00,820 --> 00:08:02,760
这个东西我们也很常见

297
00:08:02,760 --> 00:08:03,680
这东西叫什么

298
00:08:03,680 --> 00:08:04,780
叫ESLint

299
00:08:04,780 --> 00:08:07,380
他就可以帮我们去干这件事

300
00:08:07,380 --> 00:08:08,580
就是去帮我们教验

301
00:08:08,580 --> 00:08:09,200
当性代码

302
00:08:09,200 --> 00:08:10,240
是否服务和规范

303
00:08:10,240 --> 00:08:10,760
那好

304
00:08:10,760 --> 00:08:11,920
用法人家也写了

305
00:08:11,920 --> 00:08:13,080
可以getstart开始

306
00:08:13,080 --> 00:08:14,560
这里面其实有demo

307
00:08:14,560 --> 00:08:15,080
你可以看

308
00:08:15,080 --> 00:08:16,740
ESLint的一个demo

309
00:08:16,740 --> 00:08:17,600
完了人说了

310
00:08:17,600 --> 00:08:18,260
这玩意怎么用

311
00:08:18,260 --> 00:08:18,540
是吧

312
00:08:18,540 --> 00:08:20,360
他其实用的就是我们后面要讲的

313
00:08:20,360 --> 00:08:21,620
这个IT语法术解析的

314
00:08:21,620 --> 00:08:23,220
网上里面可以告诉你

315
00:08:23,220 --> 00:08:24,340
这东西可以怎么去配

316
00:08:24,340 --> 00:08:24,580
是吧

317
00:08:24,580 --> 00:08:25,800
有一个配置规则

318
00:08:25,800 --> 00:08:27,220
咱们来看一看

319
00:08:27,220 --> 00:08:28,420
这网上怎么用

320
00:08:28,420 --> 00:08:29,640
非常简单

321
00:08:29,640 --> 00:08:31,000
同样还是一样

322
00:08:31,000 --> 00:08:34,000
我们需要去加上这样一个配置文件

323
00:08:34,000 --> 00:08:34,640
跟他说

324
00:08:34,640 --> 00:08:36,680
我需要去教验一下我们的预法规范

325
00:08:36,680 --> 00:08:37,960
还是教验GS

326
00:08:37,960 --> 00:08:38,940
看到了吗

327
00:08:38,940 --> 00:08:41,420
我们的loader是可以写多个的

328
00:08:41,420 --> 00:08:42,040
这是一个

329
00:08:42,040 --> 00:08:44,720
同样后面你可以怎么办

330
00:08:44,720 --> 00:08:45,400
再来一个

331
00:08:45,400 --> 00:08:47,080
use对吧

332
00:08:47,080 --> 00:08:47,660
比如说loader

333
00:08:47,660 --> 00:08:49,180
这个loader就叫什么

334
00:08:49,180 --> 00:08:51,040
eslint-loader

335
00:08:51,040 --> 00:08:52,080
一听名字就知道

336
00:08:52,080 --> 00:08:52,660
它是干嘛的

337
00:08:52,660 --> 00:08:54,740
教验我们的ESLint规范

338
00:08:54,740 --> 00:08:58,340
同样它和我们什么LiceLoader非常像

339
00:08:58,340 --> 00:09:00,340
所以说安装的时候需要怎么样

340
00:09:00,340 --> 00:09:02,080
先安装ESLint

341
00:09:02,080 --> 00:09:05,880
完了再去安装我们的所谓的ESLintLoader

342
00:09:05,880 --> 00:09:07,380
同样的两包

343
00:09:07,380 --> 00:09:08,480
安装一下

344
00:09:08,480 --> 00:09:10,620
这里面没加高大地

345
00:09:10,620 --> 00:09:11,840
我就不去加了

346
00:09:11,840 --> 00:09:12,260
好

347
00:09:12,260 --> 00:09:14,100
这里面应该就安好了

348
00:09:14,100 --> 00:09:15,240
安好以后

349
00:09:15,240 --> 00:09:15,740
同样

350
00:09:15,740 --> 00:09:16,960
我们需要怎么样

351
00:09:16,960 --> 00:09:17,440
人说了

352
00:09:17,440 --> 00:09:19,300
你需要去勾一些你的

353
00:09:19,300 --> 00:09:20,840
比如说公司里面需要这个项目

354
00:09:20,840 --> 00:09:22,080
需要弄到什么项目对吧

355
00:09:22,080 --> 00:09:23,600
需要加上什么参数啊

356
00:09:23,600 --> 00:09:24,800
是什么语法呀

357
00:09:24,800 --> 00:09:25,840
是什么环境啊

358
00:09:25,840 --> 00:09:27,280
完了语法的版本是哪个呀

359
00:09:27,280 --> 00:09:28,840
是模块的还是script

360
00:09:28,840 --> 00:09:30,280
还是gss语法呀

361
00:09:30,280 --> 00:09:31,600
反正很多一些规则

362
00:09:31,600 --> 00:09:32,680
你可以自己去配

363
00:09:32,680 --> 00:09:33,720
这里我就不管了

364
00:09:33,720 --> 00:09:34,640
比如你选好了

365
00:09:34,640 --> 00:09:35,280
你怎么办呢

366
00:09:35,280 --> 00:09:38,360
人说了可以下载这样一个第二杰森文件

367
00:09:38,360 --> 00:09:39,360
完了放到哪去啊

368
00:09:39,360 --> 00:09:40,520
你的配置文件里去

369
00:09:40,520 --> 00:09:41,240
那好了

370
00:09:41,240 --> 00:09:42,200
那怎么看呢

371
00:09:42,200 --> 00:09:43,840
看下下好了没是吧

372
00:09:43,840 --> 00:09:44,760
现在已经下好了

373
00:09:44,760 --> 00:09:46,400
我给你来一下download的

374
00:09:46,400 --> 00:09:48,120
下载完以后呢

375
00:09:48,120 --> 00:09:49,400
把这文件你发现了吗

376
00:09:49,400 --> 00:09:52,040
他说了这个文件应该前面加点

377
00:09:52,040 --> 00:09:54,000
但是你这个没加点但是无所谓

378
00:09:54,000 --> 00:09:55,800
我先给他考进来是吧

379
00:09:55,800 --> 00:09:59,000
完了在目录下找到过灵器中

380
00:09:59,000 --> 00:10:01,900
完了找到我们上一集就放到根目录下

381
00:10:01,900 --> 00:10:04,000
哎同样完了在前面加个啥呀

382
00:10:04,000 --> 00:10:06,300
加个点人人说了把规范是吧

383
00:10:06,300 --> 00:10:06,900
ok

384
00:10:06,900 --> 00:10:09,400
那就是变扫的那说明怎么样

385
00:10:09,400 --> 00:10:10,100
成功了

386
00:10:10,100 --> 00:10:11,700
那这个配置啊就ok了

387
00:10:11,700 --> 00:10:14,300
那这时候我们写代码的时候怎么执行呢

388
00:10:14,300 --> 00:10:15,400
理论是这样的

389
00:10:15,400 --> 00:10:17,000
我们都知道刚才说过啊

390
00:10:17,000 --> 00:10:18,900
loader 默认对吧

391
00:10:18,900 --> 00:10:24,540
默认是从哪从从什么右向左右边对吧

392
00:10:24,540 --> 00:10:26,500
右边向左执行

393
00:10:26,500 --> 00:10:29,700
向左执行是吧

394
00:10:29,700 --> 00:10:30,600
那同样啊

395
00:10:30,600 --> 00:10:31,000
呃

396
00:10:31,000 --> 00:10:32,540
左左是这个左是吧

397
00:10:32,540 --> 00:10:34,200
执行那行样还有什么呢

398
00:10:34,200 --> 00:10:36,900
是不是向从下到上啊

399
00:10:36,900 --> 00:10:38,200
从下到上

400
00:10:38,200 --> 00:10:39,000
那你想啊

401
00:10:39,000 --> 00:10:41,740
我写代码的时候肯定要怎么去处理啊

402
00:10:41,740 --> 00:10:43,900
是不是先去校验我们的ds

403
00:10:43,900 --> 00:10:45,800
完了再去转换我们代码

404
00:10:45,800 --> 00:10:47,660
这时候应该把它放到下面去

405
00:10:47,660 --> 00:10:50,500
但是这样写很容易怎么样写乱了

406
00:10:50,500 --> 00:10:52,160
就不知道它是在下面在上面

407
00:10:52,160 --> 00:10:53,400
可以怎么办

408
00:10:53,400 --> 00:10:54,800
你可以怎么样写成个数度方式

409
00:10:54,800 --> 00:10:56,300
把它放到这里面来

410
00:10:56,300 --> 00:10:57,260
但是也很麻烦

411
00:10:57,260 --> 00:10:58,600
我还是希望配成两个

412
00:10:58,600 --> 00:10:59,800
万一eslint不要了

413
00:10:59,800 --> 00:11:01,500
我是不是直接把它咔嚓就搞定了

414
00:11:01,500 --> 00:11:02,500
这时候我怎么办

415
00:11:02,500 --> 00:11:04,200
可以非常方便来个啥

416
00:11:04,200 --> 00:11:06,900
来个这个东西叫什么叫inforce

417
00:11:06,900 --> 00:11:08,400
给大家看一下是吧

418
00:11:08,400 --> 00:11:11,300
比如说我们右字的时候可以加上一个属性

419
00:11:11,300 --> 00:11:14,100
叫inforce

420
00:11:14,500 --> 00:11:16,360
info的啥意思叫强制

421
00:11:16,360 --> 00:11:16,780
对吧

422
00:11:16,780 --> 00:11:17,840
你可以来个叫prey

423
00:11:17,840 --> 00:11:19,660
prey的意思就是preyworth

424
00:11:19,660 --> 00:11:20,440
preyworth

425
00:11:20,440 --> 00:11:24,260
就是强制让loader在他的之前执行

426
00:11:24,260 --> 00:11:26,160
如果loader我们给他起个名字叫什么

427
00:11:26,160 --> 00:11:27,040
叫normal

428
00:11:27,040 --> 00:11:29,740
它是一个普通的loader

429
00:11:29,740 --> 00:11:32,200
比如说直应顺应什么样是从下到上

430
00:11:32,200 --> 00:11:33,860
但是如果把它变成prey了

431
00:11:33,860 --> 00:11:34,340
那好

432
00:11:34,340 --> 00:11:36,900
他就会跑到他的什么前面来执行

433
00:11:36,900 --> 00:11:38,640
有prey肯定还有后面对吧

434
00:11:38,640 --> 00:11:40,240
后面再也会说这叫post

435
00:11:40,240 --> 00:11:42,000
就是叫postloader

436
00:11:42,000 --> 00:11:44,260
他会在我们的普通loader之后执行

437
00:11:44,500 --> 00:11:46,860
你看看这个文档也有给出来

438
00:11:46,860 --> 00:11:49,060
比如说我在这里去搜一下

439
00:11:49,060 --> 00:11:52,980
我叫他叫eslint是吧

440
00:11:52,980 --> 00:11:53,900
lint loader

441
00:11:53,900 --> 00:11:55,940
人家说了这东西需要怎么样

442
00:11:55,940 --> 00:11:59,180
加一个inforce在这搜一下inforce

443
00:11:59,180 --> 00:12:00,700
是不是有

444
00:12:00,700 --> 00:12:01,540
是不是匹配的时候

445
00:12:01,540 --> 00:12:03,460
我需要加上这样一个inforce属性

446
00:12:03,460 --> 00:12:04,420
保证他怎么样

447
00:12:04,420 --> 00:12:06,500
在我们之前来执行

448
00:12:06,500 --> 00:12:07,420
好了

449
00:12:07,420 --> 00:12:09,500
现在我们接着往下看是吧

450
00:12:09,500 --> 00:12:11,460
这里面没错是吧

451
00:12:11,460 --> 00:12:12,300
use loader

452
00:12:12,300 --> 00:12:14,380
inforce我在这执行一下

453
00:12:14,500 --> 00:12:15,860
npx workpack

454
00:12:15,860 --> 00:12:17,700
ok

455
00:12:17,700 --> 00:12:23,140
哦他又告诉我这东西写的位置不对是吧

456
00:12:23,140 --> 00:12:24,100
可能是哦

457
00:12:24,100 --> 00:12:25,140
这inforce应该放到哪去

458
00:12:25,140 --> 00:12:26,300
放到外面去啊

459
00:12:26,300 --> 00:12:29,300
匹配的时候告诉他他在他的前面是吧

460
00:12:29,300 --> 00:12:31,600
这时候一样啊再来一个

461
00:12:31,600 --> 00:12:35,740
哦还是不对啊哦

462
00:12:35,740 --> 00:12:37,360
这个应该是个选项属性是吧

463
00:12:37,360 --> 00:12:38,400
那应该放到这个

464
00:12:38,400 --> 00:12:39,540
options里是吧

465
00:12:39,540 --> 00:12:40,300
放到这

466
00:12:40,300 --> 00:12:41,560
这应该对了啊

467
00:12:41,560 --> 00:12:43,140
对loader对没错

468
00:12:43,440 --> 00:12:44,660
选项在前置楼的

469
00:12:44,660 --> 00:12:44,980
是吧

470
00:12:44,980 --> 00:12:45,820
OK

471
00:12:45,820 --> 00:12:49,380
这时候他就给我打包了

472
00:12:49,380 --> 00:12:50,460
但是打包的时候

473
00:12:50,460 --> 00:12:51,980
他会教验我这预法过没过

474
00:12:51,980 --> 00:12:53,000
也说了我这预法

475
00:12:53,000 --> 00:12:54,100
哎呦写的太烂了

476
00:12:54,100 --> 00:12:54,360
是吧

477
00:12:54,360 --> 00:12:55,420
各种不识别

478
00:12:55,420 --> 00:12:56,740
什么require没定义

479
00:12:56,740 --> 00:12:57,300
对吧

480
00:12:57,300 --> 00:12:57,980
浪漫洗澡的

481
00:12:57,980 --> 00:12:58,160
是不是

482
00:12:58,160 --> 00:12:58,920
你可以怎么样

483
00:12:58,920 --> 00:13:00,420
自行去修改这些bug

484
00:13:00,420 --> 00:13:02,180
比如说你可以随便改改

485
00:13:02,180 --> 00:13:02,900
比如像这个

486
00:13:02,900 --> 00:13:03,900
说module没定义

487
00:13:03,900 --> 00:13:04,620
require没定义

488
00:13:04,620 --> 00:13:05,140
当然了

489
00:13:05,140 --> 00:13:06,480
这B什么定义了没用

490
00:13:06,480 --> 00:13:07,340
人说的很明确

491
00:13:07,340 --> 00:13:07,620
是吧

492
00:13:07,620 --> 00:13:08,620
参数没定义

493
00:13:08,620 --> 00:13:09,300
怎么着的是吧

494
00:13:09,300 --> 00:13:09,780
那好了

495
00:13:09,780 --> 00:13:11,560
这我就不去解决这个问题了

496
00:13:11,560 --> 00:13:14,840
那现在我们就可以实现一个什么代码的交验了

497
00:13:14,840 --> 00:13:15,340
那好

498
00:13:15,340 --> 00:13:15,860
那这里呢

499
00:13:15,860 --> 00:13:16,700
我就先把它关掉

500
00:13:16,700 --> 00:13:18,580
也在写代码的时候肯定是测试

501
00:13:18,580 --> 00:13:20,760
不可能一个个去改这些bug是吧

502
00:13:20,760 --> 00:13:21,460
那好了

503
00:13:21,460 --> 00:13:22,680
那这时候应该就回去了

504
00:13:22,680 --> 00:13:23,500
那好

505
00:13:23,500 --> 00:13:26,120
那我们就知道了这个gs是怎么处理的了

