1
00:00:00,000 --> 00:00:04,200
本节我们就来讲一下webpack中的Typeable

2
00:00:04,200 --> 00:00:07,100
webpack本质上是一种事件流的机制

3
00:00:07,100 --> 00:00:11,080
它的工作流程就是将各个插件的串练起来

4
00:00:11,080 --> 00:00:12,920
这么多插件我们怎么来维护呢

5
00:00:12,920 --> 00:00:15,180
主要靠的就是我们的webpack中的Typeable

6
00:00:15,180 --> 00:00:18,560
这Typeable其实它也是依赖于我们一种事件的机制

7
00:00:18,560 --> 00:00:21,160
其实很像我们这个Node中的Evance Cool

8
00:00:21,160 --> 00:00:24,900
它里面其实主要包含了我们的发布内容模式

9
00:00:24,900 --> 00:00:26,180
我们可以来看一下

10
00:00:26,180 --> 00:00:29,580
这个webpack里面集成了很多这样一个Typeable插件

11
00:00:29,580 --> 00:00:30,600
这个插件里面呢

12
00:00:30,600 --> 00:00:31,520
有很多方法

13
00:00:31,520 --> 00:00:33,420
比如包括它的同步的和异步的

14
00:00:33,420 --> 00:00:34,560
我们可以简单看一下

15
00:00:34,560 --> 00:00:35,420
这个webpack的缘满

16
00:00:35,420 --> 00:00:36,520
这里呢

17
00:00:36,520 --> 00:00:38,320
我们下载了一个webpack

18
00:00:38,320 --> 00:00:40,060
我们点看一下

19
00:00:40,060 --> 00:00:40,940
这里呢

20
00:00:40,940 --> 00:00:42,720
就包含着我们这样一个webpack

21
00:00:42,720 --> 00:00:43,540
我找一下

22
00:00:43,540 --> 00:00:45,360
webpack

23
00:00:45,360 --> 00:00:48,800
我点开以后呢

24
00:00:48,800 --> 00:00:49,700
我们可以看到啊

25
00:00:49,700 --> 00:00:50,320
往来这里呢

26
00:00:50,320 --> 00:00:52,140
我们可以点开我们的这个lab库

27
00:00:52,140 --> 00:00:52,980
往里面呢

28
00:00:52,980 --> 00:00:53,860
有个变异模块

29
00:00:53,860 --> 00:00:54,420
是吧

30
00:00:54,420 --> 00:00:55,620
这是它的组合性模块

31
00:00:55,620 --> 00:00:56,540
这里面啊

32
00:00:56,540 --> 00:00:58,060
它主要靠的就是我们的Typeable

33
00:00:58,060 --> 00:00:59,020
往里面呢

34
00:00:59,020 --> 00:01:00,920
引用了我们所谓的这个一些钩子

35
00:01:00,920 --> 00:01:01,180
对吧

36
00:01:01,180 --> 00:01:01,840
叫Sync Hook

37
00:01:01,840 --> 00:01:03,480
还有我们的这些Earthink Hook

38
00:01:03,480 --> 00:01:05,660
就说我们这个插件啊

39
00:01:05,660 --> 00:01:07,700
里面包含着一部方法和同步方法

40
00:01:07,700 --> 00:01:08,640
那这时候呢

41
00:01:08,640 --> 00:01:09,480
我们就要来区分开

42
00:01:09,480 --> 00:01:10,300
比如同步呢

43
00:01:10,300 --> 00:01:11,720
使用同步钩子来串联起来

44
00:01:11,720 --> 00:01:12,360
一部呢

45
00:01:12,360 --> 00:01:13,440
我们使用一部的钩子呢

46
00:01:13,440 --> 00:01:14,200
串联起来

47
00:01:14,200 --> 00:01:14,880
那这里呢

48
00:01:14,880 --> 00:01:15,780
我们就来看一下吧

49
00:01:15,780 --> 00:01:16,900
咱们呢

50
00:01:16,900 --> 00:01:17,880
就围照这些方法

51
00:01:17,880 --> 00:01:18,440
我们呢

52
00:01:18,440 --> 00:01:19,360
依次来应用一下

53
00:01:19,360 --> 00:01:20,580
并且实现它们的原理

54
00:01:20,580 --> 00:01:21,400
这样的话呢

55
00:01:21,400 --> 00:01:23,620
我们为后面的这个WipePack手写做准备

56
00:01:23,620 --> 00:01:25,280
那我们就来看一下

57
00:01:25,280 --> 00:01:26,980
先去安装一下这个包

58
00:01:26,980 --> 00:01:28,020
它是一个计算模块

59
00:01:28,020 --> 00:01:30,280
那这里呢我们就直接安装

60
00:01:30,280 --> 00:01:32,460
进到模块里

61
00:01:32,460 --> 00:01:40,340
安好以后呢

62
00:01:40,340 --> 00:01:41,700
我们就来写一个小例子

63
00:01:41,700 --> 00:01:42,740
然后通过一个例子呢

64
00:01:42,740 --> 00:01:44,160
来看一下这东西怎么实现

65
00:01:44,160 --> 00:01:45,180
并且怎么应用

66
00:01:45,180 --> 00:01:47,180
往里面呢我们可以来一个

67
00:01:47,180 --> 00:01:50,800
一点比如说叫start.js

68
00:01:50,800 --> 00:01:54,360
这里面啊我们就先说一下同步的钩子

69
00:01:54,360 --> 00:01:55,940
比如说我们有写一个应用

70
00:01:55,940 --> 00:01:56,980
或者写个功能

71
00:01:56,980 --> 00:01:57,620
那这功能呢

72
00:01:57,620 --> 00:01:58,740
里面可能有很多步骤

73
00:01:58,740 --> 00:01:59,700
那我们可以怎么样

74
00:01:59,700 --> 00:02:00,560
一丝把它拆分开

75
00:02:00,560 --> 00:02:02,100
我们呢可以先引入

76
00:02:02,100 --> 00:02:02,900
我们的这个Typeable

77
00:02:02,900 --> 00:02:04,580
完了里面呢

78
00:02:04,580 --> 00:02:05,480
它有一些同步的

79
00:02:05,480 --> 00:02:06,060
那这里面呢

80
00:02:06,060 --> 00:02:06,880
我先把它引进来

81
00:02:06,880 --> 00:02:08,440
require一个Typeable

82
00:02:08,440 --> 00:02:09,960
并且呢

83
00:02:09,960 --> 00:02:11,640
它呢可以返回一个里面

84
00:02:11,640 --> 00:02:12,620
可以结构出来一个

85
00:02:12,620 --> 00:02:13,980
同步的构子叫Synchook

86
00:02:13,980 --> 00:02:15,440
完了比如说呀

87
00:02:15,440 --> 00:02:16,480
我们这里呢写个类

88
00:02:16,480 --> 00:02:17,780
比如叫class叫less

89
00:02:17,780 --> 00:02:19,800
而这里呢

90
00:02:19,800 --> 00:02:21,900
我们可以给它赋予一个contractor

91
00:02:21,900 --> 00:02:22,960
就是它自己的构度函数

92
00:02:22,960 --> 00:02:26,480
这里我们也可以给他再加一些自己所谓的钩子

93
00:02:26,480 --> 00:02:27,460
叫hooks

94
00:02:27,460 --> 00:02:30,420
这是一个相当于自定义的对象

95
00:02:30,420 --> 00:02:32,320
我们用的时候大致是这样的

96
00:02:32,320 --> 00:02:34,260
我们可以在这里面订阅一些钩子

97
00:02:34,260 --> 00:02:35,440
订阅好以后

98
00:02:35,440 --> 00:02:38,600
完了我们可以通过new这个实例来启动这些钩子

99
00:02:38,600 --> 00:02:41,360
比如说我在这里写一个关于在国客的一个钩子

100
00:02:41,360 --> 00:02:41,620
是吧

101
00:02:41,620 --> 00:02:42,760
叫brch

102
00:02:42,760 --> 00:02:45,220
往里面我可以直接用这样一个钩子

103
00:02:45,220 --> 00:02:46,040
叫new

104
00:02:46,040 --> 00:02:48,180
newsinglehook

105
00:02:48,180 --> 00:02:51,340
这里面当我们new这个钩子的时候

106
00:02:51,340 --> 00:02:53,560
到时候勾子之行肯定会传递一些参数

107
00:02:53,560 --> 00:02:55,580
这个参数它可以是可选的

108
00:02:55,580 --> 00:02:56,160
可以不添

109
00:02:56,160 --> 00:02:58,300
但是我们可以给一个数组

110
00:02:58,300 --> 00:02:59,100
往里面告诉他

111
00:02:59,100 --> 00:03:01,500
我这里面可能到时候会传一个参数

112
00:03:01,500 --> 00:03:02,500
这里这样

113
00:03:02,500 --> 00:03:05,020
当然了你也可以挂N个勾子

114
00:03:05,020 --> 00:03:06,040
比如说这一个不够

115
00:03:06,040 --> 00:03:07,540
我可以再写比如说GS的

116
00:03:07,540 --> 00:03:08,600
我这里面先写一个

117
00:03:08,600 --> 00:03:11,900
完了之后我们肯定要有一个启动勾子的方法

118
00:03:11,900 --> 00:03:14,240
这里面我们给他写个名字叫start

119
00:03:14,240 --> 00:03:19,080
并且我们可以在这里面去创建一个L的实例

120
00:03:19,080 --> 00:03:19,380
是吧

121
00:03:19,380 --> 00:03:20,180
new lesson

122
00:03:20,180 --> 00:03:21,400
 lesson

123
00:03:21,400 --> 00:03:22,500
并且呢

124
00:03:22,500 --> 00:03:24,380
我可以调用l的start方法

125
00:03:24,380 --> 00:03:27,340
就是启动我们的勾子

126
00:03:27,340 --> 00:03:27,640
对吧

127
00:03:27,640 --> 00:03:28,460
启动勾子

128
00:03:28,460 --> 00:03:30,080
但是在启动勾子之前呀

129
00:03:30,080 --> 00:03:31,660
你肯定要先注册一些事件

130
00:03:31,660 --> 00:03:33,000
比如说你来学习加工课

131
00:03:33,000 --> 00:03:34,340
那我们肯定需要怎么样

132
00:03:34,340 --> 00:03:34,600
是不是

133
00:03:34,600 --> 00:03:35,980
先放上我们的

134
00:03:35,980 --> 00:03:37,080
比如说课程的一些大概

135
00:03:37,080 --> 00:03:38,340
有node的react

136
00:03:38,340 --> 00:03:39,420
那到时候呢

137
00:03:39,420 --> 00:03:40,700
当我们启动这勾子的时候呢

138
00:03:40,700 --> 00:03:41,480
可以让这些

139
00:03:41,480 --> 00:03:42,520
这些方法呢

140
00:03:42,520 --> 00:03:42,960
一丝之行

141
00:03:42,960 --> 00:03:44,800
其实就是我们发布定位的模式

142
00:03:44,800 --> 00:03:45,820
那我们呢

143
00:03:45,820 --> 00:03:46,440
可以在这里呢

144
00:03:46,440 --> 00:03:47,140
有一个方法

145
00:03:47,140 --> 00:03:48,160
专门用来注册

146
00:03:48,160 --> 00:03:48,900
叫type

147
00:03:48,900 --> 00:03:50,660
可以认为对吧

148
00:03:50,660 --> 00:03:52,180
这是注册

149
00:03:52,180 --> 00:03:54,220
监听函数对吧

150
00:03:54,220 --> 00:03:54,940
监听函数

151
00:03:54,940 --> 00:03:58,080
就说我们可以在钩子上挂上一些函数

152
00:03:58,080 --> 00:03:59,680
当我们启动的时候

153
00:03:59,680 --> 00:04:02,020
可以让这些函数一次执行

154
00:04:02,020 --> 00:04:03,540
这里我们可以直接来做

155
00:04:03,540 --> 00:04:05,880
叫this.hooks

156
00:04:05,880 --> 00:04:07,920
它里面有一个.rsh

157
00:04:07,920 --> 00:04:10,080
可以拿到当前类的实例

158
00:04:10,080 --> 00:04:12,660
这个实例上就有一个方法

159
00:04:12,660 --> 00:04:13,900
叫注册事件叫type

160
00:04:13,900 --> 00:04:16,660
并且它里面的参数有两个

161
00:04:16,660 --> 00:04:17,720
第一个是个名字

162
00:04:17,720 --> 00:04:18,580
第二个是回调

163
00:04:18,580 --> 00:04:20,740
那我在这里面可以直接写一个

164
00:04:20,740 --> 00:04:22,620
这个名字没有什么实际意

165
00:04:22,620 --> 00:04:23,740
只是说它是个标识

166
00:04:23,740 --> 00:04:25,900
比如说架构课里面有很多课程部分

167
00:04:25,900 --> 00:04:28,580
那我怎么知道这个函数对应的是哪一个

168
00:04:28,580 --> 00:04:30,260
我们所谓的执行的名字呢

169
00:04:30,260 --> 00:04:32,780
所以这个名字就是一个没有意义的名字

170
00:04:32,780 --> 00:04:33,360
我给大家写

171
00:04:33,360 --> 00:04:36,180
比如说珠峰里面有node课程

172
00:04:36,180 --> 00:04:37,660
并且给个回调

173
00:04:37,660 --> 00:04:41,160
完了此时这里面就会有一个我们所谓的事件

174
00:04:41,160 --> 00:04:41,800
一个参数

175
00:04:41,800 --> 00:04:43,000
因为我们已经规定了

176
00:04:43,000 --> 00:04:43,940
它的参数有一个

177
00:04:43,940 --> 00:04:45,560
这里我就写上

178
00:04:45,560 --> 00:04:46,300
比如叫内

179
00:04:46,300 --> 00:04:48,680
同样我可以再绑定一个

180
00:04:48,680 --> 00:04:50,180
这里面再加一个

181
00:04:50,180 --> 00:04:52,500
我说这个名字可以一样可以不一样

182
00:04:52,500 --> 00:04:54,060
只是用来给我们开发者

183
00:04:54,060 --> 00:04:55,820
来提供一个方便阅读

184
00:04:55,820 --> 00:04:57,160
这里我再来一个叫React

185
00:04:57,160 --> 00:04:59,260
现在我们就相当于

186
00:04:59,260 --> 00:05:00,800
我们调用这个方法

187
00:05:00,800 --> 00:05:02,500
叫l.type

188
00:05:02,500 --> 00:05:04,820
相当于就是注册了这两个世界

189
00:05:04,820 --> 00:05:06,880
注册这两个世界

190
00:05:06,880 --> 00:05:11,280
当我们调用stata方法的时候

191
00:05:11,280 --> 00:05:13,000
就会把对应的这两个世界怎么样

192
00:05:13,000 --> 00:05:13,680
进行执行

193
00:05:13,680 --> 00:05:15,540
其实就是一个先是什么

194
00:05:15,540 --> 00:05:16,500
先听的过程

195
00:05:16,500 --> 00:05:17,680
再是一个出发的过程

196
00:05:17,680 --> 00:05:19,980
这里我们可以一样写个参数

197
00:05:19,980 --> 00:05:21,680
Coslog答应一下

198
00:05:21,680 --> 00:05:23,000
比如这个叫start

199
00:05:23,000 --> 00:05:25,680
里面我们就加上这样一个名字

200
00:05:25,680 --> 00:05:27,460
放在这

201
00:05:27,460 --> 00:05:28,800
这还写上node

202
00:05:28,800 --> 00:05:29,920
看着标示

203
00:05:29,920 --> 00:05:31,360
这加一个

204
00:05:31,360 --> 00:05:32,600
这个叫我们的名字

205
00:05:32,600 --> 00:05:33,180
这叫react

206
00:05:33,180 --> 00:05:34,660
好了

207
00:05:34,660 --> 00:05:35,900
我们start的时候干嘛

208
00:05:35,900 --> 00:05:37,700
是不是要触发这样件事执行

209
00:05:37,700 --> 00:05:38,820
非常简单

210
00:05:38,820 --> 00:05:40,360
我们可以通过这个钩子上面

211
00:05:40,360 --> 00:05:41,720
有个方法叫什么

212
00:05:41,720 --> 00:05:42,180
叫call

213
00:05:42,180 --> 00:05:44,060
call的意思

214
00:05:44,060 --> 00:05:45,400
就是让我们这个函数呢

215
00:05:45,400 --> 00:05:45,700
执行

216
00:05:45,700 --> 00:05:46,920
这里面少了一个叫

217
00:05:46,920 --> 00:05:47,720
DLASH

218
00:05:47,720 --> 00:05:49,880
相当于就是一个发布订阅

219
00:05:49,880 --> 00:05:50,220
对吧

220
00:05:50,220 --> 00:05:51,940
那它的原理大概是这样的

221
00:05:51,940 --> 00:05:52,800
就是它呢

222
00:05:52,800 --> 00:05:54,360
调type方法的时候呢

223
00:05:54,360 --> 00:05:55,840
会先把这两个方法

224
00:05:55,840 --> 00:05:56,940
注册到一个数组里

225
00:05:56,940 --> 00:05:58,820
当我们调用code的时候呢

226
00:05:58,820 --> 00:05:59,700
会让这两个方法

227
00:05:59,700 --> 00:06:00,180
一次执行

228
00:06:00,180 --> 00:06:01,020
那好了

229
00:06:01,020 --> 00:06:01,580
那这里呢

230
00:06:01,580 --> 00:06:01,960
我们可以

231
00:06:01,960 --> 00:06:02,760
调code的时候

232
00:06:02,760 --> 00:06:03,880
是不是还要传个参数啊

233
00:06:03,880 --> 00:06:04,380
叫名字

234
00:06:04,380 --> 00:06:05,080
那这里呢

235
00:06:05,080 --> 00:06:05,780
比如说谁讲的

236
00:06:05,780 --> 00:06:06,580
那可能是我讲的

237
00:06:06,580 --> 00:06:06,840
是吧

238
00:06:06,840 --> 00:06:07,980
填上我的名字

239
00:06:07,980 --> 00:06:08,740
那到时候一调用

240
00:06:08,740 --> 00:06:09,680
它会把这参数呢

241
00:06:09,680 --> 00:06:10,680
传到这里来

242
00:06:10,680 --> 00:06:11,000
那好了

243
00:06:11,000 --> 00:06:11,980
它这个方法呢

244
00:06:11,980 --> 00:06:12,900
其实实现很简单

245
00:06:12,900 --> 00:06:13,860
再来看一下

246
00:06:13,860 --> 00:06:14,600
右键Run

247
00:06:14,600 --> 00:06:17,860
这里我们拉上去

248
00:06:17,860 --> 00:06:19,100
这就可以看到两个结果

249
00:06:19,100 --> 00:06:20,980
有了这样一个基础以后

250
00:06:20,980 --> 00:06:23,440
我们就可以很方便的实现这样的一个原理

251
00:06:23,440 --> 00:06:24,880
我在这里来个例子

252
00:06:24,880 --> 00:06:26,580
来写它的对应的圆码

253
00:06:26,580 --> 00:06:27,080
怎么实现

254
00:06:27,080 --> 00:06:28,780
然后来个2.Kiss

255
00:06:28,780 --> 00:06:31,760
这里我们一样

256
00:06:31,760 --> 00:06:34,140
不用引导自己来写个这样的类

257
00:06:34,140 --> 00:06:35,780
叫class叫thinkhook

258
00:06:35,780 --> 00:06:37,320
一个同步的勾子

259
00:06:37,320 --> 00:06:38,200
我们也看到了

260
00:06:38,200 --> 00:06:40,340
它就是同步的勾子

261
00:06:40,340 --> 00:06:44,380
钩子是同步的

262
00:06:44,380 --> 00:06:46,760
那好了

263
00:06:46,760 --> 00:06:47,780
那这里面的一样

264
00:06:47,780 --> 00:06:48,980
我们有几个方法

265
00:06:48,980 --> 00:06:50,160
刚才我们看到了

266
00:06:50,160 --> 00:06:51,400
创建完这个实例上

267
00:06:51,400 --> 00:06:52,320
一共有两个

268
00:06:52,320 --> 00:06:53,060
一个叫type

269
00:06:53,060 --> 00:06:53,960
还有个叫call

270
00:06:53,960 --> 00:06:55,940
那我在这里就加上两个方法

271
00:06:55,940 --> 00:06:56,720
一个叫call

272
00:06:56,720 --> 00:06:59,200
一个叫type

273
00:06:59,200 --> 00:07:01,600
比如说这个type的方式

274
00:07:01,600 --> 00:07:02,920
就叫同步注册

275
00:07:02,920 --> 00:07:03,260
是吧

276
00:07:03,260 --> 00:07:04,900
我在这里切过去

277
00:07:04,900 --> 00:07:06,360
并且我们可以怎么做

278
00:07:06,360 --> 00:07:08,160
是不是创建这样一个hook

279
00:07:08,160 --> 00:07:10,140
等于newsynchook

280
00:07:10,140 --> 00:07:14,480
它里面参数是一个数组里面有参数

281
00:07:14,480 --> 00:07:15,860
所以它可以传多个参数

282
00:07:15,860 --> 00:07:17,740
但是如果你在这里面不写

283
00:07:17,740 --> 00:07:19,480
那表示它就是没有传参数

284
00:07:19,480 --> 00:07:21,180
就是说这它是一个限制作用

285
00:07:21,180 --> 00:07:22,320
并没有什么实际意义

286
00:07:22,320 --> 00:07:23,180
那好我就放在这

287
00:07:23,180 --> 00:07:25,560
完了这里面我们在nute的时候

288
00:07:25,560 --> 00:07:27,180
可能会有一个够的参数

289
00:07:27,180 --> 00:07:29,880
完了这里同样会放上这个参数

290
00:07:29,880 --> 00:07:31,420
这个参数就是这个数组

291
00:07:31,420 --> 00:07:32,260
标一下

292
00:07:32,260 --> 00:07:35,140
args就是我们这样一个数组

293
00:07:35,140 --> 00:07:37,560
里面放上一个name

294
00:07:37,560 --> 00:07:39,600
ok各式化一下

295
00:07:39,600 --> 00:07:42,140
完了当我们调type的时候非常简单

296
00:07:42,140 --> 00:07:43,300
就相当于是绑定实践

297
00:07:43,300 --> 00:07:44,460
就相当于订阅

298
00:07:44,460 --> 00:07:46,260
那type我们可以在这

299
00:07:46,260 --> 00:07:48,120
我说这个名字没有任何意义

300
00:07:48,120 --> 00:07:49,780
比如说我们刚才写的react

301
00:07:49,780 --> 00:07:51,160
后面也有一个回调

302
00:07:51,160 --> 00:07:52,640
完了此时

303
00:07:52,640 --> 00:07:54,940
我们这里面接收到参数就两个

304
00:07:54,940 --> 00:07:56,840
第一个就是我们的一个name

305
00:07:56,840 --> 00:07:57,840
实践名字

306
00:07:57,840 --> 00:07:59,480
第二个就是我们的一个任务

307
00:07:59,480 --> 00:08:00,980
要做的事叫task

308
00:08:00,980 --> 00:08:03,860
同样我在这里也写上totalog

309
00:08:03,860 --> 00:08:06,600
这里面我就写上一个叫react

310
00:08:06,600 --> 00:08:08,740
完了并且把名字放在这

311
00:08:08,740 --> 00:08:10,380
这名字是这传的

312
00:08:10,380 --> 00:08:12,380
因为我注册的时候告诉他了

313
00:08:12,380 --> 00:08:13,260
要必须传个参数

314
00:08:13,260 --> 00:08:15,780
这里一样我们可以写两个

315
00:08:15,780 --> 00:08:17,620
注册两个

316
00:08:17,620 --> 00:08:19,320
这里名字我也可以改一下

317
00:08:19,320 --> 00:08:20,220
看到区别

318
00:08:20,220 --> 00:08:21,840
那默认情况下

319
00:08:21,840 --> 00:08:22,640
我注册的时候

320
00:08:22,640 --> 00:08:24,720
其实就是把这两个函数怎么样存起来

321
00:08:24,720 --> 00:08:26,920
所以说我要在这个勾造函数上

322
00:08:26,920 --> 00:08:27,980
加上一个什么

323
00:08:27,980 --> 00:08:29,360
加上一个数组

324
00:08:29,360 --> 00:08:29,660
tas

325
00:08:29,660 --> 00:08:32,480
完了并且我们每次注册的时候

326
00:08:32,480 --> 00:08:33,840
都放到这个数组中

327
00:08:33,840 --> 00:08:36,760
我们可以通过this.tas.push

328
00:08:36,760 --> 00:08:38,220
把它一个丢进去

329
00:08:38,220 --> 00:08:40,280
相当于就订阅

330
00:08:40,280 --> 00:08:42,140
那我们都OK了

331
00:08:42,140 --> 00:08:42,780
到时候呢

332
00:08:42,780 --> 00:08:43,240
我们怎么样

333
00:08:43,240 --> 00:08:44,380
是不是来真正的去开始

334
00:08:44,380 --> 00:08:45,460
学习这些东西了

335
00:08:45,460 --> 00:08:45,820
那好

336
00:08:45,820 --> 00:08:47,140
肯定是一样一样来学

337
00:08:47,140 --> 00:08:49,280
那我们可以通过这个hook的一个方法

338
00:08:49,280 --> 00:08:50,080
叫这个

339
00:08:50,080 --> 00:08:51,140
靠

340
00:08:51,140 --> 00:08:53,180
那执行的时候还要告诉他

341
00:08:53,180 --> 00:08:53,360
对吧

342
00:08:53,360 --> 00:08:54,380
当前呢是谁讲的

343
00:08:54,380 --> 00:08:54,920
那这时候呢

344
00:08:54,920 --> 00:08:55,980
我需要签上我的名字

345
00:08:55,980 --> 00:08:56,400
是吧

346
00:08:56,400 --> 00:08:56,960
OK

347
00:08:56,960 --> 00:08:57,900
猜数就过来了

348
00:08:57,900 --> 00:08:59,860
但这个猜数我说了

349
00:08:59,860 --> 00:09:01,100
可能不止只是一个

350
00:09:01,100 --> 00:09:02,160
可能有两个有三个

351
00:09:02,160 --> 00:09:02,400
对吧

352
00:09:02,400 --> 00:09:04,000
但是如果我们要穿多个的话

353
00:09:04,000 --> 00:09:05,100
这里面你要怎么样

354
00:09:05,100 --> 00:09:05,720
配上

355
00:09:05,720 --> 00:09:06,580
有内蒙A值

356
00:09:06,580 --> 00:09:06,840
对吧

357
00:09:06,840 --> 00:09:07,920
当然你要写上A和B

358
00:09:07,920 --> 00:09:09,320
可能不方便阅读

359
00:09:09,320 --> 00:09:10,120
所以这一般呢

360
00:09:10,120 --> 00:09:11,280
我们就会有个标识作用

361
00:09:11,280 --> 00:09:12,460
这里呢会有参数

362
00:09:12,460 --> 00:09:13,420
拿过来以后呢

363
00:09:13,420 --> 00:09:14,720
多个可能会这样写

364
00:09:14,720 --> 00:09:16,520
那我们非常简单了

365
00:09:16,520 --> 00:09:17,420
一定要这个方法

366
00:09:17,420 --> 00:09:18,860
就是让这个数租里的函数怎么样

367
00:09:18,860 --> 00:09:19,780
一次执行

368
00:09:19,780 --> 00:09:21,660
那我们就可以得到这样一个结论

369
00:09:21,660 --> 00:09:22,040
是吧

370
00:09:22,040 --> 00:09:24,920
叫this.task.什么呢

371
00:09:24,920 --> 00:09:26,180
这个for each

372
00:09:26,180 --> 00:09:28,040
我料理面呢

373
00:09:28,040 --> 00:09:30,540
拿到我们每一个所谓的任务

374
00:09:30,540 --> 00:09:30,960
task

375
00:09:30,960 --> 00:09:32,800
也就是我们这个每一个函数

376
00:09:32,800 --> 00:09:34,440
我让这个函数干嘛呢

377
00:09:34,440 --> 00:09:35,340
直接执行就好了

378
00:09:35,340 --> 00:09:36,140
执行

379
00:09:36,140 --> 00:09:37,860
把我们这个参数呢传进去

380
00:09:37,860 --> 00:09:41,260
这样我们就实现了这样一个同步的钩子

381
00:09:41,260 --> 00:09:42,380
我们来看一下效果

382
00:09:42,380 --> 00:09:43,380
邮件转一下

383
00:09:43,380 --> 00:09:46,640
好了 此时出来的结果就是OK了

384
00:09:46,640 --> 00:09:49,460
我们这样一个方法就实现了

