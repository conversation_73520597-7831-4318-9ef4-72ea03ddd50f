1
00:00:00,000 --> 00:00:05,900
这一节呢我们来讲一下如何配置我们的WIPAC中解析CSS模块

2
00:00:05,900 --> 00:00:09,020
那这里呢我们就把这个配置文件为了方便

3
00:00:09,020 --> 00:00:10,580
放到历史记录里一份啊

4
00:00:10,580 --> 00:00:12,320
这样的话我来优化一下

5
00:00:12,320 --> 00:00:15,260
所以呢我把注释删掉啊都不要了

6
00:00:15,260 --> 00:00:18,540
那第一位serv呢我们不配默认8080无所谓的啊

7
00:00:18,540 --> 00:00:21,200
完了环境呢我们先给改成开发环境吧

8
00:00:21,200 --> 00:00:23,420
第一位e rop mnt

9
00:00:23,420 --> 00:00:25,820
ok 完这个呢我先把它住掉啊

10
00:00:25,820 --> 00:00:26,740
这也不要了啊

11
00:00:26,740 --> 00:00:28,280
那同样这也都是一样的

12
00:00:28,280 --> 00:00:30,180
闪一闪

13
00:00:30,180 --> 00:00:31,140
听得干净一点

14
00:00:31,140 --> 00:00:32,740
现在我们还是一样

15
00:00:32,740 --> 00:00:34,640
就是配置了个入口出口和一个插件

16
00:00:34,640 --> 00:00:35,720
我们来看看

17
00:00:35,720 --> 00:00:40,080
我们说了我们的webpack要默认的只支持我们的GS模块

18
00:00:40,080 --> 00:00:41,780
这里我们就来个css

19
00:00:41,780 --> 00:00:43,140
比如说index.css

20
00:00:43,140 --> 00:00:45,040
这里我们写点样式

21
00:00:45,040 --> 00:00:45,680
比如body

22
00:00:45,680 --> 00:00:46,980
来个background

23
00:00:46,980 --> 00:00:48,700
比如给个write

24
00:00:48,700 --> 00:00:50,540
这时候我们希望怎么样

25
00:00:50,540 --> 00:00:52,980
在我们的GS中去引用css

26
00:00:52,980 --> 00:00:55,160
当然了你肯定不能在一天猫里去引

27
00:00:55,160 --> 00:00:56,280
你说一天猫怎么不能引

28
00:00:56,280 --> 00:00:56,980
这不可以吗

29
00:00:56,980 --> 00:00:57,920
同样我可以在这

30
00:00:57,920 --> 00:00:58,700
来一个对吧

31
00:00:58,700 --> 00:01:00,020
比如说link

32
00:01:00,020 --> 00:01:02,200
我就去引这样一个css

33
00:01:02,200 --> 00:01:03,220
但是很可惜的是

34
00:01:03,220 --> 00:01:05,160
我们会以它作为模板

35
00:01:05,160 --> 00:01:07,860
这个模板里面会冤风不动的输出过来

36
00:01:07,860 --> 00:01:11,000
因为这里面我们的css并不会打不到外面

37
00:01:11,000 --> 00:01:12,840
所以这时候我们发现它是不可以的

38
00:01:12,840 --> 00:01:14,340
这时候我们要怎么做

39
00:01:14,340 --> 00:01:17,800
希望css也能变成一个模块来进行引入

40
00:01:17,800 --> 00:01:19,940
这时候我们只能在index.js中

41
00:01:19,940 --> 00:01:21,460
通过这种预法

42
00:01:21,460 --> 00:01:22,500
比如来个require

43
00:01:22,500 --> 00:01:24,420
去引我们这样一个css

44
00:01:24,420 --> 00:01:25,700
index.css

45
00:01:25,700 --> 00:01:27,040
这是可以的

46
00:01:27,040 --> 00:01:29,180
这里面我们来看一下

47
00:01:29,180 --> 00:01:30,520
但是引入完以后

48
00:01:30,520 --> 00:01:31,620
我们再去执行

49
00:01:31,620 --> 00:01:34,060
比如NPM runDV

50
00:01:34,060 --> 00:01:36,260
他告诉我

51
00:01:36,260 --> 00:01:38,500
这个东西默认我不支持

52
00:01:38,500 --> 00:01:39,700
因为我们说了

53
00:01:39,700 --> 00:01:41,280
这东西默认它不是一个模块

54
00:01:41,280 --> 00:01:41,980
它会报错

55
00:01:41,980 --> 00:01:42,680
他说这东西

56
00:01:42,680 --> 00:01:44,860
卡珠拉稍微等一下

57
00:01:44,860 --> 00:01:46,040
报错了

58
00:01:46,040 --> 00:01:47,680
说模块其实失败了

59
00:01:47,680 --> 00:01:49,980
说当前你需要一个合适的loader

60
00:01:49,980 --> 00:01:51,380
去解析文件

61
00:01:51,380 --> 00:01:53,040
这个文件我不认识

62
00:01:53,040 --> 00:01:54,220
好了怎么做

63
00:01:54,220 --> 00:01:55,780
这时候我们一想

64
00:01:55,780 --> 00:01:56,880
这时候告诉我

65
00:01:57,040 --> 00:01:59,080
需要什么东西是不是合适的loader

66
00:01:59,080 --> 00:02:04,500
那loader干嘛的loader的作用就是用来把我们的原代码进行转化

67
00:02:04,500 --> 00:02:06,740
哎他可以帮我们转化出来一个模块

68
00:02:06,740 --> 00:02:08,940
那好了那这里面我们来试一试

69
00:02:08,940 --> 00:02:11,500
那现在我们回到我们的配这文件里

70
00:02:11,500 --> 00:02:13,980
我们要对模块来进行操作

71
00:02:13,980 --> 00:02:16,300
那这时候我们就需要配一个所谓的叫module

72
00:02:16,300 --> 00:02:18,240
哎一听名字就是模块的意思是吧

73
00:02:18,240 --> 00:02:18,900
模块

74
00:02:18,900 --> 00:02:21,640
那模块里面有很多的比如文件是吧

75
00:02:21,640 --> 00:02:23,680
不是gsss他也都叫模块

76
00:02:23,680 --> 00:02:25,880
那我们要找到确定的那个哎

77
00:02:25,880 --> 00:02:27,620
谁我可以写个正责

78
00:02:27,620 --> 00:02:32,240
比如说我要找到以css结尾的就是他

79
00:02:32,240 --> 00:02:34,780
就这个文件完了我们告诉他对吧

80
00:02:34,780 --> 00:02:37,080
应该使用一个适当的loader对吧

81
00:02:37,080 --> 00:02:39,620
这loader叫什么描述一下loader

82
00:02:39,620 --> 00:02:42,280
当然这里面少了一层

83
00:02:42,280 --> 00:02:43,640
module里面是模块

84
00:02:43,640 --> 00:02:45,680
它里面应该套上一个规则

85
00:02:45,680 --> 00:02:46,940
里面配上很多规则

86
00:02:46,940 --> 00:02:49,980
唯一的第一个规则就是我们所谓的要匹配css

87
00:02:49,980 --> 00:02:52,380
是吧这是规则少了一个规则

88
00:02:52,380 --> 00:02:55,040
是数组是因为它里面可能有很多

89
00:02:55,040 --> 00:02:57,380
比如说哎能不能再配一个gs的呀

90
00:02:57,380 --> 00:02:58,480
当然也可以了是吧

91
00:02:58,480 --> 00:02:59,600
这里面我先不管

92
00:02:59,600 --> 00:03:00,580
可以这样来配

93
00:03:00,580 --> 00:03:01,540
这里删掉啊

94
00:03:01,540 --> 00:03:04,300
那我们说了怎么叫合适的loader

95
00:03:04,300 --> 00:03:07,640
那这当然了肯定是可以处理我们的css模块的是吧

96
00:03:07,640 --> 00:03:10,940
那这里面呢我们要学第一个就叫我们的cssloader

97
00:03:10,940 --> 00:03:15,680
那他呢就可以帮我们怎么样去处理我们的css文件

98
00:03:15,680 --> 00:03:19,480
比如说呀我们css文件里面有一些特殊的东西什么呢

99
00:03:19,480 --> 00:03:20,540
比如说我们都知道啊

100
00:03:20,540 --> 00:03:22,440
css里面可以再去以css

101
00:03:22,440 --> 00:03:24,100
就是以这个a.css

102
00:03:24,340 --> 00:03:28,800
这里我们可以再去引一个比如说叫A.A.CSS对吧

103
00:03:28,800 --> 00:03:31,040
比如就来一个bodybody

104
00:03:31,040 --> 00:03:34,040
来个colorcolor是right

105
00:03:34,040 --> 00:03:35,740
right太颜色都一样了是吧

106
00:03:35,740 --> 00:03:41,540
yellowok这时候我们在css中是不是又引了a.css

107
00:03:41,540 --> 00:03:43,640
最后我们在这配置的时候怎么样

108
00:03:43,640 --> 00:03:45,400
是不是两个我们需要怎么样

109
00:03:45,400 --> 00:03:46,940
把这两个模块变成一个

110
00:03:46,940 --> 00:03:49,640
这时候我们就需要cssloader来处理

111
00:03:49,640 --> 00:03:52,000
它会怎么样把这两个文件处理好

112
00:03:52,000 --> 00:03:55,500
你说它是主要是负责解析我们这种

113
00:03:55,500 --> 00:03:58,300
比如说at import这种语法的

114
00:03:58,300 --> 00:04:01,200
这是它的特点

115
00:04:01,200 --> 00:04:01,800
好了

116
00:04:01,800 --> 00:04:04,400
这里我就可以在这里去用一下它

117
00:04:04,400 --> 00:04:05,800
说为什么你要放个书组

118
00:04:05,800 --> 00:04:07,400
你直接放个这个行不行

119
00:04:07,400 --> 00:04:08,300
当然行了

120
00:04:08,300 --> 00:04:09,600
但你这样放完以后

121
00:04:09,600 --> 00:04:12,200
你发现你把这个东西解析成一个css

122
00:04:12,200 --> 00:04:14,300
它也无法插到页面上去是吧

123
00:04:14,300 --> 00:04:16,900
这时候我们第二步还需要概念是

124
00:04:16,900 --> 00:04:19,100
需要另一个loader叫styleloader

125
00:04:19,100 --> 00:04:20,400
styleloader对吧

126
00:04:20,400 --> 00:04:21,000
干嘛

127
00:04:21,000 --> 00:04:21,900
它是对吧

128
00:04:21,900 --> 00:04:24,300
它是把css对吧

129
00:04:24,300 --> 00:04:26,600
插入到hide的标签中

130
00:04:26,600 --> 00:04:28,900
hide的标签中

131
00:04:28,900 --> 00:04:31,000
就干这件事的

132
00:04:31,000 --> 00:04:31,860
那有人说了

133
00:04:31,860 --> 00:04:33,200
那为什么要有俩loader

134
00:04:33,200 --> 00:04:35,900
我们说了loader它有个特点对吧loader的

135
00:04:35,900 --> 00:04:37,000
特点

136
00:04:37,000 --> 00:04:38,200
特点就是什么呢

137
00:04:38,200 --> 00:04:38,900
希望对吧

138
00:04:38,900 --> 00:04:39,900
希望单一

139
00:04:39,900 --> 00:04:40,900
就是说我们希望哎

140
00:04:40,900 --> 00:04:42,600
这个专门负责处理css

141
00:04:42,600 --> 00:04:43,900
这个专门负责插入

142
00:04:43,900 --> 00:04:44,700
这样的话

143
00:04:44,700 --> 00:04:47,100
我们可以把多个loader组合起来用

144
00:04:47,100 --> 00:04:48,000
比如说呀

145
00:04:48,000 --> 00:04:50,900
这里面我也说一下对吧loader的用法

146
00:04:51,000 --> 00:04:57,100
哎最简单的呀就是我们可以写个字符串就表示哎我只用什么叫字符串就是只用

147
00:04:57,100 --> 00:04:59,500
只用一个楼的

148
00:04:59,500 --> 00:05:05,600
那当然了那如果想多个的话那你就想到了对吧那多个楼的对吧多个楼的需要

149
00:05:05,600 --> 00:05:11,500
需要什么呢需要一个数组是吧那这里面呢我们就可以把它呢换成个数组哎放在这

150
00:05:11,500 --> 00:05:18,900
那这时候我们说了那第一个完事的怎么样还可以放第二个是吧在这里面你要注意啊这楼的是有顺序的对吧就是楼的

151
00:05:18,900 --> 00:05:21,900
顺序叫顺序对吧顺序

152
00:05:21,900 --> 00:05:24,180
默认呢是从什么

153
00:05:24,180 --> 00:05:26,120
从右向左写

154
00:05:26,120 --> 00:05:27,100
向右

155
00:05:27,100 --> 00:05:29,400
从右向左执行对吧

156
00:05:29,400 --> 00:05:32,180
向左执行对吧

157
00:05:32,180 --> 00:05:33,100
那也就是说呀

158
00:05:33,100 --> 00:05:34,380
我们先写了cssloader

159
00:05:34,380 --> 00:05:36,260
他应该先处理完css

160
00:05:36,260 --> 00:05:37,980
再插到我们的style标签中

161
00:05:37,980 --> 00:05:38,860
那这里面呢

162
00:05:38,860 --> 00:05:40,620
我就需要把这个styleloader了

163
00:05:40,620 --> 00:05:41,480
插到什么呢

164
00:05:41,480 --> 00:05:42,140
插到这个

165
00:05:42,140 --> 00:05:42,440
哎

166
00:05:42,440 --> 00:05:43,560
前面去是吧

167
00:05:43,560 --> 00:05:44,480
放个数足

168
00:05:44,480 --> 00:05:45,220
逗号

169
00:05:45,220 --> 00:05:46,940
来一个

170
00:05:46,940 --> 00:05:48,220
那这样的话

171
00:05:48,220 --> 00:05:50,720
就可以实现两个loader的并排使用了

172
00:05:50,720 --> 00:05:52,800
而且我们说了除了这种写法

173
00:05:52,800 --> 00:05:57,100
其实loader还可以写成什么

174
00:05:57,100 --> 00:05:59,600
还可以写成我们的对象方式

175
00:05:59,600 --> 00:06:01,560
这是什么意思

176
00:06:01,560 --> 00:06:03,560
也就是说我们可以在这里面给他放个对象

177
00:06:03,560 --> 00:06:06,480
对象里面你拿个loader这是一样的

178
00:06:06,480 --> 00:06:08,440
他说这有啥区别不一样吗

179
00:06:08,440 --> 00:06:11,340
但是他的好处是你可以再传一个参数

180
00:06:11,340 --> 00:06:12,060
比如options

181
00:06:12,060 --> 00:06:14,260
当然现在没有选项的话

182
00:06:14,260 --> 00:06:14,680
你可以怎么样

183
00:06:14,680 --> 00:06:15,300
可以不写

184
00:06:15,300 --> 00:06:16,480
他俩是一样的

185
00:06:16,480 --> 00:06:18,880
就是他那好处就是可以多传点东西是吧

186
00:06:18,880 --> 00:06:19,580
那好了

187
00:06:19,580 --> 00:06:23,380
那我们现在这样一个css呢就变成了这个模样是吧

188
00:06:23,380 --> 00:06:24,220
格式化一下

189
00:06:24,220 --> 00:06:27,480
ok 完了这里呢也格式化一下是吧

190
00:06:27,480 --> 00:06:30,680
ok 完了这里呢我们也稍微隔一隔是吧

191
00:06:30,680 --> 00:06:31,780
看着有点丑啊

192
00:06:31,780 --> 00:06:32,540
好

193
00:06:32,540 --> 00:06:33,380
大致这样吧

194
00:06:33,380 --> 00:06:35,780
那咱来看看啊

195
00:06:35,780 --> 00:06:38,180
我们当前这页面的能不能变红啊

196
00:06:38,180 --> 00:06:40,480
每次改完了我们的配置文件之后呢

197
00:06:40,480 --> 00:06:41,480
都需要重启

198
00:06:41,480 --> 00:06:44,080
但是如果你要是改这个内容文件呢

199
00:06:44,080 --> 00:06:45,780
他会去自动去更新哎

200
00:06:45,780 --> 00:06:46,280
当然当然了

201
00:06:46,280 --> 00:06:47,900
你看现在这时候告诉我了

202
00:06:47,900 --> 00:06:48,780
css loader有问题

203
00:06:48,780 --> 00:06:49,220
为啥呢

204
00:06:49,220 --> 00:06:50,060
没有安装是吧

205
00:06:50,060 --> 00:06:54,080
这里我们需要安装一个css loader

206
00:06:54,080 --> 00:06:56,040
还有我们的style loader

207
00:06:56,040 --> 00:06:57,480
但是安装的顺序看见没关系

208
00:06:57,480 --> 00:06:58,860
只是使用的时候

209
00:06:58,860 --> 00:06:59,520
我们要记住

210
00:06:59,520 --> 00:07:01,280
它是从右向左执行

211
00:07:01,280 --> 00:07:02,740
而且也还有一个对吧

212
00:07:02,740 --> 00:07:04,920
叫从下到上执行

213
00:07:04,920 --> 00:07:05,840
待会会看到

214
00:07:05,840 --> 00:07:07,180
先这样写上

215
00:07:07,180 --> 00:07:07,960
好了

216
00:07:07,960 --> 00:07:09,420
这里面配好了

217
00:07:09,420 --> 00:07:11,260
我们再来执行一下

218
00:07:11,260 --> 00:07:13,420
叫npm run dv

219
00:07:13,420 --> 00:07:15,320
看看效果

220
00:07:15,320 --> 00:07:16,580
按马上出来

221
00:07:16,580 --> 00:07:19,580
完了这里还是三千端口是吧

222
00:07:19,580 --> 00:07:22,460
因为配好的这里面一样把它打开

223
00:07:22,460 --> 00:07:26,220
哦他还说这个模块找不到是吧

224
00:07:26,220 --> 00:07:27,260
哦刚按错了吗

225
00:07:27,260 --> 00:07:28,260
我再来一次

226
00:07:28,260 --> 00:07:31,620
np呀的哦我转成赖死了是吧

227
00:07:31,620 --> 00:07:33,520
这里面应该是css是吧

228
00:07:33,520 --> 00:07:37,820
ok我这样的话比较快是吧

229
00:07:37,820 --> 00:07:40,280
完了里面我就npm

230
00:07:40,280 --> 00:07:43,060
软dv我再跑一下

231
00:07:45,320 --> 00:07:47,060
完了这里面应该就可以了

232
00:07:47,060 --> 00:07:48,560
我刷新把它粘过来

233
00:07:48,560 --> 00:07:49,260
3000

234
00:07:49,260 --> 00:07:51,920
发现应该是都变红了

235
00:07:51,920 --> 00:07:53,480
而且看看样式

236
00:07:53,480 --> 00:07:54,680
因为没有写文字

237
00:07:54,680 --> 00:07:56,040
肯定也是OK的

238
00:07:56,040 --> 00:07:57,880
这包错应该不是我这外拍个包的

239
00:07:57,880 --> 00:07:58,940
这是非外抗

240
00:07:58,940 --> 00:07:59,540
没有用

241
00:07:59,540 --> 00:08:01,500
这里我们来看一下元素

242
00:08:01,500 --> 00:08:02,520
看看包底

243
00:08:02,520 --> 00:08:04,020
它是不是有background

244
00:08:04,020 --> 00:08:06,860
你看是红的还有黄的

245
00:08:06,860 --> 00:08:07,960
而且它插在哪了

246
00:08:07,960 --> 00:08:10,880
是不是插在我们hide标签的最底部了

247
00:08:10,880 --> 00:08:12,500
同样我们说了

248
00:08:12,500 --> 00:08:14,780
有的时候我希望自己来点样式

249
00:08:14,780 --> 00:08:15,940
我在这里

250
00:08:15,940 --> 00:08:17,960
我在这来一个比如style标签

251
00:08:17,960 --> 00:08:20,160
我希望颜色对吧

252
00:08:20,160 --> 00:08:22,540
背景颜色不太好看是吧

253
00:08:22,540 --> 00:08:23,780
我还是希望粉色是吧

254
00:08:23,780 --> 00:08:25,460
比如说来个body

255
00:08:25,460 --> 00:08:27,300
background pink

256
00:08:27,300 --> 00:08:32,400
好了我们再来看看效果刷新

257
00:08:32,400 --> 00:08:35,060
发现它并不会变粉为啥呢

258
00:08:35,060 --> 00:08:36,560
因为我们下面你看

259
00:08:36,560 --> 00:08:38,640
哦当然这个pink好像写了点问题

260
00:08:38,640 --> 00:08:39,780
你不能这样写对吧

261
00:08:39,780 --> 00:08:40,740
pink是吧

262
00:08:40,740 --> 00:08:42,240
这里我再刷新一下

263
00:08:42,240 --> 00:08:43,780
发现也不行对吧

264
00:08:43,780 --> 00:08:45,520
因为pink它是在什么

265
00:08:45,520 --> 00:08:46,920
在它的上面的

266
00:08:46,920 --> 00:08:50,140
就是我们希望我写的样式优先级更高

267
00:08:50,140 --> 00:08:51,240
这时候怎么做

268
00:08:51,240 --> 00:08:53,180
其实你可以去翻阅它的文档

269
00:08:53,180 --> 00:08:54,380
人家文档提供好了

270
00:08:54,380 --> 00:08:56,880
说如果你想自己写这个样式

271
00:08:56,880 --> 00:08:58,880
它的层级比别人的高

272
00:08:58,880 --> 00:09:00,780
我们写style标签的时候

273
00:09:00,780 --> 00:09:03,540
你是不是把style插到它的上面就好了

274
00:09:03,540 --> 00:09:04,180
好了

275
00:09:04,180 --> 00:09:06,020
这里里面给大家写一下

276
00:09:06,020 --> 00:09:07,380
可以在这里

277
00:09:07,380 --> 00:09:08,180
再加一个配置

278
00:09:08,180 --> 00:09:09,480
我说了写成对象的好处

279
00:09:09,480 --> 00:09:11,240
就可以再全一个什么参数

280
00:09:11,240 --> 00:09:11,680
对

281
00:09:11,680 --> 00:09:13,080
参数的话我可以写

282
00:09:13,080 --> 00:09:13,680
比如写什么

283
00:09:13,680 --> 00:09:15,360
第一我希望它能插到哪儿去

284
00:09:15,360 --> 00:09:17,200
叫什么叫insert at

285
00:09:17,200 --> 00:09:19,040
insert at

286
00:09:19,040 --> 00:09:19,840
插到哪儿呢

287
00:09:19,840 --> 00:09:21,040
我希望插到顶部

288
00:09:21,040 --> 00:09:21,800
那好了

289
00:09:21,800 --> 00:09:23,140
有时候style标签会插到什么

290
00:09:23,140 --> 00:09:24,280
上面去是吧

291
00:09:24,280 --> 00:09:24,600
那好

292
00:09:24,600 --> 00:09:25,500
重新启动

293
00:09:25,500 --> 00:09:27,740
有点慢是吧

294
00:09:27,740 --> 00:09:28,220
稍等一下

295
00:09:28,220 --> 00:09:30,320
好

296
00:09:30,320 --> 00:09:31,400
马上出来了

297
00:09:31,400 --> 00:09:32,060
刷新

298
00:09:32,060 --> 00:09:35,420
它是不是已经变成粉色了

299
00:09:35,420 --> 00:09:37,060
现在我们就知道了

300
00:09:37,060 --> 00:09:37,280
OK

301
00:09:37,280 --> 00:09:38,960
这个css可以这么处理

302
00:09:38,960 --> 00:09:40,700
同样还可以怎么样

303
00:09:40,700 --> 00:09:41,700
可以对吧

304
00:09:41,700 --> 00:09:42,680
可以处理什么

305
00:09:42,680 --> 00:09:44,260
处理Less文件

306
00:09:44,260 --> 00:09:47,220
比如说我就给大家演示一下Less文件

307
00:09:47,220 --> 00:09:48,820
这时候我们就要怎么样

308
00:09:48,820 --> 00:09:49,800
再加一个了

309
00:09:49,800 --> 00:09:51,620
比如说把文件再改一个

310
00:09:51,620 --> 00:09:53,940
当然这里面我最好放点内容

311
00:09:53,940 --> 00:09:55,360
一直都看不到效果

312
00:09:55,360 --> 00:09:56,760
比如说来个内容

313
00:09:56,760 --> 00:09:59,440
内容对吧

314
00:09:59,440 --> 00:10:00,600
区域

315
00:10:00,600 --> 00:10:02,160
比如说来个div

316
00:10:02,160 --> 00:10:03,740
来个div

317
00:10:03,740 --> 00:10:05,400
好了

318
00:10:05,400 --> 00:10:08,260
在这里我再建上一个src下

319
00:10:08,260 --> 00:10:08,540
对吧

320
00:10:08,540 --> 00:10:10,080
我再建一个Less文件

321
00:10:10,080 --> 00:10:11,560
IndessLess

322
00:10:11,560 --> 00:10:13,920
这里我给它漂亮一点

323
00:10:13,920 --> 00:10:16,220
比如说它下的DAV可以这样

324
00:10:16,220 --> 00:10:19,200
比如说来个Border EPS

325
00:10:19,200 --> 00:10:20,320
或者Solid

326
00:10:20,320 --> 00:10:22,220
比如说来个警号

327
00:10:22,220 --> 00:10:24,000
比如说0A0A0A

328
00:10:24,000 --> 00:10:25,820
这是黑色我随便编的

329
00:10:25,820 --> 00:10:27,220
这里面我换个

330
00:10:27,220 --> 00:10:29,000
比如说FQFQFQ

331
00:10:29,000 --> 00:10:31,000
不能这么写这不对

332
00:10:31,000 --> 00:10:32,660
FAFAFE

333
00:10:32,660 --> 00:10:34,220
白的也不太好看

334
00:10:34,220 --> 00:10:36,220
我就随便拼点了

335
00:10:36,220 --> 00:10:39,060
比如说DADADA这啥字

336
00:10:39,060 --> 00:10:40,460
紫色还不对

337
00:10:40,460 --> 00:10:40,740
是吧

338
00:10:40,740 --> 00:10:42,620
我就随便写点了

339
00:10:42,620 --> 00:10:44,180
为了看这颜色好看点

340
00:10:44,180 --> 00:10:44,600
那就灰的

341
00:10:44,600 --> 00:10:46,700
这里面我就可以怎么样

342
00:10:46,700 --> 00:10:47,600
这是个less文件

343
00:10:47,600 --> 00:10:50,140
现在我们是不是也希望它可以怎么样

344
00:10:50,140 --> 00:10:50,520
OK

345
00:10:50,520 --> 00:10:51,700
再来require

346
00:10:51,700 --> 00:10:52,780
require

347
00:10:52,780 --> 00:10:53,860
index.less

348
00:10:53,860 --> 00:10:55,940
你看我想的多美

349
00:10:55,940 --> 00:10:56,160
是吧

350
00:10:56,160 --> 00:10:56,740
都能生效

351
00:10:56,740 --> 00:10:58,560
index.less

352
00:10:58,560 --> 00:11:00,720
这时候我们配完以后

353
00:11:00,720 --> 00:11:03,300
同样需要在配置文件中

354
00:11:03,300 --> 00:11:05,340
再加上一条所谓的规则

355
00:11:05,340 --> 00:11:05,800
是吧

356
00:11:05,800 --> 00:11:07,180
你看这时候是不是就多个了

357
00:11:07,180 --> 00:11:07,740
逗号

358
00:11:07,740 --> 00:11:10,120
这时候匹配的就应该是less

359
00:11:10,120 --> 00:11:11,900
这时候我们说了

360
00:11:11,900 --> 00:11:13,680
从下到上之行

361
00:11:13,680 --> 00:11:14,920
就是说或者从左到右

362
00:11:14,920 --> 00:11:16,280
从右向左

363
00:11:16,280 --> 00:11:19,400
不对是从左到右上上下左右

364
00:11:19,400 --> 00:11:20,080
从右向左

365
00:11:20,080 --> 00:11:20,700
没错

366
00:11:20,700 --> 00:11:22,680
就是我们先走CSLoader

367
00:11:22,680 --> 00:11:24,780
走完以后再走StyleLoader

368
00:11:24,780 --> 00:11:26,160
但是现在又多了一步

369
00:11:26,160 --> 00:11:28,160
应该先处理一个什么Less文件

370
00:11:28,160 --> 00:11:30,200
所以这里面应该再往上加了

371
00:11:30,200 --> 00:11:32,520
用一个东西叫什么叫LessLoader

372
00:11:32,520 --> 00:11:33,940
它的作用是干嘛

373
00:11:33,940 --> 00:11:35,460
就是把Less

374
00:11:35,460 --> 00:11:38,220
Less转换成CSS

375
00:11:38,220 --> 00:11:39,680
它的作用刚才说了

376
00:11:39,680 --> 00:11:42,380
就是解析我们这种这import语法的是吧

377
00:11:42,380 --> 00:11:44,680
或者后面可以说就是包括路径的对吧

378
00:11:44,680 --> 00:11:45,680
就解析路径的

379
00:11:45,680 --> 00:11:49,580
最后我们会怎么样把它插到style标签里

380
00:11:49,580 --> 00:11:52,180
到后面我们会手写这三个的原理是吧

381
00:11:52,180 --> 00:11:54,880
让大家更好理解为什么这样来写

382
00:11:54,880 --> 00:11:57,880
这时候我们同样再去安装

383
00:11:57,880 --> 00:11:59,180
我们用了lice

384
00:11:59,180 --> 00:12:00,980
我们就需要怎么样配下lice

385
00:12:00,980 --> 00:12:02,580
一二二的就是lice

386
00:12:02,580 --> 00:12:05,680
用lice的话还需要一个东西叫什么叫lice loader

387
00:12:05,680 --> 00:12:09,080
就说lice loader他会调用lice来进行转化

388
00:12:09,080 --> 00:12:10,380
先用lice的run的方法

389
00:12:10,380 --> 00:12:12,360
这里面一样我安装一下

390
00:12:12,360 --> 00:12:13,380
这是规定好的

391
00:12:13,380 --> 00:12:14,640
你先这样记着是吧

392
00:12:14,640 --> 00:12:15,640
好

393
00:12:15,640 --> 00:12:17,920
除了处理lice

394
00:12:17,920 --> 00:12:19,380
可能还处理sus对吧

395
00:12:19,380 --> 00:12:21,080
还有我们的什么stylus

396
00:12:21,080 --> 00:12:23,540
它同样也有我们所谓的包

397
00:12:23,540 --> 00:12:23,740
是吧

398
00:12:23,740 --> 00:12:25,620
比如说sus用的应该就是node

399
00:12:25,620 --> 00:12:25,940
对吧

400
00:12:25,940 --> 00:12:26,880
-sus

401
00:12:26,880 --> 00:12:29,520
完了同样应该有个叫sus什么loader的

402
00:12:29,520 --> 00:12:31,740
你配的时候就把susloader放到最下面

403
00:12:31,740 --> 00:12:32,180
是吧

404
00:12:32,180 --> 00:12:34,380
比如说你还有什么stylus是吧

405
00:12:34,380 --> 00:12:36,940
stylus它也是一个预处理器是吧

406
00:12:36,940 --> 00:12:39,020
这里一样你可以先安装stylus

407
00:12:39,020 --> 00:12:40,980
还有这个starless什么loader

408
00:12:40,980 --> 00:12:42,260
哎你都可以自己配啊

409
00:12:42,260 --> 00:12:43,960
starlessloader

410
00:12:43,960 --> 00:12:45,980
OK我就写一个了

411
00:12:45,980 --> 00:12:47,260
功能都是一样的

412
00:12:47,260 --> 00:12:50,880
那这时候呢我们再去执行npm run dv

413
00:12:50,880 --> 00:12:53,620
我们再来看一下效果啊

414
00:12:53,620 --> 00:12:55,980
这时候呢我们看到效果应该就ok了

415
00:12:55,980 --> 00:13:02,380
那这里呢我们来看看是不是可以了呢

416
00:13:02,380 --> 00:13:05,480
是不是有边框有变黄色有变成粉色背景

417
00:13:05,480 --> 00:13:08,920
那现在啊我们就知道了基本的css已经可以处理了

418
00:13:08,920 --> 00:13:13,120
接下来我们再来看一些比如说CSS的其他的处理方式

