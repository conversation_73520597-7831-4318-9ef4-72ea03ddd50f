1
00:00:00,000 --> 00:00:01,940
奔小杰呢

2
00:00:01,940 --> 00:00:05,560
我们就来讲一下如何在webpack中处理我们的图片模块

3
00:00:05,560 --> 00:00:06,500
这里呢

4
00:00:06,500 --> 00:00:07,580
我们要先准备一张图片

5
00:00:07,580 --> 00:00:08,940
这里我收一下

6
00:00:08,940 --> 00:00:10,740
我桌面上有一张图片

7
00:00:10,740 --> 00:00:11,560
我把它拉进来

8
00:00:11,560 --> 00:00:12,380
src

9
00:00:12,380 --> 00:00:14,620
这是我的二维码

10
00:00:14,620 --> 00:00:15,660
完了并且呢

11
00:00:15,660 --> 00:00:16,260
我们在这里呢

12
00:00:16,260 --> 00:00:17,460
可以直接去使用

13
00:00:17,460 --> 00:00:18,700
在gs中吧

14
00:00:18,700 --> 00:00:19,480
我把它都住掉

15
00:00:19,480 --> 00:00:21,920
这里都住掉了

16
00:00:21,920 --> 00:00:22,780
完了我们呢

17
00:00:22,780 --> 00:00:24,600
可以直接去写一个图片

18
00:00:24,600 --> 00:00:26,000
来let一个

19
00:00:26,000 --> 00:00:28,680
比如说amg等于document

20
00:00:28,680 --> 00:00:30,580
第二 create element

21
00:00:30,580 --> 00:00:32,680
我们就创建一个图片

22
00:00:32,680 --> 00:00:33,880
完了并且呢

23
00:00:33,880 --> 00:00:36,280
我们在一卫之上的加上这样一个路径

24
00:00:36,280 --> 00:00:37,680
他这个路径我们都知道

25
00:00:37,680 --> 00:00:39,480
必须在外派个中的需要导进来

26
00:00:39,480 --> 00:00:41,580
否则呢不会去改变图片是吧

27
00:00:41,580 --> 00:00:43,180
那这里呢我们就来个屁吧

28
00:00:43,180 --> 00:00:45,780
我们当前的这样一张图片

29
00:00:45,780 --> 00:00:48,580
第二杠叫名字我稍微改一下

30
00:00:48,580 --> 00:00:49,680
打写的

31
00:00:49,680 --> 00:00:51,180
狗批记

32
00:00:51,180 --> 00:00:55,780
你就叫他public p o b r i c 点狗批记

33
00:00:55,780 --> 00:00:56,880
往里面呢

34
00:00:56,880 --> 00:00:58,280
我就把你屁啊放在这

35
00:00:58,280 --> 00:01:00,180
完最后呢我把这个p呢

36
00:01:00,180 --> 00:01:02,640
c进去document.createelement

37
00:01:02,640 --> 00:01:05,340
完了啊不对叫opend child是吧

38
00:01:05,340 --> 00:01:06,540
.opend child

39
00:01:06,540 --> 00:01:10,140
完了把我们当前的图片的c进去

40
00:01:10,140 --> 00:01:12,840
ok那这时候呢我们来看看效果

41
00:01:12,840 --> 00:01:14,820
我们来打包一下是吧

42
00:01:14,820 --> 00:01:16,440
但是这时候你会告诉我

43
00:01:16,440 --> 00:01:17,220
当前没有这个loader

44
00:01:17,220 --> 00:01:18,000
你看已经报错了

45
00:01:18,000 --> 00:01:18,960
影片就是watch模式

46
00:01:18,960 --> 00:01:20,900
那这时候已经告诉我不识别了

47
00:01:20,900 --> 00:01:22,380
那这时候我要怎么做呢

48
00:01:22,380 --> 00:01:24,340
我们以前是不是需要一个叫fileloader

49
00:01:24,340 --> 00:01:26,560
那好那我在这里呢就先配一下

50
00:01:26,560 --> 00:01:28,260
同样再配个规则吧

51
00:01:28,260 --> 00:01:28,820
是吧

52
00:01:28,820 --> 00:01:29,260
这里呢

53
00:01:29,260 --> 00:01:29,760
一样

54
00:01:29,760 --> 00:01:32,460
来个test

55
00:01:32,460 --> 00:01:33,760
完了我们来一个叫

56
00:01:33,760 --> 00:01:35,260
gs 结尾的

57
00:01:35,260 --> 00:01:37,260
完了我们就用这个file loader是吧

58
00:01:37,260 --> 00:01:38,260
还不是gs 结尾了

59
00:01:38,260 --> 00:01:39,760
应该叫go pg 是吧

60
00:01:39,760 --> 00:01:40,660
或者什么png

61
00:01:40,660 --> 00:01:41,860
我就写着一个了

62
00:01:41,860 --> 00:01:44,260
完了用字我们就用这个file loader

63
00:01:44,260 --> 00:01:47,560
那这file loader我们需要怎么样

64
00:01:47,560 --> 00:01:48,960
自己来实现一下是吧

65
00:01:48,960 --> 00:01:49,860
其实这个file loader

66
00:01:49,860 --> 00:01:51,260
其实还是很简单的

67
00:01:51,260 --> 00:01:52,460
它的目的就是什么

68
00:01:52,460 --> 00:01:54,560
目的就是根据对吧

69
00:01:54,560 --> 00:01:57,160
根据图片生成一个什么

70
00:01:57,160 --> 00:01:58,760
生成一个md5戳

71
00:01:58,760 --> 00:01:59,760
这当时也看过啊

72
00:01:59,760 --> 00:02:02,060
是一个拉马马其道亚托一个数字的是吧

73
00:02:02,060 --> 00:02:03,860
完了呢怎么样是不是发射到

74
00:02:03,860 --> 00:02:05,860
第四头目录下

75
00:02:05,860 --> 00:02:08,160
并且发射完以后啊

76
00:02:08,160 --> 00:02:09,360
我们是不是还会怎么样

77
00:02:09,360 --> 00:02:10,660
是不是这个file loader

78
00:02:10,660 --> 00:02:12,160
file loader对吧

79
00:02:12,160 --> 00:02:12,860
还会

80
00:02:12,860 --> 00:02:18,460
返回当前的什么当前的对吧

81
00:02:18,460 --> 00:02:19,760
这个图片路径

82
00:02:19,760 --> 00:02:20,560
你想啊

83
00:02:20,560 --> 00:02:22,460
我们在这里面是不是引的应该是什么

84
00:02:22,460 --> 00:02:24,260
是不是引的是打包后的这个路径啊

85
00:02:24,260 --> 00:02:26,360
不过在这里我们穿件来把它引进来

86
00:02:26,460 --> 00:02:28,600
他已经过来的肯定是一个软软软码是吧

87
00:02:28,600 --> 00:02:29,900
好了一个md5戳

88
00:02:29,900 --> 00:02:32,960
我需要把这md5戳放在src上

89
00:02:32,960 --> 00:02:34,800
最后把它怎么样在cdl面积去

90
00:02:34,800 --> 00:02:36,560
好了我们来试试

91
00:02:36,560 --> 00:02:39,060
我们就写这样一个file loader

92
00:02:39,060 --> 00:02:41,360
在这里加上一个file loader

93
00:02:41,360 --> 00:02:42,360
.js

94
00:02:42,360 --> 00:02:45,720
完了这里我们就非常快速的写下基本逻辑是吧

95
00:02:45,720 --> 00:02:46,300
loader

96
00:02:46,300 --> 00:02:48,620
完了a spot default

97
00:02:48,620 --> 00:02:50,060
不应该这样写

98
00:02:50,060 --> 00:02:51,700
应该module.a spots

99
00:02:51,700 --> 00:02:53,700
这里只能用

100
00:02:54,200 --> 00:02:55,500
抗门节规范

101
00:02:55,500 --> 00:02:57,300
这里我们把这圆码放进来

102
00:02:57,300 --> 00:02:58,400
但是你要明确

103
00:02:58,400 --> 00:03:00,600
这个圆码也不太有问题是吧

104
00:03:00,600 --> 00:03:02,000
我们刚才拿到都是字幕串

105
00:03:02,000 --> 00:03:04,800
我们需要把这个圆码给改成什么模式

106
00:03:04,800 --> 00:03:06,600
改成我们buffer的模式

107
00:03:06,600 --> 00:03:08,400
如果我们需要是什么是二进制

108
00:03:08,400 --> 00:03:08,600
对吧

109
00:03:08,600 --> 00:03:09,300
因为它是图片

110
00:03:09,300 --> 00:03:10,600
你不能转着字幕串

111
00:03:10,600 --> 00:03:11,300
那这样的话

112
00:03:11,300 --> 00:03:12,400
我们拿到就是二进制了

113
00:03:12,400 --> 00:03:12,700
对吧

114
00:03:12,700 --> 00:03:14,100
那看看cotallog

115
00:03:14,100 --> 00:03:15,900
我们把这个sauce打出来

116
00:03:15,900 --> 00:03:17,500
看看能不能匹配到这张图片

117
00:03:17,500 --> 00:03:18,500
先跑一下

118
00:03:18,500 --> 00:03:22,000
抱错了

119
00:03:22,000 --> 00:03:23,400
他说当前加载失败了

120
00:03:23,400 --> 00:03:24,040
说需要返回

121
00:03:24,200 --> 00:03:24,960
直是吧

122
00:03:24,960 --> 00:03:26,960
那这里呢我们把这个sauce先返回回去

123
00:03:26,960 --> 00:03:28,300
再来一次

124
00:03:28,300 --> 00:03:30,100
执行

125
00:03:30,100 --> 00:03:34,400
看是不是一个buffer也是我们二斤质

126
00:03:34,400 --> 00:03:36,900
那有了二斤质以后我们就好办了

127
00:03:36,900 --> 00:03:37,900
我们可以怎么做呢

128
00:03:37,900 --> 00:03:40,400
是不是可以把当前这样一个东西怎么样

129
00:03:40,400 --> 00:03:43,700
是不是生成通过内容来生成一个md5车

130
00:03:43,700 --> 00:03:45,500
那这时候呢还是用这个工具库

131
00:03:45,500 --> 00:03:47,300
工具库叫什么叫loader

132
00:03:47,300 --> 00:03:48,200
youtils

133
00:03:48,200 --> 00:03:49,200
等于require

134
00:03:49,200 --> 00:03:50,700
我们就叫他loaderyoutils

135
00:03:50,700 --> 00:03:52,800
完了我们可以通过他呢点什么呢

136
00:03:52,800 --> 00:03:54,880
去直接点哪个方法呢

137
00:03:54,880 --> 00:03:56,560
有个叫interpolate name

138
00:03:56,560 --> 00:03:57,000
对吧

139
00:03:57,000 --> 00:03:57,780
它的内容

140
00:03:57,780 --> 00:03:58,640
它的作用呢

141
00:03:58,640 --> 00:04:00,400
就是根据我们当前

142
00:04:00,400 --> 00:04:02,200
这个我们的一个格式呢

143
00:04:02,200 --> 00:04:04,220
来生成这样一个图片路径

144
00:04:04,220 --> 00:04:05,960
或者是就生成路径用的是吧

145
00:04:05,960 --> 00:04:06,700
完了里面呢

146
00:04:06,700 --> 00:04:07,660
我可以给上这样一个

147
00:04:07,660 --> 00:04:08,380
比如叫哈希

148
00:04:08,380 --> 00:04:11,060
他呢可能是就不给位数了

149
00:04:11,060 --> 00:04:12,200
来一个扩展名

150
00:04:12,200 --> 00:04:12,980
这扩展名呢

151
00:04:12,980 --> 00:04:14,300
取的就是当前我们this

152
00:04:14,300 --> 00:04:15,700
就是这个当前loader

153
00:04:15,700 --> 00:04:17,200
处理这个文件的一个扩展名

154
00:04:17,200 --> 00:04:18,500
完了产生一个哈希值

155
00:04:18,500 --> 00:04:20,160
完了根据什么来产生呢

156
00:04:20,160 --> 00:04:21,240
这里面我还需要

157
00:04:21,240 --> 00:04:22,940
要给它一个content的内容

158
00:04:22,940 --> 00:04:25,300
根据它的内容的来产生放在这

159
00:04:25,300 --> 00:04:26,840
这时候我们就可以怎么样

160
00:04:26,840 --> 00:04:28,740
是不是拿到这样一个file name

161
00:04:28,740 --> 00:04:31,080
完最后我们可以怎么样

162
00:04:31,080 --> 00:04:33,000
是不是来发射这样一个文件名

163
00:04:33,000 --> 00:04:34,580
input叫email file

164
00:04:34,580 --> 00:04:36,180
这是发射是吧

165
00:04:36,180 --> 00:04:37,500
也是它内部的方法

166
00:04:37,500 --> 00:04:38,500
叫发射文件

167
00:04:38,500 --> 00:04:40,580
发射文件

168
00:04:40,580 --> 00:04:42,940
好了发射的文件的名字叫什么

169
00:04:42,940 --> 00:04:43,540
叫他

170
00:04:43,540 --> 00:04:45,540
往上并且这里面可以怎么样

171
00:04:45,540 --> 00:04:47,080
是不是还要把我的内容放进去

172
00:04:47,080 --> 00:04:47,780
那就是他

173
00:04:47,780 --> 00:04:49,440
那就是名字叫他内容是他

174
00:04:49,440 --> 00:04:50,740
完了最后我们这里面

175
00:04:50,740 --> 00:04:52,540
也不需要return source

176
00:04:52,540 --> 00:04:53,640
因为这时候我们说了

177
00:04:53,640 --> 00:04:55,940
这个我们所谓的叫file loader

178
00:04:55,940 --> 00:04:56,440
对吧

179
00:04:56,440 --> 00:04:57,540
他需要干嘛

180
00:04:57,540 --> 00:04:59,940
需要返回一个路径

181
00:04:59,940 --> 00:05:01,240
为什么要返回路径

182
00:05:01,240 --> 00:05:02,240
因为我们可以看到

183
00:05:02,240 --> 00:05:03,740
我们在页面里面取的时候

184
00:05:03,740 --> 00:05:05,440
是不是拿到当前

185
00:05:05,440 --> 00:05:07,140
他引入这个模块的一个路径

186
00:05:07,140 --> 00:05:09,040
那肯定是require过的路径

187
00:05:09,040 --> 00:05:10,440
所以这里面我可以怎么样

188
00:05:10,440 --> 00:05:11,940
在我们这个代码里面导出

189
00:05:11,940 --> 00:05:12,740
这样来写

190
00:05:12,740 --> 00:05:15,240
你看module.exports

191
00:05:15,240 --> 00:05:17,040
等于大括号

192
00:05:17,040 --> 00:05:17,440
对吧

193
00:05:17,440 --> 00:05:18,640
这里面要加个双引号

194
00:05:18,640 --> 00:05:20,640
完了我把我的file name的放进去

195
00:05:20,740 --> 00:05:21,640
这的意思就是什么

196
00:05:21,640 --> 00:05:23,100
是不是就产生了一个啊

197
00:05:23,100 --> 00:05:24,940
什么猫对的泡子一个字幕串啊

198
00:05:24,940 --> 00:05:25,540
那这时候呢

199
00:05:25,540 --> 00:05:26,540
我们在烟面中

200
00:05:26,540 --> 00:05:28,440
是不是相当于把这个模块导入

201
00:05:28,440 --> 00:05:30,140
那拿到的是不是就是那字幕串啊

202
00:05:30,140 --> 00:05:32,440
我把这字幕串放到image的 src 上

203
00:05:32,440 --> 00:05:33,840
那来看看吧

204
00:05:33,840 --> 00:05:35,340
那现在这样一张图片啊

205
00:05:35,340 --> 00:05:36,640
应该就正常了

206
00:05:36,640 --> 00:05:37,540
webpack

207
00:05:37,540 --> 00:05:38,140
哎

208
00:05:38,140 --> 00:05:39,240
你看是没有包错了

209
00:05:39,240 --> 00:05:41,340
而且是不是产生了这样一长串图片啊

210
00:05:41,340 --> 00:05:41,740
哎

211
00:05:41,740 --> 00:05:43,040
而且后转呢是偏激

212
00:05:43,040 --> 00:05:45,040
你看后转偏激也能看

213
00:05:45,040 --> 00:05:45,540
完了啊

214
00:05:45,540 --> 00:05:46,640
是Grupy D啊

215
00:05:46,640 --> 00:05:47,140
那这里呢

216
00:05:47,140 --> 00:05:49,640
我们是不是就可以到这样一个builder gs

217
00:05:49,640 --> 00:05:50,800
来试试效果刷新

218
00:05:50,800 --> 00:05:52,920
你发现有点问题是吧

219
00:05:52,920 --> 00:05:54,760
他说不能执行append child

220
00:05:54,760 --> 00:05:56,020
有问题是吧

221
00:05:56,020 --> 00:05:57,100
少点的一层

222
00:05:57,100 --> 00:05:59,200
我回到我的代码里

223
00:05:59,200 --> 00:06:00,300
我的代码在这

224
00:06:00,300 --> 00:06:00,600
是吧

225
00:06:00,600 --> 00:06:02,280
document.body

226
00:06:02,280 --> 00:06:02,980
是吧

227
00:06:02,980 --> 00:06:04,040
这能自己进去吗

228
00:06:04,040 --> 00:06:04,520
是吧

229
00:06:04,520 --> 00:06:05,800
看是不是图片就有了

230
00:06:05,800 --> 00:06:06,680
这样的话

231
00:06:06,680 --> 00:06:08,460
我们就实现这样一个功能了

232
00:06:08,460 --> 00:06:10,180
但是我们还需要怎么样

233
00:06:10,180 --> 00:06:11,560
再稍微的改一改

234
00:06:11,560 --> 00:06:12,280
怎么改

235
00:06:12,280 --> 00:06:13,860
因为我们一般用loader

236
00:06:13,860 --> 00:06:15,780
并不是用的叫fileloader

237
00:06:15,780 --> 00:06:17,960
一般会用一个叫URLloader

238
00:06:17,960 --> 00:06:18,580
你说这里面

239
00:06:18,580 --> 00:06:19,900
我们一般会稍微改一下

240
00:06:19,900 --> 00:06:20,740
叫UrLoader

241
00:06:20,740 --> 00:06:22,180
它什么特解是吧

242
00:06:22,180 --> 00:06:23,060
我也在这描述一下

243
00:06:23,060 --> 00:06:24,220
UrLoader

244
00:06:24,220 --> 00:06:25,940
Loader对吧

245
00:06:25,940 --> 00:06:26,920
会处理什么呢

246
00:06:26,920 --> 00:06:28,280
会处理路径

247
00:06:28,280 --> 00:06:29,400
对吧

248
00:06:29,400 --> 00:06:30,460
怎么叫处理路径呢

249
00:06:30,460 --> 00:06:31,660
就是它第一功能处理路径

250
00:06:31,660 --> 00:06:32,760
处理路径交给谁呢

251
00:06:32,760 --> 00:06:34,480
它交给的就是我们的FileLoader

252
00:06:34,480 --> 00:06:35,540
就是FileLoader

253
00:06:35,540 --> 00:06:37,160
它会把这个文件发展出来

254
00:06:37,160 --> 00:06:38,140
UrLoader

255
00:06:38,140 --> 00:06:39,980
它一般会有一个参数叫什么

256
00:06:39,980 --> 00:06:40,820
叫Options选项

257
00:06:40,820 --> 00:06:41,820
我来写一下

258
00:06:41,820 --> 00:06:42,780
是这样的

259
00:06:42,780 --> 00:06:43,880
有一个叫Options

260
00:06:43,880 --> 00:06:44,740
咱也能用过是吧

261
00:06:44,740 --> 00:06:45,640
用过的话也没问题

262
00:06:45,640 --> 00:06:47,120
不叫Options

263
00:06:47,120 --> 00:06:47,740
这个叫Loader

264
00:06:47,740 --> 00:06:48,960
就用这个loader

265
00:06:48,960 --> 00:06:50,700
但是我们也给它一个限制

266
00:06:50,700 --> 00:06:50,920
对吧

267
00:06:50,920 --> 00:06:52,380
比如说来个limit

268
00:06:52,380 --> 00:06:54,620
比如说它如果大于200k

269
00:06:54,620 --> 00:06:55,300
对吧

270
00:06:55,300 --> 00:06:56,100
那需要怎么样

271
00:06:56,100 --> 00:06:56,720
产生文件

272
00:06:56,720 --> 00:06:58,260
如果小于200k

273
00:06:58,260 --> 00:06:58,700
那好

274
00:06:58,700 --> 00:06:59,540
那就需要怎么样

275
00:06:59,540 --> 00:07:01,340
变成我们所谓的base64

276
00:07:01,340 --> 00:07:02,520
所以这里面

277
00:07:02,520 --> 00:07:03,920
我们知道这样一个概念以后

278
00:07:03,920 --> 00:07:04,960
我们就可以怎么样

279
00:07:04,960 --> 00:07:06,440
回到我们的代码里找一下

280
00:07:06,440 --> 00:07:08,560
我们应该再来一个叫什么

281
00:07:08,560 --> 00:07:09,700
叫URLloader

282
00:07:09,700 --> 00:07:11,480
它会调用我们的fileloader

283
00:07:11,480 --> 00:07:12,120
关掉

284
00:07:12,120 --> 00:07:12,700
关闭其他

285
00:07:12,700 --> 00:07:14,180
我在这里建一个

286
00:07:14,180 --> 00:07:16,400
就叫我们的URLloader

287
00:07:16,400 --> 00:07:17,780
来看一看

288
00:07:17,780 --> 00:07:21,260
然后没有写.js改个名字

289
00:07:21,260 --> 00:07:24,220
同样我们在这里

290
00:07:24,220 --> 00:07:26,260
建上这样一个文件叫loader

291
00:07:26,260 --> 00:07:27,680
并且把它导出去

292
00:07:27,680 --> 00:07:32,840
module.ispos等于我们的loader

293
00:07:32,840 --> 00:07:36,300
这里有我们的sauce

294
00:07:36,300 --> 00:07:37,920
好了我们需要怎么做

295
00:07:37,920 --> 00:07:39,880
是不是还是需要获取当前什么

296
00:07:39,880 --> 00:07:41,560
我们执行时传过来的参数

297
00:07:41,560 --> 00:07:43,100
看看类面的是多少

298
00:07:43,100 --> 00:07:46,320
这时候我们都习义为常

299
00:07:46,320 --> 00:07:48,900
Let 一个叫 loader utils

300
00:07:48,900 --> 00:07:50,160
大写的

301
00:07:50,160 --> 00:07:53,380
require叫 loader utils

302
00:07:53,380 --> 00:07:54,700
 utils

303
00:07:54,700 --> 00:07:56,880
完了用的时候非常方便了

304
00:07:56,880 --> 00:07:57,580
叫什么来着

305
00:07:57,580 --> 00:07:59,740
叫 der get option

306
00:07:59,740 --> 00:08:00,640
对吧

307
00:08:00,640 --> 00:08:01,620
没有提示了

308
00:08:01,620 --> 00:08:02,680
又是写错了吗

309
00:08:02,680 --> 00:08:04,000
require肯定写错了

310
00:08:04,000 --> 00:08:05,860
这里面我们可以怎么做

311
00:08:05,860 --> 00:08:06,580
是不是就一银

312
00:08:06,580 --> 00:08:09,000
因为我们当前的这样一个参数

313
00:08:09,000 --> 00:08:11,440
let options

314
00:08:11,440 --> 00:08:12,280
OK

315
00:08:12,280 --> 00:08:13,660
有了这个参数以后

316
00:08:13,660 --> 00:08:15,280
我们就可以稍微来处理一下了

317
00:08:15,280 --> 00:08:15,900
怎么处理

318
00:08:15,900 --> 00:08:18,140
这是不是我们需要取他的什么

319
00:08:18,140 --> 00:08:18,640
Limit

320
00:08:18,640 --> 00:08:20,160
那我在这里就解构了

321
00:08:20,160 --> 00:08:21,360
我就认为他必传

322
00:08:21,360 --> 00:08:22,300
但有可能没传

323
00:08:22,300 --> 00:08:23,540
这里面我就不去包套了

324
00:08:23,540 --> 00:08:24,700
还是这样吧

325
00:08:24,700 --> 00:08:26,060
我做个判断是吧

326
00:08:26,060 --> 00:08:26,900
我就在这

327
00:08:26,900 --> 00:08:28,400
Limit

328
00:08:28,400 --> 00:08:29,660
并且如果对吧

329
00:08:29,660 --> 00:08:31,040
如果Limit有

330
00:08:31,040 --> 00:08:34,600
好了并且当前我们Limit

331
00:08:34,600 --> 00:08:35,060
对吧

332
00:08:35,060 --> 00:08:38,060
它是大于我们sauce的什么Lens

333
00:08:38,060 --> 00:08:39,360
那就说明它有是吧

334
00:08:39,360 --> 00:08:39,900
有

335
00:08:39,900 --> 00:08:41,560
有的话我要怎么做

336
00:08:41,560 --> 00:08:42,560
非常简单了

337
00:08:42,560 --> 00:08:44,100
也就说明我需要怎么样

338
00:08:44,100 --> 00:08:45,100
是不是

339
00:08:45,900 --> 00:08:48,600
把文件变成我们所谓的贝斯64

340
00:08:48,600 --> 00:08:50,200
咋变也非常简单

341
00:08:50,200 --> 00:08:51,440
这时候我们还是一样

342
00:08:51,440 --> 00:08:53,140
他返回的肯定是个路径

343
00:08:53,140 --> 00:08:53,460
对吧

344
00:08:53,460 --> 00:08:55,360
还是一样叫猫丢点

345
00:08:55,360 --> 00:08:56,100
Xpos

346
00:08:56,100 --> 00:08:58,800
等于网站里面我就写了叫什么

347
00:08:58,800 --> 00:09:00,600
叫data冒号

348
00:09:00,600 --> 00:09:00,960
对吧

349
00:09:00,960 --> 00:09:02,800
网站里面是不是应该有个类型

350
00:09:02,800 --> 00:09:04,260
什么类型我们都知道

351
00:09:04,260 --> 00:09:05,300
其实可以看一下

352
00:09:05,300 --> 00:09:07,600
我们图片的贝斯柔四有个规则是吧

353
00:09:07,600 --> 00:09:08,400
百度

354
00:09:08,400 --> 00:09:12,040
你在这图片贝斯64是吧

355
00:09:12,040 --> 00:09:15,400
有个随便找一张图片来转上给你看看

356
00:09:15,500 --> 00:09:17,900
不是选择要转换的北四桌图片

357
00:09:17,900 --> 00:09:19,340
就这张放过来

358
00:09:19,340 --> 00:09:21,780
发现北四桌子前面都会有个前坠

359
00:09:21,780 --> 00:09:22,240
长这样

360
00:09:22,240 --> 00:09:24,040
这个地方是死的

361
00:09:24,040 --> 00:09:25,780
完了后面就是我们的内容了

362
00:09:25,780 --> 00:09:27,000
这地方改改

363
00:09:27,000 --> 00:09:28,740
前面叫image.pg

364
00:09:28,740 --> 00:09:29,440
这是什么

365
00:09:29,440 --> 00:09:30,960
这是图片的类型

366
00:09:30,960 --> 00:09:32,500
类型的话我们怎么拿到

367
00:09:32,500 --> 00:09:34,260
我们是不是有文件的后坠

368
00:09:34,260 --> 00:09:35,940
有后坠就能拿到类型

369
00:09:35,940 --> 00:09:36,980
可以用到我们

370
00:09:36,980 --> 00:09:39,820
讲node的时候有个模块叫mem

371
00:09:39,820 --> 00:09:42,080
这里我就安装一下

372
00:09:42,080 --> 00:09:43,480
我就把它用一下

373
00:09:43,480 --> 00:09:47,180
let mem等于require一个东西叫mem

374
00:09:47,180 --> 00:09:49,000
并且呢

375
00:09:49,000 --> 00:09:49,720
我在这里呢

376
00:09:49,720 --> 00:09:51,060
可以直接来这样写了啊

377
00:09:51,060 --> 00:09:52,500
那这个东西就是可变的

378
00:09:52,500 --> 00:09:53,120
变成一个什么呢

379
00:09:53,120 --> 00:09:56,260
道路夫大括号叫meme.gettype

380
00:09:56,260 --> 00:09:57,560
完了里面呢

381
00:09:57,560 --> 00:09:59,300
我就可以放上这样一个路径

382
00:09:59,300 --> 00:10:00,460
那路径怎么放呢

383
00:10:00,460 --> 00:10:01,320
就是this

384
00:10:01,320 --> 00:10:02,200
稍微改改

385
00:10:02,200 --> 00:10:04,320
叫this点什么叫resource

386
00:10:04,320 --> 00:10:04,840
还记得吧

387
00:10:04,840 --> 00:10:05,660
pass

388
00:10:05,660 --> 00:10:07,160
就是我们资源的路径

389
00:10:07,160 --> 00:10:08,520
完了拿完以后呢

390
00:10:08,520 --> 00:10:10,100
是不是这个就是image什么什么

391
00:10:10,100 --> 00:10:10,860
那把写到是吧

392
00:10:10,860 --> 00:10:11,920
那拿完以后呢

393
00:10:11,920 --> 00:10:13,300
我们这个地方是base逗号

394
00:10:13,300 --> 00:10:14,500
往后面有个值吧

395
00:10:14,500 --> 00:10:15,880
那这个值是哪来的呢

396
00:10:15,880 --> 00:10:17,240
就是我们当前这样一个值

397
00:10:17,240 --> 00:10:18,680
值就是我们当前的

398
00:10:18,680 --> 00:10:20,400
是不是sauce要转成base04

399
00:10:20,400 --> 00:10:21,920
sauce.to

400
00:10:21,920 --> 00:10:23,060
子俊对吧

401
00:10:23,060 --> 00:10:24,740
我们可以直接去写上

402
00:10:24,740 --> 00:10:24,920
对吧

403
00:10:24,920 --> 00:10:25,680
它是一个base04

404
00:10:25,680 --> 00:10:26,680
但是别忘了

405
00:10:26,680 --> 00:10:28,200
现在sauce是个字母串

406
00:10:28,200 --> 00:10:29,460
我们还需要把它怎么样

407
00:10:29,460 --> 00:10:30,200
变成buffer

408
00:10:30,200 --> 00:10:31,240
变成buffer的话

409
00:10:31,240 --> 00:10:32,680
是不是就是loader

410
00:10:32,680 --> 00:10:33,140
完了

411
00:10:33,140 --> 00:10:33,600
第二

412
00:10:33,600 --> 00:10:34,220
row

413
00:10:34,220 --> 00:10:35,220
等于一个

414
00:10:35,220 --> 00:10:36,360
处

415
00:10:36,360 --> 00:10:38,460
这样的话

416
00:10:38,460 --> 00:10:39,780
是不是就导出了这样一个字母串

417
00:10:39,780 --> 00:10:40,700
但字母串外面

418
00:10:40,700 --> 00:10:41,880
是不是还应该有个双引号

419
00:10:41,880 --> 00:10:42,140
是吧

420
00:10:42,140 --> 00:10:42,880
那这里面

421
00:10:42,880 --> 00:10:44,700
你就应该加个这东西是吧

422
00:10:44,700 --> 00:10:46,420
省着等会往丢了

423
00:10:46,420 --> 00:10:49,160
因为这里面我们转完以后

424
00:10:49,160 --> 00:10:49,940
有个问题

425
00:10:49,940 --> 00:10:50,920
把它放到外面去

426
00:10:50,920 --> 00:10:52,880
这就是一个贝斯柔斯码

427
00:10:52,880 --> 00:10:53,540
把它转出去

428
00:10:53,540 --> 00:10:54,560
转出去以后

429
00:10:54,560 --> 00:10:56,120
是不是在内部会被require到

430
00:10:56,120 --> 00:10:57,200
require的时候就是这个路径

431
00:10:57,200 --> 00:10:59,600
否则的话不好意思了

432
00:10:59,600 --> 00:11:01,000
说明它不是一个什么

433
00:11:01,000 --> 00:11:02,560
没有变成贝斯柔斯

434
00:11:02,560 --> 00:11:03,760
应该还是原来图片

435
00:11:03,760 --> 00:11:05,520
图片的话应该怎么办

436
00:11:05,520 --> 00:11:07,460
是不是应该调用我们的file loader来处理

437
00:11:07,460 --> 00:11:09,100
我在这里就require

438
00:11:09,100 --> 00:11:11,140
是不是.gov file loader

439
00:11:11,140 --> 00:11:12,000
完了往它执行

440
00:11:12,000 --> 00:11:12,940
之前的时候呢

441
00:11:12,940 --> 00:11:14,360
我需要把this给他之外

442
00:11:14,360 --> 00:11:15,720
要否则this就乱了

443
00:11:15,720 --> 00:11:17,060
this给他以后呢

444
00:11:17,060 --> 00:11:18,280
我们还需要给个什么呢

445
00:11:18,280 --> 00:11:19,660
是不是把这圆码给他呀

446
00:11:19,660 --> 00:11:21,220
因为每个loader的参数呢

447
00:11:21,220 --> 00:11:21,880
都是圆码

448
00:11:21,880 --> 00:11:22,880
那这时候呢

449
00:11:22,880 --> 00:11:24,500
他是不是也会返回一个组串啊

450
00:11:24,500 --> 00:11:24,880
那好

451
00:11:24,880 --> 00:11:25,580
我再把它怎么样

452
00:11:25,580 --> 00:11:26,660
再进行返回

453
00:11:26,660 --> 00:11:27,860
事事结了

454
00:11:27,860 --> 00:11:29,260
所以说一般以前啊

455
00:11:29,260 --> 00:11:30,160
我们都用什么呢

456
00:11:30,160 --> 00:11:31,540
都用这个urloader

457
00:11:31,540 --> 00:11:32,660
因为urloader怎么样

458
00:11:32,660 --> 00:11:33,840
会自动去识别

459
00:11:33,840 --> 00:11:35,800
是否调用这个fileloader

460
00:11:35,800 --> 00:11:36,060
那好

461
00:11:36,060 --> 00:11:37,240
来看看是吧

462
00:11:37,240 --> 00:11:39,240
现在应该就是base64了啊

463
00:11:39,240 --> 00:11:40,760
因为我给这个值很大

464
00:11:40,760 --> 00:11:41,400
200k呢

465
00:11:41,400 --> 00:11:43,060
人家图片没有200K

466
00:11:43,060 --> 00:11:45,300
你看图片没有吧

467
00:11:45,300 --> 00:11:46,420
没有打包出图片来

468
00:11:46,420 --> 00:11:47,920
但是看看有没有东西是吧

469
00:11:47,920 --> 00:11:49,060
我回过来刷新

470
00:11:49,060 --> 00:11:50,960
这时候图片还在

471
00:11:50,960 --> 00:11:52,020
完了我看看这路径

472
00:11:52,020 --> 00:11:52,960
现在是不是一个B64

473
00:11:52,960 --> 00:11:53,860
是吧

474
00:11:53,860 --> 00:11:56,640
这就是一个URL和FailL的写法

475
00:11:56,640 --> 00:11:57,520
好了

476
00:11:57,520 --> 00:11:58,680
我们再来试一下

477
00:11:58,680 --> 00:12:00,020
为了保证正确

478
00:12:00,020 --> 00:12:02,280
我们在这里再去给它改个小点的

479
00:12:02,280 --> 00:12:02,980
比如说20K

480
00:12:02,980 --> 00:12:05,020
我这图片应该是超过20K了

481
00:12:05,020 --> 00:12:05,920
还是挺大的

482
00:12:05,920 --> 00:12:06,580
是个高清图

483
00:12:06,580 --> 00:12:08,640
完了里面我刷新

484
00:12:08,640 --> 00:12:10,900
你看这时候是不是就是一个真实图片了

485
00:12:10,900 --> 00:12:12,660
而且打包的时候也告诉我了

486
00:12:12,660 --> 00:12:13,720
又发射一张图片

487
00:12:13,720 --> 00:12:14,240
是这个纸

488
00:12:14,240 --> 00:12:15,680
那现在好了

489
00:12:15,680 --> 00:12:17,860
我们就实现了一个完整的

490
00:12:17,860 --> 00:12:19,380
UI loader和file loader

