1
00:00:00,000 --> 00:00:02,240
接下来呢

2
00:00:02,240 --> 00:00:05,100
我们就来说一下这个vipac中的比较重要的一个点

3
00:00:05,100 --> 00:00:05,960
那同样呢

4
00:00:05,960 --> 00:00:08,400
我们先把刚才加的这些插件先住掉啊

5
00:00:08,400 --> 00:00:09,860
为了防止营销我们的代码

6
00:00:09,860 --> 00:00:11,240
那同样这个里面呢

7
00:00:11,240 --> 00:00:13,100
我接着改成我们的index.tml

8
00:00:13,100 --> 00:00:15,340
我的clean这个插件我也先删掉啊

9
00:00:15,340 --> 00:00:18,120
那我现在把这个doc也没用了就删掉了

10
00:00:18,120 --> 00:00:20,800
那现在我们可以怎么样呢

11
00:00:20,800 --> 00:00:23,980
是自己写个复端来实现这个跨域功能是吧

12
00:00:23,980 --> 00:00:25,780
那我们肯定需要写个server

13
00:00:25,780 --> 00:00:26,700
这里呢

14
00:00:26,700 --> 00:00:28,000
因为我们的epress啊

15
00:00:28,000 --> 00:00:30,440
默认他会起个webpack dv server

16
00:00:30,440 --> 00:00:30,640
哎

17
00:00:30,640 --> 00:00:31,260
其实啊

18
00:00:31,260 --> 00:00:33,460
他内部就自己带了一个什么

19
00:00:33,460 --> 00:00:34,640
e price

20
00:00:34,640 --> 00:00:35,160
e price呢

21
00:00:35,160 --> 00:00:36,640
就是node的一个框架对吧

22
00:00:36,640 --> 00:00:38,740
他可以帮我们实现写服务端的功能

23
00:00:38,740 --> 00:00:39,300
那这里呢

24
00:00:39,300 --> 00:00:41,200
我们就写一下了

25
00:00:41,200 --> 00:00:42,700
对不懂也没关系

26
00:00:42,700 --> 00:00:44,100
也相当于起个服务哎

27
00:00:44,100 --> 00:00:46,660
其实大致代码还是比较容易懂的

28
00:00:46,660 --> 00:00:47,100
这里呢

29
00:00:47,100 --> 00:00:48,000
我们来个app

30
00:00:48,000 --> 00:00:49,160
叫e price怎么样

31
00:00:49,160 --> 00:00:49,960
这行

32
00:00:49,960 --> 00:00:52,640
完了我们说写个服务端要先听一个什么

33
00:00:52,640 --> 00:00:54,100
端口号3000

34
00:00:54,100 --> 00:00:54,760
完了之后呢

35
00:00:54,760 --> 00:00:56,440
我们就可以写这样个接口

36
00:00:56,440 --> 00:00:57,700
比如说app

37
00:00:57,700 --> 00:01:02,560
第二什么的gat当你访问比如杠API完了杠我们user的时候呢

38
00:01:02,560 --> 00:01:04,800
他就会怎么样触发这样一个回调

39
00:01:04,800 --> 00:01:05,960
那回调的时候呢

40
00:01:05,960 --> 00:01:07,800
他就有所谓的叫请求和响应

41
00:01:07,800 --> 00:01:08,760
往那里面呢

42
00:01:08,760 --> 00:01:11,060
我就直接抛出来一个结果就好了

43
00:01:11,060 --> 00:01:14,200
我就给个结果叫内是珠峰架构

44
00:01:14,200 --> 00:01:16,660
相当于我访问这个路径

45
00:01:16,660 --> 00:01:18,300
他就应该返回我这样一个数据

46
00:01:18,300 --> 00:01:19,040
那好了

47
00:01:19,040 --> 00:01:20,300
我就把这服务启动一下

48
00:01:20,300 --> 00:01:21,600
告诉我有问题是吧

49
00:01:21,600 --> 00:01:22,660
我把这个东西关掉

50
00:01:22,660 --> 00:01:23,900
重新启动

51
00:01:23,900 --> 00:01:24,500
如何

52
00:01:24,500 --> 00:01:26,340
往这此时我们可以在这里呢

53
00:01:26,340 --> 00:01:27,000
直接访问

54
00:01:27,000 --> 00:01:28,640
比如说叫localhouse多少呢

55
00:01:28,640 --> 00:01:29,600
是3000吧

56
00:01:29,600 --> 00:01:31,740
3000端口localhouse

57
00:01:31,740 --> 00:01:32,500
放在这里呢

58
00:01:32,500 --> 00:01:33,140
3000

59
00:01:33,140 --> 00:01:37,440
-我们的API-右手是吧

60
00:01:37,440 --> 00:01:39,240
那现在是不是就可以拿到结果了

61
00:01:39,240 --> 00:01:41,740
那现在我们就可以在我们的代码里怎么样

62
00:01:41,740 --> 00:01:42,880
是不是发这样一个请求

63
00:01:42,880 --> 00:01:44,360
请求我们的3000端口啊

64
00:01:44,360 --> 00:01:45,120
那这样的话呢

65
00:01:45,120 --> 00:01:47,460
我们就直接在我们这个呃

66
00:01:47,460 --> 00:01:48,360
配置里面加上

67
00:01:48,360 --> 00:01:49,360
index里面

68
00:01:49,360 --> 00:01:50,460
我把这个代码先住掉

69
00:01:50,460 --> 00:01:51,360
没有用了

70
00:01:51,360 --> 00:01:52,860
往那里面呢

71
00:01:52,860 --> 00:01:55,700
我就创建这样一个adx等于new

72
00:01:55,700 --> 00:01:56,900
一个xml

73
00:01:56,900 --> 00:01:59,160
http request

74
00:01:59,160 --> 00:02:01,600
完了里面呢

75
00:02:01,600 --> 00:02:03,600
我可以通过叉叉的open

76
00:02:03,600 --> 00:02:05,500
来开启这样一个请求

77
00:02:05,500 --> 00:02:06,300
完了里面呢

78
00:02:06,300 --> 00:02:08,100
我给他一个get的方法

79
00:02:08,100 --> 00:02:11,400
路径呢是告API告我们的user

80
00:02:11,400 --> 00:02:14,400
后面呢是否一步一步

81
00:02:14,400 --> 00:02:15,200
完了并且呢

82
00:02:15,200 --> 00:02:16,800
我们当他成功以后呢

83
00:02:16,800 --> 00:02:19,400
想把他成功后的结果呢打印出来

84
00:02:19,400 --> 00:02:19,800
这里呢

85
00:02:19,800 --> 00:02:21,600
我就可以去打印totallog

86
00:02:21,600 --> 00:02:25,200
比如说就叫他叉叉叉的response

87
00:02:25,200 --> 00:02:26,200
他想的结果

88
00:02:26,200 --> 00:02:27,100
最后别忘了

89
00:02:27,100 --> 00:02:29,300
我要把这个阿达克斯怎么样发出去

90
00:02:29,300 --> 00:02:32,120
这是阿达克斯的四部曲是吧

91
00:02:32,120 --> 00:02:34,360
那现在我们把它呢重新运行一下

92
00:02:34,360 --> 00:02:35,600
OK运行一下

93
00:02:35,600 --> 00:02:36,160
run dv

94
00:02:36,160 --> 00:02:38,420
这里面我们要注意一点

95
00:02:38,420 --> 00:02:40,320
现在我这样写的话呢

96
00:02:40,320 --> 00:02:42,900
是不是默认访问的路径就是agp

97
00:02:42,900 --> 00:02:44,980
孟后刚刚localhouse多少了

98
00:02:44,980 --> 00:02:47,260
是不是相当于8080端口了

99
00:02:47,260 --> 00:02:50,340
但是我们这个服务端写的是3000端口

100
00:02:50,340 --> 00:02:52,080
那我们就需要怎么样

101
00:02:52,080 --> 00:02:54,300
是不是你说你可以把它怎么样写死

102
00:02:54,300 --> 00:02:55,600
那你要写死的话

103
00:02:55,600 --> 00:02:57,020
是不是就肯定会跨域了

104
00:02:57,020 --> 00:02:58,400
那我们肯定怎么样

105
00:02:58,400 --> 00:02:59,960
我说了人家服务端可能说

106
00:02:59,960 --> 00:03:01,400
哎我不想帮你处理这个跨域

107
00:03:01,400 --> 00:03:02,660
你自己来处理吧

108
00:03:02,660 --> 00:03:04,120
那这时候我们就怎么样

109
00:03:04,120 --> 00:03:05,880
哎先发到我的8080

110
00:03:05,880 --> 00:03:07,300
这个8080啊

111
00:03:07,300 --> 00:03:08,260
是Wipec

112
00:03:08,260 --> 00:03:10,200
DV server的这个服务

113
00:03:10,200 --> 00:03:10,540
对吧

114
00:03:10,540 --> 00:03:13,380
DV server的服务

115
00:03:13,380 --> 00:03:14,240
我可以干嘛呢

116
00:03:14,240 --> 00:03:15,920
把这个他的这个请求

117
00:03:15,920 --> 00:03:17,040
再转发给谁啊

118
00:03:17,040 --> 00:03:18,080
转发给这个3000

119
00:03:18,080 --> 00:03:19,920
是不是这问题就解决了

120
00:03:19,920 --> 00:03:21,800
哎那这时候非常方便啊

121
00:03:21,800 --> 00:03:22,560
那怎么配呢

122
00:03:22,560 --> 00:03:23,740
那现在肯定不行

123
00:03:23,740 --> 00:03:24,820
肯定会告诉我这玩意儿

124
00:03:24,820 --> 00:03:25,740
找不到是吧

125
00:03:25,740 --> 00:03:26,580
我就访问一下

126
00:03:26,580 --> 00:03:28,200
是8080刷新

127
00:03:28,200 --> 00:03:30,080
告诉我是不是8080上

128
00:03:30,080 --> 00:03:31,600
没有API-user

129
00:03:31,600 --> 00:03:32,520
因为它是3000上的

130
00:03:32,520 --> 00:03:35,100
这时候我可以怎么做

131
00:03:35,100 --> 00:03:36,480
我就需要配代理了

132
00:03:36,480 --> 00:03:38,500
其实以前我们在Node课程中

133
00:03:38,500 --> 00:03:39,340
其实有讲过

134
00:03:39,340 --> 00:03:40,060
代理的话

135
00:03:40,060 --> 00:03:41,400
主要靠的就是这样一个叫

136
00:03:41,400 --> 00:03:44,100
叫httpproxy这样一个模块

137
00:03:44,100 --> 00:03:47,380
同样在我们的WinePack里面

138
00:03:47,380 --> 00:03:48,800
也可以配置这样一个模块

139
00:03:48,800 --> 00:03:51,140
我们可以在配转件里

140
00:03:51,140 --> 00:03:52,240
再加一条

141
00:03:52,240 --> 00:03:54,540
肯定跟开发服务器有关

142
00:03:54,540 --> 00:03:55,840
哎这样来写

143
00:03:55,840 --> 00:03:57,240
往那里面呢

144
00:03:57,240 --> 00:03:59,540
我就可以加一个属性叫什么叫proxy

145
00:03:59,540 --> 00:04:01,640
比如说我访问的是什么呢

146
00:04:01,640 --> 00:04:03,840
是不是如果是-api的话

147
00:04:03,840 --> 00:04:06,140
那是不是相当于就找到了这个路径了

148
00:04:06,140 --> 00:04:07,240
你看一片开头了

149
00:04:07,240 --> 00:04:08,800
那好了我就怎么办呢

150
00:04:08,800 --> 00:04:10,580
我就跟你说你就去哪找啊

151
00:04:10,580 --> 00:04:12,860
ATP冒号刚刚local号多少

152
00:04:12,860 --> 00:04:15,700
local号三千

153
00:04:15,700 --> 00:04:17,640
这相当于就是配置了一个什么

154
00:04:17,640 --> 00:04:20,560
配置了一个一个代理

155
00:04:20,560 --> 00:04:21,680
名字叫proxy

156
00:04:21,680 --> 00:04:22,080
是吧

157
00:04:22,080 --> 00:04:23,520
相当于我访问API开头了

158
00:04:23,520 --> 00:04:24,520
那好都去哪

159
00:04:24,520 --> 00:04:27,360
3,000去找,找什么呢?找API的User

160
00:04:27,360 --> 00:04:31,060
这里我配好以后,我再重新运行

161
00:04:31,060 --> 00:04:33,440
其实非常简单,你可以配就OK了

162
00:04:33,440 --> 00:04:35,120
这就是Wipeg配跨域

163
00:04:35,120 --> 00:04:37,300
这里我再刷新

164
00:04:37,300 --> 00:04:40,200
看是不是结果拿到了

165
00:04:40,200 --> 00:04:42,720
同样,刚才我们想的挺好的

166
00:04:42,720 --> 00:04:43,880
这里面加个API

167
00:04:43,880 --> 00:04:46,440
但是人家后端一般都会怎么样

168
00:04:46,440 --> 00:04:47,840
不会加这个API

169
00:04:47,840 --> 00:04:49,540
你说这时候你要怎么写

170
00:04:49,540 --> 00:04:51,160
你不能把所有的后端接口

171
00:04:51,160 --> 00:04:52,540
我杠User放在这

172
00:04:52,540 --> 00:04:53,600
我杠一个杠一个

173
00:04:53,600 --> 00:04:54,680
是不是要加很多呀

174
00:04:54,680 --> 00:04:55,500
那很麻烦

175
00:04:55,500 --> 00:04:57,020
那这时候怎么样呢

176
00:04:57,020 --> 00:04:58,120
其实我可以这样做

177
00:04:58,120 --> 00:04:59,780
就是我请求的时候啊

178
00:04:59,780 --> 00:05:01,100
还是以这个API开头

179
00:05:01,100 --> 00:05:02,980
只是我转发过去的时候

180
00:05:02,980 --> 00:05:04,100
我把这API怎么样

181
00:05:04,100 --> 00:05:06,180
是不是去掉就可以了

182
00:05:06,180 --> 00:05:07,040
就这样一个写法

183
00:05:07,040 --> 00:05:08,840
为了表明我这更新了

184
00:05:08,840 --> 00:05:09,540
我得打个E

185
00:05:09,540 --> 00:05:10,980
我把我这服务端呀

186
00:05:10,980 --> 00:05:11,820
重新启动一下

187
00:05:11,820 --> 00:05:13,180
乱一下

188
00:05:13,180 --> 00:05:14,780
那这里面我再来看看

189
00:05:14,780 --> 00:05:15,460
回到这

190
00:05:15,460 --> 00:05:17,780
那我们现在访问的还是以什么

191
00:05:17,780 --> 00:05:19,040
是不是API开头

192
00:05:19,040 --> 00:05:20,160
因为这个代码啊

193
00:05:20,160 --> 00:05:21,240
还是API-user

194
00:05:21,240 --> 00:05:22,540
但是啊

195
00:05:22,540 --> 00:05:23,540
在我们写的时候呢

196
00:05:23,540 --> 00:05:24,740
目的地还没变是吧

197
00:05:24,740 --> 00:05:25,980
是他那个对象

198
00:05:25,980 --> 00:05:26,940
是吧

199
00:05:26,940 --> 00:05:29,680
这里面来个他改的对象

200
00:05:29,680 --> 00:05:31,940
但是我们在发送请求之后

201
00:05:31,940 --> 00:05:32,980
还需要干嘛

202
00:05:32,980 --> 00:05:36,040
就把我们的API怎么样重写掉

203
00:05:36,040 --> 00:05:37,100
这时候我们叫他啥呢

204
00:05:37,100 --> 00:05:40,600
叫他 rewrite rewrite rewrite

205
00:05:40,600 --> 00:05:43,900
重写路径对吧

206
00:05:43,900 --> 00:05:45,700
应该叫pass rewrite

207
00:05:45,700 --> 00:05:49,740
pass rewrite

208
00:05:49,740 --> 00:05:50,180
好了

209
00:05:50,180 --> 00:05:50,980
怎么重写呢

210
00:05:50,980 --> 00:05:53,180
我就把API怎么样重写成空

211
00:05:53,180 --> 00:05:54,020
就可以了

212
00:05:54,020 --> 00:05:55,420
那现在我们在这来个空

213
00:05:55,420 --> 00:05:57,140
pass write 空

214
00:05:57,140 --> 00:05:59,580
那现在我们来看看这样靠谱

215
00:05:59,580 --> 00:06:00,920
能不能把 API 干掉

216
00:06:00,920 --> 00:06:02,120
再发这样一个请求

217
00:06:02,120 --> 00:06:03,780
这里一样是吧

218
00:06:03,780 --> 00:06:04,640
重新启动

219
00:06:04,640 --> 00:06:06,480
软 dv

220
00:06:06,480 --> 00:06:08,620
啊来试一下是吧

221
00:06:08,620 --> 00:06:09,340
哦有点

222
00:06:09,340 --> 00:06:10,640
报错了是吧

223
00:06:10,640 --> 00:06:11,980
他说这个配置有问题

224
00:06:11,980 --> 00:06:12,880
他说他应该是个对象

225
00:06:12,880 --> 00:06:13,440
ok

226
00:06:13,440 --> 00:06:14,420
那对象对吧

227
00:06:14,420 --> 00:06:15,340
这里面我来个对象

228
00:06:15,340 --> 00:06:16,140
我说里面呢

229
00:06:16,140 --> 00:06:16,720
我应该怎么办

230
00:06:16,720 --> 00:06:18,980
是把杠 API 提完成空是吧

231
00:06:18,980 --> 00:06:20,280
人家说的挺明确的啊

232
00:06:20,280 --> 00:06:21,640
这样写的话就怎么样

233
00:06:21,640 --> 00:06:23,740
相当于把我们这个路径干掉了

234
00:06:23,740 --> 00:06:25,240
再来一次是吧

235
00:06:25,240 --> 00:06:27,340
问第1位

236
00:06:27,340 --> 00:06:31,000
启动了是吧

237
00:06:31,000 --> 00:06:32,640
8080来看一看

238
00:06:32,640 --> 00:06:33,940
这个一能不能拿到呢

239
00:06:33,940 --> 00:06:35,140
是不是就拿到了

240
00:06:35,140 --> 00:06:36,840
那这样的话我们就知道了

241
00:06:36,840 --> 00:06:37,240
OK

242
00:06:37,240 --> 00:06:38,240
我们可以怎么样

243
00:06:38,240 --> 00:06:40,540
是不是通过这样一个重写的方式

244
00:06:40,540 --> 00:06:40,940
对吧

245
00:06:40,940 --> 00:06:43,140
重写的方式对吧

246
00:06:43,140 --> 00:06:44,340
把请求怎么样

247
00:06:44,340 --> 00:06:48,040
请求代理给叫代理到是吧

248
00:06:48,240 --> 00:06:51,820
代理到我们的epress服务器上

249
00:06:51,820 --> 00:06:54,900
那当然了

250
00:06:54,900 --> 00:06:56,100
这是前提是什么

251
00:06:56,100 --> 00:06:58,660
是不是这个服务器是别人提供好吧

252
00:06:58,660 --> 00:07:00,960
那有的时候可能我们需要干嘛呢

253
00:07:00,960 --> 00:07:02,280
哎不需要代理

254
00:07:02,280 --> 00:07:03,240
我们要干什么事呢

255
00:07:03,240 --> 00:07:04,160
就是前端呀

256
00:07:04,160 --> 00:07:05,580
想默克一些数据

257
00:07:05,580 --> 00:07:06,600
这是第一种写法

258
00:07:06,600 --> 00:07:07,080
对吧

259
00:07:07,080 --> 00:07:08,520
那肯定还有什么的第二种

260
00:07:08,520 --> 00:07:11,300
就是我们的我们前端

261
00:07:11,300 --> 00:07:14,340
前端只想单纯什么

262
00:07:14,340 --> 00:07:18,000
只想单纯来模拟数据

263
00:07:18,240 --> 00:07:22,120
想单纯来模拟数据

264
00:07:22,120 --> 00:07:24,020
这时候我要怎么处理

265
00:07:24,020 --> 00:07:24,820
其实也很简单

266
00:07:24,820 --> 00:07:25,980
因为我们说了

267
00:07:25,980 --> 00:07:27,920
它内部本来就是个Epress

268
00:07:27,920 --> 00:07:29,060
我就可以怎么样

269
00:07:29,060 --> 00:07:31,700
在这里面直接用它的Epress来写些接口

270
00:07:31,700 --> 00:07:32,460
好了

271
00:07:32,460 --> 00:07:32,840
同样

272
00:07:32,840 --> 00:07:34,880
我可以在这里写个笔付方法

273
00:07:34,880 --> 00:07:35,720
这是人家怎么样

274
00:07:35,720 --> 00:07:38,060
提供的一个方法

275
00:07:38,060 --> 00:07:40,440
提供的方法

276
00:07:40,440 --> 00:07:42,260
相当于在启动服务之前

277
00:07:42,260 --> 00:07:43,500
我可以掉下这方法

278
00:07:43,500 --> 00:07:44,440
相当于钩子一样

279
00:07:44,440 --> 00:07:45,680
这可能就是钩子

280
00:07:45,680 --> 00:07:47,160
到时候我写上

281
00:07:47,160 --> 00:07:47,780
它会自己去掉

282
00:07:47,780 --> 00:07:49,720
那调用的时候会给我传个APP

283
00:07:49,720 --> 00:07:51,180
这个APP啊

284
00:07:51,180 --> 00:07:53,120
其实就是我们刚才这个sower的什么

285
00:07:53,120 --> 00:07:54,100
这个APP

286
00:07:54,100 --> 00:07:54,880
那好了

287
00:07:54,880 --> 00:07:55,640
我把这代码怎么样

288
00:07:55,640 --> 00:07:56,560
你就想到了是不是

289
00:07:56,560 --> 00:07:57,080
站到这来

290
00:07:57,080 --> 00:07:58,080
完了这里呢

291
00:07:58,080 --> 00:07:58,800
我格式化一下

292
00:07:58,800 --> 00:08:00,500
格式化不生效了啊

293
00:08:00,500 --> 00:08:01,400
锁中锁进

294
00:08:01,400 --> 00:08:02,960
那这里面就写上一个叫什么

295
00:08:02,960 --> 00:08:03,500
叫before

296
00:08:03,500 --> 00:08:05,760
那看看现在啊

297
00:08:05,760 --> 00:08:06,400
行不行是吧

298
00:08:06,400 --> 00:08:08,040
相当于我访问的时候啊

299
00:08:08,040 --> 00:08:09,280
说直接写杠优子

300
00:08:09,280 --> 00:08:10,480
那这里面就可以怎么样

301
00:08:10,480 --> 00:08:12,200
是大大方方的来个什么

302
00:08:12,200 --> 00:08:12,860
杠优子

303
00:08:12,860 --> 00:08:14,400
那现在啊

304
00:08:14,400 --> 00:08:14,880
是不是相当于

305
00:08:14,880 --> 00:08:16,280
我我访问这个杠优子

306
00:08:16,280 --> 00:08:19,700
访问的是E-PressDV Server里的E-Press

307
00:08:19,700 --> 00:08:21,880
此时它会通过这个接口怎么样

308
00:08:21,880 --> 00:08:22,680
发挥结果

309
00:08:22,680 --> 00:08:23,460
来试试

310
00:08:23,460 --> 00:08:25,400
现在是不是相当于就没有快遇了

311
00:08:25,400 --> 00:08:27,200
因为没有我们的所有的服务了

312
00:08:27,200 --> 00:08:28,060
就这两者

313
00:08:28,060 --> 00:08:28,840
好

314
00:08:28,840 --> 00:08:30,240
这里一样运行

315
00:08:30,240 --> 00:08:33,560
当然不用再乱了

316
00:08:33,560 --> 00:08:35,360
这Servor已经关掉就可以了

317
00:08:35,360 --> 00:08:35,800
stop

318
00:08:35,800 --> 00:08:38,080
把它重启动一下

319
00:08:38,080 --> 00:08:39,720
这文件更改了

320
00:08:39,720 --> 00:08:40,380
重启一下

321
00:08:40,380 --> 00:08:43,140
行不行

322
00:08:43,140 --> 00:08:44,040
8080

323
00:08:44,040 --> 00:08:45,580
完这里我就直接访问

324
00:08:45,580 --> 00:08:45,980
8080

325
00:08:45,980 --> 00:08:46,380
刷新

326
00:08:46,380 --> 00:08:48,980
看看是不是也拿到了这个before

327
00:08:48,980 --> 00:08:50,300
那这相当于什么

328
00:08:50,300 --> 00:08:52,760
是不是相当于前端自己模拟数据

329
00:08:52,760 --> 00:08:53,840
我来实现一些功能

330
00:08:53,840 --> 00:08:56,720
那但是我们其实有的时候还是怎么样

331
00:08:56,720 --> 00:08:58,180
还是有服务端的

332
00:08:58,180 --> 00:08:58,500
对吧

333
00:08:58,500 --> 00:08:59,300
就是第三种情况

334
00:08:59,300 --> 00:09:01,720
反正每种情况都有自己的特点

335
00:09:01,720 --> 00:09:01,980
是吧

336
00:09:01,980 --> 00:09:03,740
第三种就是有服务端

337
00:09:03,740 --> 00:09:06,640
有服务端

338
00:09:06,640 --> 00:09:08,260
但是有服务端

339
00:09:08,260 --> 00:09:09,120
我不想怎么样

340
00:09:09,120 --> 00:09:09,880
是不是用

341
00:09:09,880 --> 00:09:11,180
不想怎么样

342
00:09:11,180 --> 00:09:12,900
不想用这个代理来处理

343
00:09:12,900 --> 00:09:15,820
不想用代理来处理

344
00:09:15,820 --> 00:09:17,540
那我想到什么了呢

345
00:09:17,540 --> 00:09:18,480
能不能对吧

346
00:09:18,480 --> 00:09:20,560
能不能在服务端中

347
00:09:20,560 --> 00:09:22,620
中启动WIPPAC

348
00:09:22,620 --> 00:09:24,920
并且呢

349
00:09:24,920 --> 00:09:26,040
我们这个端口对吧

350
00:09:26,040 --> 00:09:26,720
端口用谁的

351
00:09:26,720 --> 00:09:28,900
端口用WIPPAC端口

352
00:09:28,900 --> 00:09:32,140
比如说应该是端口不用WIPPAC

353
00:09:32,140 --> 00:09:33,560
应该用服务端端口是吧

354
00:09:33,560 --> 00:09:34,640
服务端端口

355
00:09:34,640 --> 00:09:36,500
相当于啊

356
00:09:36,500 --> 00:09:37,880
前端和服务端怎么样

357
00:09:37,880 --> 00:09:39,320
启动在一个端口上

358
00:09:39,320 --> 00:09:40,400
那这样的话

359
00:09:40,400 --> 00:09:41,800
是不是也不会有跨越问题啊

360
00:09:41,800 --> 00:09:43,120
相当于我在服务端里面

361
00:09:43,120 --> 00:09:44,060
启动了一个页面

362
00:09:44,060 --> 00:09:45,020
那页面里怎么样

363
00:09:45,020 --> 00:09:46,520
就可以反问我的对吧

364
00:09:46,520 --> 00:09:47,860
比如说的接口哎

365
00:09:47,860 --> 00:09:49,380
那这样呢怎么写是吧

366
00:09:49,380 --> 00:09:50,880
那这时候我们就需要干嘛呢

367
00:09:50,880 --> 00:09:51,520
这里呀

368
00:09:51,520 --> 00:09:53,360
我就不能在这运行webpack了

369
00:09:53,360 --> 00:09:54,480
我应该运行的谁呀

370
00:09:54,480 --> 00:09:55,480
是不是服务端呀

371
00:09:55,480 --> 00:09:56,480
而且服务端呢

372
00:09:56,480 --> 00:09:57,920
还要把这个webpack怎么样

373
00:09:57,920 --> 00:09:59,020
启动编译

374
00:09:59,020 --> 00:10:00,020
那这时候怎么做呢

375
00:10:00,020 --> 00:10:00,660
同样啊

376
00:10:00,660 --> 00:10:01,660
我说了webpack呢

377
00:10:01,660 --> 00:10:03,560
其实它就是一个模块

378
00:10:03,560 --> 00:10:05,420
那就先把这个webpack拿到

379
00:10:05,420 --> 00:10:07,480
webpack是吧

380
00:10:07,480 --> 00:10:09,360
相当于我在服务端里面怎么样

381
00:10:09,360 --> 00:10:11,420
自己来起这个webpack

382
00:10:11,420 --> 00:10:14,080
当然了我们这里面说了

383
00:10:14,080 --> 00:10:16,840
你要想启动的这个中间的时候怎么样

384
00:10:16,840 --> 00:10:17,620
插一刀

385
00:10:17,620 --> 00:10:19,820
那肯定需要一些所谓的叫中间键

386
00:10:19,820 --> 00:10:20,240
是吧

387
00:10:20,240 --> 00:10:21,640
这是一块子的一个特点

388
00:10:21,640 --> 00:10:22,480
中间键

389
00:10:22,480 --> 00:10:23,620
中间键怎么来呢

390
00:10:23,620 --> 00:10:25,720
他这个中间键也是外派的提供的

391
00:10:25,720 --> 00:10:27,920
他叫压二的叫外派

392
00:10:27,920 --> 00:10:29,480
-dv-什么

393
00:10:29,480 --> 00:10:30,980
没头外啥意思

394
00:10:30,980 --> 00:10:33,580
就是外派可开发服务的一个中间键

395
00:10:33,580 --> 00:10:34,340
他可以啊

396
00:10:34,340 --> 00:10:37,720
在我们服务端启动外派可来个杠大定

397
00:10:37,720 --> 00:10:39,440
那这里面

398
00:10:39,440 --> 00:10:40,740
然后我就可以怎么样做呢

399
00:10:40,740 --> 00:10:41,680
是不是引进来

400
00:10:41,680 --> 00:10:41,940
对吧

401
00:10:41,940 --> 00:10:43,080
这个非常简单啊

402
00:10:43,080 --> 00:10:44,680
medal=require

403
00:10:44,680 --> 00:10:44,980
对吧

404
00:10:44,980 --> 00:10:48,180
叫ypec-dv-medal wire

405
00:10:48,180 --> 00:10:48,680
是吧

406
00:10:48,680 --> 00:10:49,880
名字挺长的

407
00:10:49,880 --> 00:10:50,980
那这时候我们说了

408
00:10:50,980 --> 00:10:53,480
其实ypec它的大致的流程是这样的

409
00:10:53,480 --> 00:10:54,580
我需要怎么样呢

410
00:10:54,580 --> 00:10:57,180
先通过ypec拿到我们的配置对象

411
00:10:57,180 --> 00:10:58,280
我来教给它怎么样

412
00:10:58,280 --> 00:10:59,280
ypec来处理

413
00:10:59,280 --> 00:11:00,580
它会产生一个编译对象

414
00:11:00,580 --> 00:11:02,280
完了我们把编译对象呢

415
00:11:02,280 --> 00:11:03,780
扔给这样一个中间间

416
00:11:03,780 --> 00:11:04,580
就OK了

417
00:11:04,580 --> 00:11:06,380
你看大致这样一个写法

418
00:11:06,380 --> 00:11:07,180
咱们先这样用

419
00:11:07,180 --> 00:11:08,680
后面咱们讲圆满的时候

420
00:11:08,680 --> 00:11:10,280
会对它怎么样有所掌握

421
00:11:10,280 --> 00:11:11,580
那大致这样呢

422
00:11:11,580 --> 00:11:12,180
第一步

423
00:11:12,180 --> 00:11:13,740
我需要拿到这个config

424
00:11:13,740 --> 00:11:15,060
config怎么来的呢

425
00:11:15,060 --> 00:11:16,300
是不是就是我们这样一个

426
00:11:16,300 --> 00:11:19,380
wipac.config.js文件

427
00:11:19,380 --> 00:11:22,540
那有了config以后怎么样呢

428
00:11:22,540 --> 00:11:24,260
是不是我需要用这个wipac怎么样

429
00:11:24,260 --> 00:11:25,940
来处理这个配置文件

430
00:11:25,940 --> 00:11:27,140
把这个config往里放

431
00:11:27,140 --> 00:11:28,780
处理

432
00:11:28,780 --> 00:11:29,900
那处理好以后啊

433
00:11:29,900 --> 00:11:30,540
我们怎么样

434
00:11:30,540 --> 00:11:32,460
是不是他返回的就应该是一个

435
00:11:32,460 --> 00:11:34,140
所谓的叫编译后的结果

436
00:11:34,140 --> 00:11:35,380
叫compiler

437
00:11:35,380 --> 00:11:36,940
这里面明确

438
00:11:36,940 --> 00:11:38,020
require这个文件什么意思

439
00:11:38,020 --> 00:11:39,180
就是把这个对象怎么样

440
00:11:39,180 --> 00:11:39,740
拿到

441
00:11:39,740 --> 00:11:40,980
拿到以后呢

442
00:11:40,980 --> 00:11:42,140
完了通过wipac处理

443
00:11:42,140 --> 00:11:43,220
处理成这样一个东西

444
00:11:43,220 --> 00:11:44,340
完了通过它呢

445
00:11:44,340 --> 00:11:44,580
怎么样

446
00:11:44,580 --> 00:11:47,400
我再去用这个加载叫bp.use

447
00:11:47,400 --> 00:11:49,540
使用这个中间键叫medal

448
00:11:49,540 --> 00:11:51,420
那用中间键的时候呢

449
00:11:51,420 --> 00:11:53,260
我们还需要把这computer怎么样

450
00:11:53,260 --> 00:11:54,200
也传过来

451
00:11:54,200 --> 00:11:55,260
哎

452
00:11:55,260 --> 00:11:56,500
大致这样就可以了

453
00:11:56,500 --> 00:11:58,020
相当于我们运行私信端口

454
00:11:58,020 --> 00:11:59,260
它会连带着怎么样

455
00:11:59,260 --> 00:12:01,100
帮我们把webpack也启动了

456
00:12:01,100 --> 00:12:02,540
再来试一下啊

457
00:12:02,540 --> 00:12:02,860
这里

458
00:12:02,860 --> 00:12:05,400
叫npm run

459
00:12:05,400 --> 00:12:07,620
应该就直接执行服务端了

460
00:12:07,620 --> 00:12:09,420
node就是server.js

461
00:12:09,420 --> 00:12:11,180
那这时候会怎么做

462
00:12:11,180 --> 00:12:13,060
哎

463
00:12:13,060 --> 00:12:13,660
你看啊

464
00:12:13,660 --> 00:12:16,000
这里面就不单单好像是刚才那个代码了吧

465
00:12:16,000 --> 00:12:17,940
是不是还相当于启动了一个webpack呀

466
00:12:17,940 --> 00:12:18,780
而且呢

467
00:12:18,780 --> 00:12:20,180
我们这里面也可以访问啊

468
00:12:20,180 --> 00:12:21,120
比如访问这个-user

469
00:12:21,120 --> 00:12:22,180
看怎么访问到啊

470
00:12:22,180 --> 00:12:22,860
楼口号3000

471
00:12:22,860 --> 00:12:25,380
-3000-user

472
00:12:25,380 --> 00:12:27,520
是不是可以拿到

473
00:12:27,520 --> 00:12:28,940
是不是这个一呀

474
00:12:28,940 --> 00:12:29,420
没问题

475
00:12:29,420 --> 00:12:30,460
那同样啊

476
00:12:30,460 --> 00:12:31,640
如果我们在这里呢

477
00:12:31,640 --> 00:12:32,880
直接访问他是吧

478
00:12:32,880 --> 00:12:33,860
哎

479
00:12:33,860 --> 00:12:35,840
看看是不是也拿到这个结果了

480
00:12:35,840 --> 00:12:37,060
那访问的结果是谁

481
00:12:37,060 --> 00:12:40,160
是不是就是当前我们这个打包后这个目录下的什么

482
00:12:40,160 --> 00:12:40,980
那个indexmail

483
00:12:40,980 --> 00:12:43,020
ATML里面是不是就发了阿迦克斯

484
00:12:43,020 --> 00:12:44,460
是不是去获取这个GS了

485
00:12:44,460 --> 00:12:45,080
你看看是不是

486
00:12:45,080 --> 00:12:45,720
Network

487
00:12:45,720 --> 00:12:47,520
是不是三千下的后目

488
00:12:47,520 --> 00:12:48,620
这相当于什么

489
00:12:48,620 --> 00:12:50,480
后端加前端怎么样

490
00:12:50,480 --> 00:12:51,320
启动在一起

491
00:12:51,320 --> 00:12:53,360
也可以怎么样解决跨域问题

492
00:12:53,360 --> 00:12:56,400
这就是我们解决跨域的三种方式

493
00:12:56,400 --> 00:12:58,920
第一种就是我们所说的这种代理

494
00:12:58,920 --> 00:13:00,280
那会相当于

495
00:13:00,280 --> 00:13:01,400
就是服务端是别人的

496
00:13:01,400 --> 00:13:02,660
我需要怎么样代理过去

497
00:13:02,660 --> 00:13:05,300
第二种就是前端可以模拟一些数据

498
00:13:05,300 --> 00:13:06,360
第三种就是什么

499
00:13:06,360 --> 00:13:07,780
我服务端是自己写的

500
00:13:07,780 --> 00:13:08,980
我可以把前端代码怎么样

501
00:13:08,980 --> 00:13:10,720
也启动到服务端上

502
00:13:10,720 --> 00:13:11,920
那好

503
00:13:11,920 --> 00:13:13,680
我们的跨越就讲到这里

