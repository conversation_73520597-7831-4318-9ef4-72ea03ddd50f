1
00:00:00,000 --> 00:00:04,040
OK那刚才呢我们说了一下这个同步的勾子

2
00:00:04,040 --> 00:00:06,120
那现在呢我们把这个文件保存一下

3
00:00:06,120 --> 00:00:07,480
这样方便历史记录

4
00:00:07,480 --> 00:00:08,480
我来个history

5
00:00:08,480 --> 00:00:12,680
把这个1呢和和这个case的吧

6
00:00:12,680 --> 00:00:14,480
我把它名字也改一下叫1.case

7
00:00:14,480 --> 00:00:18,320
把这两个呢放去

8
00:00:18,320 --> 00:00:21,780
我们再基于啊刚才的代码来基于修改

9
00:00:21,780 --> 00:00:24,880
刚才啊我们说的这个勾子呢叫叫同步的勾子

10
00:00:24,880 --> 00:00:27,900
那我们这个同步的勾子啊还有一些比如说其他的勾子

11
00:00:27,900 --> 00:00:29,440
我们可以来呢看一下是吧

12
00:00:29,440 --> 00:00:31,720
这里面我们sync的勾字还有很多

13
00:00:31,720 --> 00:00:33,320
比如叫sync bell hook

14
00:00:33,320 --> 00:00:35,240
还有什么sync waterfall hook

15
00:00:35,240 --> 00:00:36,640
还有什么sync loop hook

16
00:00:36,640 --> 00:00:38,300
这些都是同步的

17
00:00:38,300 --> 00:00:40,580
所以相对来讲还是比较简单的

18
00:00:40,580 --> 00:00:41,520
我们来看一下

19
00:00:41,520 --> 00:00:43,420
我们先说一下sync bell hook

20
00:00:43,420 --> 00:00:44,400
这什么意思

21
00:00:44,400 --> 00:00:45,520
叫同步

22
00:00:45,520 --> 00:00:47,260
bell叫保险

23
00:00:47,260 --> 00:00:48,020
叫hook

24
00:00:48,020 --> 00:00:48,340
什么意思

25
00:00:48,340 --> 00:00:50,700
就是我们写同步的时候可以加上一个保险

26
00:00:50,700 --> 00:00:53,200
这个保险我们一般叫它熔断型的

27
00:00:53,200 --> 00:00:53,540
什么意思

28
00:00:53,540 --> 00:00:57,340
就是当我们在前面这样一个事件中注册的时候

29
00:00:57,340 --> 00:00:58,120
我们可以怎么样

30
00:00:58,120 --> 00:01:00,060
决定是否向下执行

31
00:01:00,060 --> 00:01:00,860
就叫保险

32
00:01:00,860 --> 00:01:02,080
如果OK

33
00:01:02,080 --> 00:01:03,640
他不OK的话怎么办

34
00:01:03,640 --> 00:01:04,400
就不向下执行了

35
00:01:04,400 --> 00:01:05,760
我们来看一下

36
00:01:05,760 --> 00:01:07,080
这里一样

37
00:01:07,080 --> 00:01:09,080
我们可以把这个钩子引进来

38
00:01:09,080 --> 00:01:10,100
叫Sync Biohook

39
00:01:10,100 --> 00:01:12,140
比如说当我们学习的过程中

40
00:01:12,140 --> 00:01:13,560
先学node再去react

41
00:01:13,560 --> 00:01:15,260
发现这过程中可能很难

42
00:01:15,260 --> 00:01:16,780
可能卡到这node是怎么样

43
00:01:16,780 --> 00:01:18,020
我就学不下去了

44
00:01:18,020 --> 00:01:20,240
这里面我们可能就会加一个东西

45
00:01:20,240 --> 00:01:21,640
出了一些状况

46
00:01:21,640 --> 00:01:23,040
写上出错了

47
00:01:23,040 --> 00:01:25,200
比如说学习

48
00:01:25,200 --> 00:01:27,280
想停止学习

49
00:01:27,280 --> 00:01:32,200
想停止学习

50
00:01:32,200 --> 00:01:32,900
OK

51
00:01:32,900 --> 00:01:36,400
那这里面我们new的时候就要new这个think bell hook

52
00:01:36,400 --> 00:01:37,860
那再来看看效果

53
00:01:37,860 --> 00:01:39,480
那这时候我在执行的时候

54
00:01:39,480 --> 00:01:40,540
那肯定是迟到这

55
00:01:40,540 --> 00:01:43,440
因为你返回了一个非undefined的值

56
00:01:43,440 --> 00:01:46,820
所以说当前世界执行到这里就不会再向下执行了

57
00:01:46,820 --> 00:01:47,480
OK

58
00:01:47,480 --> 00:01:49,100
那再来看看运行

59
00:01:49,100 --> 00:01:51,340
我来到这里面你看

60
00:01:51,340 --> 00:01:52,800
是不是走到no的这停了

61
00:01:52,800 --> 00:01:54,820
那比如说什么时候会正常呢

62
00:01:54,820 --> 00:01:56,780
比如说在这里你返回了一个undefined

63
00:01:56,780 --> 00:01:57,960
你看看效果

64
00:01:57,960 --> 00:01:59,000
那就是卡在这

65
00:01:59,000 --> 00:01:59,860
是不是继续往下走

66
00:01:59,860 --> 00:02:01,500
因为它反复的结果是按机犯

67
00:02:01,500 --> 00:02:03,540
这样一个结果也非常方便

68
00:02:03,540 --> 00:02:04,920
相当于我们可以在里面怎么样

69
00:02:04,920 --> 00:02:06,580
随时停止我们的这样一个

70
00:02:06,580 --> 00:02:07,960
监听的一个类型

71
00:02:07,960 --> 00:02:08,240
好了

72
00:02:08,240 --> 00:02:09,980
我们在这里也同样

73
00:02:09,980 --> 00:02:11,240
把这个方法加进去

74
00:02:11,240 --> 00:02:12,760
我们在这里改一下

75
00:02:12,760 --> 00:02:14,480
其他的关掉

76
00:02:14,480 --> 00:02:15,540
关闭其他

77
00:02:15,540 --> 00:02:19,040
这时候我们一样改一下名字

78
00:02:19,040 --> 00:02:21,000
就叫Sync Bell Hook

79
00:02:21,000 --> 00:02:22,800
再说一遍

80
00:02:22,800 --> 00:02:24,580
Bell代表的是保险的意思

81
00:02:24,580 --> 00:02:26,640
就是只要有任何一个监听函数

82
00:02:26,640 --> 00:02:28,760
返回了一个非undefine的结果

83
00:02:28,760 --> 00:02:29,840
那它就会怎么样

84
00:02:29,840 --> 00:02:30,580
停止执行

85
00:02:30,580 --> 00:02:32,460
往这里我们可以返回

86
00:02:32,460 --> 00:02:33,300
Legary 10

87
00:02:33,300 --> 00:02:34,520
比如写一个叫

88
00:02:34,520 --> 00:02:35,820
这是react对吧

89
00:02:35,820 --> 00:02:37,520
就是停一停是吧

90
00:02:37,520 --> 00:02:38,260
停一下

91
00:02:38,260 --> 00:02:39,840
停止执行对吧

92
00:02:39,840 --> 00:02:41,560
停止向下执行

93
00:02:41,560 --> 00:02:44,140
那刚才我们看到了

94
00:02:44,140 --> 00:02:46,240
那现在就不能上来就直接循环了

95
00:02:46,240 --> 00:02:47,680
应该先取出第一个

96
00:02:47,680 --> 00:02:49,280
看看第一个它没有返回值

97
00:02:49,280 --> 00:02:50,640
如果的返回值

98
00:02:50,640 --> 00:02:52,300
并不是我们所谓的undefine

99
00:02:52,300 --> 00:02:53,720
那就应该停止

100
00:02:53,720 --> 00:02:55,180
所以说这时候我们写的时候

101
00:02:55,180 --> 00:02:57,720
只需要改一下我们这个靠方法的实现

102
00:02:57,720 --> 00:02:59,000
改一下

103
00:02:59,000 --> 00:03:01,440
首先我们肯定要先执行第一个函数

104
00:03:01,440 --> 00:03:04,060
第一个函数我们就是我们有很多对吧

105
00:03:04,060 --> 00:03:06,440
那我们需要先拿着速度第一个先执行一下

106
00:03:06,440 --> 00:03:08,300
OK再怎么样再执行第二个

107
00:03:08,300 --> 00:03:10,840
所以我们想到用的就是我们所谓的这个Doive

108
00:03:10,840 --> 00:03:14,060
就是我们怎么样要至少做一个

109
00:03:14,060 --> 00:03:15,720
看看这一个有没有返回值

110
00:03:15,720 --> 00:03:17,220
没有的话怎么样再执行

111
00:03:17,220 --> 00:03:20,480
OK那在这里我们需要有这样一个返回值

112
00:03:20,480 --> 00:03:21,660
Lite一个IT

113
00:03:21,660 --> 00:03:24,260
并且我们还需要一个所以

114
00:03:24,260 --> 00:03:31,360
这个所以表示的就是当前要先执行第一个

115
00:03:31,360 --> 00:03:33,920
这个IET代表的是什么

116
00:03:33,920 --> 00:03:36,700
就是当前这个函数的什么返回值

117
00:03:36,700 --> 00:03:40,700
当前这个函数的返回值

118
00:03:40,700 --> 00:03:44,720
OK

119
00:03:44,720 --> 00:03:46,020
那咱来看看吧

120
00:03:46,020 --> 00:03:46,820
看看效果

121
00:03:46,820 --> 00:03:50,180
我们应该先在这个task中取到第一个

122
00:03:50,180 --> 00:03:52,220
那我们去取方过号index

123
00:03:52,220 --> 00:03:53,860
拿到第一个

124
00:03:53,860 --> 00:03:55,700
那第一个以后我们需要干嘛呀

125
00:03:55,700 --> 00:03:56,260
让它执行

126
00:03:56,260 --> 00:03:57,220
执行的时候呢

127
00:03:57,220 --> 00:03:58,960
还要把这些参数怎么样传过去

128
00:03:58,960 --> 00:04:00,020
那我们需要怎么样

129
00:04:00,020 --> 00:04:00,020
是不是来个dx

130
00:04:00,020 --> 00:04:01,700
是不是要来个点点ARGS

131
00:04:01,700 --> 00:04:04,700
那此时我们执行完以后

132
00:04:04,700 --> 00:04:06,200
就能拿到这个函数的结果了

133
00:04:06,200 --> 00:04:07,080
那非常简单

134
00:04:07,080 --> 00:04:08,620
就让IT等于它

135
00:04:08,620 --> 00:04:10,620
当然了

136
00:04:10,620 --> 00:04:12,740
如果当前这个IT是什么呢

137
00:04:12,740 --> 00:04:14,780
IT他等等了我们拿地方

138
00:04:14,780 --> 00:04:15,800
那说明怎么样

139
00:04:15,800 --> 00:04:16,040
是不是

140
00:04:16,040 --> 00:04:17,200
well继续执行

141
00:04:17,200 --> 00:04:18,380
如果不是按地方

142
00:04:18,380 --> 00:04:19,520
那怎么样就卡住了

143
00:04:19,520 --> 00:04:20,700
当然了还有个特殊情况

144
00:04:20,700 --> 00:04:21,820
就是说如果呀

145
00:04:21,820 --> 00:04:23,600
我这都顺利执行了

146
00:04:23,600 --> 00:04:24,860
可能还有个问题对吧

147
00:04:24,860 --> 00:04:26,720
你每次是不是执行下一个的时候

148
00:04:26,720 --> 00:04:27,820
还需要干嘛呀

149
00:04:27,820 --> 00:04:28,140
加价

150
00:04:28,140 --> 00:04:29,700
第一个执行完

151
00:04:29,700 --> 00:04:30,320
执行第二个

152
00:04:30,320 --> 00:04:31,420
但是这时候会怎么样

153
00:04:31,420 --> 00:04:32,900
不停止执行的话会预计

154
00:04:32,900 --> 00:04:34,760
所以说我们应该还再来一下

155
00:04:34,760 --> 00:04:37,060
就是当前我们这个缩印

156
00:04:37,060 --> 00:04:39,320
和我们当前这样一个函数的个数怎么样

157
00:04:39,320 --> 00:04:40,560
说相等的时候

158
00:04:40,560 --> 00:04:42,080
我就应该停止往下走

159
00:04:42,080 --> 00:04:44,520
这时候我们就来个按的服务

160
00:04:44,520 --> 00:04:45,500
我们判断一下

161
00:04:45,500 --> 00:04:47,720
叫this的tax length

162
00:04:47,720 --> 00:04:49,860
好了和我们当前谁比呢

163
00:04:49,860 --> 00:04:52,640
和我们当前这个参数对吧

164
00:04:52,640 --> 00:04:54,160
叫index对吧

165
00:04:54,160 --> 00:04:56,140
第一次是0

166
00:04:56,140 --> 00:04:56,760
第二次是1

167
00:04:56,760 --> 00:04:58,720
如果它小于这个一个结果的话

168
00:04:58,720 --> 00:04:59,760
怎么样就停止执行

169
00:04:59,760 --> 00:05:01,980
这样个效果

170
00:05:01,980 --> 00:05:02,540
再来看看

171
00:05:02,540 --> 00:05:03,900
我不协助他的时候

172
00:05:03,900 --> 00:05:04,860
说和刚才一样

173
00:05:04,860 --> 00:05:06,760
应该串性执行

174
00:05:06,760 --> 00:05:08,280
这里面名字没有改

175
00:05:08,280 --> 00:05:09,100
把它改一下

176
00:05:09,100 --> 00:05:10,940
Sync Biohook

177
00:05:10,940 --> 00:05:12,220
这里又就不去处理了

178
00:05:12,220 --> 00:05:13,100
其实就是个参数

179
00:05:13,100 --> 00:05:14,260
但是没有什么意义

180
00:05:14,260 --> 00:05:14,980
运行一下

181
00:05:14,980 --> 00:05:17,540
两个

182
00:05:17,540 --> 00:05:18,420
也OK

183
00:05:18,420 --> 00:05:20,440
但是如果其中任何一个

184
00:05:20,440 --> 00:05:21,860
返回了一个普通值

185
00:05:21,860 --> 00:05:23,600
这个值并不是安逆范的

186
00:05:23,600 --> 00:05:24,400
那这时候怎么样

187
00:05:24,400 --> 00:05:25,300
说就卡住了

188
00:05:25,300 --> 00:05:26,660
因为这Val不会通过

189
00:05:26,660 --> 00:05:27,680
那在这里面怎么样

190
00:05:27,680 --> 00:05:28,460
就执行完成了

191
00:05:28,460 --> 00:05:29,360
看看效果

192
00:05:29,360 --> 00:05:30,000
走

193
00:05:30,000 --> 00:05:33,460
是不是就实现这块效果了

194
00:05:33,460 --> 00:05:34,280
这也是一个钩子

195
00:05:34,280 --> 00:05:35,360
其实你发现了

196
00:05:35,360 --> 00:05:36,400
它主要的功能

197
00:05:36,400 --> 00:05:38,060
就是来实现我们同步

198
00:05:38,060 --> 00:05:39,220
怎么去执行

199
00:05:39,220 --> 00:05:40,680
完了之后我们再来看

200
00:05:40,680 --> 00:05:41,920
这我也保存一份

201
00:05:41,920 --> 00:05:42,860
来个二

202
00:05:42,860 --> 00:05:45,000
我们再往下看

203
00:05:45,000 --> 00:05:47,100
刚才除了我们这个SinkBiohook

204
00:05:47,100 --> 00:05:49,260
还有我们所谓的SinkWaterfallhook

205
00:05:49,260 --> 00:05:50,780
这个名字很奇怪

206
00:05:50,780 --> 00:05:53,700
叫什么Sink是同步

207
00:05:53,700 --> 00:05:54,240
对吧

208
00:05:54,240 --> 00:05:55,660
Waterfall叫瀑布

209
00:05:55,660 --> 00:05:56,920
完了hook是钩子

210
00:05:56,920 --> 00:05:59,200
比如说我们刚才执行的时候

211
00:05:59,200 --> 00:06:00,960
虽然是第一个执行完执行第二个

212
00:06:00,960 --> 00:06:02,560
但是第一个和第二个执行怎么样

213
00:06:02,560 --> 00:06:03,600
并没有什么关系

214
00:06:03,600 --> 00:06:05,080
我们更希望怎么样

215
00:06:05,080 --> 00:06:07,300
他们可以怎么样产生关系

216
00:06:07,300 --> 00:06:09,180
比如说我学node的时候

217
00:06:09,180 --> 00:06:10,500
我应该知道read学的好不好

218
00:06:10,500 --> 00:06:13,080
这时候我们把拿过来

219
00:06:13,080 --> 00:06:13,900
稍微改造一下

220
00:06:13,900 --> 00:06:15,980
这里面我不用sync bellhook了

221
00:06:15,980 --> 00:06:17,540
而用我们不是这个

222
00:06:17,540 --> 00:06:18,160
关掉

223
00:06:18,160 --> 00:06:20,920
往里面我打开start

224
00:06:20,920 --> 00:06:24,240
这里面改下名字叫sync

225
00:06:24,240 --> 00:06:25,280
对吧

226
00:06:25,280 --> 00:06:26,560
就出来了

227
00:06:26,560 --> 00:06:28,180
叫water hook

228
00:06:28,180 --> 00:06:29,700
我把它放在这里

229
00:06:29,700 --> 00:06:30,820
扭它

230
00:06:30,820 --> 00:06:32,540
此时我们来看看

231
00:06:32,540 --> 00:06:34,160
这时候我们学node

232
00:06:34,160 --> 00:06:35,820
学node肯定有个反馈

233
00:06:35,820 --> 00:06:36,820
学到read的时候

234
00:06:36,820 --> 00:06:38,300
我说node学的不错

235
00:06:38,300 --> 00:06:39,520
这里面可以添个词

236
00:06:39,520 --> 00:06:41,680
比如node学的还不错

237
00:06:41,680 --> 00:06:45,400
这样一个结果就会传给什么

238
00:06:45,400 --> 00:06:46,500
下一个

239
00:06:46,500 --> 00:06:48,280
这就相当于两个函数怎么样

240
00:06:48,280 --> 00:06:48,900
有了关系

241
00:06:48,900 --> 00:06:50,760
这个结果就会传到这也来

242
00:06:50,760 --> 00:06:52,040
我们可以把它放在这

243
00:06:52,040 --> 00:06:52,520
date

244
00:06:52,520 --> 00:06:54,120
我们把它也打印出来

245
00:06:54,120 --> 00:06:56,240
我这时候我再出发

246
00:06:56,240 --> 00:06:56,680
再说怎么样

247
00:06:56,680 --> 00:06:58,480
是不是相当于第一次先走他

248
00:06:58,480 --> 00:07:00,080
第二次打出来的结果就是什么

249
00:07:00,080 --> 00:07:00,300
是不是

250
00:07:00,300 --> 00:07:01,060
REACT

251
00:07:01,060 --> 00:07:02,100
能斗的学的还不错

252
00:07:02,100 --> 00:07:03,860
这是两个人的有关系了

253
00:07:03,860 --> 00:07:05,040
所以说叫瀑布

254
00:07:05,040 --> 00:07:05,940
看看

255
00:07:05,940 --> 00:07:07,660
是这样的

256
00:07:07,660 --> 00:07:08,660
你说这个词呢

257
00:07:08,660 --> 00:07:09,620
我写上意思是吧

258
00:07:09,620 --> 00:07:10,580
叫Waterfall

259
00:07:10,580 --> 00:07:13,220
Waterfall对吧

260
00:07:13,220 --> 00:07:14,320
叫瀑布

261
00:07:14,320 --> 00:07:16,680
你可以认为就是上面的怎么样

262
00:07:16,680 --> 00:07:17,800
比如消耗掉了

263
00:07:17,800 --> 00:07:18,640
揣给下面的怎么样

264
00:07:18,640 --> 00:07:19,220
就剩下的

265
00:07:19,220 --> 00:07:21,140
比如说我们一般举例的就是

266
00:07:21,140 --> 00:07:22,120
吃饭对吧

267
00:07:22,120 --> 00:07:22,740
你吃完了

268
00:07:22,740 --> 00:07:23,600
把这饭给我

269
00:07:23,600 --> 00:07:25,020
那我吃的就是剩饭是吧

270
00:07:25,020 --> 00:07:25,880
这样一个效果

271
00:07:25,880 --> 00:07:26,680
那好了

272
00:07:26,680 --> 00:07:28,160
那我们这个结果呢

273
00:07:28,160 --> 00:07:28,920
也非常好实现

274
00:07:28,920 --> 00:07:30,780
那咱们就顺便把它也实现了

275
00:07:30,780 --> 00:07:31,440
在这里

276
00:07:31,440 --> 00:07:33,600
名字呢稍微改一下

277
00:07:33,600 --> 00:07:35,200
叫sync 对吧

278
00:07:35,200 --> 00:07:37,760
sync waterfall hook

279
00:07:37,760 --> 00:07:41,940
并且呢

280
00:07:41,940 --> 00:07:43,160
把下面这些改造一下

281
00:07:43,160 --> 00:07:44,100
叫new

282
00:07:44,100 --> 00:07:45,380
其实实现原理

283
00:07:45,380 --> 00:07:46,820
就是这个code的地方不太一样

284
00:07:46,820 --> 00:07:48,180
我就把它删掉了

285
00:07:48,180 --> 00:07:49,200
这其他地方还是一样

286
00:07:49,200 --> 00:07:50,720
都是先注册放到速度里

287
00:07:50,720 --> 00:07:52,400
但是这时候就不太一样的

288
00:07:52,400 --> 00:07:53,200
结果相当于什么

289
00:07:53,200 --> 00:07:54,960
是不是需要先取出第一个

290
00:07:54,960 --> 00:07:56,580
完了把第一个的结果怎么样

291
00:07:56,580 --> 00:07:58,260
拿出来传给下一个

292
00:07:58,260 --> 00:08:00,020
那这时候我怎么拿呢

293
00:08:00,020 --> 00:08:00,320
对吧

294
00:08:00,320 --> 00:08:01,580
那我肯定会先想到怎么样

295
00:08:01,580 --> 00:08:01,820
是不是

296
00:08:01,820 --> 00:08:03,480
先把这个函数执行

297
00:08:03,480 --> 00:08:05,420
完了把这个函数执行的结果怎么样

298
00:08:05,420 --> 00:08:06,440
再传给下一个吧

299
00:08:06,440 --> 00:08:07,800
当然这里面肯定有多个

300
00:08:07,800 --> 00:08:09,140
我再多注册一个

301
00:08:09,140 --> 00:08:09,900
看看效果

302
00:08:09,900 --> 00:08:12,720
里面可能他会放着这个webpack

303
00:08:12,720 --> 00:08:13,700
是吧

304
00:08:13,700 --> 00:08:17,080
完了这里呢

305
00:08:17,080 --> 00:08:18,000
我也可以在这表示一下

306
00:08:18,000 --> 00:08:18,540
对吧

307
00:08:18,540 --> 00:08:19,760
改个名字

308
00:08:19,760 --> 00:08:22,320
reactok是吧

309
00:08:22,320 --> 00:08:23,900
这里是nodeok

310
00:08:23,900 --> 00:08:27,180
这里面打出来的结果

311
00:08:27,180 --> 00:08:28,020
是不叫name了

312
00:08:28,020 --> 00:08:28,700
叫date是吧

313
00:08:28,700 --> 00:08:29,200
数据

314
00:08:29,200 --> 00:08:30,740
上一个人的返回值

315
00:08:30,740 --> 00:08:31,680
是下一个人的输入

316
00:08:31,680 --> 00:08:32,020
是吧

317
00:08:32,020 --> 00:08:33,780
现在下一个人的返回值

318
00:08:33,780 --> 00:08:34,720
是下一个人的输入

319
00:08:34,720 --> 00:08:35,320
这样的话

320
00:08:35,320 --> 00:08:36,280
我们就可以把它传起来了

321
00:08:36,280 --> 00:08:37,900
我们想到的是怎么样

322
00:08:37,900 --> 00:08:40,100
是不是需要先拿到这样一个值

323
00:08:40,100 --> 00:08:42,100
把这个值传到我们函数里

324
00:08:42,100 --> 00:08:43,780
并且让函数执行

325
00:08:43,780 --> 00:08:46,140
返回的结果再传给下一个

326
00:08:46,140 --> 00:08:47,340
我们把它的返回值都往下传

327
00:08:47,340 --> 00:08:48,660
第一次是怎么样

328
00:08:48,660 --> 00:08:49,200
是死的

329
00:08:49,200 --> 00:08:50,740
我需要把这值传进去

330
00:08:50,740 --> 00:08:51,640
所以说无论如何

331
00:08:51,640 --> 00:08:53,640
我要先把这第一个哈数拿到

332
00:08:53,640 --> 00:08:55,160
把它的返回值怎么样取出来

333
00:08:55,160 --> 00:08:57,160
我们想到的就是这样的

334
00:08:57,160 --> 00:08:58,560
改一下这里

335
00:08:58,560 --> 00:09:01,460
我们需要把第一个哈数拿出来

336
00:09:01,460 --> 00:09:02,460
给怎么拿呢

337
00:09:02,460 --> 00:09:04,560
这个tasks是所有的

338
00:09:04,560 --> 00:09:05,920
我们可以解构一下

339
00:09:05,920 --> 00:09:07,780
完了这个数组里面的第一个

340
00:09:07,780 --> 00:09:08,760
就是我们想要的

341
00:09:08,760 --> 00:09:10,700
完了后面依旧我把它返回去

342
00:09:10,700 --> 00:09:11,320
还是other

343
00:09:11,320 --> 00:09:12,400
other死吧

344
00:09:12,400 --> 00:09:13,000
很多个

345
00:09:13,000 --> 00:09:15,080
我们先要让第一个哈数执行

346
00:09:15,080 --> 00:09:16,700
我们需要在这来一个叫什么

347
00:09:16,700 --> 00:09:17,980
叫for的执行

348
00:09:17,980 --> 00:09:20,140
完了并且把参数传去

349
00:09:20,140 --> 00:09:22,320
那是不是相当于这个哈数就执行完了

350
00:09:22,320 --> 00:09:23,720
那执行完呢

351
00:09:23,720 --> 00:09:24,880
它会返回一个结果

352
00:09:24,880 --> 00:09:26,020
这个结果呢是IT

353
00:09:26,020 --> 00:09:28,020
我要干什么呢

354
00:09:28,020 --> 00:09:29,220
我需要让这个函数执行

355
00:09:29,220 --> 00:09:30,920
执行完返回结果冲击下一个吧

356
00:09:30,920 --> 00:09:31,840
那我们想到了

357
00:09:31,840 --> 00:09:33,180
是不是这样是一个迭代的过程

358
00:09:33,180 --> 00:09:34,580
并且有返回值

359
00:09:34,580 --> 00:09:36,120
而且会把返回值冲击下一个

360
00:09:36,120 --> 00:09:38,180
那这个写法就非常像到我们

361
00:09:38,180 --> 00:09:39,960
这个Others那个方法

362
00:09:39,960 --> 00:09:41,120
速度的Reduce

363
00:09:41,120 --> 00:09:42,200
迭代

364
00:09:42,200 --> 00:09:44,240
那我们呢可以这样写个函数

365
00:09:44,240 --> 00:09:45,840
这里面的参数

366
00:09:45,840 --> 00:09:46,860
第一个是谁呢

367
00:09:46,860 --> 00:09:48,720
是不是叫所谓的上一个下一个

368
00:09:48,720 --> 00:09:49,480
AB

369
00:09:49,480 --> 00:09:51,960
我们可以把这R1T传进去

370
00:09:51,960 --> 00:09:55,720
这个R1T就是我们的RedOK传到这来了

371
00:09:55,720 --> 00:09:57,480
完了我现在要做什么事

372
00:09:57,480 --> 00:09:59,220
是不是让这个函数执行

373
00:09:59,220 --> 00:10:00,620
完了把这数据传过去

374
00:10:00,620 --> 00:10:02,720
这个函数指的就是我们的B

375
00:10:02,720 --> 00:10:04,800
我可以让B怎么样执行

376
00:10:04,800 --> 00:10:08,840
并且把我们当前的A怎么样传进去

377
00:10:08,840 --> 00:10:12,820
这样的话我们就能拿到这个函数执行结果

378
00:10:12,820 --> 00:10:13,900
把这个结果怎么样

379
00:10:13,900 --> 00:10:15,020
是不是再传给A

380
00:10:15,020 --> 00:10:15,940
完了再这样执行下去

381
00:10:15,940 --> 00:10:17,360
好了我们可以来个什么

382
00:10:17,360 --> 00:10:19,620
是自动循环了

383
00:10:19,620 --> 00:10:20,820
那把他的结果怎么样

384
00:10:20,820 --> 00:10:21,640
会传给下一个人

385
00:10:21,640 --> 00:10:22,960
下个人是不是就是这个东西

386
00:10:22,960 --> 00:10:24,000
那OK了

387
00:10:24,000 --> 00:10:25,280
这样一个方法就实现了

388
00:10:25,280 --> 00:10:27,400
我们来看一下效果是吧

389
00:10:27,400 --> 00:10:28,320
运行一下

390
00:10:28,320 --> 00:10:30,880
好像报了个错

391
00:10:30,880 --> 00:10:31,920
看看什么错误

392
00:10:31,920 --> 00:10:33,040
他说内容没定义是吧

393
00:10:33,040 --> 00:10:34,900
23号可能没有改过来

394
00:10:34,900 --> 00:10:35,460
找一下

395
00:10:35,460 --> 00:10:37,320
应该在这里是吧

396
00:10:37,320 --> 00:10:37,800
改一下

397
00:10:37,800 --> 00:10:39,120
运行一下

398
00:10:39,120 --> 00:10:42,360
是不是上一个人是下一个人的法规制

399
00:10:42,360 --> 00:10:44,120
那这个其实就是我们相当于

400
00:10:44,120 --> 00:10:46,140
如果要是有同步有关联的话怎么样

401
00:10:46,140 --> 00:10:47,200
我是不是可以写第一步

402
00:10:47,200 --> 00:10:48,440
把第一步的接往后抛

403
00:10:48,440 --> 00:10:49,740
第二步第三步

404
00:10:49,740 --> 00:10:51,140
现在非常清晰是吧

405
00:10:51,140 --> 00:10:52,680
其实也就是一个流程控制

406
00:10:52,680 --> 00:10:53,780
再往下看

407
00:10:53,780 --> 00:10:55,520
我们还有一个比较怪异的

408
00:10:55,520 --> 00:10:56,540
在webpack里面

409
00:10:56,540 --> 00:10:58,100
其实并没有用到这个方法

410
00:10:58,100 --> 00:10:59,640
但这个方法挺有意思的

411
00:10:59,640 --> 00:11:00,820
叫Sync Loop Hook

412
00:11:00,820 --> 00:11:03,340
你说它遇到某个钩子的时候

413
00:11:03,340 --> 00:11:04,140
可以怎么样

414
00:11:04,140 --> 00:11:05,080
叫循环执行

415
00:11:05,080 --> 00:11:07,300
比如说我在这里来改一下

416
00:11:07,300 --> 00:11:09,820
同样我把这个代码也保存一份

417
00:11:09,820 --> 00:11:10,780
来个3

418
00:11:10,780 --> 00:11:13,080
OK在这里就有了

419
00:11:13,080 --> 00:11:13,620
关掉

420
00:11:13,620 --> 00:11:15,680
把它改过来

421
00:11:15,680 --> 00:11:17,000
我要这里先写例子

422
00:11:17,000 --> 00:11:18,320
比如说是这样的

423
00:11:18,320 --> 00:11:20,860
我们学习的时候可能这样

424
00:11:20,860 --> 00:11:21,720
先学node

425
00:11:21,720 --> 00:11:23,520
发现这node学一遍学不会

426
00:11:23,520 --> 00:11:24,640
那我会怎么做呢

427
00:11:24,640 --> 00:11:25,900
我是不是想学三遍

428
00:11:25,900 --> 00:11:26,120
是不是

429
00:11:26,120 --> 00:11:27,520
那怎么判断呢

430
00:11:27,520 --> 00:11:29,280
比如说我想这个函数执行三遍

431
00:11:29,280 --> 00:11:30,560
再执行下一个

432
00:11:30,560 --> 00:11:32,660
此时它有这样一个规则

433
00:11:32,660 --> 00:11:35,900
这个规则就是我们所谓的think loop hook

434
00:11:35,900 --> 00:11:37,320
同样把它放在这

435
00:11:37,320 --> 00:11:39,020
这叫什么呢

436
00:11:39,020 --> 00:11:39,740
我写上了对吧

437
00:11:39,740 --> 00:11:40,800
这不叫瀑布了

438
00:11:40,800 --> 00:11:41,480
叫什么呢

439
00:11:41,480 --> 00:11:42,360
叫同步

440
00:11:42,360 --> 00:11:43,840
同步对吧

441
00:11:43,840 --> 00:11:45,420
遇到某个

442
00:11:45,420 --> 00:11:46,300
对吧

443
00:11:46,300 --> 00:11:50,700
不返回andifind的函数

444
00:11:50,700 --> 00:11:51,860
叫见听函数

445
00:11:51,860 --> 00:11:54,800
会多次执行

446
00:11:54,800 --> 00:11:59,100
比如说我希望这个方法执行N次

447
00:11:59,100 --> 00:12:00,000
好了很简单

448
00:12:00,000 --> 00:12:01,880
我需要在这来个索引

449
00:12:01,880 --> 00:12:02,560
叫index

450
00:12:02,560 --> 00:12:05,040
当index每次执行完一次

451
00:12:05,040 --> 00:12:05,860
我就加加

452
00:12:05,860 --> 00:12:08,020
什么时候index变成3的时候

453
00:12:08,020 --> 00:12:08,900
我就看他怎么样

454
00:12:08,900 --> 00:12:10,200
往下走下一个

455
00:12:10,200 --> 00:12:11,980
那就是我想到的是这样写

456
00:12:11,980 --> 00:12:13,860
叫z.index

457
00:12:13,860 --> 00:12:16,600
每次执行对吧

458
00:12:16,600 --> 00:12:17,720
我就让这index干嘛呀

459
00:12:17,720 --> 00:12:18,080
加加

460
00:12:18,080 --> 00:12:18,560
那好

461
00:12:18,560 --> 00:12:19,160
那就先加

462
00:12:19,160 --> 00:12:20,960
完了如果呀

463
00:12:20,960 --> 00:12:22,060
他要是等于3的话

464
00:12:22,060 --> 00:12:22,760
那好了

465
00:12:22,760 --> 00:12:23,640
我就需要干嘛呀

466
00:12:23,640 --> 00:12:25,140
是不是不返回暗地范

467
00:12:25,140 --> 00:12:26,340
就不往下执行

468
00:12:26,340 --> 00:12:27,500
那返回暗地范怎么样

469
00:12:27,500 --> 00:12:28,320
就往下执行

470
00:12:28,320 --> 00:12:29,940
所以这里面我就给暗地范

471
00:12:29,940 --> 00:12:30,880
那否则呢

472
00:12:30,880 --> 00:12:31,240
我就怎么样

473
00:12:31,240 --> 00:12:32,260
说继续学

474
00:12:32,260 --> 00:12:33,580
继续学

475
00:12:33,580 --> 00:12:35,240
他不会管你给的什么值

476
00:12:35,240 --> 00:12:36,460
只要不是暗地范怎么样

477
00:12:36,460 --> 00:12:38,100
这个函数会再次执行

478
00:12:38,100 --> 00:12:39,720
直到他为暗地范为止

479
00:12:39,720 --> 00:12:40,440
那好了

480
00:12:40,440 --> 00:12:41,560
这里面可以来个 return

481
00:12:41,560 --> 00:12:43,940
看一下效果

482
00:12:43,940 --> 00:12:44,840
邮件乱

483
00:12:44,840 --> 00:12:48,500
好像有点bug

484
00:12:48,500 --> 00:12:50,260
他说index没定义

485
00:12:50,260 --> 00:12:50,760
看看

486
00:12:50,760 --> 00:12:52,400
this.index

487
00:12:52,400 --> 00:12:53,540
这里面进行函数

488
00:12:53,540 --> 00:12:55,240
又是this的问题

489
00:12:55,240 --> 00:12:56,660
这也改了吧

490
00:12:56,660 --> 00:12:58,740
这里面加上

491
00:12:58,740 --> 00:13:00,160
他说this没找到

492
00:13:00,160 --> 00:13:00,820
我再乱

493
00:13:00,820 --> 00:13:04,320
你看是不是node先学了三次

494
00:13:04,320 --> 00:13:05,440
学完了以后怎么样

495
00:13:05,440 --> 00:13:06,360
再往下走到react

496
00:13:06,360 --> 00:13:08,100
这时候好了

497
00:13:08,100 --> 00:13:09,580
我们可以很容易的

498
00:13:09,580 --> 00:13:10,860
其实区别都不大

499
00:13:10,860 --> 00:13:11,760
还是怎么样

500
00:13:11,760 --> 00:13:12,540
实现有问题

501
00:13:12,540 --> 00:13:13,660
就是我们需要怎么样

502
00:13:13,660 --> 00:13:13,860
是不是

503
00:13:13,860 --> 00:13:15,160
让这两个函数都执行

504
00:13:15,160 --> 00:13:17,200
但是如果第一个函数执行的结果

505
00:13:17,200 --> 00:13:18,020
是Andify

506
00:13:18,020 --> 00:13:18,720
那好

507
00:13:18,720 --> 00:13:19,340
才走下一个

508
00:13:19,340 --> 00:13:20,460
所以这里面

509
00:13:20,460 --> 00:13:21,720
我就可以把这个代码

510
00:13:21,720 --> 00:13:22,820
再演化一下

511
00:13:22,820 --> 00:13:24,200
这里面我就不叫Think

512
00:13:24,200 --> 00:13:24,960
Waterfall了

513
00:13:24,960 --> 00:13:25,220
对吧

514
00:13:25,220 --> 00:13:25,980
叫Think

515
00:13:25,980 --> 00:13:28,200
我们的叫loophook

516
00:13:28,200 --> 00:13:30,960
Loophook

517
00:13:30,960 --> 00:13:31,760
这里一样

518
00:13:31,760 --> 00:13:32,880
稍微改改

519
00:13:32,880 --> 00:13:34,100
改掉

520
00:13:34,100 --> 00:13:35,600
参数就不要了

521
00:13:35,600 --> 00:13:36,420
删掉

522
00:13:36,420 --> 00:13:37,760
WriteNode

523
00:13:37,760 --> 00:13:39,080
这些我们就

524
00:13:39,080 --> 00:13:40,420
这样写的话就毁了

525
00:13:40,420 --> 00:13:41,300
就死循环了

526
00:13:41,300 --> 00:13:42,100
卡在这不动了

527
00:13:42,100 --> 00:13:43,480
所以这里面我还是一样

528
00:13:43,480 --> 00:13:44,160
为了方便

529
00:13:44,160 --> 00:13:45,020
我弄个变量

530
00:13:45,020 --> 00:13:45,640
叫total

531
00:13:45,640 --> 00:13:48,260
total

532
00:13:48,260 --> 00:13:49,520
默认等于0

533
00:13:49,520 --> 00:13:51,120
完了每次执行的时候

534
00:13:51,120 --> 00:13:51,900
我就判断

535
00:13:51,900 --> 00:13:52,980
让它加加total

536
00:13:52,980 --> 00:13:54,900
我看看它等不等于3

537
00:13:54,900 --> 00:13:56,040
等于3的话

538
00:13:56,040 --> 00:13:56,600
就是安基范

539
00:13:56,600 --> 00:13:57,600
完了不等于3

540
00:13:57,600 --> 00:13:58,340
就是继续学

541
00:13:58,340 --> 00:13:58,660
是吧

542
00:13:58,660 --> 00:13:59,460
继续学

543
00:13:59,460 --> 00:14:02,120
下面这就不写了

544
00:14:02,120 --> 00:14:02,920
不写的话

545
00:14:02,920 --> 00:14:03,440
反弱安基范

546
00:14:03,440 --> 00:14:04,160
就往下接着走

547
00:14:04,160 --> 00:14:05,000
好了

548
00:14:05,000 --> 00:14:06,540
现在我们要怎么做

549
00:14:06,540 --> 00:14:08,080
第一步我们想到的是

550
00:14:08,080 --> 00:14:09,940
这三个方法都要执行

551
00:14:09,940 --> 00:14:12,580
所以说我们无论如何都会怎么样

552
00:14:12,580 --> 00:14:14,160
是不是来个for each都循环一下

553
00:14:14,160 --> 00:14:16,480
那this.tasks

554
00:14:16,480 --> 00:14:19,800
tasks.for each

555
00:14:19,800 --> 00:14:22,640
完了里面我就可以拿到每一个的任务

556
00:14:22,640 --> 00:14:24,060
让这个每一个任务怎么样

557
00:14:24,060 --> 00:14:24,680
执行

558
00:14:24,680 --> 00:14:28,080
当然了执行的时候还需要传一个东西叫name

559
00:14:28,080 --> 00:14:29,360
但是这样写的话

560
00:14:29,360 --> 00:14:30,640
这里面名字都改掉

561
00:14:30,640 --> 00:14:31,320
都叫name

562
00:14:31,320 --> 00:14:33,040
因为这个值肯定是一样的

563
00:14:33,040 --> 00:14:34,680
感觉怪怪的

564
00:14:34,680 --> 00:14:35,640
完了我在这里执行

565
00:14:35,640 --> 00:14:37,000
我需要把参数怎么样

566
00:14:37,000 --> 00:14:38,160
对进去

567
00:14:38,160 --> 00:14:39,320
丢进去以后

568
00:14:39,320 --> 00:14:41,440
但是这样写的话会有点问题

569
00:14:41,440 --> 00:14:42,880
如果这个函数我需要干嘛

570
00:14:42,880 --> 00:14:44,460
是不是至少得走一次

571
00:14:44,460 --> 00:14:46,340
如果第一次执行

572
00:14:46,340 --> 00:14:47,680
他返回的不是暗地藩

573
00:14:47,680 --> 00:14:48,740
那好还要再执行

574
00:14:48,740 --> 00:14:50,240
所以说至少走一次

575
00:14:50,240 --> 00:14:51,520
又想到了杜拜

576
00:14:51,520 --> 00:14:52,380
我说杜拜

577
00:14:52,380 --> 00:14:55,680
这里面一样

578
00:14:55,680 --> 00:14:57,540
把这个方法怎么样放进去

579
00:14:57,540 --> 00:14:59,100
同样的

580
00:14:59,100 --> 00:15:00,600
我们肯定还需要拿它的返回值

581
00:15:00,600 --> 00:15:02,220
我们需要在这里面

582
00:15:02,220 --> 00:15:02,680
let it

583
00:15:02,680 --> 00:15:06,080
比如说刚开始就等于一个

584
00:15:06,080 --> 00:15:06,620
对吧

585
00:15:06,620 --> 00:15:07,200
等于一个空

586
00:15:07,200 --> 00:15:08,040
let it

587
00:15:08,040 --> 00:15:11,020
并且我让这IET等于它的范围值

588
00:15:11,020 --> 00:15:13,520
如果这个IET怎么样

589
00:15:13,520 --> 00:15:14,480
它不等于安尼范

590
00:15:14,480 --> 00:15:15,780
不等于安尼范

591
00:15:15,780 --> 00:15:16,860
那就一直执行

592
00:15:16,860 --> 00:15:18,800
什么时候等于安尼范了怎么样

593
00:15:18,800 --> 00:15:20,360
它是不是就停止执行了

594
00:15:20,360 --> 00:15:21,340
并且怎么样

595
00:15:21,340 --> 00:15:22,140
是不是往下接着走

596
00:15:22,140 --> 00:15:23,540
那好了咱来看看

597
00:15:23,540 --> 00:15:25,580
是不是它会走三次

598
00:15:25,580 --> 00:15:27,900
两次Node

599
00:15:27,900 --> 00:15:29,520
但是发现个好玩的问题

600
00:15:29,520 --> 00:15:31,340
后面那个好像怎么样

601
00:15:31,340 --> 00:15:32,640
没走

602
00:15:32,640 --> 00:15:33,880
那咱看看什么原因

603
00:15:33,880 --> 00:15:36,240
Node这里面改个名字

604
00:15:36,240 --> 00:15:37,400
这个叫WyPack

605
00:15:37,400 --> 00:15:38,560
应该是走了

606
00:15:38,560 --> 00:15:39,640
走了

607
00:15:39,640 --> 00:15:40,820
名字一样

608
00:15:40,820 --> 00:15:41,640
我再来一次

609
00:15:41,640 --> 00:15:44,420
三边react对吧

610
00:15:44,420 --> 00:15:45,500
一边node一边react

611
00:15:45,500 --> 00:15:50,480
现在我们就实现了所有的type拨中的同步方法

612
00:15:50,480 --> 00:15:51,920
其实难度倒不高

613
00:15:51,920 --> 00:15:54,660
接下来我们就来说一下这个eep方法

614
00:15:54,660 --> 00:15:57,280
这个eep方法相对来说就比较难一些了

