1
00:00:00,000 --> 00:00:02,960
本节我们就来介绍一下

2
00:00:02,960 --> 00:00:04,900
如何在WipePack中使用的图片

3
00:00:04,900 --> 00:00:07,420
并且能让WipePack来打包我们的图片

4
00:00:07,420 --> 00:00:09,700
这里我就把GS先住掉

5
00:00:09,700 --> 00:00:11,040
好了我们写一下

6
00:00:11,040 --> 00:00:14,240
主要想实现的就是WipePack打包

7
00:00:14,240 --> 00:00:16,100
打包我们的图片

8
00:00:16,100 --> 00:00:19,080
图片的引入方式有很多种

9
00:00:19,080 --> 00:00:21,580
第一种最常见的就是在GS中

10
00:00:21,580 --> 00:00:21,940
对吧

11
00:00:21,940 --> 00:00:23,760
往上创建图片引入

12
00:00:23,760 --> 00:00:26,320
创建图片来引入

13
00:00:26,320 --> 00:00:27,920
往来第二种还有一样

14
00:00:27,920 --> 00:00:30,600
是吧我们可能会在这个css中对吧

15
00:00:30,600 --> 00:00:33,080
通过我们的这种background来引入对吧

16
00:00:33,080 --> 00:00:38,040
就是在在css引入对吧

17
00:00:38,040 --> 00:00:40,240
比如说可以通过这样的background

18
00:00:40,240 --> 00:00:42,680
我的url这种方式

19
00:00:42,680 --> 00:00:45,800
同样可能还有第三种

20
00:00:45,800 --> 00:00:48,640
就是我们可以在天面要中直接写死

21
00:00:48,640 --> 00:00:50,080
比如说我来个image

22
00:00:50,080 --> 00:00:52,160
我来src这种方式也算

23
00:00:52,160 --> 00:00:53,680
再来试一下吧

24
00:00:53,680 --> 00:00:56,040
我们就通过gs来创建张图片

25
00:00:56,040 --> 00:01:00,400
在这里我们可以let一个image等于new一个image是吧

26
00:01:00,400 --> 00:01:01,100
img

27
00:01:01,100 --> 00:01:04,380
完了里面我们可以直接去readimage

28
00:01:04,380 --> 00:01:07,480
image完了dr把它拿过来

29
00:01:07,480 --> 00:01:09,600
image.src

30
00:01:09,600 --> 00:01:11,480
我就等于我们当前这样一张图片

31
00:01:11,480 --> 00:01:13,280
那图片的话我们就找一张

32
00:01:13,280 --> 00:01:14,440
我找一张logo

33
00:01:14,440 --> 00:01:16,440
比如说就这个吧

34
00:01:16,440 --> 00:01:18,000
logo拿过来

35
00:01:18,000 --> 00:01:19,600
完了里面我可以怎么样

36
00:01:19,600 --> 00:01:21,340
你要写的话你要这样写就毁了

37
00:01:21,340 --> 00:01:22,500
对吧直接.gun

38
00:01:22,500 --> 00:01:23,840
比如说logo.png

39
00:01:23,840 --> 00:01:25,800
其实你这样写的话表示它就是一个什么

40
00:01:25,800 --> 00:01:29,860
就是一个就是一个普通的普通的字符串

41
00:01:29,860 --> 00:01:30,760
那打包完以后

42
00:01:30,760 --> 00:01:32,400
他也认为这东西就是个字符串

43
00:01:32,400 --> 00:01:34,260
那再不信咱来看看对吧

44
00:01:34,260 --> 00:01:35,760
document their body

45
00:01:35,760 --> 00:01:38,100
后来 their append child 是吧

46
00:01:38,100 --> 00:01:40,260
 append child

47
00:01:40,260 --> 00:01:42,360
我把这个 image 放进去

48
00:01:42,360 --> 00:01:43,740
往来里面呢

49
00:01:43,740 --> 00:01:44,840
我就来进行打包啊

50
00:01:44,840 --> 00:01:45,700
看看效果

51
00:01:45,700 --> 00:01:48,240
这里呢我们就 mpx webpack

52
00:01:48,240 --> 00:01:50,400
来看看效果啊

53
00:01:50,400 --> 00:01:51,140
行

54
00:01:51,140 --> 00:01:53,840
往来这里面你看他并没有包错

55
00:01:53,840 --> 00:01:55,740
并没有告诉我需要一个loader

56
00:01:55,740 --> 00:01:58,000
那说明啊这个东西呢并没有怎么样

57
00:01:58,000 --> 00:01:59,540
真正的区域那样图片

58
00:01:59,540 --> 00:02:02,040
所以这时候呢我们引入的东西这个结果呀

59
00:02:02,040 --> 00:02:03,640
肯定也没有进行打爆图片

60
00:02:03,640 --> 00:02:04,940
我们可以看一下啊

61
00:02:04,940 --> 00:02:07,540
这里面没有嗯好像有点问题啊

62
00:02:07,540 --> 00:02:08,540
我们给你看看

63
00:02:08,540 --> 00:02:09,340
console

64
00:02:09,340 --> 00:02:12,940
告诉我他说image没定义啊

65
00:02:12,940 --> 00:02:14,340
应该是全拼啊

66
00:02:14,340 --> 00:02:16,840
你有一个image这个无所谓啊

67
00:02:16,840 --> 00:02:17,940
我稍微改一下

68
00:02:17,940 --> 00:02:19,640
完了我再来生成一下

69
00:02:19,640 --> 00:02:21,340
看看现在的效果呢是吧

70
00:02:21,340 --> 00:02:23,940
这里我刷新一下

71
00:02:24,540 --> 00:02:27,440
你发现是不是根本没有找到build下的logo点凭借

72
00:02:27,440 --> 00:02:31,940
因为当前这个目录下并没有我们所谓的什么logo点凭借

73
00:02:31,940 --> 00:02:32,920
比如他没有被打包

74
00:02:32,920 --> 00:02:34,580
那这时候要怎么做呢

75
00:02:34,580 --> 00:02:35,400
这时候我说了

76
00:02:35,400 --> 00:02:37,880
如果想让他能有这种依赖关系

77
00:02:37,880 --> 00:02:39,460
那我们需要怎么是不是导入进来

78
00:02:39,460 --> 00:02:40,840
有requare预法的话

79
00:02:40,840 --> 00:02:42,760
他就会认为你有了资源

80
00:02:42,760 --> 00:02:44,920
那这时候他才会被打包进来

81
00:02:44,920 --> 00:02:46,540
所以这里面我可以怎么样呢

82
00:02:46,540 --> 00:02:47,660
直接requare

83
00:02:47,660 --> 00:02:49,660
当然你也可以有我们的yesu预法

84
00:02:49,660 --> 00:02:50,860
这里面就import

85
00:02:50,860 --> 00:02:52,500
import的意思就是把它导进来

86
00:02:52,500 --> 00:02:55,140
这里我们直接引logo

87
00:02:55,140 --> 00:02:57,560
引我们的logo.png

88
00:02:57,560 --> 00:02:58,780
这样就可以了

89
00:02:58,780 --> 00:03:00,060
相当于怎么样

90
00:03:00,060 --> 00:03:00,980
它有什么作用

91
00:03:00,980 --> 00:03:03,840
就是把图片引入

92
00:03:03,840 --> 00:03:07,020
并且你看我这里面的返回的结果

93
00:03:07,020 --> 00:03:08,700
就相当于我们的require的返回结果

94
00:03:08,700 --> 00:03:10,100
这个结果代表什么

95
00:03:10,100 --> 00:03:11,460
返回的结果

96
00:03:11,460 --> 00:03:13,420
是一个什么

97
00:03:13,420 --> 00:03:15,380
是一个新的图片

98
00:03:15,380 --> 00:03:17,100
为什么这么来写

99
00:03:17,100 --> 00:03:18,440
这个新的图片

100
00:03:18,440 --> 00:03:19,040
不是图片

101
00:03:19,040 --> 00:03:20,420
应该是个图片地址

102
00:03:20,420 --> 00:03:22,960
比如说我们引了这样一个logo.png

103
00:03:22,960 --> 00:03:26,180
它内部会把logo.png发射出来

104
00:03:26,180 --> 00:03:28,600
发射一个新的文件名字

105
00:03:28,600 --> 00:03:29,760
因为你要想想

106
00:03:29,760 --> 00:03:31,560
比如说有很多人都引了个叫logo的

107
00:03:31,560 --> 00:03:32,500
名字肯定是

108
00:03:32,500 --> 00:03:33,960
可能有重复

109
00:03:33,960 --> 00:03:35,440
这里面我们可以

110
00:03:35,440 --> 00:03:39,140
它默认会在内部生成一个哈希串的图片

111
00:03:39,140 --> 00:03:40,640
把它发射到build下

112
00:03:40,640 --> 00:03:43,340
并且logo代表了就是我们这样一个

113
00:03:43,340 --> 00:03:45,100
生成新的图片的一个UIL

114
00:03:45,100 --> 00:03:46,540
这里我们可以打印一下

115
00:03:46,540 --> 00:03:47,040
在这

116
00:03:47,040 --> 00:03:48,540
coslog logo

117
00:03:48,540 --> 00:03:50,120
但是很可惜

118
00:03:50,120 --> 00:03:51,740
这个东西一看就怎么样

119
00:03:51,740 --> 00:03:53,060
它不支持这样的模块

120
00:03:53,060 --> 00:03:54,980
所以说这时候又会报一场

121
00:03:54,980 --> 00:03:58,540
告诉我这东西需要一个合适的loader来解析

122
00:03:58,540 --> 00:04:00,300
这时候好办了

123
00:04:00,300 --> 00:04:02,600
我们说了loader我们都用了很多了

124
00:04:02,600 --> 00:04:04,060
这里的loader叫什么

125
00:04:04,060 --> 00:04:05,340
叫fileloader

126
00:04:05,340 --> 00:04:06,120
来个钢D

127
00:04:06,120 --> 00:04:08,080
在这描述一下

128
00:04:08,080 --> 00:04:11,980
fileloader它的作用就是像我刚才说的

129
00:04:11,980 --> 00:04:13,780
默认会在内部

130
00:04:13,780 --> 00:04:17,400
会在内部生成一张图片

131
00:04:17,400 --> 00:04:19,160
到了到哪

132
00:04:19,160 --> 00:04:21,460
到我们build的目录下

133
00:04:21,460 --> 00:04:23,580
到build的目录下

134
00:04:23,580 --> 00:04:24,880
完了还会怎么样

135
00:04:24,880 --> 00:04:28,160
还会把生成的图片的路径

136
00:04:28,160 --> 00:04:31,760
生成的图片的名字

137
00:04:31,760 --> 00:04:32,100
对吧

138
00:04:32,100 --> 00:04:33,300
反回回来

139
00:04:33,300 --> 00:04:35,100
好了

140
00:04:35,100 --> 00:04:36,100
有这个安营配置

141
00:04:36,100 --> 00:04:36,480
那好

142
00:04:36,480 --> 00:04:37,580
我就去webpack

143
00:04:37,580 --> 00:04:39,100
config.js配一下

144
00:04:39,100 --> 00:04:40,040
这里一样

145
00:04:40,040 --> 00:04:41,500
我把这个代码保存一份

146
00:04:41,500 --> 00:04:42,900
放到这个history下

147
00:04:42,900 --> 00:04:45,120
完了这里我们再来去改

148
00:04:45,120 --> 00:04:46,740
比如把注射的就删掉了

149
00:04:46,740 --> 00:04:47,480
还有很多

150
00:04:47,480 --> 00:04:48,940
规则一样

151
00:04:48,940 --> 00:04:52,080
现在我们要匹配的就是图片了

152
00:04:52,080 --> 00:04:54,480
这里我们可以去来个正责

153
00:04:54,480 --> 00:04:56,540
比如说里面我可以来个

154
00:04:56,540 --> 00:04:59,040
比如说叫以png

155
00:04:59,040 --> 00:05:00,600
括号的括号

156
00:05:00,600 --> 00:05:02,980
以png或者什么gpg

157
00:05:02,980 --> 00:05:04,400
或者gf

158
00:05:04,400 --> 00:05:06,020
完了来结尾的

159
00:05:06,020 --> 00:05:07,920
完了我们可以去用一下

160
00:05:07,920 --> 00:05:10,320
我们的所谓的叫图片的loader

161
00:05:10,320 --> 00:05:12,260
这里我们可以直接写上

162
00:05:12,260 --> 00:05:13,800
比如叫fileloader

163
00:05:13,800 --> 00:05:15,460
这样的话

164
00:05:15,460 --> 00:05:17,200
他就可以去解析这样一个东西了

165
00:05:17,200 --> 00:05:18,760
为了能看到效果

166
00:05:18,760 --> 00:05:21,660
这里面我们去npm run dv

167
00:05:21,660 --> 00:05:22,820
看看效果

168
00:05:22,820 --> 00:05:25,760
这时候我们说了

169
00:05:25,760 --> 00:05:26,980
他会在目录下怎么样

170
00:05:26,980 --> 00:05:29,320
因为他现在其实是什么

171
00:05:29,320 --> 00:05:29,860
是一个

172
00:05:29,860 --> 00:05:31,500
但是已经打包出来了

173
00:05:31,500 --> 00:05:32,220
他在内存中

174
00:05:32,220 --> 00:05:33,420
我们不能看到这个

175
00:05:33,420 --> 00:05:33,960
看不到

176
00:05:33,960 --> 00:05:35,260
这里面你发现

177
00:05:35,260 --> 00:05:36,540
他是不是一个哈希说的图片

178
00:05:36,540 --> 00:05:38,400
同样有bund有index

179
00:05:38,400 --> 00:05:39,840
完了再来看看效果

180
00:05:39,840 --> 00:05:40,280
我刷新

181
00:05:40,280 --> 00:05:42,780
这时候好像又报了个错

182
00:05:42,780 --> 00:05:43,080
是吧

183
00:05:43,080 --> 00:05:44,400
他说还是没法解析

184
00:05:44,400 --> 00:05:45,400
路径有问题

185
00:05:45,400 --> 00:05:47,420
这里面应该用localhouse的8080

186
00:05:47,420 --> 00:05:48,280
是吧

187
00:05:48,280 --> 00:05:48,940
刷新

188
00:05:48,940 --> 00:05:52,640
好像还是有点小小的问题

189
00:05:52,640 --> 00:05:54,140
可能是端口的问题

190
00:05:54,140 --> 00:05:54,960
是8080是吧

191
00:05:54,960 --> 00:05:56,440
这里面我再重新起一下

192
00:05:56,440 --> 00:05:57,360
我打包吧

193
00:05:57,360 --> 00:05:57,940
run build

194
00:05:57,940 --> 00:06:00,320
我把这个东西

195
00:06:00,320 --> 00:06:01,280
它因为有实体文件

196
00:06:01,280 --> 00:06:02,740
它会先找这个实体文件

197
00:06:02,740 --> 00:06:04,320
我把这个文件夹先删掉

198
00:06:04,320 --> 00:06:06,120
重新生下就好了

199
00:06:06,120 --> 00:06:07,680
回来这里面呢

200
00:06:07,680 --> 00:06:09,380
我们再去运行一下是吧

201
00:06:09,380 --> 00:06:10,180
再运行一下

202
00:06:10,180 --> 00:06:11,200
run build

203
00:06:11,200 --> 00:06:15,900
有的时候它会报一些奇葩的错误

204
00:06:15,900 --> 00:06:17,360
这时候就对了

205
00:06:17,360 --> 00:06:18,320
肯定是最新的了

206
00:06:18,320 --> 00:06:19,600
好了运行一下

207
00:06:19,600 --> 00:06:23,560
哦还是没有找到写实是吧

208
00:06:23,560 --> 00:06:24,800
我看看什么原因

209
00:06:24,800 --> 00:06:27,880
他说路径确实拿到了什么3D71

210
00:06:27,880 --> 00:06:28,700
OK的

211
00:06:28,700 --> 00:06:29,660
完了这里面呢

212
00:06:29,660 --> 00:06:31,660
我再刷新看看它元素有什么问题啊

213
00:06:31,660 --> 00:06:31,940
body

214
00:06:31,940 --> 00:06:33,400
完了里面有个image

215
00:06:33,400 --> 00:06:36,120
哦这里面还有一个有问题是吧

216
00:06:36,120 --> 00:06:37,700
这个图片不是这引的啊

217
00:06:37,700 --> 00:06:40,100
这个图片我这写的还是死字不串

218
00:06:40,100 --> 00:06:41,580
我把它稍微改造一下啊

219
00:06:41,580 --> 00:06:42,580
在index里面

220
00:06:42,580 --> 00:06:43,320
这里面啊

221
00:06:43,320 --> 00:06:45,560
你不能再用这个点高logo点偏激了

222
00:06:45,560 --> 00:06:46,100
肯定找不到

223
00:06:46,100 --> 00:06:47,700
这里面我们应该用的是

224
00:06:47,700 --> 00:06:49,100
它返回的这样一个

225
00:06:49,100 --> 00:06:50,120
走出来是吧

226
00:06:50,120 --> 00:06:50,600
OK

227
00:06:50,600 --> 00:06:51,000
放在这

228
00:06:51,000 --> 00:06:52,380
这样肯定就行了

229
00:06:52,380 --> 00:06:53,800
刚才应该不是它的问题

230
00:06:53,800 --> 00:06:55,000
我再来一次是吧

231
00:06:55,000 --> 00:06:56,400
npm run dv

232
00:06:56,400 --> 00:07:00,360
看内存中生产的是吧

233
00:07:00,360 --> 00:07:00,780
刷新

234
00:07:00,780 --> 00:07:03,340
有点慢是吧

235
00:07:03,340 --> 00:07:03,700
稍等

236
00:07:03,700 --> 00:07:04,620
刷新

237
00:07:04,620 --> 00:07:05,960
你看它有的时候

238
00:07:05,960 --> 00:07:08,540
你需要先把目录下的东西

239
00:07:08,540 --> 00:07:09,160
怎么样删掉

240
00:07:09,160 --> 00:07:10,580
因为有实体文件

241
00:07:10,580 --> 00:07:12,340
它会先去找什么实体文件

242
00:07:12,340 --> 00:07:14,000
我把它就整个删掉了

243
00:07:14,000 --> 00:07:15,160
这样就靠谱了

244
00:07:15,160 --> 00:07:16,200
干掉

245
00:07:16,200 --> 00:07:18,020
完了我再刷新是吧

246
00:07:18,020 --> 00:07:18,660
这时候

247
00:07:18,660 --> 00:07:21,280
我没有通过localhouse访问

248
00:07:21,280 --> 00:07:22,540
这里面应该就行了

249
00:07:22,540 --> 00:07:24,220
localhouse是他是吧

250
00:07:24,220 --> 00:07:26,040
你看是不是这个logo就出来了

251
00:07:26,040 --> 00:07:28,300
我们知道这样一个效果以后

252
00:07:28,300 --> 00:07:29,020
就知道了

253
00:07:29,020 --> 00:07:29,220
OK

254
00:07:29,220 --> 00:07:31,040
如果我们要引一个图片

255
00:07:31,040 --> 00:07:33,600
那必须得有这种require或者import语法

256
00:07:33,600 --> 00:07:34,640
把它导入进来

257
00:07:34,640 --> 00:07:36,240
这样的话才能进行什么

258
00:07:36,240 --> 00:07:38,440
打爆我们的GS或者打爆我们的图片

259
00:07:38,440 --> 00:07:39,800
好了

260
00:07:39,800 --> 00:07:40,860
这里也一样

261
00:07:40,860 --> 00:07:43,520
我们要在背景图中使用的也很像是吧

262
00:07:43,520 --> 00:07:44,860
比如说我在我的Less里

263
00:07:44,860 --> 00:07:48,560
我Less里面就随便写个背景图

264
00:07:48,560 --> 00:07:50,360
来个比如背景图

265
00:07:50,360 --> 00:07:51,520
来个UIL

266
00:07:51,520 --> 00:07:54,320
同样我这里我可以直接写

267
00:07:54,320 --> 00:07:55,940
不是.gov.local.png

268
00:07:55,940 --> 00:07:57,900
这个它是默认就支持的

269
00:07:57,900 --> 00:07:59,900
因为我们用了CSS loader

270
00:07:59,900 --> 00:08:03,300
CSS loader会把这样一个东西进行转化

271
00:08:03,300 --> 00:08:04,360
和这样转化大致

272
00:08:04,360 --> 00:08:06,240
变成require这个东西

273
00:08:06,240 --> 00:08:07,740
一有require是不是相当于怎么样

274
00:08:07,740 --> 00:08:09,080
它就已经用了这张图片

275
00:08:09,080 --> 00:08:10,200
所以说它也会打包

276
00:08:10,200 --> 00:08:11,280
这里先住掉

277
00:08:11,280 --> 00:08:12,020
你看是不是这样

278
00:08:12,020 --> 00:08:15,460
同样我们来把这个文件印进来

279
00:08:15,460 --> 00:08:17,600
放在上面

280
00:08:17,600 --> 00:08:19,520
Import

281
00:08:19,520 --> 00:08:20,560
我们直接引

282
00:08:20,560 --> 00:08:22,660
比如说这个叫第二告

283
00:08:22,660 --> 00:08:24,920
index.less

284
00:08:24,920 --> 00:08:25,260
是吧

285
00:08:25,260 --> 00:08:26,260
是less文件

286
00:08:26,260 --> 00:08:28,340
这里我们就把它引进来了

287
00:08:28,340 --> 00:08:30,020
完了我们刷新一下

288
00:08:30,020 --> 00:08:32,540
完了可以看看

289
00:08:32,540 --> 00:08:34,160
好像是有点小小的异常

290
00:08:34,160 --> 00:08:35,180
往中抓一下是吧

291
00:08:35,180 --> 00:08:35,640
看看body

292
00:08:35,640 --> 00:08:38,540
完了这里面好像它引入的是

293
00:08:38,540 --> 00:08:39,600
哪个文件

294
00:08:39,600 --> 00:08:39,920
看看

295
00:08:39,920 --> 00:08:42,180
就这个白光的pink是吧

296
00:08:42,180 --> 00:08:43,300
我看一眼是哪个

297
00:08:43,300 --> 00:08:44,860
Less应该就是他

298
00:08:44,860 --> 00:08:46,460
哦我又引错了是吧

299
00:08:46,460 --> 00:08:47,200
应该引css

300
00:08:47,200 --> 00:08:48,520
这里面再改一下

301
00:08:48,520 --> 00:08:49,620
叫css

302
00:08:49,620 --> 00:08:51,580
好了我再来刷新

303
00:08:51,580 --> 00:08:52,620
你可以看到

304
00:08:52,620 --> 00:08:53,800
现在就是两个是吧

305
00:08:53,800 --> 00:08:55,400
哦你看背景图是有的

306
00:08:55,400 --> 00:08:56,740
为了好看点吧

307
00:08:56,740 --> 00:08:57,180
这样吧

308
00:08:57,180 --> 00:08:58,320
我稍微改造一下

309
00:08:58,320 --> 00:08:59,420
我把这天喵里面

310
00:08:59,420 --> 00:09:01,040
这个div改一下是吧

311
00:09:01,040 --> 00:09:01,480
这里

312
00:09:01,480 --> 00:09:03,060
为了能看得清晰一点

313
00:09:03,060 --> 00:09:04,080
别写那么多

314
00:09:04,080 --> 00:09:06,460
这里呢我们给这个div

315
00:09:06,460 --> 00:09:08,020
加上一个宽高是吧

316
00:09:08,020 --> 00:09:09,420
不是比如说100

317
00:09:09,420 --> 00:09:10,900
完了 hat 100

318
00:09:10,900 --> 00:09:11,860
对吧

319
00:09:11,860 --> 00:09:12,660
完了一个 background

320
00:09:12,660 --> 00:09:13,580
把它粘过来

321
00:09:13,580 --> 00:09:15,660
这样的话看着可能会更好一点

322
00:09:15,660 --> 00:09:17,220
不会那么乱了

323
00:09:17,220 --> 00:09:19,000
不过有旋转是吧

324
00:09:19,000 --> 00:09:20,540
完了这里面我们看看也没有吧

325
00:09:20,540 --> 00:09:22,040
都挡住了

326
00:09:22,040 --> 00:09:23,260
这里呢我们看一下

327
00:09:23,260 --> 00:09:24,820
当前的body上呢

328
00:09:24,820 --> 00:09:27,560
确实有这样一张旋转的图片

329
00:09:27,560 --> 00:09:28,760
rotate

330
00:09:28,760 --> 00:09:29,660
完了div呢

331
00:09:29,660 --> 00:09:31,680
我把这个旋转也删掉吧

332
00:09:31,680 --> 00:09:32,800
看着很恶心是吧

333
00:09:32,800 --> 00:09:33,580
删掉

334
00:09:33,580 --> 00:09:36,340
这样的话看看也没有啊

335
00:09:36,340 --> 00:09:37,020
这是一个

336
00:09:37,020 --> 00:09:38,300
完了我们内容区呢

337
00:09:38,300 --> 00:09:39,180
发现好像没出来

338
00:09:39,180 --> 00:09:40,220
因为太小了

339
00:09:40,220 --> 00:09:42,640
这里我们把它再给的大一点

340
00:09:42,640 --> 00:09:44,580
比如说宽度就300

341
00:09:44,580 --> 00:09:46,260
这样就有了

342
00:09:46,260 --> 00:09:47,880
但是还是有点短

343
00:09:47,880 --> 00:09:49,220
咱就为了能看出清楚一点

344
00:09:49,220 --> 00:09:49,860
看看它多大

345
00:09:49,860 --> 00:09:52,100
比如说954 249

346
00:09:52,100 --> 00:09:54,020
就是954

347
00:09:54,020 --> 00:09:56,680
这就是249

348
00:09:56,680 --> 00:09:58,840
这就是我们的背景图

349
00:09:58,840 --> 00:10:00,320
理论上也是OK的

350
00:10:00,320 --> 00:10:01,560
你看两张图就都有了

351
00:10:01,560 --> 00:10:03,080
我们再往下看

352
00:10:03,080 --> 00:10:04,580
这是我们的前两种

353
00:10:04,580 --> 00:10:05,300
我们说了

354
00:10:05,300 --> 00:10:07,240
CSS里面可以直接去引入

355
00:10:07,240 --> 00:10:08,880
完了在我们当前的

356
00:10:08,880 --> 00:10:10,480
这个GS中如果使用的话

357
00:10:10,480 --> 00:10:11,680
需要这种import语法

358
00:10:11,680 --> 00:10:13,660
还有一种比较特殊的

359
00:10:13,660 --> 00:10:15,480
就是我们可以在ATML里面

360
00:10:15,480 --> 00:10:17,140
直接去这样来引入

361
00:10:17,140 --> 00:10:17,420
对吧

362
00:10:17,420 --> 00:10:18,480
image src

363
00:10:18,480 --> 00:10:20,160
你这样引的话也很恶心

364
00:10:20,160 --> 00:10:21,280
你需要这样引点账

365
00:10:21,280 --> 00:10:22,000
logo点PNG

366
00:10:22,000 --> 00:10:23,260
你可想而知了

367
00:10:23,260 --> 00:10:26,400
现在引好像就有点问题

368
00:10:26,400 --> 00:10:28,000
但是看起来还好

369
00:10:28,000 --> 00:10:29,620
因为这里面我们打包完以后

370
00:10:29,620 --> 00:10:30,500
他打包到什么

371
00:10:30,500 --> 00:10:31,800
是不是build的目录下

372
00:10:31,800 --> 00:10:33,060
build的目录下

373
00:10:33,060 --> 00:10:34,860
兵本就没有这样一个logoPNG

374
00:10:34,860 --> 00:10:35,720
是不是就引不到

375
00:10:35,720 --> 00:10:37,960
所以这时候就不是很高谱了

376
00:10:37,960 --> 00:10:40,760
这时候我们希望它是不是也可以变成什么

377
00:10:40,760 --> 00:10:42,180
你看看效果是吧

378
00:10:42,180 --> 00:10:42,860
我把它打开

379
00:10:42,860 --> 00:10:45,500
这里把它拉出来

380
00:10:45,500 --> 00:10:48,080
完了你看看我们希望引入的logo

381
00:10:48,080 --> 00:10:48,960
你是不是还是死的

382
00:10:48,960 --> 00:10:49,600
找不到

383
00:10:49,600 --> 00:10:52,580
我希望是不是也变成这种3D什么的格式

384
00:10:52,580 --> 00:10:53,740
这样一样

385
00:10:53,740 --> 00:10:55,320
它也需要一个loader

386
00:10:55,320 --> 00:10:56,960
这个loader可以帮我们看这件事

387
00:10:56,960 --> 00:10:58,760
这个loader是一个中国人写的

388
00:10:58,760 --> 00:11:02,220
它叫atml with image loader

389
00:11:02,220 --> 00:11:03,020
它这事的

390
00:11:03,020 --> 00:11:05,000
它专门可以解析我们的atml

391
00:11:05,000 --> 00:11:06,160
帮我们怎么样

392
00:11:06,160 --> 00:11:07,880
去编译我们这样一个图片

393
00:11:07,880 --> 00:11:08,900
这里一样

394
00:11:08,900 --> 00:11:10,340
我把它拿过来

395
00:11:10,340 --> 00:11:12,780
放给我们的WayPackConfig

396
00:11:12,780 --> 00:11:14,680
完了里面我就可以怎么做

397
00:11:14,680 --> 00:11:15,960
在这里边再加一个

398
00:11:15,960 --> 00:11:17,820
现在我们要匹配的是什么

399
00:11:17,820 --> 00:11:19,120
是不是HTML文件

400
00:11:19,120 --> 00:11:20,760
好你就这样写

401
00:11:20,760 --> 00:11:23,360
以HTML结尾的

402
00:11:23,360 --> 00:11:25,120
完了我们在这里去Use

403
00:11:25,120 --> 00:11:25,780
Use谁

404
00:11:25,780 --> 00:11:28,480
我们可以直接Use我们当前HTML

405
00:11:28,480 --> 00:11:30,400
好了位子对吧

406
00:11:30,400 --> 00:11:31,580
image-的

407
00:11:31,580 --> 00:11:32,960
这样的效果

408
00:11:32,960 --> 00:11:34,500
好了咱来试试

409
00:11:34,500 --> 00:11:37,000
这里一样直接重新启动就好了

410
00:11:37,000 --> 00:11:39,560
这时候应该里面

411
00:11:39,560 --> 00:11:41,140
理论上就会有三张图片了

412
00:11:41,140 --> 00:11:41,400
是吧

413
00:11:41,400 --> 00:11:42,420
看看啊

414
00:11:42,420 --> 00:11:42,880
我刷新

415
00:11:42,880 --> 00:11:44,500
时间看啊

416
00:11:44,500 --> 00:11:46,880
我们这路径是不是也变成了3D7这些

417
00:11:46,880 --> 00:11:47,180
是吧

418
00:11:47,180 --> 00:11:48,440
我那图片应该是三张

419
00:11:48,440 --> 00:11:48,880
哎

420
00:11:48,880 --> 00:11:50,260
你看是不是就OK了

421
00:11:50,260 --> 00:11:51,380
那现在啊

422
00:11:51,380 --> 00:11:53,420
我们就实现了这个图片的加载

423
00:11:53,420 --> 00:11:55,100
但是有的时候我们发现啊

424
00:11:55,100 --> 00:11:56,980
可能这个烟面上用的一些图片呀

425
00:11:56,980 --> 00:11:57,780
都非常小

426
00:11:57,780 --> 00:11:58,700
那这时候呢

427
00:11:58,700 --> 00:12:00,520
我们不希望他发一些ATP请求

428
00:12:00,520 --> 00:12:01,400
那这时候呢

429
00:12:01,400 --> 00:12:01,960
我们可以怎么样

430
00:12:01,960 --> 00:12:02,420
哎

431
00:12:02,420 --> 00:12:04,040
把它变成贝斯94

432
00:12:04,040 --> 00:12:05,260
那这时候啊

433
00:12:05,260 --> 00:12:06,200
我们有一个loader

434
00:12:06,200 --> 00:12:07,200
也可以跟这件事

435
00:12:07,200 --> 00:12:09,560
一般情况下我们用图片的话

436
00:12:09,560 --> 00:12:11,420
不会直接用FileLoader

437
00:12:11,420 --> 00:12:13,460
而且我们用的时候

438
00:12:13,460 --> 00:12:15,900
一般用这个东西叫UILLoader

439
00:12:15,900 --> 00:12:19,680
这里我们就把这个名字稍微改造一下

440
00:12:19,680 --> 00:12:20,160
UILLoader

441
00:12:20,160 --> 00:12:22,240
它的用法有什么特点

442
00:12:22,240 --> 00:12:23,480
就是说我们可以怎么样

443
00:12:23,480 --> 00:12:25,500
做一个限制

444
00:12:25,500 --> 00:12:28,500
比如说当我们的图片

445
00:12:28,500 --> 00:12:31,680
比如说小于多少k的时候

446
00:12:31,680 --> 00:12:32,800
多少k的时候

447
00:12:32,800 --> 00:12:33,820
完了用什么

448
00:12:33,820 --> 00:12:36,520
用Base64来转化

449
00:12:36,520 --> 00:12:38,420
来转化

450
00:12:38,420 --> 00:12:40,800
如果要是大于我们所谓的Base64

451
00:12:40,800 --> 00:12:42,200
大于这个限制的话

452
00:12:42,200 --> 00:12:42,960
那就用什么

453
00:12:42,960 --> 00:12:45,260
用file loader来把这张图片怎么样

454
00:12:45,260 --> 00:12:45,820
铲出

455
00:12:45,820 --> 00:12:48,700
这里面我们写的时候可以放个对象

456
00:12:48,700 --> 00:12:49,500
放对象

457
00:12:49,500 --> 00:12:50,320
我来个loader

458
00:12:50,320 --> 00:12:52,980
我直接把这个东西放进来

459
00:12:52,980 --> 00:12:55,660
并且我下面可以再配一个参数

460
00:12:55,660 --> 00:12:56,540
叫options是吧

461
00:12:56,540 --> 00:12:58,360
我loader的写法可以放个对象

462
00:12:58,360 --> 00:13:00,080
然后里面我给它一个什么

463
00:13:00,080 --> 00:13:01,240
叫限制limit

464
00:13:01,240 --> 00:13:02,680
比如说我给它个200

465
00:13:02,680 --> 00:13:03,960
200k对吧

466
00:13:03,960 --> 00:13:06,700
你说如果这个图片小于200k

467
00:13:06,700 --> 00:13:07,200
那好

468
00:13:07,200 --> 00:13:08,260
全部变成什么

469
00:13:08,260 --> 00:13:09,060
Base64

470
00:13:09,060 --> 00:13:10,080
那否则的话

471
00:13:10,080 --> 00:13:10,420
对吧

472
00:13:10,420 --> 00:13:11,900
否则用什么呢

473
00:13:11,900 --> 00:13:14,920
用这个file loader来产生什么

474
00:13:14,920 --> 00:13:17,800
产生真实的图片

475
00:13:17,800 --> 00:13:18,620
那好了

476
00:13:18,620 --> 00:13:19,980
我就把这个目录干掉

477
00:13:19,980 --> 00:13:20,920
已经删掉了

478
00:13:20,920 --> 00:13:22,940
那这里面我再来运行一下

479
00:13:22,940 --> 00:13:23,820
在这里

480
00:13:23,820 --> 00:13:26,580
run build

481
00:13:26,580 --> 00:13:27,740
看到这个实体文件

482
00:13:27,740 --> 00:13:28,320
更放心

483
00:13:28,320 --> 00:13:29,340
run build

484
00:13:29,340 --> 00:13:31,260
那此时

485
00:13:31,260 --> 00:13:32,400
我们现在这个图片

486
00:13:32,400 --> 00:13:33,320
肯定没有200k

487
00:13:33,320 --> 00:13:34,920
肯定它会怎么样

488
00:13:34,920 --> 00:13:36,060
真正的打包出来

489
00:13:36,060 --> 00:13:38,580
你看是不是变成我们的base64

490
00:13:38,580 --> 00:13:38,900
是吧

491
00:13:38,900 --> 00:13:40,600
慢点css里面是不是

492
00:13:40,600 --> 00:13:42,200
你看是变成了base64了

493
00:13:42,200 --> 00:13:45,400
同样atml里是不是也变成了base64

494
00:13:45,400 --> 00:13:47,000
同样我们的bundle里面

495
00:13:47,000 --> 00:13:49,240
是不是也会变成什么base64

496
00:13:49,240 --> 00:13:50,100
好了

497
00:13:50,100 --> 00:13:51,740
我们可以简单运行一下

498
00:13:51,740 --> 00:13:52,820
图片应该还是三张

499
00:13:52,820 --> 00:13:54,880
因为base64唯一的区别就是什么

500
00:13:54,880 --> 00:13:56,340
就是不会再去

501
00:13:56,340 --> 00:13:56,760
对吧

502
00:13:56,760 --> 00:13:58,160
加载我们的atp请求了

503
00:13:58,160 --> 00:14:00,080
但是我们的图片的大小

504
00:14:00,080 --> 00:14:01,620
其实base64的大小怎么样

505
00:14:01,620 --> 00:14:03,700
会比原文件大这种三分之一

506
00:14:03,700 --> 00:14:05,180
这是它的特点

507
00:14:05,180 --> 00:14:07,540
现在我们有了这样一个配置

508
00:14:07,540 --> 00:14:09,560
比如说我现在不加这个配置

509
00:14:09,560 --> 00:14:11,680
比如说我希望它还是正常产出

510
00:14:11,680 --> 00:14:12,900
这里我给它个一

511
00:14:12,900 --> 00:14:15,500
大于一字节的

512
00:14:15,500 --> 00:14:16,760
我就把它产出

