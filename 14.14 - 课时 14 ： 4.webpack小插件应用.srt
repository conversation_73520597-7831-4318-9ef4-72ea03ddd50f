1
00:00:00,000 --> 00:00:04,680
我们呢再来介绍一下这个webpack中的一些小的插件

2
00:00:04,680 --> 00:00:06,000
哎我们来看一看啊

3
00:00:06,000 --> 00:00:07,640
我们要介绍的有三个插件

4
00:00:07,640 --> 00:00:08,840
先能介绍一下

5
00:00:08,840 --> 00:00:10,040
第一个呢我们叫做什么呢

6
00:00:10,040 --> 00:00:12,280
叫它cleanwebpack plug-in

7
00:00:12,280 --> 00:00:13,800
哎我先把它放在这啊

8
00:00:13,800 --> 00:00:15,040
webpack plug-in

9
00:00:15,040 --> 00:00:16,680
这一听名字什么意思

10
00:00:16,680 --> 00:00:18,640
叫清除webpack的插件

11
00:00:18,640 --> 00:00:19,960
对当然了还有第二个

12
00:00:19,960 --> 00:00:21,160
我们把它写出来啊

13
00:00:21,160 --> 00:00:21,880
别急

14
00:00:21,880 --> 00:00:25,160
完了还有个叫什么叫copywebpack plug-in

15
00:00:25,160 --> 00:00:26,880
webpack plug-in

16
00:00:26,880 --> 00:00:28,560
然后第三个呢还有一个对吧

17
00:00:28,560 --> 00:00:29,920
叫什么叫banner

18
00:00:29,920 --> 00:00:30,520
对吧

19
00:00:30,520 --> 00:00:30,520
比如说wip

20
00:00:30,520 --> 00:01:00,520
比如说wip

21
00:01:00,520 --> 00:01:01,480
打包出来的东西怎么样

22
00:01:01,480 --> 00:01:03,640
是不是就别加到第四目录下了

23
00:01:03,640 --> 00:01:05,280
这里面把它断掉

24
00:01:05,280 --> 00:01:06,480
我希望干嘛

25
00:01:06,480 --> 00:01:09,200
是不是比如在这里我再来一个改个名字

26
00:01:09,200 --> 00:01:10,120
我打包的时候

27
00:01:10,120 --> 00:01:12,320
我希望它不叫index的叫home

28
00:01:12,320 --> 00:01:13,720
我这时候再打包是吧

29
00:01:13,720 --> 00:01:15,120
npm run build

30
00:01:15,120 --> 00:01:17,600
说这时候又会多出来个文件叫home

31
00:01:17,600 --> 00:01:21,040
你看稍等稍微有点慢出来了

32
00:01:21,040 --> 00:01:22,200
我希望干嘛

33
00:01:22,200 --> 00:01:24,720
是不是你可以每次在输出目录之前

34
00:01:24,720 --> 00:01:26,280
先把第四目录怎么样

35
00:01:26,280 --> 00:01:27,400
你给我删掉

36
00:01:27,400 --> 00:01:28,680
完了我只想看到什么

37
00:01:28,680 --> 00:01:29,880
最新的目录

38
00:01:29,880 --> 00:01:31,020
那这时候你可以怎么做呢

39
00:01:31,020 --> 00:01:31,920
其实也很简单

40
00:01:31,920 --> 00:01:33,220
我们就可以用这样的插件了

41
00:01:33,220 --> 00:01:35,160
叫clean webpack plug in

42
00:01:35,160 --> 00:01:36,540
那这里呢我们就安装

43
00:01:36,540 --> 00:01:37,180
一按对吧

44
00:01:37,180 --> 00:01:38,220
在这放大点啊

45
00:01:38,220 --> 00:01:41,160
一按二的比如说叫clean webpack

46
00:01:41,160 --> 00:01:41,440
对吧

47
00:01:41,440 --> 00:01:43,380
-webpack-plug in

48
00:01:43,380 --> 00:01:45,480
第三方呢

49
00:01:45,480 --> 00:01:46,740
这里就安装

50
00:01:46,740 --> 00:01:48,240
完了用法都非常像是吧

51
00:01:48,240 --> 00:01:49,780
我们需要一个这样的clean

52
00:01:49,780 --> 00:01:50,040
对吧

53
00:01:50,040 --> 00:01:54,820
大写的是个类clean webpack plug in

54
00:01:54,820 --> 00:01:57,840
webpack plug in

55
00:01:57,840 --> 00:01:59,180
等于require对吧

56
00:01:59,180 --> 00:02:01,420
我就叫他clean vpack plug in

57
00:02:01,420 --> 00:02:02,860
完了用的时候非常简单

58
00:02:02,860 --> 00:02:03,860
我就可以怎么样的

59
00:02:03,860 --> 00:02:05,580
是不是在这里面直接去new

60
00:02:05,580 --> 00:02:07,620
这样一个clean vpack plug in

61
00:02:07,620 --> 00:02:09,860
好家伙又改错文件了

62
00:02:09,860 --> 00:02:11,540
这伸手也是够那个的了

63
00:02:11,540 --> 00:02:12,940
我把它拿过来

64
00:02:12,940 --> 00:02:14,940
每次一改的时候就改错文件

65
00:02:14,940 --> 00:02:15,820
这里拿过来

66
00:02:15,820 --> 00:02:17,180
我在这里再去new一下是吧

67
00:02:17,180 --> 00:02:18,460
把这个文件粘过来

68
00:02:18,460 --> 00:02:20,500
我在底下去清空一下

69
00:02:20,500 --> 00:02:23,460
这里new clean vpack plug in

70
00:02:23,460 --> 00:02:25,460
他里面告诉我了需要传个路径

71
00:02:25,460 --> 00:02:27,340
就是你需要把谁清空是吧

72
00:02:27,340 --> 00:02:28,180
清空的话

73
00:02:28,180 --> 00:02:30,160
肯定就是当前我们这个第四目录

74
00:02:30,160 --> 00:02:31,000
当然不是对象

75
00:02:31,000 --> 00:02:34,340
那同样的你也可以放一个数组

76
00:02:34,340 --> 00:02:34,560
对吧

77
00:02:34,560 --> 00:02:35,920
告诉他我要轻松多个问题家

78
00:02:35,920 --> 00:02:36,820
这也是ok的

79
00:02:36,820 --> 00:02:37,960
现在表明

80
00:02:37,960 --> 00:02:39,360
我就是要把这个第四怎么样

81
00:02:39,360 --> 00:02:40,080
先删掉

82
00:02:40,080 --> 00:02:40,760
再打包

83
00:02:40,760 --> 00:02:41,440
试试

84
00:02:41,440 --> 00:02:42,240
哦

85
00:02:42,240 --> 00:02:42,940
别错了

86
00:02:42,940 --> 00:02:43,300
是吧

87
00:02:43,300 --> 00:02:43,880
run build

88
00:02:43,880 --> 00:02:46,620
看看能否把我们第四

89
00:02:46,620 --> 00:02:47,540
先删掉

90
00:02:47,540 --> 00:02:47,800
对吧

91
00:02:47,800 --> 00:02:49,380
你看是不是第四先没了

92
00:02:49,380 --> 00:02:50,140
完之后又铲出

93
00:02:50,140 --> 00:02:52,920
是不是这里面放的就是我们的最新的了

94
00:02:52,920 --> 00:02:53,760
那好了

95
00:02:53,760 --> 00:02:55,380
那我们知道这个查件的用法了

96
00:02:55,380 --> 00:02:56,800
那同样还有个东西叫什么

97
00:02:56,800 --> 00:02:58,360
叫copyypack planning

98
00:02:58,360 --> 00:03:00,820
比如说我们在写一些东西的时候

99
00:03:00,820 --> 00:03:02,560
可能真有这种需求

100
00:03:02,560 --> 00:03:03,520
我在这里面

101
00:03:03,520 --> 00:03:05,380
我有一个文件夹

102
00:03:05,380 --> 00:03:07,700
我叫他什么叫public

103
00:03:07,700 --> 00:03:10,840
我这个东西可能是一些

104
00:03:10,840 --> 00:03:11,800
或者是不叫public

105
00:03:11,800 --> 00:03:12,600
改个名叫doc

106
00:03:12,600 --> 00:03:13,500
我有些文档

107
00:03:13,500 --> 00:03:15,300
这个文档

108
00:03:15,300 --> 00:03:17,060
我最后也想打报到第四下

109
00:03:17,060 --> 00:03:19,340
这里面我写个就hello.tat

110
00:03:19,340 --> 00:03:21,860
这时候我希望把这个文件夹

111
00:03:21,860 --> 00:03:22,780
也拷贝到第四里

112
00:03:22,780 --> 00:03:24,020
这时候怎么做

113
00:03:24,020 --> 00:03:25,940
可能它只是一些信息

114
00:03:25,940 --> 00:03:27,080
一些输入数据是吧

115
00:03:27,080 --> 00:03:28,280
放放进去

116
00:03:28,280 --> 00:03:29,420
那这时候可以这样做啊

117
00:03:29,420 --> 00:03:30,340
就用这样一个插件

118
00:03:30,340 --> 00:03:31,840
它也是个第三方的

119
00:03:31,840 --> 00:03:32,600
安装

120
00:03:32,600 --> 00:03:34,500
一二二的叫copy

121
00:03:34,500 --> 00:03:37,700
wire pack plug-in

122
00:03:37,700 --> 00:03:39,900
完了这里呢一样引进来是吧

123
00:03:39,900 --> 00:03:41,480
light copy

124
00:03:41,480 --> 00:03:43,880
wire pack plug-in

125
00:03:43,880 --> 00:03:45,140
等于require对吧

126
00:03:45,140 --> 00:03:46,280
叫copy

127
00:03:46,280 --> 00:03:47,780
都有点没提示是吧

128
00:03:47,780 --> 00:03:50,180
copywire pack got plug-in

129
00:03:50,180 --> 00:03:51,180
用法呢一样

130
00:03:51,180 --> 00:03:52,180
也需要干嘛呢

131
00:03:52,180 --> 00:03:52,840
new是吧

132
00:03:52,840 --> 00:03:54,540
new

133
00:03:54,540 --> 00:03:57,040
虐他完了并且那里面你也怎么办呢

134
00:03:57,040 --> 00:03:57,200
哎

135
00:03:57,200 --> 00:03:58,000
应该放个书组

136
00:03:58,000 --> 00:03:59,640
因为你要拷贝的时候怎么样

137
00:03:59,640 --> 00:04:01,080
可能有很多个是吧

138
00:04:01,080 --> 00:04:01,240
哎

139
00:04:01,240 --> 00:04:02,280
比如说拷贝什么呢

140
00:04:02,280 --> 00:04:04,000
是不是从这个叫什么呢

141
00:04:04,000 --> 00:04:05,300
叫道克

142
00:04:05,300 --> 00:04:05,500
哎

143
00:04:05,500 --> 00:04:06,780
从这个道路下对吧

144
00:04:06,780 --> 00:04:06,900
啊

145
00:04:06,900 --> 00:04:07,400
doc

146
00:04:07,400 --> 00:04:09,780
冒号对吧

147
00:04:09,780 --> 00:04:10,940
完了这个第二杠

148
00:04:10,940 --> 00:04:11,900
doc目录

149
00:04:11,900 --> 00:04:12,740
完了到哪去呢

150
00:04:12,740 --> 00:04:14,900
兔兔这个第四目录

151
00:04:14,900 --> 00:04:14,980
哎

152
00:04:14,980 --> 00:04:15,840
这样就可以了

153
00:04:15,840 --> 00:04:16,900
你要拷贝多个怎么办

154
00:04:16,900 --> 00:04:17,840
你就写哎

155
00:04:17,840 --> 00:04:19,540
多个对象就可以了

156
00:04:19,540 --> 00:04:20,500
再来试一试啊

157
00:04:20,500 --> 00:04:22,880
看看能不能把道可给我考到第四代下

158
00:04:22,880 --> 00:04:23,580
是吧

159
00:04:23,580 --> 00:04:25,120
这里面一样 run build

160
00:04:25,120 --> 00:04:27,940
用法都非常简单

161
00:04:27,940 --> 00:04:30,480
它在我们的实习开发中用处还是很大

162
00:04:30,480 --> 00:04:33,220
这时候你看是不是又拷贝了一个hello.tit

163
00:04:33,220 --> 00:04:36,680
是把我们这个doc目录下的文件也拷贝到了

164
00:04:36,680 --> 00:04:39,060
好像没出来是吧

165
00:04:39,060 --> 00:04:42,960
他说第一次他有个hello.tit

166
00:04:42,960 --> 00:04:43,360
是吧

167
00:04:43,360 --> 00:04:45,240
但是告诉我已经零个字节

168
00:04:45,240 --> 00:04:46,180
我看看对不对

169
00:04:46,180 --> 00:04:47,180
cd

170
00:04:47,180 --> 00:04:48,940
ls

171
00:04:48,940 --> 00:04:51,820
好像是没有看到

172
00:04:51,820 --> 00:04:52,300
哈喽

173
00:04:52,300 --> 00:04:54,480
但是他告诉我这玩意已经出来了

174
00:04:54,480 --> 00:04:56,340
完了再来刷新刷新

175
00:04:56,340 --> 00:04:57,020
看看什么原因

176
00:04:57,020 --> 00:04:57,920
我把它呢

177
00:04:57,920 --> 00:04:58,820
要不这样吧

178
00:04:58,820 --> 00:04:59,180
doc

179
00:04:59,180 --> 00:05:00,780
这样fromdoc

180
00:05:00,780 --> 00:05:01,800
这个不加

181
00:05:01,800 --> 00:05:02,700
看看行不行

182
00:05:02,700 --> 00:05:04,920
进去是吧

183
00:05:04,920 --> 00:05:07,260
再去npm run build

184
00:05:07,260 --> 00:05:10,120
疫情的时候可能会有些问题

185
00:05:10,120 --> 00:05:10,940
稍等

186
00:05:10,940 --> 00:05:13,260
哦

187
00:05:13,260 --> 00:05:14,300
有可能是这个原因

188
00:05:14,300 --> 00:05:15,800
可能是这样的

189
00:05:15,800 --> 00:05:17,080
就是我们第一次怎么样

190
00:05:17,080 --> 00:05:17,780
他可能知性

191
00:05:17,780 --> 00:05:18,020
哦

192
00:05:18,020 --> 00:05:18,860
这考过来了吧

193
00:05:18,860 --> 00:05:19,360
你看有了

194
00:05:19,360 --> 00:05:21,000
是不是把我们当前的doc

195
00:05:21,000 --> 00:05:22,440
考到这个第四目录下

196
00:05:22,440 --> 00:05:23,680
这里面我把它再删掉

197
00:05:23,680 --> 00:05:24,500
不需要这个目录

198
00:05:24,500 --> 00:05:24,820
是吧

199
00:05:24,820 --> 00:05:25,900
我直接写个什么呢

200
00:05:25,900 --> 00:05:26,840
写个点杠

201
00:05:26,840 --> 00:05:28,220
应该是ok的

202
00:05:28,220 --> 00:05:29,080
再来一次

203
00:05:29,080 --> 00:05:30,040
run build

204
00:05:30,040 --> 00:05:32,220
刚才不知道为什么

205
00:05:32,220 --> 00:05:32,880
考没考出来

206
00:05:32,880 --> 00:05:33,680
我再刷一下

207
00:05:33,680 --> 00:05:34,880
看什么

208
00:05:34,880 --> 00:05:36,840
是不是有个hello

209
00:05:36,840 --> 00:05:37,360
点铁梯

210
00:05:37,360 --> 00:05:38,920
受相当于我们把doc

211
00:05:38,920 --> 00:05:40,240
这个原封不动内容怎么样

212
00:05:40,240 --> 00:05:41,120
进行了拷贝

213
00:05:41,120 --> 00:05:43,020
这是我们的拷贝插件

214
00:05:43,020 --> 00:05:45,020
拷贝插件

215
00:05:45,020 --> 00:05:47,120
完了最后一个

216
00:05:47,120 --> 00:05:47,880
就是我们所谓的

217
00:05:47,880 --> 00:05:49,400
这个叫版权声明插件

218
00:05:49,400 --> 00:05:50,120
比如说

219
00:05:50,120 --> 00:05:51,400
我们也写的一些代码

220
00:05:51,400 --> 00:05:52,640
那这代码到最后

221
00:05:52,640 --> 00:05:54,180
你看打包出来这个结果

222
00:05:54,180 --> 00:05:56,280
我们一般会看到人家这个前面会怎么样

223
00:05:56,280 --> 00:05:57,460
给我来个注释

224
00:05:57,460 --> 00:05:57,860
对吧

225
00:05:57,860 --> 00:05:59,820
完了说版权归谁谁谁所有

226
00:05:59,820 --> 00:06:01,200
谁谁谁写的这个代码

227
00:06:01,200 --> 00:06:01,480
是吧

228
00:06:01,480 --> 00:06:02,040
那好

229
00:06:02,040 --> 00:06:02,840
你这里也可以

230
00:06:02,840 --> 00:06:03,940
比如说在这里

231
00:06:03,940 --> 00:06:06,680
我们说了它是webpack内部的一个查件

232
00:06:06,680 --> 00:06:09,480
所以我们需要先把这个webpack怎么样

233
00:06:09,480 --> 00:06:10,460
引进来

234
00:06:10,460 --> 00:06:11,040
require

235
00:06:11,040 --> 00:06:12,920
require这个叫webpack

236
00:06:12,920 --> 00:06:15,880
那用法呢

237
00:06:15,880 --> 00:06:17,260
分号写错了

238
00:06:17,260 --> 00:06:19,000
我们可以怎么做

239
00:06:19,000 --> 00:06:19,820
还是插件

240
00:06:19,820 --> 00:06:20,180
是吧

241
00:06:20,180 --> 00:06:22,100
那一样就是你有他

242
00:06:22,100 --> 00:06:24,420
完了并且呢里面呢他有个方法叫什么

243
00:06:24,420 --> 00:06:25,820
叫Banner plugin是吧

244
00:06:25,820 --> 00:06:26,620
它是个插件

245
00:06:26,620 --> 00:06:28,140
他说了这可以放个字不串

246
00:06:28,140 --> 00:06:30,540
这个字不串啊就是你的版权声明

247
00:06:30,540 --> 00:06:30,820
哎

248
00:06:30,820 --> 00:06:32,620
比如说当前就是make对吧

249
00:06:32,620 --> 00:06:35,780
比如说制作在这个2019年对吧

250
00:06:35,780 --> 00:06:36,620
2019年

251
00:06:36,620 --> 00:06:39,020
完了后面呢我可以拜我的名字是吧

252
00:06:39,020 --> 00:06:39,180
哎

253
00:06:39,180 --> 00:06:40,180
就是我自己来写的

254
00:06:40,180 --> 00:06:43,260
那这样的话这句话呀就被插到每一个什么

255
00:06:43,260 --> 00:06:45,100
我们的打包车的结果的什么

256
00:06:45,100 --> 00:06:45,740
头部

257
00:06:45,740 --> 00:06:45,980
哎

258
00:06:45,980 --> 00:06:47,380
那当然了再来试一试是吧

259
00:06:47,380 --> 00:06:48,460
看个行不行啊

260
00:06:48,700 --> 00:06:50,860
这是我们的BunderWiPAC+E

261
00:06:50,860 --> 00:06:53,300
运行一下

262
00:06:53,300 --> 00:06:54,340
看看是吧

263
00:06:54,340 --> 00:06:56,300
这时候声明出来的结果

264
00:06:56,300 --> 00:06:57,140
再来看看

265
00:06:57,140 --> 00:06:58,500
第1次的Home

266
00:06:58,500 --> 00:07:00,220
是不是每个人怎么样

267
00:07:00,220 --> 00:07:01,340
都加了这样一个声明

268
00:07:01,340 --> 00:07:02,300
但是Home里面会不会加

269
00:07:02,300 --> 00:07:03,180
Home里不会是吧

270
00:07:03,180 --> 00:07:04,540
因为它不是我们的GS

271
00:07:04,540 --> 00:07:05,420
这样的话

272
00:07:05,420 --> 00:07:07,540
是不是就给每个人都加了一个版线声明

273
00:07:07,540 --> 00:07:09,020
现在我们就知道了

274
00:07:09,020 --> 00:07:11,420
其实WiPAC的插件用法还是比较单一的

275
00:07:11,420 --> 00:07:12,700
拿过来以后怎么样

276
00:07:12,700 --> 00:07:13,140
mew

277
00:07:13,140 --> 00:07:15,100
如果是它自带的怎么办

278
00:07:15,100 --> 00:07:16,580
需要引入WiPAC模块

279
00:07:16,580 --> 00:07:17,940
如果是第三方的怎么样

