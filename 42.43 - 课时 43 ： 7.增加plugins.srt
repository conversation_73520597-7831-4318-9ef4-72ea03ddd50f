1
00:00:00,000 --> 00:00:02,640
那最后一步呢

2
00:00:02,640 --> 00:00:04,560
我们把我们的插件的也加上去

3
00:00:04,560 --> 00:00:05,540
这里面为了简单

4
00:00:05,540 --> 00:00:06,800
我们就用这typeable

5
00:00:06,800 --> 00:00:08,500
完了加上我们一些沟图函数

6
00:00:08,500 --> 00:00:10,480
在每个需要用沟图的时候呢

7
00:00:10,480 --> 00:00:10,820
加上

8
00:00:10,820 --> 00:00:11,660
那好了

9
00:00:11,660 --> 00:00:12,440
我们来看一下

10
00:00:12,440 --> 00:00:13,400
这里呢

11
00:00:13,400 --> 00:00:14,760
我们需要先用到这个typeable

12
00:00:14,760 --> 00:00:15,560
那好

13
00:00:15,560 --> 00:00:16,940
我先把这type安装一下

14
00:00:16,940 --> 00:00:19,620
加ad typeable

15
00:00:19,620 --> 00:00:21,400
还借这个库吧

16
00:00:21,400 --> 00:00:22,660
这个库可以实现什么呢

17
00:00:22,660 --> 00:00:24,160
这个发不订阅是吧

18
00:00:24,160 --> 00:00:24,600
那好

19
00:00:24,600 --> 00:00:25,600
那这里面哦

20
00:00:25,600 --> 00:00:26,860
名字有有问题啊

21
00:00:26,860 --> 00:00:27,420
typeable

22
00:00:27,420 --> 00:00:28,600
tpble

23
00:00:28,600 --> 00:00:30,940
OK这里呢

24
00:00:30,940 --> 00:00:32,660
我就把这个模块呢引进来

25
00:00:32,660 --> 00:00:34,860
Let一个Typeable

26
00:00:34,860 --> 00:00:38,900
等于Require一个Typeable

27
00:00:38,900 --> 00:00:40,540
IQAE

28
00:00:40,540 --> 00:00:42,000
Require一个Typeable

29
00:00:42,000 --> 00:00:43,140
这里呢

30
00:00:43,140 --> 00:00:43,880
我们为了方便

31
00:00:43,880 --> 00:00:45,300
就直接用这个同步的钩子了

32
00:00:45,300 --> 00:00:45,860
就SingleHook

33
00:00:45,860 --> 00:00:47,420
完了在这里呢

34
00:00:47,420 --> 00:00:48,560
我们可以生命一些

35
00:00:48,560 --> 00:00:48,900
对吧

36
00:00:48,900 --> 00:00:49,900
比如说我们webpack

37
00:00:49,900 --> 00:00:50,620
有些生命周期

38
00:00:50,620 --> 00:00:51,760
那我可以在这里呢

39
00:00:51,760 --> 00:00:53,260
直接加上Hooks

40
00:00:53,260 --> 00:00:54,720
比如说刚开始

41
00:00:54,720 --> 00:00:56,020
那个叫EntryOption

42
00:00:56,020 --> 00:00:57,740
就当我们入口的时候啊

43
00:00:57,740 --> 00:00:59,640
有一个这样的入口选项的时候

44
00:00:59,640 --> 00:01:01,320
参数的时候会有这样一个钩子

45
00:01:01,320 --> 00:01:03,140
我在这NewSyncHook

46
00:01:03,140 --> 00:01:05,540
同样我们可以再加

47
00:01:05,540 --> 00:01:07,120
比如说出来这个可能有编译

48
00:01:07,120 --> 00:01:09,620
编译的时候我们也可以创建个钩子

49
00:01:09,620 --> 00:01:10,680
叫NewSyncHook

50
00:01:10,680 --> 00:01:13,360
同样还有个叫

51
00:01:13,360 --> 00:01:15,000
AfterCompile

52
00:01:15,000 --> 00:01:16,680
在编译之后

53
00:01:16,680 --> 00:01:19,040
我们同样也可以加一个叫NewSyncHook

54
00:01:19,040 --> 00:01:20,260
就一样了

55
00:01:20,260 --> 00:01:23,780
同样可能我们还需要有什么叫什么插件

56
00:01:23,780 --> 00:01:25,200
AfterPlugins

57
00:01:25,200 --> 00:01:28,040
一些比如到边缘买插件以后

58
00:01:28,040 --> 00:01:29,300
我需要执行这个钩子

59
00:01:29,300 --> 00:01:31,000
完了plug is

60
00:01:31,000 --> 00:01:33,620
完了冒号new一个thinghook

61
00:01:33,620 --> 00:01:36,240
完了同样我们还有

62
00:01:36,240 --> 00:01:37,800
还有比如说有run

63
00:01:37,800 --> 00:01:39,460
当我们运行的时候

64
00:01:39,460 --> 00:01:39,780
对吧

65
00:01:39,780 --> 00:01:41,040
也有一个这样的thinghook

66
00:01:41,040 --> 00:01:43,280
完了同样可能还有一个执行完成

67
00:01:43,280 --> 00:01:44,880
到或者还有发射文件

68
00:01:44,880 --> 00:01:45,100
是吧

69
00:01:45,100 --> 00:01:47,040
叫imit等于new一个thinghook

70
00:01:47,040 --> 00:01:49,120
完了同样还有一个叫完成

71
00:01:49,120 --> 00:01:50,280
叫newthinghook

72
00:01:50,280 --> 00:01:51,560
还有这么多钩子

73
00:01:51,560 --> 00:01:52,120
那好了

74
00:01:52,120 --> 00:01:52,940
那这些钩子

75
00:01:52,940 --> 00:01:54,960
就是我们需要在合适的地点去调用

76
00:01:54,960 --> 00:01:56,720
那现在我们要看一看

77
00:01:56,720 --> 00:01:58,200
当前用户有没有插件

78
00:01:58,200 --> 00:01:58,520
对吧

79
00:01:58,520 --> 00:02:01,240
如果传递了对吧

80
00:02:01,240 --> 00:02:03,080
传递了plugins参数

81
00:02:03,080 --> 00:02:04,540
那好

82
00:02:04,540 --> 00:02:05,480
那说明有是吧

83
00:02:05,480 --> 00:02:06,020
那好

84
00:02:06,020 --> 00:02:06,800
可以在这里面怎么样

85
00:02:06,800 --> 00:02:07,040
是不是

86
00:02:07,040 --> 00:02:09,180
light一个叫plugins

87
00:02:09,180 --> 00:02:12,380
等于我们的this的点什么呢

88
00:02:12,380 --> 00:02:13,620
是不是我们的安

89
00:02:13,620 --> 00:02:14,260
不是安锤

90
00:02:14,260 --> 00:02:16,140
是config里面有一个plugins

91
00:02:16,140 --> 00:02:18,840
drconfigdrplugins

92
00:02:18,840 --> 00:02:21,140
完了如果它又是有对吧

93
00:02:21,140 --> 00:02:21,900
它得是数组

94
00:02:21,900 --> 00:02:22,960
叫array

95
00:02:22,960 --> 00:02:24,600
drA字array

96
00:02:24,600 --> 00:02:24,940
对吧

97
00:02:24,940 --> 00:02:26,260
把这plugins往里放

98
00:02:26,260 --> 00:02:27,520
如果是书组的话

99
00:02:27,520 --> 00:02:27,880
那好了

100
00:02:27,880 --> 00:02:29,960
那我就让这每一个怎么样都执行

101
00:02:29,960 --> 00:02:31,160
那可以怎么做呢

102
00:02:31,160 --> 00:02:31,900
叫plugins

103
00:02:31,900 --> 00:02:32,740
第二什么呢

104
00:02:32,740 --> 00:02:33,440
叫for each

105
00:02:33,440 --> 00:02:35,080
for each

106
00:02:35,080 --> 00:02:35,860
往这里呢

107
00:02:35,860 --> 00:02:38,020
我就可以拿到每一个的这个plugin

108
00:02:38,020 --> 00:02:38,400
对吧

109
00:02:38,400 --> 00:02:39,260
每个插件

110
00:02:39,260 --> 00:02:40,520
往来这plugin呢

111
00:02:40,520 --> 00:02:40,900
干嘛呢

112
00:02:40,900 --> 00:02:41,340
执行

113
00:02:41,340 --> 00:02:42,440
第二apply

114
00:02:42,440 --> 00:02:43,340
往来里面呢

115
00:02:43,340 --> 00:02:46,040
我就放上我们当前的这样一个参数

116
00:02:46,040 --> 00:02:47,560
apply

117
00:02:47,560 --> 00:02:49,080
往来里面放上什么呢

118
00:02:49,080 --> 00:02:50,620
就放上我们对应的这个结果

119
00:02:50,620 --> 00:02:51,780
那结果的话呢

120
00:02:51,780 --> 00:02:52,840
就是我们当前的这个this

121
00:02:52,840 --> 00:02:53,480
也就是compile

122
00:02:53,480 --> 00:02:54,560
把它放进去

123
00:02:54,560 --> 00:02:56,520
意思执行

124
00:02:56,520 --> 00:02:57,740
那执行的时候呢

125
00:02:57,740 --> 00:02:58,440
我们可以干嘛呢

126
00:02:58,440 --> 00:02:58,600
是不是

127
00:02:58,600 --> 00:03:00,440
我可以在我们的代码里啊

128
00:03:00,440 --> 00:03:01,240
先摸几个插件

129
00:03:01,240 --> 00:03:02,000
我在这里

130
00:03:02,000 --> 00:03:02,860
来一个是吧

131
00:03:02,860 --> 00:03:04,320
我就写个插件吧

132
00:03:04,320 --> 00:03:05,100
叫leticp

133
00:03:05,100 --> 00:03:06,520
等于比如说class

134
00:03:06,520 --> 00:03:08,840
就这样写吧

135
00:03:08,840 --> 00:03:09,400
classp

136
00:03:09,400 --> 00:03:11,500
这个插件

137
00:03:11,500 --> 00:03:12,540
那插件的话呢

138
00:03:12,540 --> 00:03:13,300
应该会有一个

139
00:03:13,300 --> 00:03:14,480
比如说apply方法

140
00:03:14,480 --> 00:03:14,840
对吧

141
00:03:14,840 --> 00:03:15,600
掉的时候呢

142
00:03:15,600 --> 00:03:16,820
就是每个人执行

143
00:03:16,820 --> 00:03:18,080
掉的apply方法

144
00:03:18,080 --> 00:03:19,000
完了里面呢

145
00:03:19,000 --> 00:03:19,480
应该就会有

146
00:03:19,480 --> 00:03:20,120
这是c啊

147
00:03:20,120 --> 00:03:20,860
小写的

148
00:03:20,860 --> 00:03:21,600
我这里呢

149
00:03:21,600 --> 00:03:22,480
我就可以拿到什么呢

150
00:03:22,480 --> 00:03:23,540
这个compiler是吧

151
00:03:23,540 --> 00:03:24,260
我在这里呢

152
00:03:24,260 --> 00:03:25,060
就写上对吧

153
00:03:25,060 --> 00:03:25,620
Cardlog

154
00:03:25,620 --> 00:03:28,020
比如说打印一下就start随便

155
00:03:28,020 --> 00:03:29,680
那这里面呢

156
00:03:29,680 --> 00:03:31,340
我们是不是就应该在这里提供个参数

157
00:03:31,340 --> 00:03:33,000
叫Plugins

158
00:03:33,000 --> 00:03:34,260
往来里面的是个数组

159
00:03:34,260 --> 00:03:36,620
那我就需要new这样一个类是吧

160
00:03:36,620 --> 00:03:37,200
newp

161
00:03:37,200 --> 00:03:38,200
OK

162
00:03:38,200 --> 00:03:39,420
那new的时候呢

163
00:03:39,420 --> 00:03:40,260
是不是就在这里面

164
00:03:40,260 --> 00:03:42,300
相当于我会循环这个数组里的什么

165
00:03:42,300 --> 00:03:43,380
每一项在这里

166
00:03:43,380 --> 00:03:44,800
循环数里每一项

167
00:03:44,800 --> 00:03:46,200
说掉它的apply方法

168
00:03:46,200 --> 00:03:47,840
往来把这个this放去

169
00:03:47,840 --> 00:03:49,020
这个不是改变z的指向

170
00:03:49,020 --> 00:03:50,220
是它的apply方法

171
00:03:50,220 --> 00:03:51,320
往来去把这个this

172
00:03:51,320 --> 00:03:52,820
这个compiler怎么样传进去了

173
00:03:52,820 --> 00:03:55,140
那现在我们是不是在这里面可以干嘛呢

174
00:03:55,140 --> 00:03:56,300
比如说我希望

175
00:03:56,300 --> 00:03:58,200
干一件事是吧

176
00:03:58,200 --> 00:04:00,240
比如说在我们这个代码里啊

177
00:04:00,240 --> 00:04:01,800
需要干一件事

178
00:04:01,800 --> 00:04:03,400
好了比如说监听个事件吧

179
00:04:03,400 --> 00:04:04,040
叫compiler

180
00:04:04,040 --> 00:04:05,700
它有些钩子嘛

181
00:04:05,700 --> 00:04:05,940
对吧

182
00:04:05,940 --> 00:04:08,340
我就可以拿到当前的这个hooks

183
00:04:08,340 --> 00:04:09,740
完了里面呢

184
00:04:09,740 --> 00:04:10,440
可能就会有一个

185
00:04:10,440 --> 00:04:12,180
比如说叫他发射事件的时候

186
00:04:12,180 --> 00:04:12,400
对吧

187
00:04:12,400 --> 00:04:13,180
我想监听一下

188
00:04:13,180 --> 00:04:13,800
那好

189
00:04:13,800 --> 00:04:15,540
我可以在这里面加个type

190
00:04:15,540 --> 00:04:16,940
完了里面呢

191
00:04:16,940 --> 00:04:17,720
我就可以放上

192
00:04:17,720 --> 00:04:19,320
比如说这是个emate

193
00:04:19,320 --> 00:04:20,520
我后面给个函数

194
00:04:20,520 --> 00:04:21,880
完了函数里面呢

195
00:04:21,880 --> 00:04:23,120
是不是就会有对应的参数

196
00:04:23,120 --> 00:04:25,520
比如说叫世界发射

197
00:04:25,520 --> 00:04:25,900
对吧

198
00:04:25,900 --> 00:04:26,600
CodeLog

199
00:04:26,600 --> 00:04:28,280
比如说叫Emit世界

200
00:04:28,280 --> 00:04:30,300
Emit

201
00:04:30,300 --> 00:04:31,000
OK

202
00:04:31,000 --> 00:04:32,840
现在我们这样写完以后

203
00:04:32,840 --> 00:04:34,580
你会发现这个方法还不会执行

204
00:04:34,580 --> 00:04:35,080
对吧

205
00:04:35,080 --> 00:04:36,060
来跑一下

206
00:04:36,060 --> 00:04:38,360
是编译好以后没执行

207
00:04:38,360 --> 00:04:40,000
因为并没有怎么样

208
00:04:40,000 --> 00:04:41,760
绑定我们这样一个Code时间

209
00:04:41,760 --> 00:04:42,040
对吧

210
00:04:42,040 --> 00:04:42,560
你还要告

211
00:04:42,560 --> 00:04:43,440
你这只是怎么样

212
00:04:43,440 --> 00:04:43,920
是绑定

213
00:04:43,920 --> 00:04:44,800
但是没有发布

214
00:04:44,800 --> 00:04:46,780
我是不是应该把这些钩子

215
00:04:46,780 --> 00:04:48,660
依次在合适的位置怎么样

216
00:04:48,660 --> 00:04:49,360
进行使用

217
00:04:49,360 --> 00:04:51,740
比如说我们传entry option的时候

218
00:04:51,740 --> 00:04:53,920
我是不是可以在这里找一下

219
00:04:53,920 --> 00:04:56,160
在我们这个中文派克

220
00:04:56,160 --> 00:04:57,700
是不是在我们这个传餐的时候

221
00:04:57,700 --> 00:04:59,680
是不是就相当于掉那个方法

222
00:04:59,680 --> 00:05:01,300
那是不是应该是compiler

223
00:05:01,300 --> 00:05:04,640
完了DR我们的hooks

224
00:05:04,640 --> 00:05:06,720
完了DR我们的这个entry option

225
00:05:06,720 --> 00:05:07,320
对吧

226
00:05:07,320 --> 00:05:07,780
DR call

227
00:05:07,780 --> 00:05:09,880
是不是就是这样一个视频的东西

228
00:05:09,880 --> 00:05:12,000
那同样我们可以再往下看

229
00:05:12,000 --> 00:05:12,900
比如说编译呢

230
00:05:12,900 --> 00:05:15,180
那编译肯定是在我们刚才这样一个勾子里

231
00:05:15,180 --> 00:05:15,460
是吧

232
00:05:15,460 --> 00:05:16,380
编译编译

233
00:05:16,380 --> 00:05:17,280
回顾一下

234
00:05:17,280 --> 00:05:19,240
是不是在这个buildmodule的时候

235
00:05:19,240 --> 00:05:19,760
我是编译

236
00:05:19,760 --> 00:05:20,620
这里

237
00:05:20,620 --> 00:05:22,820
那这里面是就可以调用

238
00:05:22,820 --> 00:05:23,480
比如说叫

239
00:05:23,480 --> 00:05:25,700
this.什么呢

240
00:05:25,700 --> 00:05:27,360
叫this.hooks

241
00:05:27,360 --> 00:05:27,560
完了

242
00:05:27,560 --> 00:05:29,020
drcompile.call

243
00:05:29,020 --> 00:05:31,340
那同样还有一个编译完成

244
00:05:31,340 --> 00:05:31,600
是吧

245
00:05:31,600 --> 00:05:32,580
那应该叫call什么

246
00:05:32,580 --> 00:05:34,320
是不是叫compile

247
00:05:34,320 --> 00:05:35,020
叫aftercall

248
00:05:35,020 --> 00:05:35,300
是吧

249
00:05:35,300 --> 00:05:36,760
叫aftercompile

250
00:05:36,760 --> 00:05:37,820
call

251
00:05:37,820 --> 00:05:38,720
那同样啊

252
00:05:38,720 --> 00:05:39,660
比如发射文件的时候

253
00:05:39,660 --> 00:05:40,440
应该调的是哪个

254
00:05:40,440 --> 00:05:40,900
是不是在这里

255
00:05:40,900 --> 00:05:41,640
应该调一下

256
00:05:41,640 --> 00:05:42,280
发射完

257
00:05:42,280 --> 00:05:43,380
那这里面应该叫什么

258
00:05:43,380 --> 00:05:44,240
叫emit

259
00:05:44,240 --> 00:05:45,440
完事了以后

260
00:05:45,440 --> 00:05:46,480
还有个什么叫down是吧

261
00:05:46,480 --> 00:05:46,780
那好

262
00:05:46,780 --> 00:05:48,000
一次都贴上就好了

263
00:05:48,000 --> 00:05:48,520
到

264
00:05:48,520 --> 00:05:49,640
完了接着呢

265
00:05:49,640 --> 00:05:50,340
我们可以怎么办

266
00:05:50,340 --> 00:05:52,060
在run的时候也发射一个是吧

267
00:05:52,060 --> 00:05:54,360
在run的时候发射的就是run点靠

268
00:05:54,360 --> 00:05:57,740
那现在这个webpack整个的生命周期

269
00:05:57,740 --> 00:05:59,020
是不是我就可以在这里面怎么样

270
00:05:59,020 --> 00:06:00,240
全都给它夹上了

271
00:06:00,240 --> 00:06:01,940
那现在是不是这些方法就都OK了

272
00:06:01,940 --> 00:06:04,180
那同样咱来试试吧

273
00:06:04,180 --> 00:06:05,200
还有个after plug in

274
00:06:05,200 --> 00:06:06,380
那这里面我还没有用

275
00:06:06,380 --> 00:06:08,180
不是fulle吃完以后

276
00:06:08,180 --> 00:06:10,260
是不是this.hooks

277
00:06:10,260 --> 00:06:12,620
完了让它去靠

278
00:06:12,620 --> 00:06:14,080
靠执行

279
00:06:14,080 --> 00:06:16,700
完了之后怎么样是不是就OK了

280
00:06:16,700 --> 00:06:18,580
那现在我们来看看效果吧

281
00:06:18,580 --> 00:06:20,780
能不能实现这样一个插件的机制

282
00:06:20,780 --> 00:06:21,080
OK

283
00:06:21,080 --> 00:06:21,780
运行

284
00:06:21,780 --> 00:06:24,840
你看是不是就触发Emit了

285
00:06:24,840 --> 00:06:25,460
那好了

286
00:06:25,460 --> 00:06:29,000
我们是不是就可以自己去实现一些我们这样的插件

287
00:06:29,000 --> 00:06:30,380
完了在这是基础上怎么样

288
00:06:30,380 --> 00:06:31,260
就是来使用了

289
00:06:31,260 --> 00:06:32,600
比如说你再来一个除了P

290
00:06:32,600 --> 00:06:33,640
还可以再来个PE

291
00:06:33,640 --> 00:06:35,740
PE可能它要监听什么

292
00:06:35,740 --> 00:06:37,940
可能要发不知道Emit

293
00:06:37,940 --> 00:06:39,200
比如叫after plugin

294
00:06:39,200 --> 00:06:40,120
对吧

295
00:06:40,120 --> 00:06:41,400
你可以自己去选择是吧

296
00:06:41,400 --> 00:06:42,320
这里面的一样

297
00:06:42,320 --> 00:06:43,820
我把这PE放进来是吧

298
00:06:43,820 --> 00:06:45,100
有多个的话怎么样

299
00:06:45,100 --> 00:06:46,260
你就拗多次是吧

300
00:06:46,260 --> 00:06:46,900
拗它

301
00:06:46,900 --> 00:06:47,700
OK

302
00:06:47,700 --> 00:06:49,760
完了我们再来实现一下吧

303
00:06:49,760 --> 00:06:51,380
那就插件就搞定了

304
00:06:51,380 --> 00:06:53,640
哦发现type有问题是吧

305
00:06:53,640 --> 00:06:55,120
那名字是不是写错了

306
00:06:55,120 --> 00:06:56,240
叫afterplugin

307
00:06:56,240 --> 00:06:57,860
应该好像没有s啊

308
00:06:57,860 --> 00:07:00,940
在这个plugin之后运行

309
00:07:00,940 --> 00:07:03,120
哦还是有问题啊

310
00:07:03,120 --> 00:07:04,700
叫hooksafterplugin

311
00:07:04,700 --> 00:07:06,880
看看名字是不是写的还是有问题啊

312
00:07:06,880 --> 00:07:09,760
再找一下叫afterplugins

313
00:07:09,760 --> 00:07:10,440
没错是吧

314
00:07:10,440 --> 00:07:12,260
那这里面还是写的有问题啊

315
00:07:12,260 --> 00:07:12,960
改过来

316
00:07:12,960 --> 00:07:17,540
叫compile.hooksafterplugin.type是吧

317
00:07:17,700 --> 00:07:20,760
再来一次 这里走一下

318
00:07:20,760 --> 00:07:22,660
是不是就行了

319
00:07:22,660 --> 00:07:25,120
是不是插件发射完之后要走的插件

320
00:07:25,120 --> 00:07:25,860
这是什么

321
00:07:25,860 --> 00:07:28,520
当我们的文件发射出来的时候怎么样

322
00:07:28,520 --> 00:07:29,180
走的时间

323
00:07:29,180 --> 00:07:33,560
现在我们就实现了一个这样的减便宜的webpack

