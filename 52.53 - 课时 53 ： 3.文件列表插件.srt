1
00:00:00,000 --> 00:00:03,400
我们呢再来写一个文件列表插件

2
00:00:03,400 --> 00:00:04,800
这个插件的主要目的呢

3
00:00:04,800 --> 00:00:06,600
就是比如说我们先打包出来的资源

4
00:00:06,600 --> 00:00:07,300
可能有一个

5
00:00:07,300 --> 00:00:08,600
当然我可以多写几个啊

6
00:00:08,600 --> 00:00:09,300
比如说在这里呢

7
00:00:09,300 --> 00:00:10,900
我再去用一下这

8
00:00:10,900 --> 00:00:12,500
atml webpack plugin 吧

9
00:00:12,500 --> 00:00:15,800
wepack plugin 等于require

10
00:00:15,800 --> 00:00:18,000
html webpack plugin

11
00:00:18,000 --> 00:00:20,600
嗯评错了require

12
00:00:20,600 --> 00:00:23,400
atml webpack plugin

13
00:00:23,400 --> 00:00:25,200
那这里呢

14
00:00:25,200 --> 00:00:26,700
我们可以去安装一下这个插件啊

15
00:00:26,700 --> 00:00:27,800
把它安装一下

16
00:00:27,800 --> 00:00:29,000
一按二的

17
00:00:30,000 --> 00:00:40,100
那这里来个杠地完了此时我们可以直接用一下这个插件是吧这个东西都是内置的了你有他是吧完了里面呢我们需要一个叫弹配给这个摸吧

18
00:00:40,100 --> 00:00:43,560
这里呢我们就写src 下的

19
00:00:43,560 --> 00:00:45,760
index.gmail

20
00:00:45,760 --> 00:00:50,200
那这里呢我们就把它生成一下新建一个叫index.gmail

21
00:00:50,200 --> 00:00:55,200
我这里呢我们就随便写个公共摸板就好了什么都不放

22
00:00:55,200 --> 00:00:59,700
那现在我们是不是打包后现在肯定会出来两个文件吧再来试一下

23
00:01:00,000 --> 00:01:01,660
呃npx vipac

24
00:01:01,660 --> 00:01:05,300
嗯稍等现在是不是有一个bundle

25
00:01:05,300 --> 00:01:06,600
有一个index tml啊

26
00:01:06,600 --> 00:01:07,960
那这样有两个文件以后

27
00:01:07,960 --> 00:01:08,800
我想干嘛呢

28
00:01:08,800 --> 00:01:10,800
我想再在这个打包出来结果上啊

29
00:01:10,800 --> 00:01:12,100
再加一个资源文件

30
00:01:12,100 --> 00:01:13,440
比如说他给我帮我显示一下

31
00:01:13,440 --> 00:01:15,260
那当前打包有多少个文件

32
00:01:15,260 --> 00:01:16,660
文件大小是多少

33
00:01:16,660 --> 00:01:18,440
那这时候我们就需要怎么样

34
00:01:18,440 --> 00:01:20,000
是不是在我们打包的资源里面

35
00:01:20,000 --> 00:01:21,440
再加一个新的资源

36
00:01:21,440 --> 00:01:23,740
来描述当前我们打包出来的列表

37
00:01:23,740 --> 00:01:25,560
这里面很方便啊

38
00:01:25,560 --> 00:01:26,900
我们可以自己写这样的插件

39
00:01:26,900 --> 00:01:28,100
先看写法是吧

40
00:01:28,100 --> 00:01:29,040
第一步用的时候呢

41
00:01:29,040 --> 00:01:29,940
我们可能会这样

42
00:01:29,940 --> 00:01:33,640
叫lite一个我们叫他fail list plug in

43
00:01:33,640 --> 00:01:35,340
fail list plug in

44
00:01:35,340 --> 00:01:38,040
我要我把他引进来叫fail list

45
00:01:38,040 --> 00:01:40,340
当然也是插件里的叫plug in

46
00:01:40,340 --> 00:01:42,140
下的fail list plug in

47
00:01:42,140 --> 00:01:44,640
list plug in

48
00:01:44,640 --> 00:01:46,040
我用的时候非常简单

49
00:01:46,040 --> 00:01:46,840
比如说在这里呢

50
00:01:46,840 --> 00:01:48,340
可以直接去new他是吧

51
00:01:48,340 --> 00:01:48,840
new

52
00:01:48,840 --> 00:01:50,040
那肯定是希望怎么样

53
00:01:50,040 --> 00:01:53,340
是不是在文件没有没有发射出来之前

54
00:01:53,340 --> 00:01:54,340
比如文件已经创建好了

55
00:01:54,340 --> 00:01:55,240
要发射的时候

56
00:01:55,240 --> 00:01:57,040
我们是不是要再添一个文件

57
00:01:57,040 --> 00:01:58,140
那添的那个文件啊

58
00:01:58,140 --> 00:01:59,440
肯定给了个文件名

59
00:01:59,940 --> 00:02:00,960
这个文件名呢

60
00:02:00,960 --> 00:02:02,820
我就叫他fail或者叫list吧

61
00:02:02,820 --> 00:02:03,740
list.md

62
00:02:03,740 --> 00:02:05,620
这是我要这么去用

63
00:02:05,620 --> 00:02:06,960
那这时候好了

64
00:02:06,960 --> 00:02:08,640
那同样我把这东西往这拉一拉

65
00:02:08,640 --> 00:02:11,060
那现在我们需要在这里面创建个插件

66
00:02:11,060 --> 00:02:13,320
这个插件就叫他fail list

67
00:02:13,320 --> 00:02:13,740
对吧

68
00:02:13,740 --> 00:02:14,160
plug

69
00:02:14,160 --> 00:02:16,080
那他呢

70
00:02:16,080 --> 00:02:16,840
我就换个行

71
00:02:16,840 --> 00:02:18,040
这里放在这的啊

72
00:02:18,040 --> 00:02:20,100
那现在我们就需要写这样一个类了

73
00:02:20,100 --> 00:02:21,020
叫fail list

74
00:02:21,020 --> 00:02:22,520
plug class他

75
00:02:22,520 --> 00:02:24,100
完了他里面也一样

76
00:02:24,100 --> 00:02:24,880
有个apply

77
00:02:24,880 --> 00:02:25,800
那参数呢

78
00:02:25,800 --> 00:02:26,660
肯定也叫什么

79
00:02:26,660 --> 00:02:27,880
叫compiler是吧

80
00:02:27,880 --> 00:02:28,640
往这里呢

81
00:02:28,640 --> 00:02:29,340
我们还是一样

82
00:02:29,340 --> 00:02:32,080
那我们说了在文件发射之前

83
00:02:32,080 --> 00:02:33,280
那个事件叫什么呢

84
00:02:33,280 --> 00:02:34,220
就是文件对吧

85
00:02:34,220 --> 00:02:36,160
就文件已经准备好了

86
00:02:36,160 --> 00:02:39,800
文件已经准备好了

87
00:02:39,800 --> 00:02:40,680
完了干嘛呢

88
00:02:40,680 --> 00:02:41,520
要发射对吧

89
00:02:41,520 --> 00:02:42,260
准备好了

90
00:02:42,260 --> 00:02:44,360
要进行发射

91
00:02:44,360 --> 00:02:46,000
那靠的肯定是什么呢

92
00:02:46,000 --> 00:02:47,060
肯定发射是吧

93
00:02:47,060 --> 00:02:48,940
肯定是刚才我们用过的那个事件了

94
00:02:48,940 --> 00:02:49,740
叫什么叫Emit

95
00:02:49,740 --> 00:02:50,620
那好了

96
00:02:50,620 --> 00:02:52,600
我就在这个compiler的勾子上啊

97
00:02:52,600 --> 00:02:54,020
先听一下这个事件啊

98
00:02:54,020 --> 00:02:55,100
在这里compiler

99
00:02:55,100 --> 00:02:57,100
完了可以通过它的hooks

100
00:02:57,100 --> 00:02:59,360
往来去点什么呢

101
00:02:59,360 --> 00:03:00,520
是不是第二一妹他

102
00:03:00,520 --> 00:03:02,080
往来第二叫type

103
00:03:02,080 --> 00:03:03,320
那当然他是异步的

104
00:03:03,320 --> 00:03:04,880
那我这里面就用个sync也行

105
00:03:04,880 --> 00:03:05,120
是吧

106
00:03:05,120 --> 00:03:06,120
但挂同步得ok

107
00:03:06,120 --> 00:03:07,480
往来这里面呢

108
00:03:07,480 --> 00:03:08,740
我们都知道参数第一个呢

109
00:03:08,740 --> 00:03:10,120
是当前文件的插件

110
00:03:10,120 --> 00:03:10,440
是吧

111
00:03:10,440 --> 00:03:11,500
那第二个呢

112
00:03:11,500 --> 00:03:12,460
是我们这样一个

113
00:03:12,460 --> 00:03:14,020
一个东西是吧

114
00:03:14,020 --> 00:03:14,840
我说第一个参数

115
00:03:14,840 --> 00:03:16,000
放什么都ok啊

116
00:03:16,000 --> 00:03:16,580
再次说比

117
00:03:16,580 --> 00:03:17,460
说一下

118
00:03:17,460 --> 00:03:19,080
那这里面

119
00:03:19,080 --> 00:03:20,140
我们可以直接告诉他

120
00:03:20,140 --> 00:03:21,040
他当天参数叫什么

121
00:03:21,040 --> 00:03:22,340
叫compliation

122
00:03:22,340 --> 00:03:22,900
还记得吧

123
00:03:22,900 --> 00:03:23,780
我们打印一下

124
00:03:23,780 --> 00:03:24,560
看看是什么东西

125
00:03:24,560 --> 00:03:24,840
是吧

126
00:03:24,840 --> 00:03:27,080
这里面存了很多东西啊

127
00:03:27,100 --> 00:03:27,900
complayation

128
00:03:27,900 --> 00:03:29,740
打印一下

129
00:03:29,740 --> 00:03:30,800
运行一下

130
00:03:30,800 --> 00:03:31,940
bx webpack

131
00:03:31,940 --> 00:03:35,500
这里面也就是包含着当前我们打包的资源是吧

132
00:03:35,500 --> 00:03:36,660
里面资源都在哪呢

133
00:03:36,660 --> 00:03:38,540
哦他告诉我fail list 没定义

134
00:03:38,540 --> 00:03:39,900
这里面没有导出

135
00:03:39,900 --> 00:03:41,300
module is post

136
00:03:41,300 --> 00:03:43,760
等于我们这样一个fail list plugin

137
00:03:43,760 --> 00:03:45,800
这里我再来执行是吧

138
00:03:45,800 --> 00:03:46,640
再来执行一下

139
00:03:46,640 --> 00:03:50,760
你看这时候是不是就有很多很多属性的

140
00:03:50,760 --> 00:03:52,540
这里面我们现在要看的是什么呢

141
00:03:52,540 --> 00:03:54,300
是不是他的文件列表是吧

142
00:03:54,300 --> 00:03:55,160
哪个像呢

143
00:03:55,160 --> 00:03:56,640
你看这里面好多好多好多

144
00:03:57,100 --> 00:03:58,720
我们需要用到的一个东西叫这个

145
00:03:58,720 --> 00:03:59,440
我就直接用了

146
00:03:59,440 --> 00:03:59,440
叫什么叫assa

147
00:03:59,440 --> 00:04:29,440
叫什么叫assa

148
00:04:29,440 --> 00:04:31,140
各个sense里面再加一个文件

149
00:04:31,140 --> 00:04:32,280
那他这个文件怎么样

150
00:04:32,280 --> 00:04:33,640
也会被打包进来

151
00:04:33,640 --> 00:04:34,740
就这个意思啊

152
00:04:34,740 --> 00:04:36,040
那比如说咱来试一试

153
00:04:36,040 --> 00:04:37,240
那这里面怎么做呢

154
00:04:37,240 --> 00:04:37,880
我们首先啊

155
00:04:37,880 --> 00:04:40,180
他拥有这个插件的时候

156
00:04:40,180 --> 00:04:41,540
是传了一个参数

157
00:04:41,540 --> 00:04:44,700
那这参数我就不判断的存不存在了

158
00:04:44,700 --> 00:04:45,980
我就认为他肯定有了

159
00:04:45,980 --> 00:04:47,640
相当于你会传一个叫对吧

160
00:04:47,640 --> 00:04:48,240
fail name

161
00:04:48,240 --> 00:04:49,140
我就结构出来

162
00:04:49,140 --> 00:04:51,140
这里呢

163
00:04:51,140 --> 00:04:52,540
我就把这个fail name

164
00:04:52,540 --> 00:04:53,940
挂在我们的这个实例上

165
00:04:53,940 --> 00:04:54,840
fail name

166
00:04:54,840 --> 00:04:56,840
ok

167
00:04:56,840 --> 00:04:57,840
那挂完以后啊

168
00:04:57,840 --> 00:04:58,940
这里面可以怎么做呢

169
00:04:58,940 --> 00:05:02,080
是不是我需要在当前的assess里面怎么样

170
00:05:02,080 --> 00:05:03,900
再加上这样一个file name

171
00:05:03,900 --> 00:05:04,940
哎那好了怎么加呢

172
00:05:04,940 --> 00:05:06,080
那非常简单了是不是

173
00:05:06,080 --> 00:05:07,280
complication 对吧

174
00:05:07,280 --> 00:05:08,280
完了

175
00:05:08,280 --> 00:05:09,440
complic

176
00:05:09,440 --> 00:05:11,100
complication 是吧

177
00:05:11,100 --> 00:05:12,140
诶多了个c 是吧

178
00:05:12,140 --> 00:05:13,500
这里面名字改掉啊

179
00:05:13,500 --> 00:05:16,540
完了这里面我们可以怎么样呢

180
00:05:16,540 --> 00:05:18,600
是不是drassess对吧

181
00:05:18,600 --> 00:05:20,500
这拿到的就是所有的资源对吧

182
00:05:20,500 --> 00:05:21,500
我给他起个名字吧

183
00:05:21,500 --> 00:05:22,980
起个编名叫lite

184
00:05:22,980 --> 00:05:24,440
一个叫assess

185
00:05:24,440 --> 00:05:25,280
等于他

186
00:05:25,280 --> 00:05:26,000
完了这里面呢

187
00:05:26,000 --> 00:05:27,700
我可以通过assess对吧

188
00:05:27,700 --> 00:05:28,900
完了去放直

189
00:05:28,940 --> 00:05:31,240
那指呢就是我们当前的文件名

190
00:05:31,240 --> 00:05:34,340
完他的v6呢就应该什么是不是就是哎来个对象

191
00:05:34,340 --> 00:05:35,640
应该有个什么sauce

192
00:05:35,640 --> 00:05:37,940
那sauce里面呢肯定放的是个函数是吧

193
00:05:37,940 --> 00:05:39,140
那我就写写了啊

194
00:05:39,140 --> 00:05:41,640
完了同样还应该有个什么叫size

195
00:05:41,640 --> 00:05:45,140
那我们的原文件里面是不是应该包含着什么是不是哎

196
00:05:45,140 --> 00:05:46,840
是个他是个马克当文件是吧

197
00:05:46,840 --> 00:05:48,540
里面就应该怎么写的应该长这样

198
00:05:48,540 --> 00:05:50,040
比如说有大致模拟下

199
00:05:50,040 --> 00:05:52,440
可能会有个这样的文件叫test.md

200
00:05:52,440 --> 00:05:54,140
完了里面呢可能会这样标识对吧

201
00:05:54,140 --> 00:05:55,840
比如说当前的文件

202
00:05:55,840 --> 00:05:58,340
名是吧完了资源大小

203
00:05:58,940 --> 00:06:01,000
这里可能会有个井井

204
00:06:01,000 --> 00:06:01,380
对吧

205
00:06:01,380 --> 00:06:02,440
完了后面那个杠

206
00:06:02,440 --> 00:06:05,500
完了文件名可能叫什么bundle.js

207
00:06:05,500 --> 00:06:08,400
资源可能是什么23字节

208
00:06:08,400 --> 00:06:09,220
也这样的

209
00:06:09,220 --> 00:06:10,980
那明派了

210
00:06:10,980 --> 00:06:12,700
那现在我需要怎么做呢

211
00:06:12,700 --> 00:06:15,160
我是不是应该把当前拼出这样个字符串来

212
00:06:15,160 --> 00:06:15,980
作为它的内容

213
00:06:15,980 --> 00:06:16,500
sauce

214
00:06:16,500 --> 00:06:17,140
那好了

215
00:06:17,140 --> 00:06:18,280
我就在这儿能个内容

216
00:06:18,280 --> 00:06:18,520
是吧

217
00:06:18,520 --> 00:06:19,720
lite一个比如叫content

218
00:06:19,720 --> 00:06:21,020
content长什么样的

219
00:06:21,020 --> 00:06:21,660
一个反引号

220
00:06:21,660 --> 00:06:22,880
我就用木板字符串了

221
00:06:22,880 --> 00:06:24,000
这咔嚓这样过来

222
00:06:24,000 --> 00:06:26,860
这里1234

223
00:06:26,860 --> 00:06:27,140
是吧

224
00:06:27,140 --> 00:06:27,620
四个空格

225
00:06:27,620 --> 00:06:31,360
那这时候我们还需要去循环我们这个资源吧

226
00:06:31,360 --> 00:06:32,400
那循环的话呢

227
00:06:32,400 --> 00:06:34,400
我们可以用这个object的点什么呢

228
00:06:34,400 --> 00:06:35,280
点entress

229
00:06:35,280 --> 00:06:36,360
他可以啊

230
00:06:36,360 --> 00:06:39,180
把我们把我们的这样一个对象的变成一个数组啊

231
00:06:39,180 --> 00:06:39,860
entress

232
00:06:39,860 --> 00:06:40,580
t-i-e-s

233
00:06:40,580 --> 00:06:41,140
是吧

234
00:06:41,140 --> 00:06:41,400
完了

235
00:06:41,400 --> 00:06:42,000
entress

236
00:06:42,000 --> 00:06:43,040
完了里面呢

237
00:06:43,040 --> 00:06:45,760
我就可以放上谁的放上我们当前的这个assess

238
00:06:45,760 --> 00:06:47,840
放进来

239
00:06:47,840 --> 00:06:48,440
完了他呢

240
00:06:48,440 --> 00:06:49,000
可以去循环

241
00:06:49,000 --> 00:06:50,180
for each

242
00:06:50,180 --> 00:06:51,120
这里面呢

243
00:06:51,120 --> 00:06:52,140
我们就直接看看啊

244
00:06:52,140 --> 00:06:53,300
那他长什么样呢

245
00:06:53,300 --> 00:06:53,980
其实很简单

246
00:06:53,980 --> 00:06:54,700
就是一个数组

247
00:06:54,700 --> 00:06:57,180
比如说里面刚才我们看到了一个bundle是吧

248
00:06:57,180 --> 00:07:03,580
那他就文件名就是什么叫bundle.bundle.js

249
00:07:03,580 --> 00:07:04,440
完了后面呢

250
00:07:04,440 --> 00:07:05,840
放上一个比如说就是他的内容

251
00:07:05,840 --> 00:07:06,380
内容呢

252
00:07:06,380 --> 00:07:08,160
可能是比如说是大小

253
00:07:08,160 --> 00:07:08,420
对吧

254
00:07:08,420 --> 00:07:09,420
比如说是24k

255
00:07:09,420 --> 00:07:10,440
或者烂马七糟的

256
00:07:10,440 --> 00:07:11,480
就是一个什么呢

257
00:07:11,480 --> 00:07:12,880
一个是key的名字bundle

258
00:07:12,880 --> 00:07:13,600
完了第二呢

259
00:07:13,600 --> 00:07:14,540
就是一个对象

260
00:07:14,540 --> 00:07:14,860
是吧

261
00:07:14,860 --> 00:07:15,620
那这里面

262
00:07:15,620 --> 00:07:16,980
我就可以直接就写个对象吧

263
00:07:16,980 --> 00:07:17,740
就是一个对象

264
00:07:17,740 --> 00:07:19,480
那多个的话怎么样

265
00:07:19,480 --> 00:07:20,000
是不是这是

266
00:07:20,000 --> 00:07:21,840
这个对象中肯定有多个属性吧

267
00:07:21,840 --> 00:07:23,180
所以它会是一个二维数组

268
00:07:23,180 --> 00:07:24,620
那这样

269
00:07:24,620 --> 00:07:25,520
完了比如第二个呢

270
00:07:25,520 --> 00:07:26,040
是file name

271
00:07:26,040 --> 00:07:26,360
是吧

272
00:07:26,360 --> 00:07:26,720
那好

273
00:07:26,720 --> 00:07:27,460
这里面一样

274
00:07:27,460 --> 00:07:28,460
这个就叫fail name

275
00:07:28,460 --> 00:07:30,380
可以大致看一下

276
00:07:30,380 --> 00:07:31,520
fail name

277
00:07:31,520 --> 00:07:33,060
我这里呢又是一个什么

278
00:07:33,060 --> 00:07:34,120
呃就是一个什么

279
00:07:34,120 --> 00:07:34,880
index tml 是吧

280
00:07:34,880 --> 00:07:35,960
这里面改一下吧

281
00:07:35,960 --> 00:07:37,120
index tml

282
00:07:37,120 --> 00:07:37,760
完了后面呢

283
00:07:37,760 --> 00:07:39,160
同样后面也是个对象

284
00:07:39,160 --> 00:07:40,980
对象呢就是包含着他的这个什么

285
00:07:40,980 --> 00:07:41,420
值

286
00:07:41,420 --> 00:07:42,080
这是值是吧

287
00:07:42,080 --> 00:07:42,720
这也是值

288
00:07:42,720 --> 00:07:43,620
那好了啊

289
00:07:43,620 --> 00:07:44,880
那我循环的话

290
00:07:44,880 --> 00:07:46,820
这里面拿到的就是每一个的什么

291
00:07:46,820 --> 00:07:48,220
是不是就是每一项啊

292
00:07:48,220 --> 00:07:48,980
每个对象

293
00:07:48,980 --> 00:07:49,560
那这里面呢

294
00:07:49,560 --> 00:07:50,260
我就可以在这

295
00:07:50,260 --> 00:07:50,520
哎

296
00:07:50,520 --> 00:07:51,120
结构一下

297
00:07:51,120 --> 00:07:52,480
那第一个我们知道了

298
00:07:52,480 --> 00:07:53,560
这速度的拿到第一项

299
00:07:53,560 --> 00:07:54,780
是不是他的第一个参数是

300
00:07:54,780 --> 00:07:55,380
fail name

301
00:07:56,420 --> 00:07:58,120
fail file name

302
00:07:58,120 --> 00:07:58,860
完第二个呢

303
00:07:58,860 --> 00:07:59,820
就是它对应的什么

304
00:07:59,820 --> 00:08:00,780
是不是它的对象啊

305
00:08:00,780 --> 00:08:01,360
那对象呢

306
00:08:01,360 --> 00:08:03,620
我就叫它比如说叫叫状态

307
00:08:03,620 --> 00:08:04,420
状态对象是吧

308
00:08:04,420 --> 00:08:05,280
state obg

309
00:08:05,280 --> 00:08:07,180
完了此时候可以怎么办呢

310
00:08:07,180 --> 00:08:08,380
是不是就可以往里拼了

311
00:08:08,380 --> 00:08:09,520
比如说content

312
00:08:09,520 --> 00:08:10,560
哎应该怎么样

313
00:08:10,560 --> 00:08:12,480
是不是加等于完了反引号

314
00:08:12,480 --> 00:08:13,720
告完了空格

315
00:08:13,720 --> 00:08:14,580
完了里面呢

316
00:08:14,580 --> 00:08:16,120
是不是应该放上文件名是吧

317
00:08:16,120 --> 00:08:16,680
fail name

318
00:08:16,680 --> 00:08:18,480
完了后面呢

319
00:08:18,480 --> 00:08:19,820
12344空格

320
00:08:19,820 --> 00:08:21,460
所以对应上这个资源大小啊

321
00:08:21,460 --> 00:08:22,620
那大小怎么取呢

322
00:08:22,620 --> 00:08:24,280
同样state obg 上个

323
00:08:24,280 --> 00:08:24,920
我们说了

324
00:08:24,920 --> 00:08:26,380
它里面是不是有一个叫

325
00:08:26,420 --> 00:08:28,360
叫什么叫size的方法吧

326
00:08:28,360 --> 00:08:29,280
那好你就怎么办呢

327
00:08:29,280 --> 00:08:30,720
第二size

328
00:08:30,720 --> 00:08:32,320
当然我不要那个什么内容啊

329
00:08:32,320 --> 00:08:33,180
内容不需要

330
00:08:33,180 --> 00:08:35,720
那这时候我们是不是就拼出来这样的内容

331
00:08:35,720 --> 00:08:36,720
那内容怎么办呢

332
00:08:36,720 --> 00:08:39,980
是不是应该作为当前我们这个生成的这个什么

333
00:08:39,980 --> 00:08:41,260
list md

334
00:08:41,260 --> 00:08:42,720
它的内容 return

335
00:08:42,720 --> 00:08:44,220
我们就把它content的返回

336
00:08:44,220 --> 00:08:46,020
给content

337
00:08:46,020 --> 00:08:47,620
那毫毫无疑问对吧

338
00:08:47,620 --> 00:08:48,520
content是吧

339
00:08:48,520 --> 00:08:49,120
content

340
00:08:49,120 --> 00:08:50,020
毫无疑问

341
00:08:50,020 --> 00:08:51,380
那这个size应该什么样呢

342
00:08:51,380 --> 00:08:52,520
是不是就也是return

343
00:08:52,520 --> 00:08:54,280
那当然这里面应该放的什么是不是

344
00:08:54,280 --> 00:08:55,320
就是content的什么

345
00:08:55,320 --> 00:08:56,220
愣死了是吧

346
00:08:56,420 --> 00:08:57,560
那当然了

347
00:08:57,560 --> 00:08:59,120
这里面我们配置好了

348
00:08:59,120 --> 00:09:00,240
数据也有了

349
00:09:00,240 --> 00:09:01,480
那有了数据以后怎么样

350
00:09:01,480 --> 00:09:02,740
是不是就可以了呢

351
00:09:02,740 --> 00:09:04,220
但这里面我们用的是什么

352
00:09:04,220 --> 00:09:05,760
是不是type earthsync

353
00:09:05,760 --> 00:09:07,560
那我这里面可以怎么办

354
00:09:07,560 --> 00:09:09,520
是不是我还需要把它变成一个

355
00:09:09,520 --> 00:09:09,980
对吧

356
00:09:09,980 --> 00:09:10,840
你可以掉下回掉吧

357
00:09:10,840 --> 00:09:12,400
当然你可以把它变成一个非常简单

358
00:09:12,400 --> 00:09:13,440
那就用type就好了

359
00:09:13,440 --> 00:09:14,640
变成同步的是不是

360
00:09:14,640 --> 00:09:16,020
因为这里面没有一部逻辑嘛

361
00:09:16,020 --> 00:09:16,660
那好

362
00:09:16,660 --> 00:09:17,220
我在这里呢

363
00:09:17,220 --> 00:09:17,820
直接就写了

364
00:09:17,820 --> 00:09:18,680
成功以后

365
00:09:18,680 --> 00:09:20,740
那就把这个东西放到资源里去了

366
00:09:20,740 --> 00:09:21,740
那打包的时候

367
00:09:21,740 --> 00:09:23,400
他会看把速的资源怎么样

368
00:09:23,400 --> 00:09:24,240
都打包出来

369
00:09:24,240 --> 00:09:26,180
那在这运行一下啊

370
00:09:26,420 --> 00:09:28,320
平行NPSYPAC

371
00:09:28,320 --> 00:09:31,120
而理论上竟然是三个

372
00:09:31,120 --> 00:09:31,860
你看是不是Bundle

373
00:09:31,860 --> 00:09:32,740
Index还有List

374
00:09:32,740 --> 00:09:34,040
是不是跟我们说的一样

375
00:09:34,040 --> 00:09:35,960
只要你往资源里面添加了

376
00:09:35,960 --> 00:09:36,580
它会怎么样

377
00:09:36,580 --> 00:09:37,800
也会被打包出来的

378
00:09:37,800 --> 00:09:39,360
这就是资源对象

379
00:09:39,360 --> 00:09:40,720
资源对象

380
00:09:40,720 --> 00:09:42,760
好了再来看看

381
00:09:42,760 --> 00:09:44,940
这文件的内容是不是我们想要的

382
00:09:44,940 --> 00:09:46,020
我把这保存一下

383
00:09:46,020 --> 00:09:46,800
这不要了

384
00:09:46,800 --> 00:09:48,640
完了里面我看一下

385
00:09:48,640 --> 00:09:50,160
Fail Plugin

386
00:09:50,160 --> 00:09:51,920
完了里面我把它打开

387
00:09:51,920 --> 00:09:54,180
里面应该有一个叫ListMD

388
00:09:54,180 --> 00:09:56,140
哎呦感觉好像没换行

389
00:09:56,140 --> 00:09:56,540
是吧

390
00:09:56,540 --> 00:09:57,100
那这里呢

391
00:09:57,100 --> 00:09:58,700
我们再加个换行就搞好了是吧

392
00:09:58,700 --> 00:10:00,380
这里面那个-r-n是吧

393
00:10:00,380 --> 00:10:01,100
他不认

394
00:10:01,100 --> 00:10:03,180
-r-n是吧

395
00:10:03,180 --> 00:10:04,100
我这里也一样

396
00:10:04,100 --> 00:10:05,100
拼到一起去了啊

397
00:10:05,100 --> 00:10:06,100
这里再来一个

398
00:10:06,100 --> 00:10:08,140
没打一行换个行是吧

399
00:10:08,140 --> 00:10:12,980
ok 完了我们再来看看是吧

400
00:10:12,980 --> 00:10:13,540
这是哎

401
00:10:13,540 --> 00:10:15,780
bundle是3860个字节

402
00:10:15,780 --> 00:10:17,780
完了这个呢是319是吧

403
00:10:17,780 --> 00:10:18,580
你看是不是啊

404
00:10:18,580 --> 00:10:19,380
差不多吧

405
00:10:19,380 --> 00:10:19,940
嗯

406
00:10:19,940 --> 00:10:23,180
那现在我们就实现了一个文件列表的插件

407
00:10:23,180 --> 00:10:23,340
哎

408
00:10:23,340 --> 00:10:24,940
它的主要功能就是介绍了一个属性

409
00:10:24,940 --> 00:10:27,540
我们叫 Assess 包含着什么我们所有的

