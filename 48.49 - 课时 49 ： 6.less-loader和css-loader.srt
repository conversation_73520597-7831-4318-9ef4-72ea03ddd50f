1
00:00:00,000 --> 00:00:05,440
本节呢我们来讲一下如何处理我们webpack中的样式问题

2
00:00:05,440 --> 00:00:07,680
那这里呢我们可以再建上一个文件

3
00:00:07,680 --> 00:00:10,440
这个样式呢我们为了方便直接就一步到位了

4
00:00:10,440 --> 00:00:11,160
创建个lice

5
00:00:11,160 --> 00:00:13,940
完了里面呢我可以声明这样一个变量

6
00:00:13,940 --> 00:00:15,160
我就叫它红色好吧

7
00:00:15,160 --> 00:00:17,080
完了并且呢我来个body

8
00:00:17,080 --> 00:00:19,080
body呢我给它一个background吧

9
00:00:19,080 --> 00:00:20,140
看着明显一点啊

10
00:00:20,140 --> 00:00:21,340
我就把这颜色引进来

11
00:00:21,340 --> 00:00:22,180
add color

12
00:00:22,180 --> 00:00:24,140
那这样写完以后啊

13
00:00:24,140 --> 00:00:26,000
我们需要在gs中啊也引用一下

14
00:00:26,000 --> 00:00:28,440
那背景图呢我就先把它注掉啊

15
00:00:28,440 --> 00:00:29,260
为了看着清晰

16
00:00:29,260 --> 00:00:32,180
我就import.index.ls

17
00:00:32,180 --> 00:00:33,500
你想吧

18
00:00:33,500 --> 00:00:35,040
如果我能这样引用的话

19
00:00:35,040 --> 00:00:37,140
这个引用后的结果肯定能怎么样

20
00:00:37,140 --> 00:00:38,020
是不是能执行

21
00:00:38,020 --> 00:00:39,520
放到我们代码里面执行

22
00:00:39,520 --> 00:00:40,760
执行完以后可以怎么样

23
00:00:40,760 --> 00:00:41,640
是不是创建一个

24
00:00:41,640 --> 00:00:43,280
反正最终肯定创建个4条标签

25
00:00:43,280 --> 00:00:44,340
把4条页面里去

26
00:00:44,340 --> 00:00:45,140
好了

27
00:00:45,140 --> 00:00:47,600
我们现在就可以干这件事的

28
00:00:47,600 --> 00:00:49,600
我们需要配上这样一个loader

29
00:00:49,600 --> 00:00:51,720
在这里来一个是吧

30
00:00:51,720 --> 00:00:52,980
叫test匹配

31
00:00:52,980 --> 00:00:55,280
匹配一下是吧

32
00:00:55,280 --> 00:00:56,240
叫test

33
00:00:56,240 --> 00:00:59,980
test完了里面呢我就可以这样去写

34
00:00:59,980 --> 00:01:02,440
比如说我叫他以这个lice结尾的

35
00:01:02,440 --> 00:01:03,380
我们就匹配到

36
00:01:03,380 --> 00:01:05,340
完了里面呢我们应该配个数度

37
00:01:05,340 --> 00:01:06,140
因为多个嘛

38
00:01:06,140 --> 00:01:08,340
以前我们写的是不是lice loader

39
00:01:08,340 --> 00:01:10,380
完了还有我们这样一个叫什么来着

40
00:01:10,380 --> 00:01:12,380
叫这个style loader

41
00:01:12,380 --> 00:01:13,780
style loader

42
00:01:13,780 --> 00:01:16,240
最后中间呢还有一个叫什么叫css loader

43
00:01:16,240 --> 00:01:18,240
那我们需要这三个css

44
00:01:18,240 --> 00:01:19,240
这三个loader是吧

45
00:01:19,240 --> 00:01:20,040
css loader

46
00:01:20,040 --> 00:01:23,140
那好了我在这里呢就先把这三个loader建出来

47
00:01:23,140 --> 00:01:25,980
第一个叫我们的css loader

48
00:01:25,980 --> 00:01:27,340
也不叫less load 是吧

49
00:01:27,340 --> 00:01:28,540
顺序无所谓

50
00:01:28,540 --> 00:01:29,580
less load

51
00:01:29,580 --> 00:01:32,380
好了还应该有我们的css load

52
00:01:32,380 --> 00:01:36,820
还有我们的style load

53
00:01:36,820 --> 00:01:39,620
完了这里面我们来看

54
00:01:39,620 --> 00:01:41,220
我就先快速的粘出来

55
00:01:41,220 --> 00:01:42,420
这个叫loader

56
00:01:42,420 --> 00:01:45,300
并且它里面肯定放的是我们的圆码

57
00:01:45,300 --> 00:01:47,060
最后我就在这

58
00:01:47,060 --> 00:01:48,700
module.exe pause

59
00:01:48,700 --> 00:01:51,780
导出等于我们的loader

60
00:01:51,780 --> 00:01:53,700
OK这是一个

61
00:01:53,700 --> 00:01:54,780
这是第二个

62
00:01:54,780 --> 00:01:55,820
这是第三个

63
00:01:55,980 --> 00:01:59,260
那现在我们的顺序是先编译我们的lice

64
00:01:59,260 --> 00:02:01,600
lice处理完以后怎么交给css处理

65
00:02:01,600 --> 00:02:02,880
css处理完就会什么

66
00:02:02,880 --> 00:02:03,620
就会style处理

67
00:02:03,620 --> 00:02:05,380
那好了我们就来一步步做

68
00:02:05,380 --> 00:02:07,340
那处理lice的话肯定需要

69
00:02:07,340 --> 00:02:08,200
这已经报错了

70
00:02:08,200 --> 00:02:09,200
说着没有解析不了

71
00:02:09,200 --> 00:02:10,420
那我们需要怎么样

72
00:02:10,420 --> 00:02:12,140
先安装一个模块叫lice

73
00:02:12,140 --> 00:02:13,100
跑不了的事

74
00:02:13,100 --> 00:02:14,760
因为我们需要怎么样

75
00:02:14,760 --> 00:02:17,220
用lice呢编译出来我们这个css

76
00:02:17,220 --> 00:02:17,900
那好

77
00:02:17,900 --> 00:02:18,620
引进来吧

78
00:02:18,620 --> 00:02:20,620
requare我们的lice文件

79
00:02:20,620 --> 00:02:23,820
那我们呀可以通过lice上的一个方法

80
00:02:23,820 --> 00:02:24,280
叫render

81
00:02:24,280 --> 00:02:26,280
这时候我们渲染完以后

82
00:02:26,280 --> 00:02:27,820
肯定需要返回一个什么

83
00:02:27,820 --> 00:02:30,640
是不是把它编冻成我们的CSS的文件

84
00:02:30,640 --> 00:02:32,540
然后这里我就先弄个变量

85
00:02:32,540 --> 00:02:35,360
最后把这变量怎么样放在这

86
00:02:35,360 --> 00:02:38,000
渲染的时候肯定需要渲染我的圆码

87
00:02:38,000 --> 00:02:39,340
但这里面还要注意

88
00:02:39,340 --> 00:02:40,080
回调

89
00:02:40,080 --> 00:02:42,360
它的参数第一个是error

90
00:02:42,360 --> 00:02:43,340
第二个是什么

91
00:02:43,340 --> 00:02:45,140
第二个就是我们渲染又错误

92
00:02:45,140 --> 00:02:46,200
错误完以后怎么样

93
00:02:46,200 --> 00:02:47,620
正常的话是不是就有结果

94
00:02:47,620 --> 00:02:48,580
结果就是r

95
00:02:48,580 --> 00:02:51,120
这个r里面就有一个属性叫什么

96
00:02:51,120 --> 00:02:51,760
叫CSS

97
00:02:51,760 --> 00:02:54,120
它就是我们渲染完后的CSS

98
00:02:54,120 --> 00:02:55,380
这里我可以怎么样

99
00:02:55,380 --> 00:02:57,680
是不是叫CSS等于R.CSS

100
00:02:57,680 --> 00:02:58,680
这个意思是什么

101
00:02:58,680 --> 00:03:00,520
就是把我们渲染完的结果

102
00:03:00,520 --> 00:03:01,960
放给我们的CSS边量上

103
00:03:01,960 --> 00:03:03,180
最后把这边量怎么样

104
00:03:03,180 --> 00:03:03,660
返回

105
00:03:03,660 --> 00:03:05,320
返回完以后

106
00:03:05,320 --> 00:03:07,160
这时候我们是不是就可以怎么样

107
00:03:07,160 --> 00:03:09,080
把这样一个结果往下传

108
00:03:09,080 --> 00:03:11,120
传给我们的CSS loader

109
00:03:11,120 --> 00:03:12,220
这里面CSS loader

110
00:03:12,220 --> 00:03:13,400
我先什么都不做

111
00:03:13,400 --> 00:03:14,800
你看我把它什么都不做

112
00:03:14,800 --> 00:03:15,540
接着往下传

113
00:03:15,540 --> 00:03:16,600
传给什么

114
00:03:16,600 --> 00:03:17,600
我们style loader

115
00:03:17,600 --> 00:03:18,380
为什么不写

116
00:03:18,380 --> 00:03:21,300
因为CSS loader相对来讲比较复杂

117
00:03:21,300 --> 00:03:22,080
我先放在这

118
00:03:22,080 --> 00:03:23,580
传到之后

119
00:03:23,580 --> 00:03:24,800
是不是传给了我们styleloader

120
00:03:24,800 --> 00:03:26,460
styleloader拿到以后干嘛

121
00:03:26,460 --> 00:03:29,680
是不是应该导出这样一个字幕串

122
00:03:29,680 --> 00:03:30,640
这字幕串拿到以后

123
00:03:30,640 --> 00:03:32,200
什么可以塞到我们的函数里去

124
00:03:32,200 --> 00:03:33,460
这里非常简单

125
00:03:33,460 --> 00:03:37,720
我们可以在styleloader中

126
00:03:37,720 --> 00:03:39,820
loader中导出一个脚本

127
00:03:39,820 --> 00:03:42,640
导出一个脚本

128
00:03:42,640 --> 00:03:43,600
当时也说了

129
00:03:43,600 --> 00:03:44,860
最后的一个loader

130
00:03:44,860 --> 00:03:47,420
一定要导出一个阶子合法的脚本

131
00:03:47,420 --> 00:03:48,740
它才会被执行

132
00:03:48,740 --> 00:03:50,720
所以这里面我就可以干这件事了

133
00:03:50,720 --> 00:03:51,100
怎么写

134
00:03:51,100 --> 00:03:52,700
我就弄一个字幕串

135
00:03:52,700 --> 00:03:53,940
是个脚本字符串

136
00:03:53,940 --> 00:03:55,300
当然有个等号

137
00:03:55,300 --> 00:03:57,720
完了最后

138
00:03:57,720 --> 00:03:59,920
我就把这样一个字符串返回

139
00:03:59,920 --> 00:04:02,660
这里我要稍微注意一点

140
00:04:02,660 --> 00:04:03,920
我需要干嘛

141
00:04:03,920 --> 00:04:06,140
是不是在内部返回的肯定是个脚本

142
00:04:06,140 --> 00:04:06,860
脚本字符串

143
00:04:06,860 --> 00:04:08,680
那边拿过来导进来

144
00:04:08,680 --> 00:04:10,160
导进来以后去用

145
00:04:10,160 --> 00:04:12,460
这里我们需要是创建一个style

146
00:04:12,460 --> 00:04:13,440
等于document

147
00:04:13,440 --> 00:04:14,040
很简单

148
00:04:14,040 --> 00:04:16,380
create一个element

149
00:04:16,380 --> 00:04:17,420
完了里面

150
00:04:17,420 --> 00:04:19,240
我就放上这样一个style标签

151
00:04:19,240 --> 00:04:20,720
style里面

152
00:04:20,720 --> 00:04:22,720
是不是就应该往他里面放内容

153
00:04:22,720 --> 00:04:23,620
Innetmail

154
00:04:23,620 --> 00:04:26,300
但这里面你不能直接把soud放在这

155
00:04:26,300 --> 00:04:27,660
你放在这就变成啥了

156
00:04:27,660 --> 00:04:28,840
是不是足串了

157
00:04:28,840 --> 00:04:29,780
你也不能这样做

158
00:04:29,780 --> 00:04:30,960
这不是变量吗

159
00:04:30,960 --> 00:04:32,240
但是你放下来以后

160
00:04:32,240 --> 00:04:33,820
相当于你这样写的脱带

161
00:04:33,820 --> 00:04:36,100
叫style.innetmail

162
00:04:36,100 --> 00:04:37,960
等于body

163
00:04:37,960 --> 00:04:39,600
这是啥东西

164
00:04:39,600 --> 00:04:40,580
你肯定没人认识

165
00:04:40,580 --> 00:04:42,320
如果你可以在外面再加个双引号

166
00:04:42,320 --> 00:04:42,980
不好意思

167
00:04:42,980 --> 00:04:44,020
加了双引号

168
00:04:44,020 --> 00:04:45,060
他还是不认

169
00:04:45,060 --> 00:04:46,480
因为双引号不能怎么样

170
00:04:46,480 --> 00:04:47,160
换行

171
00:04:47,160 --> 00:04:49,080
这时候我要怎么做

172
00:04:49,080 --> 00:04:50,060
教大家个技巧

173
00:04:50,060 --> 00:04:52,620
我这里可以使用这样一个语法

174
00:04:52,620 --> 00:04:54,860
叫json.sg5

175
00:04:54,860 --> 00:04:57,760
他说我们可以把这个原码怎么样

176
00:04:57,760 --> 00:04:58,840
转成一个字符串

177
00:04:58,840 --> 00:05:03,100
而且json.sg5会把什么-2-n都转化成什么

178
00:05:03,100 --> 00:05:05,480
不是就换上汇车都转化成-2-n

179
00:05:05,480 --> 00:05:07,740
这样的话他就变成啥样了

180
00:05:07,740 --> 00:05:08,440
大致就这样的

181
00:05:08,440 --> 00:05:09,360
给大家写一写

182
00:05:09,360 --> 00:05:12,540
等于

183
00:05:12,540 --> 00:05:13,840
刷一号

184
00:05:13,840 --> 00:05:14,820
什么body

185
00:05:14,820 --> 00:05:15,360
代括号

186
00:05:15,360 --> 00:05:16,200
-2-n

187
00:05:16,200 --> 00:05:18,520
我来个background ride

188
00:05:18,520 --> 00:05:19,020
这样

189
00:05:19,020 --> 00:05:20,560
这样写下来

190
00:05:20,560 --> 00:05:21,720
是不是就更加美好了

191
00:05:21,720 --> 00:05:23,320
那现在好了

192
00:05:23,320 --> 00:05:24,480
那style标签有了

193
00:05:24,480 --> 00:05:25,140
还差一步

194
00:05:25,140 --> 00:05:26,040
style怎么样

195
00:05:26,040 --> 00:05:27,780
你要把它插到我们的头部

196
00:05:27,780 --> 00:05:28,140
对吧

197
00:05:28,140 --> 00:05:31,600
document.head.open.child

198
00:05:31,600 --> 00:05:33,420
完了里面放上谁呢

199
00:05:33,420 --> 00:05:35,640
就放上我们这样一个style元素

200
00:05:35,640 --> 00:05:39,580
那到时候这个字固串会放到我们的什么

201
00:05:39,580 --> 00:05:41,420
到我们的build文件里面

202
00:05:41,420 --> 00:05:42,700
会到到我们这个函数里

203
00:05:42,700 --> 00:05:44,760
那这个函数里的代码怎么会执行

204
00:05:44,760 --> 00:05:45,800
那执行的时候呢

205
00:05:45,800 --> 00:05:47,220
就把我们这样一个文件

206
00:05:47,220 --> 00:05:48,760
这个style标签生成

207
00:05:48,760 --> 00:05:49,820
生成完以后怎么样

208
00:05:49,820 --> 00:05:50,780
插入到一面中

209
00:05:50,780 --> 00:05:51,640
那好了

210
00:05:51,640 --> 00:05:52,800
那我们在这儿呢

211
00:05:52,800 --> 00:05:53,640
来试一试

212
00:05:53,640 --> 00:05:54,980
看看是不是这样啊

213
00:05:54,980 --> 00:05:55,480
NPX

214
00:05:55,480 --> 00:05:56,160
不外拍

215
00:05:56,160 --> 00:05:59,060
稍等

216
00:05:59,060 --> 00:06:00,660
现在没有包错是吧

217
00:06:00,660 --> 00:06:02,100
看看行不行呢

218
00:06:02,100 --> 00:06:03,280
看看我们的bill在里面

219
00:06:03,280 --> 00:06:05,200
是不是我们返回的这个字不串

220
00:06:05,200 --> 00:06:07,100
稳稳当当的放到这里啊

221
00:06:07,100 --> 00:06:08,600
那放到这里面

222
00:06:08,600 --> 00:06:08,920
OK

223
00:06:08,920 --> 00:06:10,500
那我们页面一运行的时候

224
00:06:10,500 --> 00:06:11,720
是不是就会执行这个脚本呀

225
00:06:11,720 --> 00:06:13,480
因为我这里面有一个语法

226
00:06:13,480 --> 00:06:14,760
这个语法呢

227
00:06:14,760 --> 00:06:15,080
哦

228
00:06:15,080 --> 00:06:15,960
在哪用的

229
00:06:15,960 --> 00:06:16,300
你看是吧

230
00:06:16,300 --> 00:06:17,000
在这用的吧

231
00:06:17,000 --> 00:06:18,080
是不是我在这里面

232
00:06:18,080 --> 00:06:19,180
这是我们的印带js

233
00:06:19,180 --> 00:06:20,140
往来里面呢

234
00:06:20,140 --> 00:06:21,720
会去引用我们这样一个

235
00:06:21,720 --> 00:06:23,400
好像没有引是吧

236
00:06:23,400 --> 00:06:24,780
我看看你秘密啊

237
00:06:24,780 --> 00:06:25,900
引了是吧

238
00:06:25,900 --> 00:06:26,660
引了

239
00:06:26,660 --> 00:06:27,960
那引的时候是不是在这里面

240
00:06:27,960 --> 00:06:29,240
我的require没看到呢

241
00:06:29,240 --> 00:06:29,980
怎么是吧

242
00:06:29,980 --> 00:06:30,840
找一下啊

243
00:06:30,840 --> 00:06:31,160
找一下

244
00:06:31,160 --> 00:06:32,900
这里呢是吧

245
00:06:32,900 --> 00:06:34,520
你看这挺小的是吧

246
00:06:34,520 --> 00:06:35,980
说我在这引了一个less

247
00:06:35,980 --> 00:06:37,320
那引的less怎么样

248
00:06:37,320 --> 00:06:41,120
是不是就会去帮我们去调用我们当前这样一个模块

249
00:06:41,120 --> 00:06:41,680
模块呢

250
00:06:41,680 --> 00:06:42,160
模块呢

251
00:06:42,160 --> 00:06:42,760
在这儿是吧

252
00:06:42,760 --> 00:06:43,540
调用这个模块

253
00:06:43,540 --> 00:06:44,220
那一执行

254
00:06:44,220 --> 00:06:45,240
是不是把这函数怎么样

255
00:06:45,240 --> 00:06:45,800
执行了

256
00:06:45,800 --> 00:06:47,360
那执行的时候怎么样

257
00:06:47,360 --> 00:06:49,200
是不是会把这个代码运行一下

258
00:06:49,200 --> 00:06:49,900
好了

259
00:06:49,900 --> 00:06:51,620
看看最终能不能变红

260
00:06:51,620 --> 00:06:52,420
刷新

261
00:06:52,420 --> 00:06:53,700
是不是OK了

262
00:06:53,700 --> 00:06:57,200
现在我们就实现了一个less loader

263
00:06:57,200 --> 00:06:58,780
还有我们所谓的start loader

264
00:06:58,780 --> 00:07:00,860
但是还差一个叫css loader

265
00:07:00,860 --> 00:07:03,180
因为css loader稍微复杂一点

266
00:07:03,180 --> 00:07:06,640
现在再来去写这样一个css loader

