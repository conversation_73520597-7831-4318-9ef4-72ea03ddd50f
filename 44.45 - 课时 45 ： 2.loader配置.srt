1
00:00:00,000 --> 00:00:02,060
那现在呢

2
00:00:02,060 --> 00:00:04,020
我们就来讲一下怎么配置多个loader

3
00:00:04,020 --> 00:00:04,620
那好了

4
00:00:04,620 --> 00:00:06,080
那我们怎么配置多个loader呢

5
00:00:06,080 --> 00:00:06,740
非常方便

6
00:00:06,740 --> 00:00:08,620
也说我们需要把原来这个loader呢

7
00:00:08,620 --> 00:00:10,080
写成一个数组的方式

8
00:00:10,080 --> 00:00:10,960
比如说这样

9
00:00:10,960 --> 00:00:11,260
对吧

10
00:00:11,260 --> 00:00:11,680
loader1

11
00:00:11,680 --> 00:00:14,000
那现在我们说了loader的直循顺序呢

12
00:00:14,000 --> 00:00:15,480
默认就是从右向左

13
00:00:15,480 --> 00:00:16,660
像我们的lessloader

14
00:00:16,660 --> 00:00:17,820
导到cssloader

15
00:00:17,820 --> 00:00:18,260
最后呢

16
00:00:18,260 --> 00:00:19,340
变到styleloader里去

17
00:00:19,340 --> 00:00:20,800
这里一样loader2

18
00:00:20,800 --> 00:00:22,000
比如说在前面呢

19
00:00:22,000 --> 00:00:23,600
我再加一个loader3

20
00:00:23,600 --> 00:00:25,820
那好了

21
00:00:25,820 --> 00:00:26,540
我们来看一下

22
00:00:26,540 --> 00:00:27,640
把这个文件呢

23
00:00:27,640 --> 00:00:28,280
也拷贝一份

24
00:00:28,280 --> 00:00:29,240
为了表示一下

25
00:00:29,240 --> 00:00:29,440
对吧

26
00:00:29,440 --> 00:00:30,640
这里面我就写个注释

27
00:00:30,640 --> 00:00:31,600
比如说这个呢

28
00:00:31,600 --> 00:00:32,700
叫loader1

29
00:00:32,700 --> 00:00:34,580
波浪线

30
00:00:34,580 --> 00:00:36,240
好保存一下是吧

31
00:00:36,240 --> 00:00:37,580
往来我们再粘几分啊

32
00:00:37,580 --> 00:00:38,240
第二个

33
00:00:38,240 --> 00:00:41,160
第二个是吧

34
00:00:41,160 --> 00:00:41,880
完了第三个

35
00:00:41,880 --> 00:00:43,540
那这样的话呢

36
00:00:43,540 --> 00:00:44,460
我们把名字改一下

37
00:00:44,460 --> 00:00:45,780
三这个呢是二

38
00:00:45,780 --> 00:00:47,700
那我们来看一看吧

39
00:00:47,700 --> 00:00:48,560
执行的顺序呢

40
00:00:48,560 --> 00:00:49,540
是不是这样的啊

41
00:00:49,540 --> 00:00:50,840
是不是能达到我们这样一个

42
00:00:50,840 --> 00:00:51,360
对吧

43
00:00:51,360 --> 00:00:52,780
执行123的效果

44
00:00:52,780 --> 00:00:54,960
那顺序是不是123呀

45
00:00:54,960 --> 00:00:55,780
也说我们呢

46
00:00:55,780 --> 00:00:56,700
默认的loader啊

47
00:00:56,700 --> 00:00:58,180
是从右往左执行的

48
00:00:58,180 --> 00:00:59,040
那同样啊

49
00:00:59,040 --> 00:01:01,580
除了这种配法的还有一种其他的配法

50
00:01:01,580 --> 00:01:04,380
我们可以把loader拆开来写把它住掉

51
00:01:04,380 --> 00:01:05,180
这里的一样

52
00:01:05,180 --> 00:01:06,540
我们可以这样的去做

53
00:01:06,540 --> 00:01:11,280
比如说test来个叫什么的叫以JS结尾的

54
00:01:11,280 --> 00:01:13,380
我们同样可以使用这样一个loader

55
00:01:13,380 --> 00:01:15,740
用字比如说我们可以写个对象

56
00:01:15,740 --> 00:01:19,640
还记得 loader使用我们的叫loader loader

57
00:01:19,640 --> 00:01:21,400
一是吧

58
00:01:21,400 --> 00:01:23,380
同样我们可以多负几份是吧

59
00:01:23,380 --> 00:01:25,000
这是一个两个

60
00:01:25,000 --> 00:01:26,100
哎这也是可以的

61
00:01:26,100 --> 00:01:27,000
就不写成一个书组

62
00:01:27,000 --> 00:01:28,280
完了写成这样一个对象

63
00:01:28,280 --> 00:01:29,580
完了三个的格式

64
00:01:29,580 --> 00:01:30,080
那好

65
00:01:30,080 --> 00:01:31,380
我们把它再跑一下

66
00:01:31,380 --> 00:01:34,680
哎这时候效果呢是321

67
00:01:34,680 --> 00:01:35,380
那你发现了吗

68
00:01:35,380 --> 00:01:37,480
是不是我们这个代码也是倒序执行的

69
00:01:37,480 --> 00:01:39,680
也说我们先走的3再走到2再走1

70
00:01:39,680 --> 00:01:40,880
所以说我们要写顺序的话

71
00:01:40,880 --> 00:01:42,880
是不是你会这样哎来个312啊

72
00:01:42,880 --> 00:01:45,180
但是这样写的话呀很麻烦是吧

73
00:01:45,180 --> 00:01:46,980
那虽然是可以的没问题

74
00:01:46,980 --> 00:01:49,580
看个效果123

75
00:01:49,580 --> 00:01:51,680
也说我们这里面啊写loader要注意个特点

76
00:01:51,680 --> 00:01:53,080
对吧是loader的

77
00:01:53,080 --> 00:01:54,480
顺序问题

78
00:01:54,480 --> 00:01:55,680
顺序问题

79
00:01:56,780 --> 00:01:59,080
那它的顺序呢是什么呢是从右向左

80
00:01:59,080 --> 00:02:00,580
完了从下到上是吧

81
00:02:00,580 --> 00:02:01,480
从右

82
00:02:01,480 --> 00:02:06,480
向左完了从下到上

83
00:02:06,480 --> 00:02:10,680
但是啊这样写起来呀我们还是不太好理解对吧

84
00:02:10,680 --> 00:02:12,280
我怎么知道他在前面在后面

85
00:02:12,280 --> 00:02:15,180
所以这时候我们loader啊一般呢也会有几类

86
00:02:15,180 --> 00:02:17,280
我在这标识一下对吧就是loader的分类

87
00:02:17,280 --> 00:02:19,080
loader的分类

88
00:02:19,080 --> 00:02:21,780
就是他一般啊分为常常见的有几种类啊

89
00:02:21,780 --> 00:02:24,380
第一种呢就是我们所谓的叫pre就是什么

90
00:02:24,380 --> 00:02:25,680
就是在前面的loader

91
00:02:26,180 --> 00:02:26,880
前面的

92
00:02:26,880 --> 00:02:28,980
完了还有我们所谓的叫post

93
00:02:28,980 --> 00:02:30,280
这个就是在后面的

94
00:02:30,280 --> 00:02:30,780
那好了

95
00:02:30,780 --> 00:02:32,180
那加中间的叫什么呀

96
00:02:32,180 --> 00:02:34,180
我们一般会叫他什么叫normal

97
00:02:34,180 --> 00:02:35,480
就是正常load 是吧

98
00:02:35,480 --> 00:02:36,080
那好了

99
00:02:36,080 --> 00:02:38,780
比如说我可以在这呢加个属性叫一force

100
00:02:38,780 --> 00:02:39,880
force

101
00:02:39,880 --> 00:02:40,880
比如说他叫pre

102
00:02:40,880 --> 00:02:42,480
那好了我把这顺序改回去啊

103
00:02:42,480 --> 00:02:44,080
这是一完这是二这是三

104
00:02:44,080 --> 00:02:44,980
那好

105
00:02:44,980 --> 00:02:46,180
我在下面的同样

106
00:02:46,180 --> 00:02:48,080
这个normal的loader也不用加了

107
00:02:48,080 --> 00:02:49,980
比如我在这可以来个什么来个post

108
00:02:49,980 --> 00:02:52,880
OK完了我再来执行一下post

109
00:02:52,880 --> 00:02:53,780
得加个双引号

110
00:02:53,780 --> 00:02:54,880
这都得刷双引号

111
00:02:54,880 --> 00:02:55,680
这里一样

112
00:02:56,080 --> 00:02:57,440
加一个我在这里呢

113
00:02:57,440 --> 00:02:58,520
一样跑一下运行一下

114
00:02:58,520 --> 00:03:01,040
反正效果吧

115
00:03:01,040 --> 00:03:01,840
是不是可以达到的

116
00:03:01,840 --> 00:03:02,680
是不是还是一二三

117
00:03:02,680 --> 00:03:04,160
那也就是说我们可以啊

118
00:03:04,160 --> 00:03:07,520
给他加这样一个参数的来取决于这样的一个直接顺序

119
00:03:07,520 --> 00:03:09,240
那同样我们在这描述一下

120
00:03:09,240 --> 00:03:10,480
这个loader的顺序是什么样

121
00:03:10,480 --> 00:03:11,040
对吧

122
00:03:11,040 --> 00:03:12,880
loader的顺序

123
00:03:12,880 --> 00:03:14,520
现在我们看到了这个loader呢

124
00:03:14,520 --> 00:03:15,800
有几种分类是吧

125
00:03:15,800 --> 00:03:17,760
有前置的后置的还有中间的

126
00:03:17,760 --> 00:03:18,480
但是还有一种呢

127
00:03:18,480 --> 00:03:20,520
我们没有说这种叫行内loader

128
00:03:20,520 --> 00:03:22,840
那我们就描述一下loader的顺序是这样的

129
00:03:22,840 --> 00:03:23,800
先走prey

130
00:03:23,800 --> 00:03:25,600
prey呢走完了走这个normal

131
00:03:25,600 --> 00:03:28,300
那么我走完了走我们刚才所谓的这个叫行内的

132
00:03:28,300 --> 00:03:28,500
哎

133
00:03:28,500 --> 00:03:29,740
因了这个行内的呀

134
00:03:29,740 --> 00:03:30,800
不需要在这配

135
00:03:30,800 --> 00:03:31,860
但是需要在里面怎么样

136
00:03:31,860 --> 00:03:32,940
稍微改造一下

137
00:03:32,940 --> 00:03:33,900
那同样最后呢

138
00:03:33,900 --> 00:03:35,040
垫底的就要pose

139
00:03:35,040 --> 00:03:36,440
那我们来试一下

140
00:03:36,440 --> 00:03:38,600
比如说我在这个src下呀

141
00:03:38,600 --> 00:03:40,340
我再建一个a.js

142
00:03:40,340 --> 00:03:42,040
通过行内的loader拿来处理

143
00:03:42,040 --> 00:03:42,700
来看看

144
00:03:42,700 --> 00:03:44,060
我们又在这先来个loader吧

145
00:03:44,060 --> 00:03:45,360
我就给他起个名字啊

146
00:03:45,360 --> 00:03:46,060
叫inlan

147
00:03:46,060 --> 00:03:48,260
inlan

148
00:03:48,260 --> 00:03:49,760
inlanloader

149
00:03:49,760 --> 00:03:51,360
完我在这里呢

150
00:03:51,360 --> 00:03:52,600
写上这样一个函数

151
00:03:52,600 --> 00:03:53,960
完了我就叫他loader

152
00:03:54,060 --> 00:03:59,560
默认把loader导出module.ispulse等于这样一个loader

153
00:03:59,560 --> 00:04:01,760
并且我们可以在这里来个sauce

154
00:04:01,760 --> 00:04:04,560
完了最后我们可以怎么做

155
00:04:04,560 --> 00:04:06,060
是不是把它先返回

156
00:04:06,060 --> 00:04:07,660
我就给它写个名字就好了

157
00:04:07,660 --> 00:04:08,760
我就sauce

158
00:04:08,760 --> 00:04:10,760
这样code log

159
00:04:10,760 --> 00:04:12,260
比如说打印一下对吧

160
00:04:12,260 --> 00:04:13,360
叫inlineloader

161
00:04:13,360 --> 00:04:15,560
loader

162
00:04:15,560 --> 00:04:16,360
好了

163
00:04:16,360 --> 00:04:18,660
现在loader没有掉肯定不会执行

164
00:04:18,660 --> 00:04:20,060
但是我们可以怎么样

165
00:04:20,060 --> 00:04:21,960
在这里面去引用一个其他文件

166
00:04:21,960 --> 00:04:23,260
完了引用这个文件的时候

167
00:04:23,260 --> 00:04:24,560
我采用loader来处理

168
00:04:24,560 --> 00:04:26,620
这叫行内嵌到我们的代码里

169
00:04:26,620 --> 00:04:29,060
这里一样我们可以来个A.js

170
00:04:29,060 --> 00:04:31,000
并且把A导出

171
00:04:31,000 --> 00:04:32,920
module.express等于一个

172
00:04:32,920 --> 00:04:34,060
珠峰培训

173
00:04:34,060 --> 00:04:34,680
OK

174
00:04:34,680 --> 00:04:36,420
我在这里把它去引进来

175
00:04:36,420 --> 00:04:37,300
非常方便

176
00:04:37,300 --> 00:04:37,840
这个代码

177
00:04:37,840 --> 00:04:39,960
letstr等于require

178
00:04:39,960 --> 00:04:41,400
看这时候不太一样了

179
00:04:41,400 --> 00:04:42,600
我需要去取什么

180
00:04:42,600 --> 00:04:43,360
A.js

181
00:04:43,360 --> 00:04:46,200
但是使用A.js的时候

182
00:04:46,200 --> 00:04:48,540
我希望把A.js导入

183
00:04:48,540 --> 00:04:49,180
给谁呢

184
00:04:49,180 --> 00:04:50,100
给这个inloader

185
00:04:50,100 --> 00:04:51,780
所以我前面加个探号

186
00:04:51,780 --> 00:04:52,620
他我意思就是什么

187
00:04:52,620 --> 00:04:53,600
就是导到前面去

188
00:04:53,600 --> 00:04:54,240
那好了

189
00:04:54,240 --> 00:04:54,980
这里面怎么样

190
00:04:54,980 --> 00:04:57,000
我就写上这个叫InlandLoader

191
00:04:57,000 --> 00:04:59,720
这就是我们所谓的行内Loader

192
00:04:59,720 --> 00:05:00,480
那好

193
00:05:00,480 --> 00:05:01,600
来看看效果

194
00:05:01,600 --> 00:05:02,140
我们在这儿呢

195
00:05:02,140 --> 00:05:02,540
跑一下

196
00:05:02,540 --> 00:05:05,720
在这里你看效果

197
00:05:05,720 --> 00:05:06,760
是不是Loader1

198
00:05:06,760 --> 00:05:07,440
Loader2

199
00:05:07,440 --> 00:05:08,220
完了Loader3

200
00:05:08,220 --> 00:05:09,080
完之后呢

201
00:05:09,080 --> 00:05:10,120
是不是前置的

202
00:05:10,120 --> 00:05:10,780
完了这叫什么

203
00:05:10,780 --> 00:05:11,720
Normal的是吧

204
00:05:11,720 --> 00:05:12,480
完了之后呢

205
00:05:12,480 --> 00:05:13,760
是不是出来了我们的这个行内

206
00:05:13,760 --> 00:05:16,060
完了加上我们最后的这样一个PoseLoader了

207
00:05:16,060 --> 00:05:17,400
那我们就知道这个顺序了

208
00:05:17,400 --> 00:05:18,560
但是这里面

209
00:05:18,560 --> 00:05:20,440
比如说我已经使用这个行内了

210
00:05:20,440 --> 00:05:22,120
我并不希望什么前置

211
00:05:22,120 --> 00:05:24,160
还有正常的loader能执行

212
00:05:24,160 --> 00:05:24,880
你可以怎么样

213
00:05:24,880 --> 00:05:27,160
你可以这样加加一个杠贪号

214
00:05:27,160 --> 00:05:27,560
哎

215
00:05:27,560 --> 00:05:28,320
看看效果啊

216
00:05:28,320 --> 00:05:29,280
你就明白了

217
00:05:29,280 --> 00:05:30,280
我在这运行

218
00:05:30,280 --> 00:05:32,240
往上里面的是不是一二三

219
00:05:32,240 --> 00:05:35,280
你发现是不是pre还有nomal的就不会再出发了

220
00:05:35,280 --> 00:05:38,160
你说加杠贪号对吧

221
00:05:38,160 --> 00:05:40,280
杠贪号不会怎么样

222
00:05:40,280 --> 00:05:41,120
就是不会

223
00:05:41,120 --> 00:05:45,720
不会让文件文件对吧

224
00:05:45,720 --> 00:05:49,080
再去通过什么通过这个pre

225
00:05:49,080 --> 00:05:51,680
完了加上我们这样一个nomal

226
00:05:52,120 --> 00:05:53,720
loader来处理了

227
00:05:53,720 --> 00:05:57,920
其实那一步就是个数组对吧

228
00:05:57,920 --> 00:05:59,120
如果我们用

229
00:05:59,120 --> 00:06:01,320
如果我们加上这样个非号碳号

230
00:06:01,320 --> 00:06:03,260
他就不会把这样一个东西

231
00:06:03,260 --> 00:06:05,020
用pre加normal来处理

232
00:06:05,020 --> 00:06:05,760
好了

233
00:06:05,760 --> 00:06:06,500
除了这个

234
00:06:06,500 --> 00:06:08,620
我们一般还会有几个常见的标准符

235
00:06:08,620 --> 00:06:09,300
比如碳号

236
00:06:09,300 --> 00:06:10,200
碳号什么意思

237
00:06:10,200 --> 00:06:11,280
就是没有什么

238
00:06:11,280 --> 00:06:11,940
没有normal

239
00:06:11,940 --> 00:06:13,840
再来看看是不是这样

240
00:06:13,840 --> 00:06:15,020
我带他改个碳号

241
00:06:15,020 --> 00:06:16,900
我把这里也执行一下

242
00:06:16,900 --> 00:06:19,260
往这里我们看看

243
00:06:19,260 --> 00:06:21,160
是不是就有1没有2

244
00:06:21,160 --> 00:06:22,520
但是有三是吧

245
00:06:22,520 --> 00:06:24,480
那同样我不想要后面的怎么办

246
00:06:24,480 --> 00:06:26,260
但是不想要后面的一般不会有

247
00:06:26,260 --> 00:06:27,020
我们一般会有什么

248
00:06:27,020 --> 00:06:27,400
他有叫

249
00:06:27,400 --> 00:06:28,600
两个碳号

250
00:06:28,600 --> 00:06:30,240
就是什么都不要

251
00:06:30,240 --> 00:06:31,480
什么都不要

252
00:06:31,480 --> 00:06:32,560
只要什么来处理

253
00:06:32,560 --> 00:06:34,360
只要这个行给来处理

254
00:06:34,360 --> 00:06:34,680
OK

255
00:06:34,680 --> 00:06:35,540
那再来试试

256
00:06:35,540 --> 00:06:37,280
那现在我们再来运行一下

257
00:06:37,280 --> 00:06:39,200
那这时候是不是应该就只有一烂楼的

258
00:06:39,200 --> 00:06:40,300
你看一烂楼的

259
00:06:40,300 --> 00:06:42,000
但是还是在执行了

260
00:06:42,000 --> 00:06:43,060
我没有保存好像

261
00:06:43,060 --> 00:06:43,900
两个碳号

262
00:06:43,900 --> 00:06:44,420
两个碳号

263
00:06:44,420 --> 00:06:45,180
没有改

264
00:06:45,180 --> 00:06:46,040
再来一次

265
00:06:46,040 --> 00:06:46,720
是吧

266
00:06:46,720 --> 00:06:48,020
我把这里再处理一下

267
00:06:48,020 --> 00:06:50,000
那这时候我们就可以看到了

268
00:06:50,000 --> 00:06:51,820
是不是只剩了一个依赖loader了

269
00:06:51,820 --> 00:06:52,560
那好

270
00:06:52,560 --> 00:06:55,000
那现在我们知道了这样一个loader的用法

271
00:06:55,000 --> 00:06:56,540
那说完这个loader

272
00:06:56,540 --> 00:06:57,840
我们还要再说一下

273
00:06:57,840 --> 00:07:00,520
这个loader其实默认的是有两部分组成

274
00:07:00,520 --> 00:07:01,740
一个叫peachloader

275
00:07:01,740 --> 00:07:03,060
还有个叫normalloader

276
00:07:03,060 --> 00:07:04,860
就是我们虽然看着好像是

277
00:07:04,860 --> 00:07:06,460
这个loader是倒着执行的是吧

278
00:07:06,460 --> 00:07:07,980
是从右往左从下到上

279
00:07:07,980 --> 00:07:09,900
但是其实我在这描述一下

280
00:07:09,900 --> 00:07:14,560
这个loader默认是由两部分组成

281
00:07:14,560 --> 00:07:18,700
就是说它默认是由两部分组成

282
00:07:18,700 --> 00:07:19,600
那分别有什么呢

283
00:07:19,600 --> 00:07:20,540
一个叫peach部分

284
00:07:20,540 --> 00:07:23,240
还有一个就是我们所谓的叫normal部分

285
00:07:23,240 --> 00:07:24,420
那peach呢

286
00:07:24,420 --> 00:07:25,960
就是在表示这样一个loader的头

287
00:07:25,960 --> 00:07:27,600
这个就是我们普通的

288
00:07:27,600 --> 00:07:28,280
那再来看看

289
00:07:28,280 --> 00:07:29,760
我这有张图

290
00:07:29,760 --> 00:07:31,460
说我们代码啊

291
00:07:31,460 --> 00:07:32,280
其实这样执行的

292
00:07:32,280 --> 00:07:34,400
就是我们虽然写的是loader1loader2loader3

293
00:07:34,400 --> 00:07:35,580
是这样一个数组啊

294
00:07:35,580 --> 00:07:36,660
或者从下到上啊

295
00:07:36,660 --> 00:07:37,660
那这时候呢

296
00:07:37,660 --> 00:07:38,500
他其实啊

297
00:07:38,500 --> 00:07:39,580
真正执行的时候呢

298
00:07:39,580 --> 00:07:41,080
他会先执行这个peach

299
00:07:41,080 --> 00:07:42,980
也说他这个loader分为两部分

300
00:07:42,980 --> 00:07:44,120
一个上一个下是吧

301
00:07:44,120 --> 00:07:46,060
他会先把我们的peach方法啊

302
00:07:46,060 --> 00:07:47,960
是从左到右执行

303
00:07:47,960 --> 00:07:49,580
先走loader3loader2loader1

304
00:07:49,580 --> 00:07:50,760
完之后呢

305
00:07:50,760 --> 00:07:51,620
再去我们这个

306
00:07:51,620 --> 00:07:52,920
获取我们的资源

307
00:07:52,920 --> 00:07:54,340
完了把资源怎么样

308
00:07:54,340 --> 00:07:55,180
再传给楼德1

309
00:07:55,180 --> 00:07:55,800
楼德2楼德3

310
00:07:55,800 --> 00:07:57,500
也说它有两部分

311
00:07:57,500 --> 00:07:57,820
是吧

312
00:07:57,820 --> 00:07:58,800
那同样啊

313
00:07:58,800 --> 00:07:59,820
如果这个peach

314
00:07:59,820 --> 00:08:00,800
它写了

315
00:08:00,800 --> 00:08:01,900
并且能有返回值

316
00:08:01,900 --> 00:08:02,400
那好

317
00:08:02,400 --> 00:08:03,680
那这个执行的顺序

318
00:08:03,680 --> 00:08:04,840
就有点不太一样了

319
00:08:04,840 --> 00:08:06,200
就是它会先走什么呢

320
00:08:06,200 --> 00:08:07,260
先走这个楼德3

321
00:08:07,260 --> 00:08:08,200
走到楼德2

322
00:08:08,200 --> 00:08:09,120
发现楼德2怎么样

323
00:08:09,120 --> 00:08:10,460
这个函数有返回值

324
00:08:10,460 --> 00:08:10,960
那好

325
00:08:10,960 --> 00:08:11,620
直接怎么样

326
00:08:11,620 --> 00:08:13,740
跳过所有后面去的执行

327
00:08:13,740 --> 00:08:14,560
直接怎么样

328
00:08:14,560 --> 00:08:16,260
并且也跳过自己的楼德

329
00:08:16,260 --> 00:08:17,020
直接怎么样

330
00:08:17,020 --> 00:08:19,000
执行到之前的楼德3

331
00:08:19,000 --> 00:08:21,720
如果是loader3有返回值

332
00:08:21,720 --> 00:08:23,120
那就只执行什么了

333
00:08:23,120 --> 00:08:23,800
说loader3

334
00:08:23,800 --> 00:08:25,180
完了loader3的peach

335
00:08:25,180 --> 00:08:26,320
不会再执行什么了

336
00:08:26,320 --> 00:08:26,620
Normal

337
00:08:26,620 --> 00:08:29,040
这样觉得感觉好像不太容易抖

338
00:08:29,040 --> 00:08:30,200
我们来看一看

339
00:08:30,200 --> 00:08:30,960
当我们会用到

340
00:08:30,960 --> 00:08:32,120
怎么去用这个东西

341
00:08:32,120 --> 00:08:32,880
它能干什么事

342
00:08:32,880 --> 00:08:34,260
它有一个功能就是什么

343
00:08:34,260 --> 00:08:35,760
就是阻断的功能

344
00:08:35,760 --> 00:08:37,420
比如说我希望在哪个地方停下来

345
00:08:37,420 --> 00:08:38,680
不要再往下执行了

346
00:08:38,680 --> 00:08:39,120
那好

347
00:08:39,120 --> 00:08:40,740
我只需要在peach上怎么样

348
00:08:40,740 --> 00:08:41,780
加上这样一个东西

349
00:08:41,780 --> 00:08:43,060
咱来看看效果

350
00:08:43,060 --> 00:08:44,100
感觉不太懂

351
00:08:44,100 --> 00:08:44,440
那好

352
00:08:44,440 --> 00:08:45,960
那在这试试

353
00:08:45,960 --> 00:08:47,400
比如说我们说每个loader

354
00:08:47,400 --> 00:08:48,340
它都有个啥东西

355
00:08:48,340 --> 00:08:49,900
有一个这样所谓的P债

356
00:08:49,900 --> 00:08:50,260
是吧

357
00:08:50,260 --> 00:08:50,940
P债

358
00:08:50,940 --> 00:08:52,640
它呢都是一个函数

359
00:08:52,640 --> 00:08:53,180
其实是吧

360
00:08:53,180 --> 00:08:54,360
我说了这个函数呢

361
00:08:54,360 --> 00:08:55,400
如果没有返回值

362
00:08:55,400 --> 00:08:55,780
那好

363
00:08:55,780 --> 00:08:56,640
它会依次执行

364
00:08:56,640 --> 00:08:57,760
比如说来一个啊

365
00:08:57,760 --> 00:08:58,880
叫这个P债

366
00:08:58,880 --> 00:09:00,040
P

367
00:09:00,040 --> 00:09:02,300
叫这个还是loader1的P债

368
00:09:02,300 --> 00:09:02,600
是吧

369
00:09:02,600 --> 00:09:03,980
loader1的P债

370
00:09:03,980 --> 00:09:05,420
P-A-T-C债

371
00:09:05,420 --> 00:09:05,920
那好了

372
00:09:05,920 --> 00:09:06,480
我在这里呢

373
00:09:06,480 --> 00:09:07,280
就粘了几份啊

374
00:09:07,280 --> 00:09:07,720
这是2

375
00:09:07,720 --> 00:09:09,220
完了这个呢是3

376
00:09:09,220 --> 00:09:11,340
我要改个名字啊

377
00:09:11,340 --> 00:09:11,680
这是2

378
00:09:11,680 --> 00:09:12,700
这是3

379
00:09:12,700 --> 00:09:15,440
那我们来看看

380
00:09:15,440 --> 00:09:16,540
我把它执行一下

381
00:09:16,540 --> 00:09:16,960
是吧

382
00:09:16,960 --> 00:09:17,440
运行

383
00:09:17,440 --> 00:09:20,000
那现在这个效果

384
00:09:20,000 --> 00:09:20,980
是不是一天先走的什么

385
00:09:20,980 --> 00:09:22,360
是不是321

386
00:09:22,360 --> 00:09:23,720
是不是我写的时候

387
00:09:23,720 --> 00:09:24,880
是倒着写的是吧

388
00:09:24,880 --> 00:09:25,460
你看我写的时候

389
00:09:25,460 --> 00:09:26,620
是不是强制了一下

390
00:09:26,620 --> 00:09:28,500
强制的时候是在这里配了

391
00:09:28,500 --> 00:09:29,780
是不是先走的loader1

392
00:09:29,780 --> 00:09:30,440
完了23

393
00:09:30,440 --> 00:09:31,380
但你发现了吗

394
00:09:31,380 --> 00:09:32,200
那P是正好怎么样

395
00:09:32,200 --> 00:09:32,900
和它相反

396
00:09:32,900 --> 00:09:34,460
所以说我们执行的是什么

397
00:09:34,460 --> 00:09:35,300
是321

398
00:09:35,300 --> 00:09:36,440
往来走123

399
00:09:36,440 --> 00:09:38,000
就是刚才我们看到的这张图

400
00:09:38,000 --> 00:09:39,140
看是不是321

401
00:09:39,140 --> 00:09:40,120
获取资源

402
00:09:40,120 --> 00:09:41,280
之后怎么样123

403
00:09:41,280 --> 00:09:42,460
那这走完以后

404
00:09:42,460 --> 00:09:44,040
比如说我在loader2里面

405
00:09:44,040 --> 00:09:44,840
有个返回值

406
00:09:44,840 --> 00:09:46,140
看看这效果

407
00:09:46,140 --> 00:09:46,860
loader2

408
00:09:46,860 --> 00:09:48,160
我给它来一个return

409
00:09:48,160 --> 00:09:49,420
比如我随便返回

410
00:09:49,420 --> 00:09:49,620
是吧

411
00:09:49,620 --> 00:09:50,340
只要有返回值

412
00:09:50,340 --> 00:09:50,740
那好

413
00:09:50,740 --> 00:09:51,340
那不好意思

414
00:09:51,340 --> 00:09:51,620
对吧

415
00:09:51,620 --> 00:09:52,300
那就拜拜了

416
00:09:52,300 --> 00:09:53,320
那这时候怎么样

417
00:09:53,320 --> 00:09:54,200
是不是loader2

418
00:09:54,200 --> 00:09:54,920
你看是不是

419
00:09:54,920 --> 00:09:55,700
peach3

420
00:09:55,700 --> 00:09:56,740
完了loader2

421
00:09:56,740 --> 00:09:57,640
我发现loader2的

422
00:09:57,640 --> 00:09:58,560
这个也没有走吧

423
00:09:58,560 --> 00:09:59,500
是不是又回到哪去了

424
00:09:59,500 --> 00:10:00,880
是不是loader3就完事了

425
00:10:00,880 --> 00:10:02,300
比如这里面执行的时候

426
00:10:02,300 --> 00:10:03,000
就是从这到这

427
00:10:03,000 --> 00:10:03,620
往这停

428
00:10:03,620 --> 00:10:04,340
停完以后怎么样

429
00:10:04,340 --> 00:10:05,480
又返回值回掉

430
00:10:05,480 --> 00:10:08,180
那好了

431
00:10:08,180 --> 00:10:09,100
那我们就知道了

432
00:10:09,100 --> 00:10:10,560
这个loader呢

433
00:10:10,560 --> 00:10:11,600
其实是有两部分组成的

434
00:10:11,600 --> 00:10:12,440
一个叫peachloader

435
00:10:12,440 --> 00:10:14,280
还有一个就是我们的normalloader

436
00:10:14,280 --> 00:10:14,920
那好了

437
00:10:14,920 --> 00:10:15,840
那最后啊

438
00:10:15,840 --> 00:10:16,840
我们就来说一下吧

439
00:10:16,840 --> 00:10:17,840
这个loader的特点

440
00:10:17,840 --> 00:10:18,600
我们说呀

441
00:10:18,600 --> 00:10:19,680
这个loader要有个特点

442
00:10:19,680 --> 00:10:20,700
就是第一个loader

443
00:10:20,700 --> 00:10:22,180
要返回一个接着脚本

444
00:10:22,180 --> 00:10:23,180
什么意思呢

445
00:10:23,180 --> 00:10:24,220
比如说最后啊

446
00:10:24,220 --> 00:10:24,720
我们在这里

447
00:10:24,720 --> 00:10:25,640
不是一一二二三

448
00:10:25,640 --> 00:10:28,340
这个三肯定会把最终的结果处理好吧

449
00:10:28,340 --> 00:10:30,560
那处理好这个内容会打到哪去呢

450
00:10:30,560 --> 00:10:31,360
咱看看啊

451
00:10:31,360 --> 00:10:31,800
比如来这

452
00:10:31,800 --> 00:10:33,720
我们在这里面随便写一个

453
00:10:33,720 --> 00:10:34,160
比如三

454
00:10:34,160 --> 00:10:35,400
我写上一个hello

455
00:10:35,400 --> 00:10:37,160
hello

456
00:10:37,160 --> 00:10:37,760
OK

457
00:10:37,760 --> 00:10:38,720
我把这个东西呢

458
00:10:38,720 --> 00:10:39,620
运行一下是吧

459
00:10:39,620 --> 00:10:40,480
打爆

460
00:10:40,480 --> 00:10:43,460
我们看一下效果

461
00:10:43,460 --> 00:10:44,040
这里呢

462
00:10:44,040 --> 00:10:44,660
我们有bundle

463
00:10:44,660 --> 00:10:46,100
完了里面看看他的hello

464
00:10:46,100 --> 00:10:46,780
是不是就过来了

465
00:10:46,780 --> 00:10:48,360
是不是这里面放个hello

466
00:10:48,360 --> 00:10:49,860
但这里面你要明确

467
00:10:49,860 --> 00:10:50,600
你不能下放

468
00:10:50,600 --> 00:10:52,000
因为你看这里面放了个什么

469
00:10:52,000 --> 00:10:52,800
是不是EVL

470
00:10:52,800 --> 00:10:55,680
你不能说在里面放一个不是GS脚本的东西

471
00:10:55,680 --> 00:10:57,640
比如说他里面默认不知是什么

472
00:10:57,640 --> 00:10:58,560
你放个对象

473
00:10:58,560 --> 00:11:00,000
这就不行了

474
00:11:00,000 --> 00:11:00,760
你看个效果

475
00:11:00,760 --> 00:11:02,000
我运行

476
00:11:02,000 --> 00:11:02,580
他会告诉我

477
00:11:02,580 --> 00:11:04,400
这东西必须要返回个什么

478
00:11:04,400 --> 00:11:05,160
是不是告诉我

479
00:11:05,160 --> 00:11:06,740
这东西是一个string什么

480
00:11:06,740 --> 00:11:07,260
hourbuffer

481
00:11:07,260 --> 00:11:07,660
这写的

482
00:11:07,660 --> 00:11:09,580
必须要返回一个stringhourbuffer

483
00:11:09,580 --> 00:11:11,520
所以这里面我们最后一个loader

484
00:11:11,520 --> 00:11:12,680
一定要返回一个什么

485
00:11:12,680 --> 00:11:14,600
GS脚本

486
00:11:14,600 --> 00:11:18,120
因为它要放到我们这个当前代码中的EVL来执行

487
00:11:18,120 --> 00:11:19,260
所以说不能瞎搞

488
00:11:19,260 --> 00:11:20,160
那这一样

489
00:11:20,160 --> 00:11:21,480
完了我们再来看是吧

490
00:11:21,480 --> 00:11:22,240
还有什么特点

491
00:11:22,240 --> 00:11:22,960
第二呢

492
00:11:22,960 --> 00:11:24,400
就是说我们为了方便

493
00:11:24,400 --> 00:11:24,680
对吧

494
00:11:24,680 --> 00:11:26,280
我们的loader需要怎么样

495
00:11:26,280 --> 00:11:27,400
只能做一件事

496
00:11:27,400 --> 00:11:28,240
这样的话呀

497
00:11:28,240 --> 00:11:29,860
我们可以去组合loader

498
00:11:29,860 --> 00:11:31,100
比如说我们的CSIloader

499
00:11:31,100 --> 00:11:31,720
Lessloader

500
00:11:31,720 --> 00:11:32,240
Styleloader

501
00:11:32,240 --> 00:11:35,540
那为什么把Styleloader里面直接就包含了这个Lessloader

502
00:11:35,540 --> 00:11:36,340
还有CSIloader

503
00:11:36,340 --> 00:11:37,380
那也是为了怎么样

504
00:11:37,380 --> 00:11:38,500
更加方便来调用

505
00:11:38,500 --> 00:11:40,220
这样的话我们可以实现什么

506
00:11:40,220 --> 00:11:40,940
就是练习调用

507
00:11:40,940 --> 00:11:43,120
当然了我们说了每一个模块呢

508
00:11:43,120 --> 00:11:43,900
它也是一个什么

509
00:11:43,900 --> 00:11:45,460
当然了

510
00:11:45,460 --> 00:11:46,180
我们也来说了

511
00:11:46,180 --> 00:11:46,400
对吧

512
00:11:46,400 --> 00:11:47,040
每一个loader

513
00:11:47,040 --> 00:11:47,980
它就是一个模块

514
00:11:47,980 --> 00:11:48,940
其实我们看到

515
00:11:48,940 --> 00:11:50,000
是不是就是一个函数

516
00:11:50,000 --> 00:11:50,600
把它导出

517
00:11:50,600 --> 00:11:52,080
这是没问题的

518
00:11:52,080 --> 00:11:53,120
之后我们说了

519
00:11:53,120 --> 00:11:54,740
loader一定要是无状态的

520
00:11:54,740 --> 00:11:56,180
如果loader又有状态了

521
00:11:56,180 --> 00:11:56,700
那就毁了

522
00:11:56,700 --> 00:11:57,440
可能A

523
00:11:57,440 --> 00:11:58,400
B这个文件的时候

524
00:11:58,400 --> 00:11:59,440
用这样一个模式

525
00:11:59,440 --> 00:12:00,300
B另一个文件

526
00:12:00,300 --> 00:12:01,200
用到另一个模式

527
00:12:01,200 --> 00:12:02,960
你说它并不是一个纯函数了

528
00:12:02,960 --> 00:12:03,800
所以这里面

529
00:12:03,800 --> 00:12:04,900
我们不建议

530
00:12:04,900 --> 00:12:06,320
也是不应该去这么做

531
00:12:06,320 --> 00:12:07,520
我们来个什么flag

532
00:12:07,520 --> 00:12:09,180
flag对吧

533
00:12:09,180 --> 00:12:09,540
等于处

534
00:12:09,540 --> 00:12:10,720
如果处的话

535
00:12:10,720 --> 00:12:11,260
怎么着

536
00:12:11,260 --> 00:12:13,160
这事不好说

537
00:12:13,160 --> 00:12:13,620
是不是完了

538
00:12:13,620 --> 00:12:14,800
比如说第一次走了

539
00:12:14,800 --> 00:12:16,480
处处的话把这flag的变成false

540
00:12:16,480 --> 00:12:18,680
那下个人进来是不是就完了

541
00:12:18,680 --> 00:12:20,580
所以说这东西我们不应该怎么样

542
00:12:20,580 --> 00:12:21,740
让它有用状态

543
00:12:21,740 --> 00:12:22,800
这边我们也要注意

544
00:12:22,800 --> 00:12:23,620
那好了

545
00:12:23,620 --> 00:12:26,340
我们知道了这些loader的规则和loader的写法

546
00:12:26,340 --> 00:12:28,900
那接下来我们就开始来loader的实战

547
00:12:28,900 --> 00:12:30,220
我们来一个个来去做

548
00:12:30,220 --> 00:12:31,820
去实现什么babelloader

549
00:12:31,820 --> 00:12:35,700
还有我们的那个关于图片的关于cssloader

