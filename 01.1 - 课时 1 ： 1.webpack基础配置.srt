1
00:00:00,000 --> 00:00:04,360
那么本章节呢我们就来学习一下webpack

2
00:00:04,360 --> 00:00:06,960
来学习下webpack的基本配置以及用法

3
00:00:06,960 --> 00:00:09,900
那我们在学习之前呢是要先下载webpack

4
00:00:09,900 --> 00:00:13,100
那下载webpack我们可以去安装本地的webpack

5
00:00:13,100 --> 00:00:14,460
以及圈局的webpack

6
00:00:14,460 --> 00:00:17,560
那这里呢我们一般呢会采用我们的本地webpack

7
00:00:17,560 --> 00:00:18,800
哎在这写个笔记

8
00:00:18,800 --> 00:00:20,240
note.md

9
00:00:20,240 --> 00:00:22,240
那就是webpack的安装

10
00:00:22,240 --> 00:00:26,140
我们一般呢会先去安装对吧

11
00:00:26,140 --> 00:00:27,660
安装本地的webpack

12
00:00:30,000 --> 00:00:33,620
因为如果我们安装区域的webpack的话

13
00:00:33,620 --> 00:00:35,360
可能会导致版本的不一致

14
00:00:35,360 --> 00:00:37,900
我们现在学习的是webpack4.0

15
00:00:37,900 --> 00:00:40,440
所以我们安装webpack需要安装两个

16
00:00:40,440 --> 00:00:41,360
一个叫webpack

17
00:00:41,360 --> 00:00:43,300
还有一个叫webpack-CY

18
00:00:43,300 --> 00:00:46,520
他们都属于我们的开发以来

19
00:00:46,520 --> 00:00:48,360
所以这里我们一般会加个-D

20
00:00:48,360 --> 00:00:50,580
表示上线的时候不需要他们两个包

21
00:00:50,580 --> 00:00:52,360
这里我们就打开迷你行

22
00:00:52,360 --> 00:00:53,140
来安装一下

23
00:00:53,140 --> 00:00:56,300
这里我们就初始化一下

24
00:00:56,300 --> 00:00:59,720
安装之前我们需要先初始化一下

25
00:00:59,720 --> 00:01:01,680
来记录我们以下的安装依赖

26
00:01:01,680 --> 00:01:03,820
ENIIT-Y

27
00:01:03,820 --> 00:01:08,260
出了话后我们就可以安装我们刚才所需要的两个包

28
00:01:08,260 --> 00:01:09,480
一个叫ENR的

29
00:01:09,480 --> 00:01:10,880
我们就直接ENR来安装

30
00:01:10,880 --> 00:01:12,420
用法和NPM一样

31
00:01:12,420 --> 00:01:15,280
这里我们可以直接ENR的Webpack

32
00:01:15,280 --> 00:01:18,420
还有我们的Webpack-CY-D

33
00:01:18,420 --> 00:01:21,620
DD的意思就是表示它当前是开发依赖

34
00:01:21,620 --> 00:01:22,920
上线的时候不需要

35
00:01:22,920 --> 00:01:27,680
安装好后我们就可以来进入Webpack的配置

36
00:01:27,680 --> 00:01:29,420
我们说这个Webpack

37
00:01:29,420 --> 00:01:32,540
他非常方便外派克对吧可以进行零配置

38
00:01:32,540 --> 00:01:35,060
可以进行零配置

39
00:01:35,060 --> 00:01:36,300
但是我们都知道啊

40
00:01:36,300 --> 00:01:37,760
只要是零配置的话呢

41
00:01:37,760 --> 00:01:39,240
那他的配置呢肯定很弱

42
00:01:39,240 --> 00:01:41,000
那好我们来看一下啊

43
00:01:41,000 --> 00:01:42,740
那我们的外派克我们一直在说

44
00:01:42,740 --> 00:01:44,360
他是一个打包工具

45
00:01:44,360 --> 00:01:46,980
也就是说他可以把我们的圆满啊

46
00:01:46,980 --> 00:01:49,980
进行打包打包出什么输出后的结果

47
00:01:49,980 --> 00:01:51,600
哎输出后的结果

48
00:01:51,600 --> 00:01:55,760
而且呢他默认的识别的所谓的就是什么

49
00:01:55,760 --> 00:01:57,440
就是我们所谓的叫GS模块

50
00:01:57,440 --> 00:01:59,160
他会从这个入口啊

51
00:01:59,160 --> 00:02:01,920
把sodgs 经打包往来进行的输出

52
00:02:01,920 --> 00:02:03,700
这里我们来看一下

53
00:02:03,700 --> 00:02:06,400
我们肯定需要一个打包的目录对吧

54
00:02:06,400 --> 00:02:10,000
打包的目录我们在这里一般圆满的就在src

55
00:02:10,000 --> 00:02:12,120
这里我们可以建一个文件

56
00:02:12,120 --> 00:02:14,600
比如说我就叫它index.js

57
00:02:14,600 --> 00:02:16,600
这里我就随便建代码

58
00:02:16,600 --> 00:02:17,920
比如说来个console.log

59
00:02:17,920 --> 00:02:19,220
来个hello

60
00:02:19,220 --> 00:02:20,800
完了中培训

61
00:02:20,800 --> 00:02:23,900
好了我们现在有这样的一句话

62
00:02:23,900 --> 00:02:24,800
我们现在可以怎么样

63
00:02:24,800 --> 00:02:26,220
是不是可以来预行我们代码了

64
00:02:26,220 --> 00:02:27,360
非常方便

65
00:02:27,360 --> 00:02:30,160
我想把这个模块这个文件打包

66
00:02:30,160 --> 00:02:31,260
怎么打包呢

67
00:02:31,260 --> 00:02:34,060
我们可以直接运行我们的YPAC命令

68
00:02:34,060 --> 00:02:37,460
这个YPAC命令我们可以通过YPAC新生成的

69
00:02:37,460 --> 00:02:41,360
命令就是5.2支持的这样个NPX语法

70
00:02:41,360 --> 00:02:44,060
它可以直接执行我们这样一个YPAC

71
00:02:44,060 --> 00:02:45,160
好我们直接回车

72
00:02:45,160 --> 00:02:47,760
其实这样执行的话

73
00:02:47,760 --> 00:02:50,560
它会默认去找nodemodule下的B文件

74
00:02:50,560 --> 00:02:53,060
B里面就有一个叫YPAC.cmd

75
00:02:53,060 --> 00:02:54,660
完了它会怎么样

76
00:02:54,660 --> 00:02:55,560
这里面有个判断

77
00:02:55,560 --> 00:02:58,260
比如说如果当前目录下有nodeese

78
00:02:58,260 --> 00:03:01,360
他就会用我们当前文件夹里的nodeese

79
00:03:01,360 --> 00:03:02,360
来执行文件

80
00:03:02,360 --> 00:03:04,460
肯定当前目录下是没有的

81
00:03:04,460 --> 00:03:06,160
所以他会走下面这样一个语法

82
00:03:06,160 --> 00:03:09,060
他会用node去执行当前目录下的

83
00:03:09,060 --> 00:03:11,860
上一集的webpack下的webpack.js

84
00:03:11,860 --> 00:03:14,460
你说他会找什么上一集目录

85
00:03:14,460 --> 00:03:15,660
就是nodemodule

86
00:03:15,660 --> 00:03:18,660
完了指的就是我们最后安装的webpack

87
00:03:18,660 --> 00:03:19,760
我们可以看来看一下

88
00:03:19,760 --> 00:03:20,560
这里

89
00:03:20,560 --> 00:03:24,360
我这里有一个叫什么叫beam下的webpack.js

90
00:03:24,460 --> 00:03:25,860
是运行的就是这个文件

91
00:03:25,860 --> 00:03:27,700
完这个文件我们刚才说了

92
00:03:27,700 --> 00:03:29,760
他需要用到我们的那个webpack什么

93
00:03:29,760 --> 00:03:30,820
那个cli对吧

94
00:03:30,820 --> 00:03:31,560
港cli

95
00:03:31,560 --> 00:03:33,260
他说了如果你要没有安装

96
00:03:33,260 --> 00:03:35,720
他会提示你去安装这样一个webpack cli

97
00:03:35,720 --> 00:03:36,620
好了

98
00:03:36,620 --> 00:03:38,800
现在我们就知道了这两个模块的关系

99
00:03:38,800 --> 00:03:41,100
也说我们用webpack在webpack4中

100
00:03:41,100 --> 00:03:43,960
就需要安装webpack以及webpack cli

101
00:03:43,960 --> 00:03:44,720
好了

102
00:03:44,720 --> 00:03:46,260
这里我们再接着来看

103
00:03:46,260 --> 00:03:47,760
这时候我们发现

104
00:03:47,760 --> 00:03:49,460
我们把原文件放在这来

105
00:03:49,460 --> 00:03:52,460
他就会帮我们打包出来一个叫慢点解释文件

106
00:03:52,700 --> 00:03:54,380
这个文件你可以看到

107
00:03:54,380 --> 00:03:55,520
它明显怎么样

108
00:03:55,520 --> 00:03:56,700
被我们压缩过了

109
00:03:56,700 --> 00:03:57,500
因为我们都知道

110
00:03:57,500 --> 00:04:00,340
我们webpack具有什么优化的功能

111
00:04:00,340 --> 00:04:02,900
它会把你的打包的文件进行优化产出

112
00:04:02,900 --> 00:04:05,600
但是这个文件的内容好像很多

113
00:04:05,600 --> 00:04:07,040
这时候我们不要紧

114
00:04:07,040 --> 00:04:08,140
我们先不关心它

115
00:04:08,140 --> 00:04:09,540
它其实也可以怎么样

116
00:04:09,540 --> 00:04:10,280
直接运行

117
00:04:10,280 --> 00:04:11,800
你看我们邮件run

118
00:04:11,800 --> 00:04:13,760
我们这个插件用的是Code Runner

119
00:04:13,760 --> 00:04:15,860
它帮我们执行Node代码

120
00:04:15,860 --> 00:04:17,180
执行出来的结果

121
00:04:17,180 --> 00:04:18,960
和我们这里面打印出来的结果

122
00:04:18,960 --> 00:04:19,760
其实是一样的

123
00:04:19,760 --> 00:04:20,860
好了

124
00:04:20,860 --> 00:04:21,880
这里先不多说

125
00:04:21,880 --> 00:04:23,920
我们就来看一下这个效果

126
00:04:23,920 --> 00:04:26,060
这时候我们发现

127
00:04:26,060 --> 00:04:28,220
明明一行代码却输出了很多

128
00:04:28,220 --> 00:04:29,260
这是为什么呢

129
00:04:29,260 --> 00:04:30,440
因为我们说了

130
00:04:30,440 --> 00:04:32,460
webpack它的功能是什么

131
00:04:32,460 --> 00:04:33,220
是打包

132
00:04:33,220 --> 00:04:33,560
对吧

133
00:04:33,560 --> 00:04:36,320
这里面它默认支持什么

134
00:04:36,320 --> 00:04:40,660
就是支持我们的GS的模块化

135
00:04:40,660 --> 00:04:42,140
好了

136
00:04:42,140 --> 00:04:43,440
一提到模块化

137
00:04:43,440 --> 00:04:44,300
你就想到什么了

138
00:04:44,300 --> 00:04:45,120
是不是就相当于

139
00:04:45,120 --> 00:04:48,000
我们这里面可以写一些模块的代码

140
00:04:48,000 --> 00:04:49,960
比如说在这里稍微改造一下

141
00:04:49,960 --> 00:04:51,360
这里我们可以来个

142
00:04:51,360 --> 00:04:52,680
比如说a.js

143
00:04:52,680 --> 00:04:55,880
往来里面我们可以直接去写一些

144
00:04:55,880 --> 00:04:57,680
比如说commonjs规范的语法

145
00:04:57,680 --> 00:04:58,640
也就是node语法

146
00:04:58,640 --> 00:05:03,020
这里我可以默认导出module.expose等于一个中备训

147
00:05:03,020 --> 00:05:03,940
OK

148
00:05:03,940 --> 00:05:08,500
导出以后我们可以在index里面去引用它

149
00:05:08,500 --> 00:05:08,840
好

150
00:05:08,840 --> 00:05:09,360
我可以在这

151
00:05:09,360 --> 00:05:13,500
lightstr等于require我们这样一个.a.js

152
00:05:13,500 --> 00:05:18,240
我们都知道这样的语法是不可能在我们67中跑的

153
00:05:18,240 --> 00:05:20,020
因为这是我们node的一个规范

154
00:05:20,020 --> 00:05:21,860
这里我们可以直接打印

155
00:05:21,860 --> 00:05:24,400
完了我们来运行一下

156
00:05:24,400 --> 00:05:27,220
你看这个node预法中肯定可以在node中运行

157
00:05:27,220 --> 00:05:30,180
但是我们最后是不是希望把它铲出段

158
00:05:30,180 --> 00:05:32,040
这个结果运行在网友器上

159
00:05:32,040 --> 00:05:35,000
好我们这里再去打包MPX

160
00:05:35,000 --> 00:05:37,000
它执行的这个命令

161
00:05:37,000 --> 00:05:38,960
命令的时候会告诉我个提示

162
00:05:38,960 --> 00:05:41,600
说默认你没有给它一个东西叫什么

163
00:05:41,600 --> 00:05:42,620
叫配置

164
00:05:42,620 --> 00:05:43,980
这个配置叫什么

165
00:05:43,980 --> 00:05:44,600
叫模式

166
00:05:44,600 --> 00:05:46,780
这个模式如果你没有设置的话

167
00:05:46,780 --> 00:05:49,360
它会默认的去采用我们的production

168
00:05:49,360 --> 00:05:50,760
也就生产环境

169
00:05:50,760 --> 00:05:51,540
生产环境

170
00:05:51,540 --> 00:05:53,360
肯定希望代码比较小巧

171
00:05:53,360 --> 00:05:55,160
可以帮我们去优化压缩

172
00:05:55,160 --> 00:05:56,280
还有我们的一些

173
00:05:56,280 --> 00:05:57,720
比如说打包完以后

174
00:05:57,720 --> 00:05:58,580
去掉名义的代码

175
00:05:58,580 --> 00:05:59,260
叫TrayShaking

176
00:05:59,260 --> 00:06:00,440
同样的

177
00:06:00,440 --> 00:06:01,300
我们还有什么

178
00:06:01,300 --> 00:06:03,600
如果你还可以设置一个开发环境

179
00:06:03,600 --> 00:06:04,640
开发环境

180
00:06:04,640 --> 00:06:06,060
可能他就不会压缩你的代码

181
00:06:06,060 --> 00:06:07,280
可能能让你看出来

182
00:06:07,280 --> 00:06:09,660
打包结果是什么东西

183
00:06:09,660 --> 00:06:10,360
OK了

184
00:06:10,360 --> 00:06:12,360
这里我们来试一下

185
00:06:12,360 --> 00:06:13,840
打包后的结果

186
00:06:13,840 --> 00:06:15,640
好像比刚才应该是多了一点

187
00:06:15,640 --> 00:06:17,000
先不管不关心

188
00:06:17,000 --> 00:06:18,940
我们先建一个atml

189
00:06:18,940 --> 00:06:23,980
我们可以把atml直接去引用我们打包后的结果

190
00:06:23,980 --> 00:06:25,060
直接script

191
00:06:25,060 --> 00:06:28,160
这里我们就引用我们这样一个.gov

192
00:06:28,160 --> 00:06:29,160
man.js

193
00:06:29,160 --> 00:06:31,380
再来看一下

194
00:06:31,380 --> 00:06:35,540
打包后的结果能否在我们的环境中直接跑

195
00:06:35,540 --> 00:06:39,900
这里我们直接点检查

196
00:06:39,900 --> 00:06:41,380
往来看一下日志

197
00:06:41,380 --> 00:06:43,060
发现真的可以打印

198
00:06:43,060 --> 00:06:46,720
也就是说我们webpack帮我们实现个功能

199
00:06:46,720 --> 00:06:49,000
就是可以帮我们去解析GS的模块

200
00:06:49,000 --> 00:06:51,900
并且以当前GS为准

201
00:06:51,900 --> 00:06:52,320
对吧

202
00:06:52,320 --> 00:06:54,480
完了查找所有相关的依赖的文件

203
00:06:54,480 --> 00:06:56,840
把这个文件打包成一个文件

204
00:06:56,840 --> 00:06:58,660
而且它帮我们解决了

205
00:06:58,660 --> 00:07:00,300
这个浏览器的require的问题

206
00:07:00,300 --> 00:07:02,880
相当于它自己实现了一套模块化的机制

207
00:07:02,880 --> 00:07:03,820
好了

208
00:07:03,820 --> 00:07:05,020
我们刚才说了

209
00:07:05,020 --> 00:07:06,800
打包的时候非常弱

210
00:07:06,800 --> 00:07:10,840
因为我们现在默认的文件叫index

211
00:07:10,840 --> 00:07:12,480
打包出来的文件叫man

212
00:07:12,480 --> 00:07:14,540
而且目录的名字也是死的

213
00:07:14,540 --> 00:07:15,120
必须叫dist

214
00:07:15,120 --> 00:07:18,600
这时候我能不能自己去更改一下文件的

215
00:07:18,600 --> 00:07:19,560
当然可以了

216
00:07:19,560 --> 00:07:22,020
我们说了默认的零配置它很弱

217
00:07:22,020 --> 00:07:22,980
我们需要干嘛

218
00:07:22,980 --> 00:07:24,840
需要手动配置是吧

219
00:07:24,840 --> 00:07:26,880
手动配置

220
00:07:26,880 --> 00:07:30,740
我们来看看怎么去手动配置

221
00:07:30,740 --> 00:07:32,120
这里面一个要求对吧

222
00:07:32,120 --> 00:07:34,020
就是默认你要配置对吧

223
00:07:34,020 --> 00:07:40,880
默认配置文件的文件的名字对吧

224
00:07:40,880 --> 00:07:41,460
是什么呢

225
00:07:41,460 --> 00:07:44,160
是webpack.config.js

226
00:07:44,160 --> 00:07:46,900
这里我们就可以直接建这样一个文件

227
00:07:46,900 --> 00:07:47,860
来一个对吧

228
00:07:47,860 --> 00:07:50,580
叫webpack.config.js

229
00:07:50,580 --> 00:07:54,600
这时候我可以先把第四的目录整个删掉

230
00:07:54,600 --> 00:07:56,020
我们重新来生成一下

231
00:07:56,020 --> 00:07:58,740
这时候我们希望A入口是index

232
00:07:58,740 --> 00:07:59,040
好

233
00:07:59,040 --> 00:08:01,180
我们说了webpack对吧

234
00:08:01,180 --> 00:08:04,260
是node写出来的

235
00:08:04,260 --> 00:08:06,040
你说这里面我们需要干嘛

236
00:08:06,040 --> 00:08:09,320
需要采用node的node的写法

237
00:08:09,320 --> 00:08:11,400
写法来运行

238
00:08:11,400 --> 00:08:12,060
好了

239
00:08:12,060 --> 00:08:13,940
也就是说这里面我们需要怎么样

240
00:08:13,940 --> 00:08:15,780
导出的一个配置文件

241
00:08:15,780 --> 00:08:18,400
这里面需要用到这个model.ispos

242
00:08:18,400 --> 00:08:20,860
网站里面我们需要提供几个参数

243
00:08:20,860 --> 00:08:21,860
第一个叫entry

244
00:08:21,860 --> 00:08:23,560
entry的一听名字叫什么

245
00:08:23,560 --> 00:08:24,140
叫入口

246
00:08:24,140 --> 00:08:24,840
入口

247
00:08:24,840 --> 00:08:26,180
什么叫入口

248
00:08:26,180 --> 00:08:28,280
就是从哪个地方开始打包

249
00:08:28,280 --> 00:08:31,280
比如说我们要从src下的index

250
00:08:31,280 --> 00:08:33,060
这里我们就可以直接写

251
00:08:33,060 --> 00:08:34,580
这里可以写相对路径

252
00:08:34,580 --> 00:08:36,560
比如你叫什么.gov

253
00:08:36,560 --> 00:08:39,280
src下的index.js

254
00:08:39,280 --> 00:08:40,400
好了

255
00:08:40,400 --> 00:08:41,380
有了入口怎么样

256
00:08:41,380 --> 00:08:43,260
我是不是还要把它打包到一个位置

257
00:08:43,260 --> 00:08:45,060
这时候肯定需要个出口

258
00:08:45,060 --> 00:08:46,940
出口我们可以配一下

259
00:08:46,940 --> 00:08:47,680
叫output

260
00:08:47,680 --> 00:08:49,900
这里我们也需要配置一下

261
00:08:49,900 --> 00:08:50,980
比如出口的文件

262
00:08:50,980 --> 00:08:52,280
肯定有file name

263
00:08:52,280 --> 00:08:54,580
比如说我希望把文件名字

264
00:08:54,580 --> 00:08:55,980
刚才默认叫man

265
00:08:55,980 --> 00:08:58,400
我这里可以改个名字叫bundle

266
00:08:58,400 --> 00:08:59,800
bundle就是一树花的意思

267
00:08:59,800 --> 00:09:00,800
打包成一起

268
00:09:00,800 --> 00:09:01,660
好

269
00:09:01,660 --> 00:09:03,100
这里我给它起个名字

270
00:09:03,100 --> 00:09:04,080
叫bundle.js

271
00:09:04,080 --> 00:09:07,220
这就是打包后的文件名

272
00:09:07,220 --> 00:09:08,860
好了

273
00:09:08,860 --> 00:09:09,760
同样还有什么

274
00:09:09,760 --> 00:09:10,480
还有

275
00:09:10,480 --> 00:09:12,060
比如说我打包出来的目录

276
00:09:12,060 --> 00:09:13,460
这个bundle去哪呢

277
00:09:13,460 --> 00:09:13,640
对吧

278
00:09:13,640 --> 00:09:14,300
放在哪里

279
00:09:14,300 --> 00:09:16,360
那这里面肯定还需要个什么呢

280
00:09:16,360 --> 00:09:16,780
路径

281
00:09:16,780 --> 00:09:18,060
把这个文件呢

282
00:09:18,060 --> 00:09:18,800
放在哪里去

283
00:09:18,800 --> 00:09:20,240
那这时候呢

284
00:09:20,240 --> 00:09:22,240
我们就需要一个什么node模块

285
00:09:22,240 --> 00:09:24,060
这个为什么需要摸摸模块呢

286
00:09:24,060 --> 00:09:25,220
也说这个路径啊

287
00:09:25,220 --> 00:09:25,840
有个要求

288
00:09:25,840 --> 00:09:28,360
路径必须是一个什么呢

289
00:09:28,360 --> 00:09:30,880
必须是一个绝对路径

290
00:09:30,880 --> 00:09:32,260
那好了

291
00:09:32,260 --> 00:09:34,060
那怎么解析出一个绝对路径

292
00:09:34,060 --> 00:09:34,960
那这时候呢

293
00:09:34,960 --> 00:09:37,860
就要靠我们这个node的叫核心模块

294
00:09:37,860 --> 00:09:39,180
我们可以怎么样呢

295
00:09:39,180 --> 00:09:40,000
直接去引

296
00:09:40,000 --> 00:09:41,080
比如说light

297
00:09:41,080 --> 00:09:43,060
我们要到这样一个pass模块

298
00:09:43,060 --> 00:09:44,200
这是它内置的

299
00:09:44,200 --> 00:09:45,440
所以不需要去安装

300
00:09:45,440 --> 00:09:47,280
明确一下内置模块

301
00:09:47,280 --> 00:09:47,980
好了

302
00:09:47,980 --> 00:09:52,380
我们可以直接用pass去点一个resolve

303
00:09:52,380 --> 00:09:54,600
这个resolve方法的一听名叫解析

304
00:09:54,600 --> 00:09:57,500
它可以把我们这样一个相对路径

305
00:09:57,500 --> 00:09:59,300
解析成一个绝对路径

306
00:09:59,300 --> 00:10:00,920
这里面我们可以怎么样

307
00:10:00,920 --> 00:10:01,920
可以放这样一个东西

308
00:10:01,920 --> 00:10:02,380
就杠杠

309
00:10:02,380 --> 00:10:03,020
第二令

310
00:10:03,020 --> 00:10:04,120
当然你不放也可以

311
00:10:04,120 --> 00:10:05,320
你可以直接写上一个叫dist

312
00:10:05,320 --> 00:10:06,620
它就会怎么做

313
00:10:06,620 --> 00:10:09,840
以当前目录解析出来一个dist的目录

314
00:10:09,840 --> 00:10:10,640
也就是个绝对路径

315
00:10:10,640 --> 00:10:12,760
我们可以在这把他圈权打印一下

316
00:10:12,760 --> 00:10:13,780
Costlog

317
00:10:13,780 --> 00:10:17,460
这里我们来看一下这两行的代码输出的结果

318
00:10:17,460 --> 00:10:22,100
没发现它当前就是WivepackDV1下的第四目录

319
00:10:22,100 --> 00:10:23,000
就这个目录

320
00:10:23,000 --> 00:10:23,860
好了

321
00:10:23,860 --> 00:10:24,660
这里面

322
00:10:24,660 --> 00:10:25,700
这还有点bug

323
00:10:25,700 --> 00:10:27,000
不知道为什么删掉还有

324
00:10:27,000 --> 00:10:28,040
不用理它

325
00:10:28,040 --> 00:10:29,900
这时候我们发现已经有了

326
00:10:29,900 --> 00:10:32,520
这里面我们一般会再写个

327
00:10:32,520 --> 00:10:33,680
加上一句话叫杠杠

328
00:10:33,680 --> 00:10:34,220
第二例

329
00:10:34,220 --> 00:10:37,760
它指的就是以当前目录下产生一个第四目录

330
00:10:37,760 --> 00:10:38,280
都可以

331
00:10:38,280 --> 00:10:39,100
不加油OK

332
00:10:39,100 --> 00:10:40,120
刚才看到效果了

333
00:10:40,120 --> 00:10:42,060
这时候我们有了入口

334
00:10:42,060 --> 00:10:42,760
有了出口

335
00:10:42,760 --> 00:10:44,540
我们希望怎么样

336
00:10:44,540 --> 00:10:45,760
打包出来的代码

337
00:10:45,760 --> 00:10:46,920
我们能看得见

338
00:10:46,920 --> 00:10:48,940
这里面我们能希望怎么样

339
00:10:48,940 --> 00:10:50,860
是不是感觉能看得更清楚一些

340
00:10:50,860 --> 00:10:52,040
不是压缩后的代码

341
00:10:52,040 --> 00:10:54,000
这里我们可以再给个mode

342
00:10:54,000 --> 00:10:55,700
mode还是叫模式

343
00:10:55,700 --> 00:10:56,040
对吧

344
00:10:56,040 --> 00:10:58,360
模式刚才说了默认有两种

345
00:10:58,360 --> 00:10:58,780
对吧

346
00:10:58,780 --> 00:10:59,840
模式

347
00:10:59,840 --> 00:11:02,160
默认两种

348
00:11:02,160 --> 00:11:03,860
一种我们叫它什么

349
00:11:03,860 --> 00:11:05,920
是不是叫它生产环境

350
00:11:05,920 --> 00:11:06,900
Production

351
00:11:06,900 --> 00:11:09,260
还有一种就叫做开发模式

352
00:11:09,260 --> 00:11:10,660
DVE LOP MNT

353
00:11:10,660 --> 00:11:12,700
这里我们为了能看清楚

354
00:11:12,700 --> 00:11:14,320
是打包后之前的代码

355
00:11:14,320 --> 00:11:15,780
就是不是压缩后的是吧

356
00:11:15,780 --> 00:11:18,020
这里我们可以选择开发模式

357
00:11:18,020 --> 00:11:19,840
这里我们再来运行一下

358
00:11:19,840 --> 00:11:21,260
NPX Webpack

359
00:11:21,260 --> 00:11:24,120
这里改个名字

360
00:11:24,120 --> 00:11:25,300
NPX Webpack

361
00:11:25,300 --> 00:11:26,980
好了你看

362
00:11:26,980 --> 00:11:28,960
现在我们就打包出来个bundle

363
00:11:28,960 --> 00:11:30,960
这时候文件比刚才好像大了很多

364
00:11:30,960 --> 00:11:33,300
因为这里面我们的结果

365
00:11:33,300 --> 00:11:34,440
它并没有什么的

366
00:11:34,440 --> 00:11:35,120
我刷新一下

367
00:11:35,120 --> 00:11:36,640
看有迪斯的是吧

368
00:11:36,640 --> 00:11:38,820
这里面好像有点bug是吧

369
00:11:38,820 --> 00:11:40,200
因为有可能我这个

370
00:11:40,200 --> 00:11:41,380
vscode有缓存

371
00:11:41,380 --> 00:11:42,280
我把它删掉

372
00:11:42,280 --> 00:11:43,380
我改个目录

373
00:11:43,380 --> 00:11:44,320
比如说我叫他

374
00:11:44,320 --> 00:11:44,860
哎

375
00:11:44,860 --> 00:11:45,420
Build

376
00:11:45,420 --> 00:11:45,820
哎

377
00:11:45,820 --> 00:11:47,780
改个名字这样好听点是吧

378
00:11:47,780 --> 00:11:49,580
这样的话肯定不是以前的了

379
00:11:49,580 --> 00:11:51,060
因为可能删掉以后

380
00:11:51,060 --> 00:11:52,480
他会一刷新墙还在是吧

381
00:11:52,480 --> 00:11:53,520
应该是没有删除掉

382
00:11:53,520 --> 00:11:54,020
那好

383
00:11:54,020 --> 00:11:55,020
我们在这看一下哎

384
00:11:55,020 --> 00:11:56,020
这个bundle就有了

385
00:11:56,020 --> 00:11:56,780
这个bundle呢

386
00:11:56,780 --> 00:11:58,660
就是一个我们打包说的结果

387
00:11:58,660 --> 00:11:59,560
那这个结果呢

388
00:11:59,560 --> 00:12:01,180
当我们可以来简单的分析一下

389
00:12:01,180 --> 00:12:02,560
那我们来看一看吧

390
00:12:02,560 --> 00:12:05,080
是不是可以运行在我们的浏览器上呢

391
00:12:05,080 --> 00:12:05,480
哎

392
00:12:05,480 --> 00:12:06,160
tml

393
00:12:06,160 --> 00:12:06,620
这里呢

394
00:12:06,620 --> 00:12:08,060
依旧生成一个结果

395
00:12:08,060 --> 00:12:08,600
script

396
00:12:08,600 --> 00:12:10,820
我们在这里直接去打印一下

397
00:12:10,820 --> 00:12:12,180
叫第二个

398
00:12:12,180 --> 00:12:14,920
完了我们来运行一下这个文件

399
00:12:14,920 --> 00:12:17,300
看看这个gs能否正常输出

400
00:12:17,300 --> 00:12:18,220
好

401
00:12:18,220 --> 00:12:20,720
OK

402
00:12:20,720 --> 00:12:22,900
现在植物配讯依然是可以打印出来的

403
00:12:22,900 --> 00:12:24,060
我们就知道了

404
00:12:24,060 --> 00:12:25,760
现在这样一个基本的语法

405
00:12:25,760 --> 00:12:27,700
比如说我们可以见证这样一个配置文件

406
00:12:27,700 --> 00:12:29,300
这个配置文件的名字叫

407
00:12:29,300 --> 00:12:31,040
wipacconfig.js

408
00:12:31,040 --> 00:12:32,760
这个文件名字也是死的

409
00:12:32,760 --> 00:12:34,680
有人说了这个名字能不能改

410
00:12:34,680 --> 00:12:35,560
当然也能

411
00:12:35,560 --> 00:12:36,840
我们可以简单看一下

412
00:12:36,840 --> 00:12:38,140
它为什么叫这个名字

413
00:12:38,140 --> 00:12:39,380
因为node的猫丢下

414
00:12:39,380 --> 00:12:41,600
我们默认运行的是WIPAC

415
00:12:41,600 --> 00:12:42,600
WIPAC

416
00:12:42,600 --> 00:12:45,540
这个WIPAC默认会调我们的WIPAC CLI

417
00:12:45,540 --> 00:12:47,020
完了CLI里面

418
00:12:47,020 --> 00:12:50,100
它其实会有一个叫解析参数的数据

419
00:12:50,100 --> 00:12:50,960
解析这个对象

420
00:12:50,960 --> 00:12:52,060
在那解析

421
00:12:52,060 --> 00:12:53,480
它有个叫configX

422
00:12:53,480 --> 00:12:54,380
你可以看一下

423
00:12:54,380 --> 00:12:56,800
它这里面其实就有解析的关系

424
00:12:56,800 --> 00:12:58,020
在这里找一下

425
00:12:58,020 --> 00:12:59,620
有一个叫WIPAC

426
00:12:59,620 --> 00:13:00,760
你看它不是说了

427
00:13:00,760 --> 00:13:02,800
说默认的名字有两种

428
00:13:02,800 --> 00:13:05,220
一种叫WIPAC.config.js

429
00:13:05,220 --> 00:13:06,640
或者它叫什么

430
00:13:06,640 --> 00:13:08,540
叫webpackfail.js

431
00:13:08,540 --> 00:13:10,340
一般我们会选择前者

432
00:13:10,340 --> 00:13:12,040
我们知道这样一个关系

433
00:13:12,040 --> 00:13:12,500
有人说了

434
00:13:12,500 --> 00:13:15,420
我能不能强制的去改后面这样一个名字

435
00:13:15,420 --> 00:13:16,680
不叫这个名字可不可以呢

436
00:13:16,680 --> 00:13:17,300
当然也可以

437
00:13:17,300 --> 00:13:18,600
这是我们后续要说的

