1
00:00:00,000 --> 00:00:29,640
现在啊,我们看来啊,仅仅来区分这个环境变量,还是不行的,因为我们需要干嘛,是不是分成两个文件,哎,一个是专门用来怎么样,开发的,一个用来专门怎么样,用来生产的,我不能每次怎么样,都去改这个配置文件,麻烦,那这时候我们就有一个原则了,是吧,一般呢,我们会建两个文件,一个叫什么,叫production.js,对吧,漂的,完了还有一个是干嘛呢,专门用来我们开发的,就是可败派克,第二,这个叫开发,就DV.js吧,

2
00:00:29,820 --> 00:00:30,760
有两个

3
00:00:30,760 --> 00:00:32,220
那这时候我们可以怎么样呢

4
00:00:32,220 --> 00:00:35,000
分别在这里面配上自己的这种环境

5
00:00:35,000 --> 00:00:35,860
我能通过怎么样

6
00:00:35,860 --> 00:00:36,900
我把这个名字就改了

7
00:00:36,900 --> 00:00:37,800
这就不叫config了

8
00:00:37,800 --> 00:00:38,320
我叫他啥呢

9
00:00:38,320 --> 00:00:39,000
叫他base

10
00:00:39,000 --> 00:00:41,840
看base什么意思是基础的

11
00:00:41,840 --> 00:00:43,560
我们可以通过这样一个基础的怎么样

12
00:00:43,560 --> 00:00:45,700
分别引到这个dv或者pord

13
00:00:45,700 --> 00:00:46,640
那这样的话

14
00:00:46,640 --> 00:00:48,840
我们是不是就可以解决很多问题了

15
00:00:48,840 --> 00:00:50,600
比如说我可以把公共的放在这里面

16
00:00:50,600 --> 00:00:51,940
我关于开发的写这里

17
00:00:51,940 --> 00:00:53,840
关于我们的生产环境的写在这里

18
00:00:53,840 --> 00:00:56,140
那这时候呢我们就可以安装一个对吧

19
00:00:56,140 --> 00:00:57,360
这个东西我们叫他more

20
00:00:57,360 --> 00:00:57,780
对吧

21
00:00:57,780 --> 00:00:58,780
more

22
00:00:58,780 --> 00:01:02,160
那我们需要这样一个插件,叫webpack-merge

23
00:01:02,160 --> 00:01:04,680
他就可以帮我们看这件事

24
00:01:04,680 --> 00:01:06,940
在这里按好啊

25
00:01:06,940 --> 00:01:08,080
那我们先去用了

26
00:01:08,080 --> 00:01:10,080
这个第一我需要拿到这样一个对吧

27
00:01:10,080 --> 00:01:12,740
merge或者叫merge里面有个属性叫smart

28
00:01:12,740 --> 00:01:16,540
轻量等于require我就叫它webpack对吧

29
00:01:16,540 --> 00:01:19,540
mergewebpack-merge

30
00:01:19,540 --> 00:01:21,640
那用法呢非常简单

31
00:01:21,640 --> 00:01:22,880
我们可以这样去做啊

32
00:01:22,880 --> 00:01:23,580
那同样啊

33
00:01:23,580 --> 00:01:26,540
这时候呢我们还是要把两个两个变料怎么样

34
00:01:26,540 --> 00:01:27,540
变成一个是吧

35
00:01:27,540 --> 00:01:28,620
那这里面我还是一样

36
00:01:28,620 --> 00:01:30,820
叫module.esports

37
00:01:30,820 --> 00:01:31,920
往来导出

38
00:01:31,920 --> 00:01:32,860
往来导出谁呢

39
00:01:32,860 --> 00:01:34,320
用这个smart来合并

40
00:01:34,320 --> 00:01:35,220
那合并什么呢

41
00:01:35,220 --> 00:01:36,920
是不是第一我需要一个公共的对吧

42
00:01:36,920 --> 00:01:38,920
那公共的就是我们那个base

43
00:01:38,920 --> 00:01:42,220
等于require我们这样一个webpack

44
00:01:42,220 --> 00:01:43,820
pack对吧

45
00:01:43,820 --> 00:01:46,120
完了第二base.js

46
00:01:46,120 --> 00:01:46,920
完了里面呢

47
00:01:46,920 --> 00:01:48,620
我就把这个base放在前面

48
00:01:48,620 --> 00:01:49,320
完了后面呢

49
00:01:49,320 --> 00:01:50,820
加上我自己的一些配置

50
00:01:50,820 --> 00:01:52,120
比如说这base里面啊

51
00:01:52,120 --> 00:01:53,620
我就把这个东西分开啊

52
00:01:53,620 --> 00:01:55,220
以前production应该写哪啊

53
00:01:55,220 --> 00:01:57,120
是不是现在写到他自己的文件里去啊

54
00:01:57,120 --> 00:01:58,680
这里mode production

55
00:01:58,680 --> 00:02:01,520
同样我可以在这里去定义环境变量对吧

56
00:02:01,520 --> 00:02:02,620
比如说用插件

57
00:02:02,620 --> 00:02:03,720
我就先不去写了

58
00:02:03,720 --> 00:02:05,020
主要看这样一个效果

59
00:02:05,020 --> 00:02:06,420
这是生产环境

60
00:02:06,420 --> 00:02:09,420
同样我把这个东西可以占给我们的开发环境来

61
00:02:09,420 --> 00:02:11,720
这里就改成我们的DVE

62
00:02:11,720 --> 00:02:12,720
LOP

63
00:02:12,720 --> 00:02:13,720
MENT

64
00:02:13,720 --> 00:02:15,620
现在理论上怎么样

65
00:02:15,620 --> 00:02:17,220
是不是我就可以运行这个文件

66
00:02:17,220 --> 00:02:18,420
就是我们的开发

67
00:02:18,420 --> 00:02:19,820
运行这个就是我们的生产

68
00:02:19,820 --> 00:02:21,520
现在我们可以来看一看

69
00:02:21,520 --> 00:02:22,920
比如说在这里运行

70
00:02:22,920 --> 00:02:24,120
可以通过npm

71
00:02:24,120 --> 00:02:24,620
对吧

72
00:02:24,620 --> 00:02:25,120
软

73
00:02:25,220 --> 00:02:26,580
完了比如说就build 吧

74
00:02:26,580 --> 00:02:27,880
那样build 我这里呢

75
00:02:27,880 --> 00:02:31,180
我可以通过杠杠config来指定我们运行的文件

76
00:02:31,180 --> 00:02:31,820
那文件呢

77
00:02:31,820 --> 00:02:35,360
分别就是webpack先用这个.dv.js吧

78
00:02:35,360 --> 00:02:37,880
看看效果啊

79
00:02:37,880 --> 00:02:39,460
但这里面你别忘了

80
00:02:39,460 --> 00:02:40,780
我穿餐的时候需要干嘛呢

81
00:02:40,780 --> 00:02:43,060
是不是需要加两个杠杠杠杠啊

82
00:02:43,060 --> 00:02:44,260
这是要注意

83
00:02:44,260 --> 00:02:46,260
我告诉我没有解析到这个文件啊

84
00:02:46,260 --> 00:02:48,020
这个文件我忘了加点杠

85
00:02:48,020 --> 00:02:49,980
点杠这里面要注意一下

86
00:02:49,980 --> 00:02:52,720
这是一个自己写的模块

87
00:02:52,720 --> 00:02:54,480
叫贝斯杰斯是吧

88
00:02:54,480 --> 00:02:55,980
这里我再来运行一下

89
00:02:55,980 --> 00:02:59,280
看看是不是现在就走了这个开发环境

90
00:02:59,280 --> 00:03:02,520
晚上里面呢又爆了个一场说这个东西没有找到是吧

91
00:03:02,520 --> 00:03:06,520
webpac 少了个屁这里面写错字的啊

92
00:03:06,520 --> 00:03:08,940
我把它也粘过来放到我们的DV里

93
00:03:08,940 --> 00:03:09,840
我再次运行啊

94
00:03:09,840 --> 00:03:11,640
这回应该就OK了

95
00:03:11,640 --> 00:03:12,440
来一次

96
00:03:12,440 --> 00:03:15,120
运行一下

97
00:03:15,120 --> 00:03:17,240
完了跑稍等是吧

98
00:03:17,240 --> 00:03:18,320
哎这时候打包出来了

99
00:03:18,320 --> 00:03:20,320
我们看看现在是4.73k

100
00:03:20,320 --> 00:03:23,440
我来看看这个home.js是不是我想要的

101
00:03:23,440 --> 00:03:24,340
哎这应该是开发

102
00:03:24,340 --> 00:03:25,000
没错

103
00:03:25,000 --> 00:03:26,540
那同样的我可以怎么样

104
00:03:26,540 --> 00:03:27,600
用生产环境

105
00:03:27,600 --> 00:03:28,600
那生产环境呢

106
00:03:28,600 --> 00:03:30,040
我就可以用这个pld

107
00:03:30,040 --> 00:03:31,700
我再来试一下是吧

108
00:03:31,700 --> 00:03:32,500
看看行不行

109
00:03:32,500 --> 00:03:34,500
那这里面出来的结果呀

110
00:03:34,500 --> 00:03:36,040
是不是就应该是1.24k

111
00:03:36,040 --> 00:03:37,100
那应该怎么样

112
00:03:37,100 --> 00:03:38,380
确实应该被压缩了

113
00:03:38,380 --> 00:03:39,280
那好了

114
00:03:39,280 --> 00:03:40,780
那我们是不是就可以怎么样

115
00:03:40,780 --> 00:03:42,440
根据我们的功能来划分

116
00:03:42,440 --> 00:03:43,200
比如说呢

117
00:03:43,200 --> 00:03:44,300
我们一些开发的

118
00:03:44,300 --> 00:03:45,080
可能会有什么呢

119
00:03:45,080 --> 00:03:46,180
叫什么dv server

120
00:03:46,180 --> 00:03:47,000
那些配置是吧

121
00:03:47,000 --> 00:03:48,180
我说可以就放到这里来了

122
00:03:48,180 --> 00:03:48,800
哎

123
00:03:48,800 --> 00:03:49,740
比如说还有什么呢

124
00:03:49,740 --> 00:03:51,540
还有我们那个什么dv tool

125
00:03:51,540 --> 00:03:51,940
对吧

126
00:03:51,940 --> 00:03:52,600
tool

127
00:03:52,600 --> 00:03:53,800
我这也要可以放上一个什么

128
00:03:53,800 --> 00:03:55,300
Southmine 对吧 圆满营舍

129
00:03:55,300 --> 00:03:57,660
那同样的在我们生产环境中呢

130
00:03:57,660 --> 00:03:59,980
我是不是可以加上一些优化项对吧

131
00:03:59,980 --> 00:04:02,560
比如说我们的迷你什么麦子是吧

132
00:04:02,560 --> 00:04:05,080
迷你麦子应该叫optimization 对吧

133
00:04:05,080 --> 00:04:08,300
Optimization 这样的东西对吧

134
00:04:08,300 --> 00:04:10,060
往里面有个minimizer是个速度

135
00:04:10,060 --> 00:04:11,920
是不是这样的话我就可以怎么样

136
00:04:11,920 --> 00:04:13,420
单独的写两个文件

137
00:04:13,420 --> 00:04:15,220
一个控制开发 一个控制生产

138
00:04:15,220 --> 00:04:17,560
而且呢包括我们刚才定义的环境变量

139
00:04:17,560 --> 00:04:18,560
是不是也可以怎么样

140
00:04:18,560 --> 00:04:19,480
拉到这里来了

141
00:04:19,480 --> 00:04:21,040
比如在这里我可以来干什么是不是

142
00:04:21,040 --> 00:04:22,960
你有就这个plugins是吧

143
00:04:22,960 --> 00:04:28,660
OK,它会通过这样一个什么,我们的smart来把我们这两个配置文件怎么样进行合并

144
00:04:28,660 --> 00:04:33,840
这样我们就可以彻底解决什么,是不是生产环境和开发环境的不同了

