1
00:00:00,000 --> 00:00:04,660
我们呢再来说一个vipag中的比较长的一个插件

2
00:00:04,660 --> 00:00:06,340
就说啊一般情况下呀

3
00:00:06,340 --> 00:00:07,500
在我们开发的时候啊

4
00:00:07,500 --> 00:00:08,660
可能会有一套环境

5
00:00:08,660 --> 00:00:10,680
上线的时候呢可能会有一套环境

6
00:00:10,680 --> 00:00:12,480
往上这个东西呢可能会根据

7
00:00:12,480 --> 00:00:14,860
哎我这个当前的这个环境啊来判断

8
00:00:14,860 --> 00:00:16,840
他应该是选择开发的这个参数

9
00:00:16,840 --> 00:00:18,080
还是上线的参数

10
00:00:18,080 --> 00:00:19,920
比如说呀像我们写阿杰克斯

11
00:00:19,920 --> 00:00:21,780
哎我们经常会这样去写

12
00:00:21,780 --> 00:00:22,120
对吧

13
00:00:22,120 --> 00:00:23,780
比如说哎来个UIL

14
00:00:23,780 --> 00:00:26,020
那这个UIL啊可能默认是空的

15
00:00:26,020 --> 00:00:27,020
那比如我判断

16
00:00:27,020 --> 00:00:29,380
哎如果呀当前是开发环境的话

17
00:00:29,380 --> 00:00:59,360
好,我会让这个UIL对吧,可能会等于什么呢,等于我们的这个ATP,冒号,刚刚,localhost,对吧,3000,完了,可能当我们上线的时候,对吧,那我可能把这个UIL啊,变成了一个上线的地址,比如说冒号,刚刚,3w.珠珠峰培训.cn,是这样的,那这时候我们就可以在这里去打印一下我们这个UIL,是这样吧,但是你这样写完以后啊,就会有一些小小的问题啊,就是这个DV哪来的,那我肯定是在我们开中,

18
00:00:59,360 --> 00:01:00,440
开发的时候怎么样

19
00:01:00,440 --> 00:01:01,760
说插进去这样一个变量

20
00:01:01,760 --> 00:01:03,680
这时候我们webpack

21
00:01:03,680 --> 00:01:05,480
就帮我们提供了这样一个插件

22
00:01:05,480 --> 00:01:06,960
可以帮我们看这件事

23
00:01:06,960 --> 00:01:08,440
比如说我们可以注入这样一个变量

24
00:01:08,440 --> 00:01:09,440
区域变量

25
00:01:09,440 --> 00:01:10,800
在我们的代码里来使用

26
00:01:10,800 --> 00:01:13,760
我要根据这个变量来区分是开发还是上线

27
00:01:13,760 --> 00:01:14,520
好了

28
00:01:14,520 --> 00:01:15,920
那在这里非常方便

29
00:01:15,920 --> 00:01:18,360
我们可以在这里直接一入这个插件

30
00:01:18,360 --> 00:01:20,280
这个插件是webpack自带的

31
00:01:20,280 --> 00:01:22,160
这里我把配置文件再编分一份

32
00:01:22,160 --> 00:01:24,880
这里我再来一个按

33
00:01:24,880 --> 00:01:27,160
我们在下面就下面加这一个插件

34
00:01:27,160 --> 00:01:34,960
叫new 一个叫 webpack 第二什么的叫叫这个 define plug in 插戒定义插戒

35
00:01:34,960 --> 00:01:37,340
它里面可以定些变量

36
00:01:37,340 --> 00:01:41,980
刚才我们是不是说了可以定义一个变量叫 dv 而 dv 的值呢长什么样的哎

37
00:01:41,980 --> 00:01:43,180
你说他可能是开发

38
00:01:43,180 --> 00:01:44,720
这你想象的是这样啊

39
00:01:44,720 --> 00:01:46,200
但这样写并不行

40
00:01:46,200 --> 00:01:47,440
可以来运行一下

41
00:01:47,440 --> 00:01:48,300
就在这里

42
00:01:48,300 --> 00:01:49,780
npm run dv

43
00:01:49,780 --> 00:01:52,740
哎可能说不定就会报错啊

44
00:01:52,740 --> 00:01:53,500
看看效果

45
00:01:53,500 --> 00:01:55,780
呃在这里出来吧

46
00:01:55,780 --> 00:01:56,540
出来吧

47
00:01:56,540 --> 00:01:57,980
点慢啊稍等一下

48
00:01:57,980 --> 00:02:00,500
这里呢现在编译没包错

49
00:02:00,500 --> 00:02:02,000
但是我们刷一下效果啊

50
00:02:02,000 --> 00:02:02,580
console

51
00:02:02,580 --> 00:02:05,100
他会告诉我这DV怎么样没定义

52
00:02:05,100 --> 00:02:06,440
为什么没定义啊

53
00:02:06,440 --> 00:02:07,740
这里面是这样的啊

54
00:02:07,740 --> 00:02:09,700
你看报道是哪个DV是小DV

55
00:02:09,700 --> 00:02:12,240
而不是我们页面里面怎么样去用的那DV

56
00:02:12,240 --> 00:02:13,800
你看页面里面是大DV

57
00:02:13,800 --> 00:02:15,200
你说这个东西他没定义

58
00:02:15,200 --> 00:02:15,940
为什么呢

59
00:02:15,940 --> 00:02:17,040
他会这样做啊

60
00:02:17,040 --> 00:02:18,480
他会把这个值啊

61
00:02:18,480 --> 00:02:20,140
进行定义到环境变量里去

62
00:02:20,140 --> 00:02:20,940
也相当于这样

63
00:02:20,940 --> 00:02:22,340
我写代码console log

64
00:02:22,340 --> 00:02:23,640
他会把这DV怎么样

65
00:02:23,640 --> 00:02:24,800
直接放在这来

66
00:02:25,580 --> 00:02:27,540
那可不为这DV没定义吗

67
00:02:27,540 --> 00:02:28,440
那你需要怎么办呢

68
00:02:28,440 --> 00:02:29,680
你说你可以这样做

69
00:02:29,680 --> 00:02:31,820
再加一个什么双一号

70
00:02:31,820 --> 00:02:32,580
这样来写

71
00:02:32,580 --> 00:02:33,920
那这样写的话就很恶心了

72
00:02:33,920 --> 00:02:34,720
但是可以

73
00:02:34,720 --> 00:02:36,740
他会把这样一个值放到哪去呢

74
00:02:36,740 --> 00:02:37,580
放在这来

75
00:02:37,580 --> 00:02:38,980
那你看看效果是不是这样啊

76
00:02:38,980 --> 00:02:40,480
我把它出去

77
00:02:40,480 --> 00:02:41,680
来跑一下

78
00:02:41,680 --> 00:02:42,320
运行

79
00:02:42,320 --> 00:02:46,480
我们在这运行完啊

80
00:02:46,480 --> 00:02:46,980
稍等

81
00:02:46,980 --> 00:02:50,540
完了在这里呢

82
00:02:50,540 --> 00:02:52,540
我们看看刷新一下

83
00:02:52,540 --> 00:02:54,380
你看这时候啊

84
00:02:54,380 --> 00:02:56,760
好像他报了一个错误是吧

85
00:02:56,760 --> 00:02:57,880
这错误的无所谓啊

86
00:02:57,880 --> 00:02:59,720
我们看看他现在出来是哪一个

87
00:02:59,720 --> 00:03:01,220
这个没有去打印啊

88
00:03:01,220 --> 00:03:02,140
我把它打印一下

89
00:03:02,140 --> 00:03:03,600
看着好像挺别扭的

90
00:03:03,600 --> 00:03:04,480
我在这里呢

91
00:03:04,480 --> 00:03:06,560
来个UIL是吧

92
00:03:06,560 --> 00:03:07,520
我来个刚刚

93
00:03:07,520 --> 00:03:08,720
完底下的东西呢

94
00:03:08,720 --> 00:03:09,740
我把它先住掉啊

95
00:03:09,740 --> 00:03:11,900
住掉完了刷新

96
00:03:11,900 --> 00:03:16,060
嗯有点慢是吧

97
00:03:16,060 --> 00:03:17,560
稍等啊好

98
00:03:17,560 --> 00:03:18,380
编译成功了

99
00:03:18,380 --> 00:03:20,460
那这里面是不是出来的就是六号3000呀

100
00:03:20,460 --> 00:03:21,320
因为目前呢

101
00:03:21,320 --> 00:03:22,500
现在是一个低微模式

102
00:03:22,500 --> 00:03:23,240
还有值

103
00:03:23,240 --> 00:03:23,580
对吧

104
00:03:23,580 --> 00:03:24,540
你可以在这判断的吧

105
00:03:24,540 --> 00:03:25,880
他等等等第一位吗

106
00:03:25,880 --> 00:03:27,020
等等等

107
00:03:27,020 --> 00:03:29,640
如果相等的话是不是就六口号3000呀

108
00:03:29,640 --> 00:03:30,840
再刷新一下啊

109
00:03:30,840 --> 00:03:31,420
哎

110
00:03:31,420 --> 00:03:32,420
看样子没问题的

111
00:03:32,420 --> 00:03:34,380
那比如说有一天我想上线了

112
00:03:34,380 --> 00:03:35,440
那我需要干嘛呢

113
00:03:35,440 --> 00:03:36,780
你就可以把这个值对吧

114
00:03:36,780 --> 00:03:37,680
比如再改造一下

115
00:03:37,680 --> 00:03:39,780
找到这个配置文件啊

116
00:03:39,780 --> 00:03:40,820
就是他没错

117
00:03:40,820 --> 00:03:42,140
我让里面呢我可以改造什么呢

118
00:03:42,140 --> 00:03:43,240
叫production

119
00:03:43,240 --> 00:03:44,440
哎这就可以了啊

120
00:03:44,440 --> 00:03:46,240
那现在呢我们再来试试是吧

121
00:03:46,240 --> 00:03:47,440
我在这里运行

122
00:03:47,440 --> 00:03:50,520
当然配置文件只一改定一改改掉对吧

123
00:03:50,520 --> 00:03:51,240
就需要怎么样

124
00:03:51,240 --> 00:03:52,380
重新配置一下

125
00:03:52,880 --> 00:03:55,500
那这里面呢,我们看看效果啊,刷新

126
00:03:55,500 --> 00:04:00,060
看这回出来的会不会是我们的,是不是就更新环境了

127
00:04:00,060 --> 00:04:03,260
相当于这样的话呀,我们就可以定义一个环境变量

128
00:04:03,260 --> 00:04:05,480
但是这样的写法呀,我是不赞同的啊

129
00:04:05,480 --> 00:04:09,480
那我们一般会怎么给一个字符串,再去加上一个双引号

130
00:04:09,480 --> 00:04:13,060
一般会用这样的方式,叫Json点什么,Streetify

131
00:04:13,060 --> 00:04:14,740
OK,这样来做

132
00:04:14,740 --> 00:04:17,780
那当然了,有的变量是不需要加这个Streetify的

133
00:04:17,780 --> 00:04:20,120
比如说,我定义一个标识,Flag

134
00:04:20,120 --> 00:04:22,580
那它的值呢,可能是个Ball类型,对吧

135
00:04:22,680 --> 00:04:23,920
你可以这样写

136
00:04:23,920 --> 00:04:24,740
这样写的话

137
00:04:24,740 --> 00:04:27,440
是他相当于会把这个单引行引去掉啊

138
00:04:27,440 --> 00:04:28,520
只用这个值

139
00:04:28,520 --> 00:04:30,140
那这个值确实就是个什么

140
00:04:30,140 --> 00:04:30,880
波尔类型

141
00:04:30,880 --> 00:04:32,740
你要加上值的死军范子变成什么了

142
00:04:32,740 --> 00:04:34,280
变成字幕串波尔了是吧

143
00:04:34,280 --> 00:04:36,480
那同样比如说在这来个表达式

144
00:04:36,480 --> 00:04:38,440
叫easypression是吧

145
00:04:38,440 --> 00:04:39,920
easypression

146
00:04:39,920 --> 00:04:41,920
可以这样给代表来个1+1

147
00:04:41,920 --> 00:04:43,440
那这个值表示的就是什么

148
00:04:43,440 --> 00:04:45,040
取他最终的结果2

149
00:04:45,040 --> 00:04:45,920
完了复到哪去啊

150
00:04:45,920 --> 00:04:46,820
复到一面上

151
00:04:46,820 --> 00:04:47,440
但是呢

152
00:04:47,440 --> 00:04:49,840
如果你想变成1+1就是个字幕串的话

153
00:04:49,840 --> 00:04:50,720
你也可以怎么样

154
00:04:50,720 --> 00:04:53,320
用这三点什么军贩来包含一下他

155
00:04:53,320 --> 00:04:56,680
那现在我们可以来看一看回到应该是里面

156
00:04:56,680 --> 00:04:57,220
我这里呢

157
00:04:57,220 --> 00:04:58,580
我分别给你打印一下

158
00:04:58,580 --> 00:05:00,920
第一个叫我们的弗莱格是吧

159
00:05:00,920 --> 00:05:03,160
泰泡沃泡泡沃

160
00:05:03,160 --> 00:05:03,380
好了

161
00:05:03,380 --> 00:05:05,120
弗莱格看看大写的对吧

162
00:05:05,120 --> 00:05:06,420
他是不是一个波尔类型

163
00:05:06,420 --> 00:05:09,520
完了再看看另一个这个表达式

164
00:05:09,520 --> 00:05:10,320
他是什么东西

165
00:05:10,320 --> 00:05:10,820
对吧

166
00:05:10,820 --> 00:05:11,980
落的意思

167
00:05:11,980 --> 00:05:12,480
prison

168
00:05:12,480 --> 00:05:14,760
那大写的完了

169
00:05:14,760 --> 00:05:15,380
我们在这里呢

170
00:05:15,380 --> 00:05:16,880
再来执行一下是吧

171
00:05:16,880 --> 00:05:18,020
来执行一下

172
00:05:19,680 --> 00:05:21,680
现在我们来看看效果

173
00:05:21,680 --> 00:05:22,840
有个小小的包错

174
00:05:22,840 --> 00:05:24,320
他说豆号有问题是吧

175
00:05:24,320 --> 00:05:26,600
flag标识符有问题

176
00:05:26,600 --> 00:05:28,880
这里我们看看flag

177
00:05:28,880 --> 00:05:30,640
我来找到我们的配置文件里面

178
00:05:30,640 --> 00:05:32,000
config

179
00:05:32,000 --> 00:05:33,120
这里面叫flag

180
00:05:33,120 --> 00:05:33,960
没错是吧

181
00:05:33,960 --> 00:05:34,480
完了处

182
00:05:34,480 --> 00:05:35,280
这是少了个多号

183
00:05:35,280 --> 00:05:36,680
果然再来一次

184
00:05:36,680 --> 00:05:40,760
运行一下

185
00:05:40,760 --> 00:05:45,200
好了

186
00:05:45,200 --> 00:05:46,240
马上就好了

187
00:05:46,240 --> 00:05:47,040
我再刷新一下

188
00:05:47,040 --> 00:05:48,160
运行

189
00:05:48,160 --> 00:05:49,640
这里面是不是一个是布尔雷行

190
00:05:49,680 --> 00:05:51,060
我来一个确实就是

191
00:05:51,060 --> 00:05:52,340
这一块是应该拼错了

192
00:05:52,340 --> 00:05:53,760
我把这只粘过来

193
00:05:53,760 --> 00:05:56,020
一次pression多了个O

194
00:05:56,020 --> 00:05:56,940
那这里面呢

195
00:05:56,940 --> 00:05:58,360
我把它粘到配置文件里

196
00:05:58,360 --> 00:05:59,080
再打印一下

197
00:05:59,080 --> 00:06:00,300
看看效果吧

198
00:06:00,300 --> 00:06:02,040
这时候呢就没问题了

199
00:06:02,040 --> 00:06:03,860
这就出来结果已经是2了

200
00:06:03,860 --> 00:06:05,580
那如果你想让它出来的是什么

201
00:06:05,580 --> 00:06:06,080
1加1

202
00:06:06,080 --> 00:06:06,620
那好

203
00:06:06,620 --> 00:06:07,520
我刚才说了

204
00:06:07,520 --> 00:06:09,200
你是不是可以在我的配置文件里怎么样

205
00:06:09,200 --> 00:06:10,160
改上一个什么

206
00:06:10,160 --> 00:06:11,540
json.sgmify

207
00:06:11,540 --> 00:06:11,980
是吧

208
00:06:11,980 --> 00:06:13,780
他把这个字固串整个怎么样

209
00:06:13,780 --> 00:06:15,060
放到我这个变量里

210
00:06:15,060 --> 00:06:16,540
这样就可以了

211
00:06:16,540 --> 00:06:18,840
这就是我们定义环境变量的一个方法

212
00:06:18,840 --> 00:06:20,400
感谢观看

