1
00:00:00,000 --> 00:00:04,440
我们再来看webpack中的下一个优化点

2
00:00:04,440 --> 00:00:07,120
比如说在我们匹配所有gs的过程中

3
00:00:07,120 --> 00:00:08,780
我说了默认情况下

4
00:00:08,780 --> 00:00:10,540
他也会去找这个node modus目录

5
00:00:10,540 --> 00:00:12,460
这时候我们一般会怎么样

6
00:00:12,460 --> 00:00:13,780
是加上一个排除

7
00:00:13,780 --> 00:00:14,780
exclude

8
00:00:14,780 --> 00:00:16,200
这也是一个优化项

9
00:00:16,200 --> 00:00:19,720
那里面我们可以直接加上node-modus

10
00:00:19,720 --> 00:00:22,300
同样我们还需要怎么样

11
00:00:22,300 --> 00:00:24,340
有排除可能还会有一个相对的

12
00:00:24,340 --> 00:00:24,820
叫包含

13
00:00:24,820 --> 00:00:26,280
比如说我跟他说

14
00:00:26,280 --> 00:00:27,460
只找这个

15
00:00:27,460 --> 00:00:29,100
比如说叫src目录

16
00:00:29,100 --> 00:00:30,940
这两个属性的你可以怎么样

17
00:00:30,940 --> 00:00:31,900
加一个就OK了

18
00:00:31,900 --> 00:00:33,140
这就是排除和包含

19
00:00:33,140 --> 00:00:34,600
这也是一个优化点

20
00:00:34,600 --> 00:00:36,120
那我们再接转一下看

21
00:00:36,120 --> 00:00:39,320
比如说在我们引用一些其他的第三方插件的时候

22
00:00:39,320 --> 00:00:40,980
可能还会有一些优化

23
00:00:40,980 --> 00:00:43,700
比如说我们一般可能会用到这样一个酷

24
00:00:43,700 --> 00:00:45,020
这个酷也很常见

25
00:00:45,020 --> 00:00:47,180
这个酷是装门用来做时间的

26
00:00:47,180 --> 00:00:48,360
比如说我给你搜一下

27
00:00:48,360 --> 00:00:51,020
这里我就直接搜

28
00:00:51,020 --> 00:00:51,800
叫moment

29
00:00:51,800 --> 00:00:52,900
这样一个酷

30
00:00:52,900 --> 00:00:54,140
一听这个酷啥意思呢

31
00:00:54,140 --> 00:00:55,860
它是一个装门做时间插件

32
00:00:55,860 --> 00:00:58,240
比如说它可以判断

33
00:00:58,240 --> 00:00:59,840
当前我们这个时间

34
00:00:59,840 --> 00:01:01,000
你看这里面有啊

35
00:01:01,000 --> 00:01:01,900
比如说隔着挖时间

36
00:01:01,900 --> 00:01:03,360
比如我们的时间给我时间戳

37
00:01:03,360 --> 00:01:03,900
那好

38
00:01:03,900 --> 00:01:05,240
我可以帮你隔着上年月日

39
00:01:05,240 --> 00:01:06,960
比如说可以算相对时间

40
00:01:06,960 --> 00:01:08,280
比如说距离现在

41
00:01:08,280 --> 00:01:09,060
对吧

42
00:01:09,060 --> 00:01:10,300
完了距离过离现在

43
00:01:10,300 --> 00:01:11,320
完了上一个时间点

44
00:01:11,320 --> 00:01:12,040
中间的什么

45
00:01:12,040 --> 00:01:12,820
差了多久

46
00:01:12,820 --> 00:01:14,480
七年前什么十小时之前

47
00:01:14,480 --> 00:01:15,280
这个东西是吧

48
00:01:15,280 --> 00:01:16,080
那好了

49
00:01:16,080 --> 00:01:17,260
我就随便拿一句

50
00:01:17,260 --> 00:01:18,920
那我们来用一下这个酷啊

51
00:01:18,920 --> 00:01:19,520
这个酷啊

52
00:01:19,520 --> 00:01:20,280
它的好处是什么

53
00:01:20,280 --> 00:01:21,140
它做了多语言

54
00:01:21,140 --> 00:01:22,300
它可以支持什么呀

55
00:01:22,300 --> 00:01:23,160
很多语言

56
00:01:23,160 --> 00:01:24,420
那这里啊

57
00:01:24,420 --> 00:01:25,200
我们就来用一下

58
00:01:25,200 --> 00:01:27,780
首先我们需要安装一下这个moment的酷

59
00:01:27,780 --> 00:01:31,640
为了方便我们一样把开发服务器也装上

60
00:01:31,640 --> 00:01:33,580
这里面可能安装好

61
00:01:33,580 --> 00:01:34,900
这包还是比较大的

62
00:01:34,900 --> 00:01:35,620
我在这

63
00:01:35,620 --> 00:01:41,860
这里我们先把代码用上

64
00:01:41,860 --> 00:01:43,100
第一步非常简单

65
00:01:43,100 --> 00:01:45,700
我们需要先去引入我们这样一个moment

66
00:01:45,700 --> 00:01:46,480
import

67
00:01:46,480 --> 00:01:47,620
比如说moment

68
00:01:47,620 --> 00:01:49,600
from我们这样一个moment

69
00:01:49,600 --> 00:01:54,100
并且我们可以加上这样一句话

70
00:01:54,100 --> 00:01:54,420
对吧

71
00:01:54,420 --> 00:01:56,140
他就会帮我们去打运一个相对时间

72
00:01:56,140 --> 00:01:56,940
这里呢

73
00:01:56,940 --> 00:01:57,940
我们把R拿过来

74
00:01:57,940 --> 00:01:59,620
完了console log是吧

75
00:01:59,620 --> 00:02:00,900
console log

76
00:02:00,900 --> 00:02:02,460
比如说这来个R

77
00:02:02,460 --> 00:02:04,700
我们来看一下

78
00:02:04,700 --> 00:02:05,360
看看效果

79
00:02:05,360 --> 00:02:06,220
那这样的话呢

80
00:02:06,220 --> 00:02:07,180
我们就OK了

81
00:02:07,180 --> 00:02:08,340
这个意思是什么呢

82
00:02:08,340 --> 00:02:10,820
是距离现在有多少天是吧

83
00:02:10,820 --> 00:02:11,260
那好

84
00:02:11,260 --> 00:02:11,960
这里呢

85
00:02:11,960 --> 00:02:13,420
我就来运行一下

86
00:02:13,420 --> 00:02:13,960
嗯

87
00:02:13,960 --> 00:02:14,820
为了方便

88
00:02:14,820 --> 00:02:17,220
我就直接配个pizer接送吧

89
00:02:17,220 --> 00:02:17,840
这配上

90
00:02:17,840 --> 00:02:18,780
找到pizer接送

91
00:02:18,780 --> 00:02:19,620
这里呢

92
00:02:19,620 --> 00:02:20,720
我们可以加上两个脚本

93
00:02:20,720 --> 00:02:21,400
还记得吧

94
00:02:21,400 --> 00:02:22,600
一个叫script

95
00:02:22,600 --> 00:02:23,580
一个开发

96
00:02:23,580 --> 00:02:24,520
叫dv

97
00:02:24,520 --> 00:02:25,360
这里面呢

98
00:02:25,360 --> 00:02:29,760
就可以写叫webpack-dv-sour

99
00:02:29,760 --> 00:02:29,960
完了

100
00:02:29,960 --> 00:02:30,460
另一个呢

101
00:02:30,460 --> 00:02:32,460
叫打包build

102
00:02:32,460 --> 00:02:34,360
我们可以用这个webpack

103
00:02:34,360 --> 00:02:34,860
那现在啊

104
00:02:34,860 --> 00:02:35,960
我们就来用一下

105
00:02:35,960 --> 00:02:37,460
比如说npx

106
00:02:37,460 --> 00:02:38,460
我就不用npx了

107
00:02:38,460 --> 00:02:41,460
这npm run dv

108
00:02:41,460 --> 00:02:42,560
我来看看第一

109
00:02:42,560 --> 00:02:44,460
我们这个打包后的大小

110
00:02:44,460 --> 00:02:44,860
第二呢

111
00:02:44,860 --> 00:02:47,960
看看我们这个当前能不能正常运行啊

112
00:02:47,960 --> 00:02:49,460
这里面我们先看一下

113
00:02:49,460 --> 00:02:51,460
当前告诉我了打包呢

114
00:02:51,460 --> 00:02:52,060
有多大呢

115
00:02:52,060 --> 00:02:53,160
是1.2兆

116
00:02:53,160 --> 00:02:55,160
你会发现这个文件非常大

117
00:02:55,160 --> 00:02:58,240
但是我这里面其实只用了这么一个方法呀

118
00:02:58,240 --> 00:02:59,040
是这样的

119
00:02:59,040 --> 00:03:02,040
因为要我们在这个mod module 下看看这个moment

120
00:03:02,040 --> 00:03:02,960
看一眼

121
00:03:02,960 --> 00:03:04,800
obm

122
00:03:04,800 --> 00:03:05,840
在这儿是吧

123
00:03:05,840 --> 00:03:06,480
我刷新一下

124
00:03:06,480 --> 00:03:08,280
可能有的时候不会更新过来

125
00:03:08,280 --> 00:03:08,840
哎在这儿呢

126
00:03:08,840 --> 00:03:10,080
有

127
00:03:10,080 --> 00:03:11,960
里面要默认我们找佩尔杰森

128
00:03:11,960 --> 00:03:15,800
他呢会引用当前的这样一个叫moment.js看到了吧

129
00:03:15,800 --> 00:03:16,680
他引用的时候呢

130
00:03:16,680 --> 00:03:17,320
引用的谁呢

131
00:03:17,320 --> 00:03:18,680
是不是就是他呀

132
00:03:18,680 --> 00:03:20,240
那引用他的时候你可以看到啊

133
00:03:20,240 --> 00:03:20,760
这里面呢

134
00:03:20,760 --> 00:03:23,040
他直接有一个语法叫require

135
00:03:23,040 --> 00:03:24,560
我来搜一下在这儿

136
00:03:24,560 --> 00:03:27,160
是不是它直接内部就引了这样一个点告

137
00:03:27,160 --> 00:03:28,360
local这样一个文件

138
00:03:28,360 --> 00:03:31,160
那这个文件啊里面放了什么东西啊

139
00:03:31,160 --> 00:03:31,860
我们来看一下

140
00:03:31,860 --> 00:03:33,360
他放了所有的语言包

141
00:03:33,360 --> 00:03:34,460
你看很多语言包

142
00:03:34,460 --> 00:03:36,460
你说在我们家的moment的时候呢

143
00:03:36,460 --> 00:03:39,160
他会自动去把所有的语言包怎么样进行引入

144
00:03:39,160 --> 00:03:40,460
所以这个包很大

145
00:03:40,460 --> 00:03:41,860
哎那咱来看看

146
00:03:41,860 --> 00:03:42,960
比如说他有什么好处

147
00:03:42,960 --> 00:03:45,160
比如说我所有引用引用这个包以后啊

148
00:03:45,160 --> 00:03:47,060
我可以这样给这个moment

149
00:03:47,060 --> 00:03:51,160
叫什么呢叫设置moment他比如说点local

150
00:03:51,160 --> 00:03:52,460
完了里面呢

151
00:03:52,460 --> 00:03:54,460
我就可以直接给上一个比如说他叫中文

152
00:03:54,560 --> 00:03:56,660
看看他能不能变成中文的格式是吧

153
00:03:56,660 --> 00:03:59,080
在ZCN这叫设置语言

154
00:03:59,080 --> 00:04:00,520
语言对吧

155
00:04:00,520 --> 00:04:01,140
那好了

156
00:04:01,140 --> 00:04:02,240
我来看看效果

157
00:04:02,240 --> 00:04:03,480
这里面一样

158
00:04:03,480 --> 00:04:04,700
我服务又起了

159
00:04:04,700 --> 00:04:05,380
是8080

160
00:04:05,380 --> 00:04:06,380
拷贝一下

161
00:04:06,380 --> 00:04:08,640
这里面刷新一下

162
00:04:08,640 --> 00:04:10,440
哦看看啊

163
00:04:10,440 --> 00:04:11,340
因为是log是吧

164
00:04:11,340 --> 00:04:12,320
看看结果

165
00:04:12,320 --> 00:04:14,140
这里面你看是不是告诉我了

166
00:04:14,140 --> 00:04:14,900
14小时内

167
00:04:14,900 --> 00:04:15,560
OK

168
00:04:15,560 --> 00:04:17,540
你也就说他确实变成中文了

169
00:04:17,540 --> 00:04:19,040
但是我们希望怎么样

170
00:04:19,040 --> 00:04:21,040
是不是你不能把这个包都引进来

171
00:04:21,040 --> 00:04:22,120
我只想要中文

172
00:04:22,120 --> 00:04:23,520
但是你把所有包都引进来

173
00:04:23,520 --> 00:04:24,480
这就有点过分了

174
00:04:24,480 --> 00:04:25,120
是吧

175
00:04:25,120 --> 00:04:25,880
那这时候呢

176
00:04:25,880 --> 00:04:28,320
我们就需要一个webpack插件来干这件事

177
00:04:28,320 --> 00:04:29,540
就是忽略掉啊

178
00:04:29,540 --> 00:04:31,960
它内部所有都引用的这些本地文件

179
00:04:31,960 --> 00:04:33,180
把这个拿过来

180
00:04:33,180 --> 00:04:34,040
那怎么用呢

181
00:04:34,040 --> 00:04:36,340
它呢是webpack的一个内置插件

182
00:04:36,340 --> 00:04:38,500
所以说我需要先把这个webpack

183
00:04:38,500 --> 00:04:40,960
还记得我们用的那个什么banner plugin

184
00:04:40,960 --> 00:04:42,620
就是webpack自带

185
00:04:42,620 --> 00:04:43,980
那好

186
00:04:43,980 --> 00:04:44,580
那这里呢

187
00:04:44,580 --> 00:04:45,640
我们需要配个插件

188
00:04:45,640 --> 00:04:46,540
new time

189
00:04:46,540 --> 00:04:49,220
new webpack

190
00:04:49,220 --> 00:04:50,140
它里面呢

191
00:04:50,140 --> 00:04:51,200
就应该有一个东西叫什么

192
00:04:51,200 --> 00:04:52,880
叫ignore plugin

193
00:04:52,880 --> 00:04:55,880
就是他可以忽略掉某些内容

194
00:04:55,880 --> 00:04:56,800
那忽略谁呢

195
00:04:56,800 --> 00:04:57,980
是不是就忽略这个东西

196
00:04:57,980 --> 00:04:59,180
他印入他的时候

197
00:04:59,180 --> 00:05:00,000
我把它忽略掉

198
00:05:00,000 --> 00:05:00,880
你来个杠

199
00:05:00,880 --> 00:05:02,800
这里面来个反杠

200
00:05:02,800 --> 00:05:03,620
往这里呢

201
00:05:03,620 --> 00:05:03,960
反杠

202
00:05:03,960 --> 00:05:05,120
往这并且呢

203
00:05:05,120 --> 00:05:06,020
从哪里引的时候呢

204
00:05:06,020 --> 00:05:08,040
是不是如果从这个moment

205
00:05:08,040 --> 00:05:08,660
告诉我了

206
00:05:08,660 --> 00:05:09,480
他要放一个什么

207
00:05:09,480 --> 00:05:10,520
表达式正责

208
00:05:10,520 --> 00:05:12,800
在这来一个叫什么呢

209
00:05:12,800 --> 00:05:13,320
叫moment

210
00:05:13,320 --> 00:05:15,720
从moment的中

211
00:05:15,720 --> 00:05:17,600
如果他引入了这个点楼口

212
00:05:17,600 --> 00:05:18,080
那好

213
00:05:18,080 --> 00:05:19,140
我就把它怎么样

214
00:05:19,140 --> 00:05:19,800
忽略掉

215
00:05:19,800 --> 00:05:21,500
那这时候在打包的时候

216
00:05:21,500 --> 00:05:22,140
你会发现怎么样

217
00:05:22,140 --> 00:05:23,680
这时候你就想到了

218
00:05:23,680 --> 00:05:25,280
打包后的结果肯定很小

219
00:05:25,280 --> 00:05:25,620
是吧

220
00:05:25,620 --> 00:05:29,000
因为这个我们的所谓的语言包并没有被引入

221
00:05:29,000 --> 00:05:30,400
那咱来看一下

222
00:05:30,400 --> 00:05:33,920
这里面现在告诉我了是有792K是吧

223
00:05:33,920 --> 00:05:35,680
感觉好像也没小多少啊

224
00:05:35,680 --> 00:05:36,660
不过确实是小了

225
00:05:36,660 --> 00:05:38,060
刚才是对吧

226
00:05:38,060 --> 00:05:39,560
1兆1.2兆

227
00:05:39,560 --> 00:05:41,140
现在是790多对吧

228
00:05:41,140 --> 00:05:42,080
那差了500多K呢

229
00:05:42,080 --> 00:05:43,880
那这时候咱看看吧

230
00:05:43,880 --> 00:05:44,740
这时候行不行是吧

231
00:05:44,740 --> 00:05:45,060
刷新

232
00:05:45,060 --> 00:05:47,940
是不是又跑到了再14Hors里去了

233
00:05:47,940 --> 00:05:50,960
你说我们当前这里面设置的这个语言呀

234
00:05:50,960 --> 00:05:52,020
并没有生效

235
00:05:52,020 --> 00:05:53,460
原因是很简单

236
00:05:53,460 --> 00:05:55,260
因为这个包并没有怎么样被引入

237
00:05:55,260 --> 00:05:57,020
那如果没有被引入的话

238
00:05:57,020 --> 00:05:57,800
我们可以怎么办

239
00:05:57,800 --> 00:06:01,980
叫自己手动手动手动对吧

240
00:06:01,980 --> 00:06:05,940
引入所需要的所需要的语言

241
00:06:05,940 --> 00:06:09,420
那好了可以怎么做呢

242
00:06:09,420 --> 00:06:10,720
叫moment

243
00:06:10,720 --> 00:06:12,280
moment

244
00:06:12,280 --> 00:06:13,200
比如在这里面呢

245
00:06:13,200 --> 00:06:14,420
我可以这样把它引进来

246
00:06:14,420 --> 00:06:15,040
就import

247
00:06:15,040 --> 00:06:18,000
我可以去引语言包叫moment

248
00:06:18,000 --> 00:06:19,040
下的什么呢

249
00:06:19,040 --> 00:06:19,740
叫local

250
00:06:19,740 --> 00:06:21,680
完了下的什么呢

251
00:06:21,680 --> 00:06:25,140
我们就引上ZH-CN相当于手动引入注文包

252
00:06:25,140 --> 00:06:25,820
那好了

253
00:06:25,820 --> 00:06:27,040
这时候你会发现怎么样

254
00:06:27,040 --> 00:06:28,960
是不是时间就应该回来了

255
00:06:28,960 --> 00:06:29,680
哎

256
00:06:29,680 --> 00:06:30,340
出来出来

257
00:06:30,340 --> 00:06:31,460
卡住了啊

258
00:06:31,460 --> 00:06:31,820
稍等

259
00:06:31,820 --> 00:06:32,680
我看看

260
00:06:32,680 --> 00:06:34,440
有的时候经常会卡住

261
00:06:34,440 --> 00:06:36,980
它是不是又变成14小时内了

262
00:06:36,980 --> 00:06:37,820
而且呢

263
00:06:37,820 --> 00:06:40,520
我打包和的结果肯定也不会比以前怎么样大

264
00:06:40,520 --> 00:06:41,760
因为我只用了什么

265
00:06:41,760 --> 00:06:42,900
一个语言包

266
00:06:42,900 --> 00:06:45,000
这就是我们这样一个一个闹

267
00:06:45,000 --> 00:06:45,820
plugin的作用

268
00:06:45,820 --> 00:06:48,040
它也是我们WiPAC中的一个优化点

