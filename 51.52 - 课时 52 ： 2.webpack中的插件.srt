1
00:00:00,000 --> 00:00:02,120
接下来呢

2
00:00:02,120 --> 00:00:04,640
我们来写一下这个vipac中的简单的插件

3
00:00:04,640 --> 00:00:05,360
我们呢

4
00:00:05,360 --> 00:00:06,360
先实现个同步的

5
00:00:06,360 --> 00:00:07,280
再实现个异步的

6
00:00:07,280 --> 00:00:09,440
来看一下这个vipac插件的基本配置

7
00:00:09,440 --> 00:00:10,300
那这里呢

8
00:00:10,300 --> 00:00:10,580
一样

9
00:00:10,580 --> 00:00:11,200
我们呢

10
00:00:11,200 --> 00:00:12,040
需要先处置化

11
00:00:12,040 --> 00:00:13,120
安装一下vipac

12
00:00:13,120 --> 00:00:13,760
在这里

13
00:00:13,760 --> 00:00:15,380
先通过cmd

14
00:00:15,380 --> 00:00:16,360
我们呢

15
00:00:16,360 --> 00:00:17,440
直接先处置化

16
00:00:17,440 --> 00:00:18,920
用yarnit

17
00:00:18,920 --> 00:00:20,840
yarnit-y

18
00:00:20,840 --> 00:00:22,000
往那里面呢

19
00:00:22,000 --> 00:00:24,820
我们可以直接安装yarnr的vipac

20
00:00:24,820 --> 00:00:26,620
还有我们的vipac-coy

21
00:00:26,620 --> 00:00:29,280
vipac-coy

22
00:00:29,280 --> 00:00:30,080
杠大地

23
00:00:30,080 --> 00:00:32,700
这里呢

24
00:00:32,700 --> 00:00:33,960
我就可以直接去建了

25
00:00:33,960 --> 00:00:34,180
对吧

26
00:00:34,180 --> 00:00:35,900
比如说我们就建一个src目录

27
00:00:35,900 --> 00:00:38,120
咱就直接把wipad的配置

28
00:00:38,120 --> 00:00:38,960
先快速撸出来

29
00:00:38,960 --> 00:00:39,800
这里呢

30
00:00:39,800 --> 00:00:41,960
我们直接新建一个index.js

31
00:00:41,960 --> 00:00:43,220
完了最下面呢

32
00:00:43,220 --> 00:00:47,920
我们也需要建一个webpack.config.js

33
00:00:47,920 --> 00:00:49,540
完了里面呢

34
00:00:49,540 --> 00:00:51,360
我们需要去导出这样一个配置

35
00:00:51,360 --> 00:00:51,620
是吧

36
00:00:51,620 --> 00:00:53,020
叫module.xpots

37
00:00:53,020 --> 00:00:54,860
完了里面呢

38
00:00:54,860 --> 00:00:55,700
我们需要个entree

39
00:00:55,700 --> 00:00:56,040
对吧

40
00:00:56,040 --> 00:00:56,660
entree

41
00:00:56,660 --> 00:00:57,540
完了里面呢

42
00:00:57,540 --> 00:00:59,260
我们可以直接.src

43
00:00:59,260 --> 00:01:00,680
现在index.js

44
00:01:00,680 --> 00:01:03,760
同样有output

45
00:01:03,760 --> 00:01:06,000
output我们需要给一个file name

46
00:01:06,000 --> 00:01:08,180
比如说打包的文件的名字叫什么

47
00:01:08,180 --> 00:01:09,840
我就叫它bundle.js

48
00:01:09,840 --> 00:01:13,000
这里一样我给它一个叫pass

49
00:01:13,000 --> 00:01:14,240
pass必须是个决入镜

50
00:01:14,240 --> 00:01:16,140
这里我就先在外面写上

51
00:01:16,140 --> 00:01:18,400
在这里light一个pass

52
00:01:18,400 --> 00:01:19,280
pass

53
00:01:19,280 --> 00:01:21,980
等于我们require这样一个pass模块

54
00:01:21,980 --> 00:01:24,100
pass它有没有个方法

55
00:01:24,100 --> 00:01:26,320
这个东西应该都不陌生了

56
00:01:26,320 --> 00:01:27,280
刚刚第2 name

57
00:01:27,280 --> 00:01:28,400
你们点个dist

58
00:01:28,400 --> 00:01:31,000
那之后我们再给个模式吧

59
00:01:31,000 --> 00:01:32,060
叫mode对吧

60
00:01:32,060 --> 00:01:34,660
mode就是dvelopambient

61
00:01:34,660 --> 00:01:36,340
这里呢为了方便

62
00:01:36,340 --> 00:01:38,220
我就不去用那个atmlplugin了

63
00:01:38,220 --> 00:01:39,420
我就直接自己来写

64
00:01:39,420 --> 00:01:41,800
我们在这来个plugins放数组

65
00:01:41,800 --> 00:01:42,640
我们都知道啊

66
00:01:42,640 --> 00:01:43,940
这个plugin怎么执行的

67
00:01:43,940 --> 00:01:45,240
我先看一下已经安好了

68
00:01:45,240 --> 00:01:46,620
这里能刷新一下

69
00:01:46,620 --> 00:01:47,940
那个东西就有了啊

70
00:01:47,940 --> 00:01:49,740
网站里面我说了默认的情况下

71
00:01:49,740 --> 00:01:51,500
我们要执行npxwebpack

72
00:01:51,500 --> 00:01:52,740
他会怎么去做呢

73
00:01:52,740 --> 00:01:54,340
会调用我们这样一个webpack

74
00:01:54,340 --> 00:01:56,060
找到啊webpack

75
00:01:56,060 --> 00:01:57,800
网站里面他有一个叫lab

76
00:01:57,800 --> 00:02:00,100
类文里面其实是有个compiler是吧

77
00:02:00,100 --> 00:02:01,900
我们来看一下compiler

78
00:02:01,900 --> 00:02:06,100
这里c开头的在上面compiler

79
00:02:06,100 --> 00:02:10,300
compiler找找眼神不太好

80
00:02:10,300 --> 00:02:12,300
我找一下这的是吧compiler

81
00:02:12,300 --> 00:02:14,800
完了里面他就跟plugin有相关的了

82
00:02:14,800 --> 00:02:17,400
你看看他里面怎么做的叫plugin

83
00:02:17,400 --> 00:02:20,400
这说了他默认情况下他

84
00:02:20,400 --> 00:02:22,300
我说了内部会new一个compiler

85
00:02:22,300 --> 00:02:23,500
完了他会干什么事呢

86
00:02:23,500 --> 00:02:26,300
会判断当前他有没有这样个plugin属性

87
00:02:26,400 --> 00:02:28,640
如果有的话这个代码的很常见是吧

88
00:02:28,640 --> 00:02:30,460
我们会循环每一个plugin

89
00:02:30,460 --> 00:02:31,460
我让他去怎么样

90
00:02:31,460 --> 00:02:32,600
调他的什么方法

91
00:02:32,600 --> 00:02:33,260
apply

92
00:02:33,260 --> 00:02:36,400
并且呢把这个compiler对象怎么样传给他

93
00:02:36,400 --> 00:02:39,200
那这个compiler呢就是我们new的那个compiler实例

94
00:02:39,200 --> 00:02:41,000
那实例上会有什么东西呢

95
00:02:41,000 --> 00:02:42,200
就会有一些钩子对吧

96
00:02:42,200 --> 00:02:45,040
那钩子呢其实我们也见过叫this.hooks对吧

97
00:02:45,040 --> 00:02:45,800
往上找

98
00:02:45,800 --> 00:02:48,600
这里面嗯我在上面吧

99
00:02:48,600 --> 00:02:49,200
在上面

100
00:02:49,200 --> 00:02:52,900
这里面你看是不是用了一个typeboard实践流啊

101
00:02:52,900 --> 00:02:55,200
那流里面呢可能就包含着我们这些钩子

102
00:02:55,200 --> 00:02:57,700
你看是不是这个类上的挂了很多钩子呀

103
00:02:57,700 --> 00:02:59,200
钩子里面有什么的有什么档啊

104
00:02:59,200 --> 00:03:01,400
刚才看过了什么发射呀等等等等

105
00:03:01,400 --> 00:03:03,400
网上里面的机有什么叫什么

106
00:03:03,400 --> 00:03:04,500
sinc biohook 记得吧

107
00:03:04,500 --> 00:03:07,700
就是我们所谓的同步的带保险的钩子

108
00:03:07,700 --> 00:03:08,900
当然还有一步的

109
00:03:08,900 --> 00:03:10,800
还要串行的等等等等

110
00:03:10,800 --> 00:03:11,800
那咱来看看吧

111
00:03:11,800 --> 00:03:12,900
我们就一个来试一下

112
00:03:12,900 --> 00:03:13,900
看这个钩子能不能用

113
00:03:13,900 --> 00:03:14,900
是不是哎

114
00:03:14,900 --> 00:03:16,300
包括这个complication

115
00:03:16,300 --> 00:03:17,700
它就是一个同步的钩子

116
00:03:17,700 --> 00:03:19,700
那咱来试一下这同步钩子会不会执行

117
00:03:19,700 --> 00:03:21,500
里面一样把它的关掉

118
00:03:21,500 --> 00:03:23,000
完了这时候我们说了

119
00:03:23,000 --> 00:03:23,400
类啊

120
00:03:23,400 --> 00:03:25,200
这个我们的一个插件的其实就是个类

121
00:03:25,200 --> 00:03:27,360
对吧那这里呢我们就写第一个

122
00:03:27,360 --> 00:03:29,760
我们就来一个叫比如说down plugin

123
00:03:29,760 --> 00:03:31,800
吧当我们这事儿完成了

124
00:03:31,800 --> 00:03:32,960
我们用这样一个东西啊

125
00:03:32,960 --> 00:03:34,340
就那引的时候呢

126
00:03:34,340 --> 00:03:36,440
我们这里呢就需要一个文件夹吧

127
00:03:36,440 --> 00:03:37,800
就专门建个文件夹

128
00:03:37,800 --> 00:03:39,360
存放我们自己写的插件

129
00:03:39,360 --> 00:03:40,960
就plugins

130
00:03:40,960 --> 00:03:43,400
这里呢我就来个down plugin 好吧

131
00:03:43,400 --> 00:03:45,700
就是当我们这个哎打包完成了

132
00:03:45,700 --> 00:03:46,700
你跟我说一声是吧

133
00:03:46,700 --> 00:03:47,460
就这个插件

134
00:03:47,460 --> 00:03:49,440
那用法呢是我说它是个类

135
00:03:49,440 --> 00:03:51,160
以为把名字大写最好啊

136
00:03:51,160 --> 00:03:52,140
叫down plugin

137
00:03:52,140 --> 00:03:53,360
我来个class

138
00:03:53,360 --> 00:03:54,500
我觉得到

139
00:03:54,500 --> 00:03:58,060
plugin其实插件的流程写下来都是固定的写法

140
00:03:58,060 --> 00:03:59,200
完了这里面我们说了

141
00:03:59,200 --> 00:04:02,200
每个类上都要有一个方法叫apply

142
00:04:02,200 --> 00:04:05,160
完了他我们也描述了对吧

143
00:04:05,160 --> 00:04:06,920
他里面应该需要传递个什么东西

144
00:04:06,920 --> 00:04:09,660
说他会把实力的compiler给你

145
00:04:09,660 --> 00:04:11,400
然后他就会有一个compiler

146
00:04:11,400 --> 00:04:13,920
这个compiler我们可以这样去认为对吧

147
00:04:13,920 --> 00:04:15,620
他上面有一个属性叫hooks

148
00:04:15,620 --> 00:04:18,800
完了hooks里面就挂着我们那些刚才可以看到的

149
00:04:18,800 --> 00:04:20,820
这些马西道的结构子

150
00:04:20,820 --> 00:04:23,200
我们就看完成应该是当是吧

151
00:04:23,200 --> 00:04:25,700
这个Done我们可以用同步的方式来绑定

152
00:04:25,700 --> 00:04:27,340
比如说Compiler.hooks

153
00:04:27,340 --> 00:04:29,400
同步我们可以使用Type

154
00:04:29,400 --> 00:04:31,100
当然这是Done上的Type

155
00:04:31,100 --> 00:04:32,200
同步的方式

156
00:04:32,200 --> 00:04:33,400
OK

157
00:04:33,400 --> 00:04:35,500
完了这里我们来个Compiler

158
00:04:35,500 --> 00:04:36,700
完了我们绑定完

159
00:04:36,700 --> 00:04:38,900
我说的它的第一个参数永远是无所谓的

160
00:04:38,900 --> 00:04:39,700
放什么都行

161
00:04:39,700 --> 00:04:41,600
一般就是当前插件的名字

162
00:04:41,600 --> 00:04:44,100
完了第二个参数一般都是一个回调

163
00:04:44,100 --> 00:04:45,200
这个回调是什么

164
00:04:45,200 --> 00:04:46,900
我们可以在这里直接表现一下

165
00:04:46,900 --> 00:04:48,600
它里面的参数一般都是

166
00:04:48,600 --> 00:04:49,700
看一下是吧

167
00:04:49,700 --> 00:04:50,500
NodeModule

168
00:04:50,500 --> 00:04:52,500
应该有一个叫Compiler

169
00:04:53,200 --> 00:04:55,540
找一下compiler 哎呦刚才给关掉了

170
00:04:55,540 --> 00:04:56,600
还是webpack里的

171
00:04:56,600 --> 00:04:58,440
webpack里的compiler

172
00:04:58,440 --> 00:05:00,160
网络里面有个lab

173
00:05:00,160 --> 00:05:02,560
对吧有一个compiler在这里

174
00:05:02,560 --> 00:05:04,600
找的时候还挺麻烦的

175
00:05:04,600 --> 00:05:05,700
我找一下

176
00:05:05,700 --> 00:05:08,000
哇这里面是不是就有一个叫dung

177
00:05:08,000 --> 00:05:08,700
你看看啊

178
00:05:08,700 --> 00:05:10,660
他们的所有的参数啊往上找是吧

179
00:05:10,660 --> 00:05:11,260
不是这个

180
00:05:11,260 --> 00:05:14,260
说他都有一个什么什么状态是吧

181
00:05:14,260 --> 00:05:16,360
反正这里面你看参数人家描述很清楚

182
00:05:16,360 --> 00:05:17,100
这个状态呢

183
00:05:17,100 --> 00:05:19,200
其实就包含着什么completion compiler

184
00:05:19,200 --> 00:05:20,500
对吧有这个意思啊

185
00:05:20,500 --> 00:05:21,400
那我在这里面呢

186
00:05:21,400 --> 00:05:22,800
就你看一般情况下都是

187
00:05:22,800 --> 00:05:24,300
要不就是compiler是参数

188
00:05:24,300 --> 00:05:25,740
要不就是complication是参数

189
00:05:25,740 --> 00:05:26,820
所以说我们说了

190
00:05:26,820 --> 00:05:28,260
这两个变量很重要

191
00:05:28,260 --> 00:05:28,640
对吧

192
00:05:28,640 --> 00:05:29,860
好这里面回来

193
00:05:29,860 --> 00:05:31,060
我就在这来个states

194
00:05:31,060 --> 00:05:31,720
反正现在又不到

195
00:05:31,720 --> 00:05:32,460
我就放在这

196
00:05:32,460 --> 00:05:34,560
我在里面我就打印

197
00:05:34,560 --> 00:05:35,800
比如说来一句话

198
00:05:35,800 --> 00:05:36,400
叫down

199
00:05:36,400 --> 00:05:38,380
叫什么编译完成

200
00:05:38,380 --> 00:05:42,020
好了我在这里

201
00:05:42,020 --> 00:05:43,440
把这个插件引到我们这里

202
00:05:43,440 --> 00:05:45,620
第二缸plugin

203
00:05:45,620 --> 00:05:47,020
里面的downplugin

204
00:05:47,020 --> 00:05:49,180
这时候我们就可以用这样一个插件了

205
00:05:49,180 --> 00:05:49,600
它是个类

206
00:05:49,600 --> 00:05:50,440
大戏

207
00:05:50,440 --> 00:05:51,960
这里面用的时候非常简单

208
00:05:51,960 --> 00:05:52,440
就是nute

209
00:05:52,440 --> 00:05:54,340
他就会循环这个插件怎么样

210
00:05:54,340 --> 00:05:55,420
去让他意思执行

211
00:05:55,420 --> 00:05:56,340
那执行的时候

212
00:05:56,340 --> 00:05:57,860
是不是就绑定了这样一世界

213
00:05:57,860 --> 00:05:58,760
那绑定完以后

214
00:05:58,760 --> 00:06:00,260
到时候他到我们发射的时候

215
00:06:00,260 --> 00:06:01,800
怎么样就会去调用

216
00:06:01,800 --> 00:06:03,100
那这时候好了

217
00:06:03,100 --> 00:06:03,860
我们来试试

218
00:06:03,860 --> 00:06:05,040
看看能不能运行的时候

219
00:06:05,040 --> 00:06:06,380
打出来我们这样的效果

220
00:06:06,380 --> 00:06:07,940
NPX Webpack

221
00:06:07,940 --> 00:06:09,740
走你

222
00:06:09,740 --> 00:06:11,520
那这里面他告诉我

223
00:06:11,520 --> 00:06:12,120
DOWN Plugin

224
00:06:12,120 --> 00:06:13,220
他说不是一个购度函数

225
00:06:13,220 --> 00:06:14,740
可能这边没有导出

226
00:06:14,740 --> 00:06:15,940
这边要导出一下

227
00:06:15,940 --> 00:06:17,600
叫module.xpos

228
00:06:17,600 --> 00:06:20,160
xpos等于我们的DOWN Plugin

229
00:06:20,160 --> 00:06:20,900
那好了

230
00:06:20,900 --> 00:06:21,940
我们再来试试是吧

231
00:06:21,940 --> 00:06:23,980
运行是编译完成以后

232
00:06:23,980 --> 00:06:24,860
应该给我来个提示

233
00:06:24,860 --> 00:06:26,420
你看是编译完成就OK了

234
00:06:26,420 --> 00:06:28,220
这就是一个同步的插件

235
00:06:28,220 --> 00:06:30,560
同样我们要写异步的也非常简单

236
00:06:30,560 --> 00:06:31,620
比如说我们再看看

237
00:06:31,620 --> 00:06:33,500
这里面有个叫发射

238
00:06:33,500 --> 00:06:34,180
发射的时候

239
00:06:34,180 --> 00:06:34,960
我们是不是有个叫

240
00:06:34,960 --> 00:06:36,120
async series hook

241
00:06:36,120 --> 00:06:38,400
这就是一个异步串行的构子

242
00:06:38,400 --> 00:06:40,220
它的参数是completion

243
00:06:40,220 --> 00:06:40,820
直接不管

244
00:06:40,820 --> 00:06:42,960
这里我就把它先搞出来

245
00:06:42,960 --> 00:06:44,540
里面我再来个插件

246
00:06:44,540 --> 00:06:46,260
专门写个叫异步的是吧

247
00:06:46,260 --> 00:06:48,580
异步的插件叫async plugin

248
00:06:48,580 --> 00:06:50,740
完了第二件

249
00:06:50,740 --> 00:06:52,860
那这里一样我们写个类

250
00:06:52,860 --> 00:06:54,700
我就叫它async 对吧

251
00:06:54,700 --> 00:06:56,500
plugin 往来是个对象

252
00:06:56,500 --> 00:06:59,160
ok 往来呢里面呢我们就来一个

253
00:06:59,160 --> 00:07:00,520
它需要一个方法叫apply

254
00:07:00,520 --> 00:07:02,440
往来它里面呢有一个参数是什么

255
00:07:02,440 --> 00:07:04,660
人家刚才描述了啊叫complication

256
00:07:04,660 --> 00:07:06,020
是吧这呢complication

257
00:07:06,020 --> 00:07:08,180
就是我们每次编译的这个对象是吧

258
00:07:08,180 --> 00:07:10,480
但这个东西是不这个东西是compiler啊

259
00:07:10,480 --> 00:07:11,160
我没绑时间

260
00:07:11,160 --> 00:07:13,540
那这里面我们是不是可以通过compiler

261
00:07:13,540 --> 00:07:15,120
往来drhooks

262
00:07:15,120 --> 00:07:16,680
往来它里面就会有一个emate

263
00:07:16,680 --> 00:07:19,980
是不是这样一个一个叫什么type async

264
00:07:19,980 --> 00:07:20,920
因为一步的对吧

265
00:07:20,920 --> 00:07:21,960
但是你也可以用type

266
00:07:21,960 --> 00:07:23,100
这里就用type sync

267
00:07:23,100 --> 00:07:24,500
完了里面呢

268
00:07:24,500 --> 00:07:25,620
我就可以放上一个名字

269
00:07:25,620 --> 00:07:26,440
这个名字我说了

270
00:07:26,440 --> 00:07:28,480
一般都和插件的名字一样

271
00:07:28,480 --> 00:07:29,800
后面呢有一个方法

272
00:07:29,800 --> 00:07:30,720
这方法里面呢

273
00:07:30,720 --> 00:07:32,360
就有一个所谓的叫complication

274
00:07:32,360 --> 00:07:34,400
complication

275
00:07:34,400 --> 00:07:35,780
完了我就可以怎么样

276
00:07:35,780 --> 00:07:36,580
在这写一句话

277
00:07:36,580 --> 00:07:38,320
那一步的话除了complication

278
00:07:38,320 --> 00:07:39,740
那后面肯定还有个东西叫cb

279
00:07:39,740 --> 00:07:40,360
还记得吧

280
00:07:40,360 --> 00:07:41,800
这是我们typebook的写法

281
00:07:41,800 --> 00:07:42,840
那同样呢

282
00:07:42,840 --> 00:07:43,900
我们可以在这来个定义器

283
00:07:43,900 --> 00:07:44,160
对吧

284
00:07:44,160 --> 00:07:45,200
比如说我希望啊

285
00:07:45,200 --> 00:07:46,140
哎打包到这的时候

286
00:07:46,140 --> 00:07:47,420
你等一秒再完成

287
00:07:47,420 --> 00:07:48,500
那我可以在这来个什么

288
00:07:48,500 --> 00:07:49,800
叫okcb执行

289
00:07:49,800 --> 00:07:51,780
来句什么叫等一等什么

290
00:07:51,780 --> 00:07:52,520
console log

291
00:07:52,520 --> 00:07:55,120
比如说文件发射对吧

292
00:07:55,120 --> 00:07:57,740
文件发射出来了

293
00:07:57,740 --> 00:07:58,060
对吧

294
00:07:58,060 --> 00:07:58,880
发射出来

295
00:07:58,880 --> 00:07:59,920
等一下

296
00:07:59,920 --> 00:08:01,360
等一下

297
00:08:01,360 --> 00:08:03,080
那好了

298
00:08:03,080 --> 00:08:04,560
这里面我就直接把它放在这

299
00:08:04,560 --> 00:08:05,700
完了我来运行一下

300
00:08:05,700 --> 00:08:06,560
看看是不是这样的效果

301
00:08:06,560 --> 00:08:08,340
就是当我们发射完文件以后怎么样

302
00:08:08,340 --> 00:08:09,160
它会等一秒

303
00:08:09,160 --> 00:08:10,080
看是不是

304
00:08:10,080 --> 00:08:11,820
哦好像没有成功

305
00:08:11,820 --> 00:08:13,500
没有成功是因为这个插件没有用

306
00:08:13,500 --> 00:08:15,800
同样导出module.xpulse

307
00:08:15,800 --> 00:08:17,340
等于async plugin

308
00:08:17,340 --> 00:08:19,020
那在我们这里面呢

309
00:08:19,020 --> 00:08:20,920
也需要把这个sync plugin 怎么样

310
00:08:20,920 --> 00:08:21,660
引进来

311
00:08:21,660 --> 00:08:23,920
引进来不是这个文件在这里

312
00:08:23,920 --> 00:08:26,360
需要在这里叫lite

313
00:08:26,360 --> 00:08:28,780
我们叫它ersync plugin

314
00:08:28,780 --> 00:08:30,580
等于require对吧

315
00:08:30,580 --> 00:08:33,260
这个叫plugin下的ersync plugin

316
00:08:33,260 --> 00:08:34,780
完了同样用法要说了

317
00:08:34,780 --> 00:08:37,520
插件的顺序到没什么太大作用是吧

318
00:08:37,520 --> 00:08:38,920
你写的时候就这样来写就好了

319
00:08:38,920 --> 00:08:39,680
ok了啊

320
00:08:39,680 --> 00:08:41,360
这里面一样我再来运行

321
00:08:41,360 --> 00:08:44,960
当然这里面我们是不是看

322
00:08:44,960 --> 00:08:47,060
是不是好像直接就出来了

323
00:08:47,060 --> 00:08:48,360
你看其实是等了一秒啊

324
00:08:48,360 --> 00:08:49,360
你看等了一秒

325
00:08:49,360 --> 00:08:50,320
完键发射出来

326
00:08:50,320 --> 00:08:51,080
我要再等一下

327
00:08:51,080 --> 00:08:52,160
是不是编译完成了

328
00:08:52,160 --> 00:08:54,200
比如说你要再不相信的话可以怎么办

329
00:08:54,200 --> 00:08:55,760
我可以再在这里面绑个插件

330
00:08:55,760 --> 00:08:58,400
我们说了除了这种type of sync

331
00:08:58,400 --> 00:09:00,480
还可以用我们type promise

332
00:09:00,480 --> 00:09:04,040
是吧 type promise promise

333
00:09:04,040 --> 00:09:06,160
这里面就没有 cb了

334
00:09:06,160 --> 00:09:06,920
它需要干嘛

335
00:09:06,920 --> 00:09:08,440
是不是return new一个什么

336
00:09:08,440 --> 00:09:10,840
return new一个promise

337
00:09:10,840 --> 00:09:11,320
对吧

338
00:09:11,320 --> 00:09:13,120
这里面我们可以直接放

339
00:09:13,120 --> 00:09:13,680
result

340
00:09:13,680 --> 00:09:15,680
完了和reject是吧

341
00:09:15,680 --> 00:09:16,960
result

342
00:09:17,920 --> 00:09:20,020
rezo还有我们的reject

343
00:09:20,020 --> 00:09:22,620
我在这里呢我们可以调用比如说定制器

344
00:09:22,620 --> 00:09:25,620
来个1秒比如说调一下rezo

345
00:09:25,620 --> 00:09:28,320
rezo完了这里呢我就写上一句话

346
00:09:28,320 --> 00:09:31,020
我可以说这个再等1秒是吧

347
00:09:31,020 --> 00:09:32,820
再等1秒console.log

348
00:09:32,820 --> 00:09:34,820
再等1秒

349
00:09:34,820 --> 00:09:38,720
1秒啊这里面你也要注意一下

350
00:09:38,720 --> 00:09:40,820
就是我们这个插件虽然说没顺序

351
00:09:40,820 --> 00:09:43,320
但是如果我们没有把它绑到实践里面

352
00:09:43,320 --> 00:09:44,420
直接在这来写了

353
00:09:44,420 --> 00:09:46,620
那肯定这个智行顺序什么样的是不是

354
00:09:46,720 --> 00:09:49,160
我们循环的时候是不是从上到下执行

355
00:09:49,160 --> 00:09:51,320
那肯定是这里面比我打个console log1

356
00:09:51,320 --> 00:09:52,820
那肯定它会马上执行

357
00:09:52,820 --> 00:09:53,620
console log1

358
00:09:53,620 --> 00:09:55,620
这里我们再来个console log2是吧

359
00:09:55,620 --> 00:09:56,820
看个效果吧

360
00:09:56,820 --> 00:09:57,620
log2

361
00:09:57,620 --> 00:10:00,320
往来里面我再执行一下是吧

362
00:10:00,320 --> 00:10:01,620
因为这代码是同步执行的

363
00:10:01,620 --> 00:10:03,820
是吧肯定上来以后一二先出来是吧

364
00:10:03,820 --> 00:10:06,160
之后发射等一下再过一秒怎么样

365
00:10:06,160 --> 00:10:08,020
OK之后怎么样编译完成

366
00:10:08,020 --> 00:10:11,520
你看是不是我们绑事件的逻辑肯定是根据什么

367
00:10:11,520 --> 00:10:13,120
我坚定的事件来判断的

368
00:10:13,120 --> 00:10:15,120
如果是发射肯定会在什么

369
00:10:15,120 --> 00:10:15,880
党之前

370
00:10:15,880 --> 00:10:17,300
那这样的话我们就知道了

371
00:10:17,300 --> 00:10:18,700
我们编写了两个什么

372
00:10:18,700 --> 00:10:21,260
一步两个一步的方式的插件

373
00:10:21,260 --> 00:10:23,060
还有一个就是同步方式的插件

374
00:10:23,060 --> 00:10:25,880
那之后我们就来写一些有确定功能的插件

