1
00:00:00,000 --> 00:00:03,360
接下来呢我们就来分析一下这个打包后的结果

2
00:00:03,360 --> 00:00:05,220
看看这个打包后出来的结果呀

3
00:00:05,220 --> 00:00:06,380
到底写了什么东西

4
00:00:06,380 --> 00:00:07,820
为什么都出来这么多代码

5
00:00:07,820 --> 00:00:09,860
那这里呢我们把没用东西呢

6
00:00:09,860 --> 00:00:10,820
军情删一删啊

7
00:00:10,820 --> 00:00:11,880
来看看效果

8
00:00:11,880 --> 00:00:12,440
都清空

9
00:00:12,440 --> 00:00:15,080
这里呢我把它稍微呢改造一下

10
00:00:15,080 --> 00:00:16,420
把它都删掉

11
00:00:16,420 --> 00:00:19,180
没用东西我就把它删掉了啊

12
00:00:19,180 --> 00:00:20,780
这样呢大家看起来呢更清晰一些

13
00:00:20,780 --> 00:00:22,460
这也是一样注释呢

14
00:00:22,460 --> 00:00:23,320
我也把它删掉

15
00:00:23,320 --> 00:00:25,440
这里呢我也把它删掉

16
00:00:25,440 --> 00:00:27,460
这里一样

17
00:00:27,460 --> 00:00:29,920
好了

18
00:00:29,920 --> 00:00:31,860
现在我们来看一看

19
00:00:31,860 --> 00:00:34,280
这个代码其实也不是那么复杂

20
00:00:34,280 --> 00:00:36,400
你看我把这些迷宫都用删掉以后

21
00:00:36,400 --> 00:00:37,500
我再来运行一下

22
00:00:37,500 --> 00:00:38,560
看看能不能这场运行

23
00:00:38,560 --> 00:00:39,220
OK

24
00:00:39,220 --> 00:00:41,480
依旧是可以的

25
00:00:41,480 --> 00:00:43,980
这里面它大致的结构是什么样的

26
00:00:43,980 --> 00:00:44,640
比如说这里面

27
00:00:44,640 --> 00:00:46,320
其实我们的默认情况下

28
00:00:46,320 --> 00:00:47,720
它其实是一个匿名函数

29
00:00:47,720 --> 00:00:48,540
自立函数

30
00:00:48,540 --> 00:00:49,980
比如说它默认的会执行

31
00:00:49,980 --> 00:00:51,020
执行的时候

32
00:00:51,020 --> 00:00:52,320
它会传上一个对象

33
00:00:52,320 --> 00:00:53,820
这个对象有几部分

34
00:00:53,820 --> 00:00:55,180
第一部分是我们的key

35
00:00:55,180 --> 00:00:56,860
第二部分是我们的value

36
00:00:56,860 --> 00:00:58,680
你看key指的是什么

37
00:00:58,680 --> 00:01:00,040
其实就是我们当前

38
00:01:00,040 --> 00:01:01,560
模块的对吧

39
00:01:01,560 --> 00:01:02,440
模块的路径

40
00:01:02,440 --> 00:01:04,800
我们说了每个文件都是一个模块

41
00:01:04,800 --> 00:01:07,600
这个value其实就是一个所谓的函数

42
00:01:07,600 --> 00:01:09,620
就是个函数

43
00:01:09,620 --> 00:01:11,400
而且它也是一个执行函数

44
00:01:11,400 --> 00:01:12,000
匿名函数

45
00:01:12,000 --> 00:01:13,800
这里我们是不是key value

46
00:01:13,800 --> 00:01:14,680
key value就有了

47
00:01:14,680 --> 00:01:18,440
之后我们就把对象传给了module里面

48
00:01:18,440 --> 00:01:19,520
module里面说什么了

49
00:01:19,520 --> 00:01:20,880
看看他说这是一个什么

50
00:01:20,880 --> 00:01:22,680
webpack的启动函数

51
00:01:22,680 --> 00:01:24,460
这里人说了限定一个什么

52
00:01:24,460 --> 00:01:26,200
就是限定一个

53
00:01:26,200 --> 00:01:27,640
定一个对吧

54
00:01:27,640 --> 00:01:28,660
写着缓存

55
00:01:28,660 --> 00:01:29,760
缓存

56
00:01:29,760 --> 00:01:31,320
这个缓存主要放什么

57
00:01:31,320 --> 00:01:33,040
就是如果这个模块加载完了

58
00:01:33,040 --> 00:01:35,000
那我不需要再次加载模块

59
00:01:35,000 --> 00:01:35,680
而可以怎么样

60
00:01:35,680 --> 00:01:37,060
直接在缓存中去拿

61
00:01:37,060 --> 00:01:37,860
那这里呢

62
00:01:37,860 --> 00:01:40,000
现在其实我们跟着缓存关系倒不大

63
00:01:40,000 --> 00:01:40,820
我们先不管它

64
00:01:40,820 --> 00:01:42,300
那这里面我们说了

65
00:01:42,300 --> 00:01:43,360
它又干了件什么事呢

66
00:01:43,360 --> 00:01:44,480
叫require function

67
00:01:44,480 --> 00:01:45,820
这里面干什么事呢

68
00:01:45,820 --> 00:01:47,300
就是配置了一下什么

69
00:01:47,300 --> 00:01:49,700
配置了一个

70
00:01:49,700 --> 00:01:51,300
或者是实现了

71
00:01:51,300 --> 00:01:51,620
对吧

72
00:01:51,620 --> 00:01:52,700
实现了一个什么呢

73
00:01:52,700 --> 00:01:54,280
就是我们这样一个require方法

74
00:01:54,280 --> 00:01:56,480
实现了require方法

75
00:01:56,480 --> 00:01:57,360
因为我们都知道

76
00:01:57,360 --> 00:01:58,640
require是不能再乱

77
00:01:58,640 --> 00:01:59,520
其中运行的

78
00:01:59,520 --> 00:02:01,960
这里面它的名字叫WipecRequest

79
00:02:01,960 --> 00:02:02,780
好了

80
00:02:02,780 --> 00:02:04,680
这里面我Modules是个对象

81
00:02:04,680 --> 00:02:05,900
我又写了个方法

82
00:02:05,900 --> 00:02:07,860
往来这里面传了个ModuleID

83
00:02:07,860 --> 00:02:10,500
来看看这方法什么时候被掉的

84
00:02:10,500 --> 00:02:11,180
你往下找

85
00:02:11,180 --> 00:02:13,280
找到了我们这样一个东西

86
00:02:13,280 --> 00:02:13,580
对吧

87
00:02:13,580 --> 00:02:15,360
他默认我们是怎么样

88
00:02:15,360 --> 00:02:16,780
是不是掉这个函数的时候

89
00:02:16,780 --> 00:02:17,760
把对象传进来了

90
00:02:17,760 --> 00:02:18,900
他默认掉这方法

91
00:02:18,900 --> 00:02:19,840
这方法的

92
00:02:19,840 --> 00:02:22,980
他会去找我们SRC下的IntelGS

93
00:02:22,980 --> 00:02:25,720
那就是说他把它传进来的是吧

94
00:02:25,720 --> 00:02:26,920
就这样一个名字传进来了

95
00:02:26,920 --> 00:02:28,680
这个名字传进来以后

96
00:02:28,680 --> 00:02:30,000
是不是就变成到这去了

97
00:02:30,000 --> 00:02:31,680
这个我们一般叫的什么

98
00:02:31,680 --> 00:02:33,040
这是不是我们的入口

99
00:02:33,040 --> 00:02:34,540
叫入口模块

100
00:02:34,540 --> 00:02:36,740
改一下名字

101
00:02:36,740 --> 00:02:37,740
入口模块

102
00:02:37,740 --> 00:02:38,560
好了

103
00:02:38,560 --> 00:02:41,640
这个名字就会默认的去跑到这里来

104
00:02:41,640 --> 00:02:42,180
是吧

105
00:02:42,180 --> 00:02:42,820
跑到这

106
00:02:42,820 --> 00:02:44,280
这里面告诉我什么

107
00:02:44,280 --> 00:02:46,900
说检查一下这个模块是否在缓存中

108
00:02:46,900 --> 00:02:48,080
现在肯定怎么样

109
00:02:48,080 --> 00:02:49,120
它不在缓存中

110
00:02:49,120 --> 00:02:52,520
这里表示不在缓存中

111
00:02:52,520 --> 00:02:53,640
在缓存中

112
00:02:53,640 --> 00:02:54,760
它就直接返回了

113
00:02:54,760 --> 00:02:56,120
不在缓存中

114
00:02:56,120 --> 00:02:57,620
就是说我们先不看这逻辑

115
00:02:57,620 --> 00:02:58,220
折起来

116
00:02:58,220 --> 00:03:00,220
完了它会怎么做

117
00:03:00,220 --> 00:03:01,320
它会去怎么样

118
00:03:01,320 --> 00:03:02,920
叫安装这样一个模块

119
00:03:02,920 --> 00:03:04,820
怎么安装这里面也是一样

120
00:03:04,820 --> 00:03:06,220
install一个module

121
00:03:06,220 --> 00:03:08,120
完了并且把module ID传进去了

122
00:03:08,120 --> 00:03:10,820
又相当于它在对象里面加了一个这样的关系

123
00:03:10,820 --> 00:03:11,220
对吧

124
00:03:11,220 --> 00:03:12,020
就是一个key

125
00:03:12,020 --> 00:03:14,220
完了key是这样一个值

126
00:03:14,220 --> 00:03:16,720
value就是我们这样的一个什么

127
00:03:16,720 --> 00:03:17,920
是不是一个对象

128
00:03:17,920 --> 00:03:19,520
对象上有个属性叫什么

129
00:03:19,520 --> 00:03:20,820
分别有module ID

130
00:03:20,820 --> 00:03:21,220
false

131
00:03:21,220 --> 00:03:21,920
false的表什么

132
00:03:21,920 --> 00:03:23,020
就是是否加载完成

133
00:03:23,020 --> 00:03:24,220
这家属性我现在用不到

134
00:03:24,220 --> 00:03:24,820
可以不看

135
00:03:24,820 --> 00:03:26,020
主要是什么

136
00:03:26,020 --> 00:03:27,180
是这样export对象

137
00:03:27,180 --> 00:03:28,980
我说这里面其实是一个key

138
00:03:28,980 --> 00:03:30,340
后面的对应一个对象

139
00:03:30,340 --> 00:03:32,100
这样的关系人家不管

140
00:03:32,100 --> 00:03:34,340
这里面干什么事呢

141
00:03:34,340 --> 00:03:37,360
他通过modus找到了这样一个id

142
00:03:37,360 --> 00:03:38,920
完了要怎么样去call

143
00:03:38,920 --> 00:03:42,420
那是不是相当于去对象中找到了这样一个名字

144
00:03:42,420 --> 00:03:43,700
那对象中的名字是谁

145
00:03:43,700 --> 00:03:45,100
是不是就是这个名字

146
00:03:45,100 --> 00:03:46,820
完了找到函数干嘛

147
00:03:46,820 --> 00:03:48,980
人家很明确来了一个什么call

148
00:03:48,980 --> 00:03:50,320
call就是执行的意思

149
00:03:50,320 --> 00:03:52,460
执行的时候传进去了什么

150
00:03:52,460 --> 00:03:54,640
是不是当前模块的export

151
00:03:54,640 --> 00:03:55,640
还有什么modus

152
00:03:55,640 --> 00:03:56,760
还有这个module

153
00:03:56,760 --> 00:03:57,760
这是z字指向

154
00:03:57,760 --> 00:03:58,700
这是模块

155
00:03:58,700 --> 00:04:00,820
这是什么模块的空对象

156
00:04:00,820 --> 00:04:02,060
还有我们的require方法

157
00:04:02,060 --> 00:04:04,640
你想把这些东西传给了他

158
00:04:04,640 --> 00:04:05,480
他会干嘛

159
00:04:05,480 --> 00:04:07,740
我们这个里面是不是又干了一件事

160
00:04:07,740 --> 00:04:10,300
是不是又干了一件叫vipac require

161
00:04:10,300 --> 00:04:11,360
require谁呢

162
00:04:11,360 --> 00:04:13,980
这回require的你看人家挺多的把他删掉

163
00:04:13,980 --> 00:04:17,920
其实require的就是src下的a.js

164
00:04:17,920 --> 00:04:20,860
引a.js的时候你再往下找

165
00:04:20,860 --> 00:04:23,260
找完以后是不是就又走到这个方法里

166
00:04:23,260 --> 00:04:24,360
这个方法要怎么样

167
00:04:24,360 --> 00:04:27,700
是不是又去找到了a.js对应的函数

168
00:04:27,700 --> 00:04:28,860
会怎么样

169
00:04:28,860 --> 00:04:32,740
是不是又把这些module还有我们的expose传进来了

170
00:04:32,740 --> 00:04:36,140
这时候我就把空对象变成什么了

171
00:04:36,140 --> 00:04:37,900
是变成了我们这样一个中备训

172
00:04:37,900 --> 00:04:40,740
你想想这个方法的结果

173
00:04:40,740 --> 00:04:42,200
是不是它require的结果

174
00:04:42,200 --> 00:04:44,560
是不是就是当前我们这样moduleexpose的中备训

175
00:04:44,560 --> 00:04:45,540
你看看

176
00:04:45,540 --> 00:04:47,600
require的返回结果

177
00:04:47,600 --> 00:04:48,260
就这个方法

178
00:04:48,260 --> 00:04:48,680
对吧

179
00:04:48,680 --> 00:04:49,200
这个方法

180
00:04:49,200 --> 00:04:50,280
它的返回结果

181
00:04:50,280 --> 00:04:51,600
其实就是一个什么

182
00:04:51,600 --> 00:04:52,640
是不是moduleexpose

183
00:04:52,640 --> 00:04:53,400
也就是说

184
00:04:53,400 --> 00:04:55,600
我们当前电用require方法

185
00:04:55,600 --> 00:04:56,720
加载完以后

186
00:04:56,720 --> 00:04:58,520
它返回的其实就是什么

187
00:04:58,520 --> 00:04:59,400
注重培训

188
00:04:59,400 --> 00:05:00,140
好了

189
00:05:00,140 --> 00:05:01,760
那就把注重培训付给了谁

190
00:05:01,760 --> 00:05:02,980
是不是付给了STR

191
00:05:02,980 --> 00:05:04,620
理论上是不是应该可以怎么样

192
00:05:04,620 --> 00:05:06,260
继续走这样一个结果了

193
00:05:06,260 --> 00:05:08,720
其实这个就是webpack的一个大致流程

194
00:05:08,720 --> 00:05:10,340
它主要的功能就是什么

195
00:05:10,340 --> 00:05:12,720
帮我们把解析的所有的模块

196
00:05:12,720 --> 00:05:13,820
变成一个对象

197
00:05:13,820 --> 00:05:15,840
完了通过这样的一个微入口

198
00:05:15,840 --> 00:05:17,620
去加载我们这样一个东西

199
00:05:17,620 --> 00:05:18,020
完之后

200
00:05:18,020 --> 00:05:19,040
依次实现什么

201
00:05:19,040 --> 00:05:19,460
是不是

202
00:05:19,460 --> 00:05:20,800
必规的依赖关系

203
00:05:20,800 --> 00:05:21,660
完之后

204
00:05:21,660 --> 00:05:23,080
通过入口来运行

205
00:05:23,080 --> 00:05:24,020
运行所有的文件

206
00:05:24,020 --> 00:05:24,940
好了

207
00:05:24,940 --> 00:05:27,520
后面我们会详细再去来看这个例

208
00:05:27,520 --> 00:05:29,600
我们来实现这样一个简易的bypack

209
00:05:29,600 --> 00:05:31,060
这里我们先看到这

210
00:05:31,060 --> 00:05:33,640
我们回到刚才的问题

211
00:05:33,640 --> 00:05:36,520
现在我们这样打包后的文件

212
00:05:36,520 --> 00:05:37,800
其实我们还需要干嘛

213
00:05:37,800 --> 00:05:39,920
是不是希望这个名字可以变

214
00:05:39,920 --> 00:05:42,880
比如说我把这个config改一下名字

215
00:05:42,880 --> 00:05:45,720
比如叫-my.js

216
00:05:45,720 --> 00:05:47,700
我这时候我们在运行

217
00:05:47,700 --> 00:05:48,380
它肯定怎么样

218
00:05:48,380 --> 00:05:51,280
这里面我最好把这个文件备份一份

219
00:05:51,280 --> 00:05:52,580
以后万一用到

220
00:05:52,580 --> 00:05:53,640
你看它还方便

221
00:05:53,640 --> 00:05:54,720
我来个 history

222
00:05:54,720 --> 00:05:56,640
我把这个放进来

223
00:05:56,640 --> 00:05:59,920
往这里我再来打包是吧

224
00:05:59,920 --> 00:06:00,680
npx

225
00:06:00,680 --> 00:06:07,080
这时候他就理论上怎么样

226
00:06:07,080 --> 00:06:08,620
是不是就找不到配置文件了

227
00:06:08,620 --> 00:06:09,940
你看他已经包成什么

228
00:06:09,940 --> 00:06:10,680
异常了

229
00:06:10,680 --> 00:06:12,420
这时候我希望怎么样

230
00:06:12,420 --> 00:06:14,620
是他依然能找到我们这样的配置文件

231
00:06:14,620 --> 00:06:18,440
我们可以这样来选择通过我们的npx

232
00:06:18,440 --> 00:06:19,380
webpack

233
00:06:19,380 --> 00:06:21,540
我后面可以加个参数叫什么叫

234
00:06:21,540 --> 00:06:22,340
-config

235
00:06:22,340 --> 00:06:25,600
就是说我们可以手动的去指定配置文件

236
00:06:25,600 --> 00:06:27,200
这个配置文件的名字

237
00:06:27,200 --> 00:06:29,620
我可以给它写上叫webpack.config

238
00:06:29,620 --> 00:06:30,040
对吧

239
00:06:30,040 --> 00:06:31,400
config.js

240
00:06:31,400 --> 00:06:32,740
当然是mine.js

241
00:06:32,740 --> 00:06:33,360
OK

242
00:06:33,360 --> 00:06:35,320
这也是可以找到的

243
00:06:35,320 --> 00:06:35,960
非常方便

244
00:06:35,960 --> 00:06:38,240
你看是不是也可以找到我们的这样一个bundle

245
00:06:38,240 --> 00:06:39,260
还是刚才那个文件

246
00:06:39,260 --> 00:06:42,600
同样如果我说了这个名字太长了

247
00:06:42,600 --> 00:06:42,760
对吧

248
00:06:42,760 --> 00:06:43,480
根本就记不住

249
00:06:43,480 --> 00:06:45,560
这时候一般情况下

250
00:06:45,560 --> 00:06:47,280
我们会在配点json

251
00:06:47,280 --> 00:06:48,720
是配一些脚本

252
00:06:48,720 --> 00:06:51,120
通过这些脚本来执行webpack

253
00:06:51,120 --> 00:06:52,720
还有我们的Webpack CLI

254
00:06:52,720 --> 00:06:54,420
或者其他的命令是吧

255
00:06:54,420 --> 00:06:55,920
这里我们就写上了

256
00:06:55,920 --> 00:06:56,620
比如Webpack

257
00:06:56,620 --> 00:06:58,920
当然脚本的应该叫什么叫Scripts

258
00:06:58,920 --> 00:07:01,320
这是一个固定的写法

259
00:07:01,320 --> 00:07:02,420
Scripts

260
00:07:02,420 --> 00:07:03,620
OK对象

261
00:07:03,620 --> 00:07:06,420
完了里面我们就可以来进行配置了

262
00:07:06,420 --> 00:07:07,220
怎么配呢

263
00:07:07,220 --> 00:07:10,420
这里面我们希望现在肯定是要打包

264
00:07:10,420 --> 00:07:13,320
打包的话我们给它个关键字叫build

265
00:07:13,320 --> 00:07:14,920
当然这名字你随便起

266
00:07:14,920 --> 00:07:16,320
你想怎么写怎么写

267
00:07:16,320 --> 00:07:18,120
你不叫build叫b也行是吧

268
00:07:18,120 --> 00:07:20,920
这里面我们需要它运行哪个文件

269
00:07:20,920 --> 00:07:23,920
好 是不是运行当前nodemodule下的wipac

270
00:07:23,920 --> 00:07:26,820
好 以前是不是要在这是不是写什么npx

271
00:07:26,820 --> 00:07:27,620
现在不用了

272
00:07:27,620 --> 00:07:29,020
你就直接写上wipac

273
00:07:29,020 --> 00:07:33,820
它会自动的去当前这样一个nodemodule下去找wipac命令

274
00:07:33,820 --> 00:07:37,720
找完以后同样你后面可以给上这样一个config

275
00:07:37,720 --> 00:07:40,020
就说我要添加一个配置文件

276
00:07:40,020 --> 00:07:42,720
配置文件的名字是wipac.config

277
00:07:42,720 --> 00:07:43,220
对吧

278
00:07:43,220 --> 00:07:46,120
config.js当然是my.js

279
00:07:46,120 --> 00:07:48,520
完了你再来看看是吧

280
00:07:48,520 --> 00:07:50,220
这时候依旧其实

281
00:07:50,220 --> 00:07:51,220
当然这时候运行的时候

282
00:07:51,220 --> 00:07:53,320
我们肯定希望运行这个命令

283
00:07:53,320 --> 00:07:54,780
怎么来运行它

284
00:07:54,780 --> 00:07:55,760
非常方便

285
00:07:55,760 --> 00:07:57,080
我们可以通过NPM

286
00:07:57,080 --> 00:07:58,440
你想怎么样

287
00:07:58,440 --> 00:07:58,920
Run

288
00:07:58,920 --> 00:07:59,560
Run谁

289
00:07:59,560 --> 00:08:01,340
你是不是想Run build

290
00:08:01,340 --> 00:08:02,520
就执行这句命令

291
00:08:02,520 --> 00:08:03,220
好

292
00:08:03,220 --> 00:08:03,960
你就在这里

293
00:08:03,960 --> 00:08:05,960
直接写一个Run build

294
00:08:05,960 --> 00:08:06,280
好了

295
00:08:06,280 --> 00:08:07,900
这时候你看

296
00:08:07,900 --> 00:08:08,760
它执行的命令

297
00:08:08,760 --> 00:08:10,760
就是Wipec-config

298
00:08:10,760 --> 00:08:12,000
是不是就是那句命令

299
00:08:12,000 --> 00:08:12,680
好了

300
00:08:12,680 --> 00:08:13,560
执行命令

301
00:08:13,560 --> 00:08:14,760
是不是又打爆成功了

302
00:08:14,760 --> 00:08:15,640
同样

303
00:08:15,640 --> 00:08:16,520
有人说了

304
00:08:16,520 --> 00:08:17,300
我写的时候

305
00:08:17,300 --> 00:08:18,160
我就知道

306
00:08:18,160 --> 00:08:19,400
这东西不一定叫啥名字

307
00:08:19,400 --> 00:08:20,880
我想在这儿配个快捷键

308
00:08:20,880 --> 00:08:22,240
这面我干嘛

309
00:08:22,240 --> 00:08:23,420
我能不能这样写

310
00:08:23,420 --> 00:08:24,420
run build

311
00:08:24,420 --> 00:08:25,240
完了在这里

312
00:08:25,240 --> 00:08:27,100
我再把这个东西粘过来

313
00:08:27,100 --> 00:08:28,280
这样写是不行的

314
00:08:28,280 --> 00:08:29,580
因为他会认为这东西

315
00:08:29,580 --> 00:08:30,780
它并不是一个参数

316
00:08:30,780 --> 00:08:32,560
你看我运行一下就动了

317
00:08:32,560 --> 00:08:34,440
你看执行的时候

318
00:08:34,440 --> 00:08:34,980
我揭个图

319
00:08:34,980 --> 00:08:37,300
这里面出来的结果就是什么

320
00:08:37,300 --> 00:08:38,620
是不是就是webpack

321
00:08:38,620 --> 00:08:39,800
你发现了吗

322
00:08:39,800 --> 00:08:41,580
是不是少了杠杠config

323
00:08:41,580 --> 00:08:43,860
如果你偏要这样写的话

324
00:08:43,860 --> 00:08:44,560
你需要怎么写

325
00:08:44,560 --> 00:08:45,880
人家官方也说了

326
00:08:45,880 --> 00:08:46,940
你要传餐的话

327
00:08:46,940 --> 00:08:48,080
需要再加上两个杠

328
00:08:48,080 --> 00:08:50,620
后面他就认为这是一个字不串了

329
00:08:50,620 --> 00:08:51,380
是个参数了

330
00:08:51,380 --> 00:08:53,540
这样就可以进行传参了

331
00:08:53,540 --> 00:08:55,060
你看看事实是吧

332
00:08:55,060 --> 00:08:55,340
OK

333
00:08:55,340 --> 00:08:58,160
看看是不是依旧

334
00:08:58,160 --> 00:09:00,140
所以说这里面我们就知道了

335
00:09:00,140 --> 00:09:02,440
怎么去更改我们的配置文件的名字

336
00:09:02,440 --> 00:09:05,140
当然通过这样一个杠杠config配置

337
00:09:05,140 --> 00:09:06,420
这里我们稍微改一下

338
00:09:06,420 --> 00:09:08,240
还是改回我们的config.js

339
00:09:08,240 --> 00:09:10,100
这里一样我们也改回来

340
00:09:10,100 --> 00:09:13,000
接下来我们就来讲一下

341
00:09:13,000 --> 00:09:14,280
其他的一些配置

342
00:09:14,280 --> 00:09:15,560
现在这个配置很弱

343
00:09:15,560 --> 00:09:17,980
只能去打包我们所谓的js文件

344
00:09:17,980 --> 00:09:19,300
我们接着往下看

