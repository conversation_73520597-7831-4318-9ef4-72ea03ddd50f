1
00:00:00,000 --> 00:00:02,540
这节呢我们就来实现一下

2
00:00:02,540 --> 00:00:04,200
webpack比较常见的一个功能

3
00:00:04,200 --> 00:00:05,520
叫抽取公共代码

4
00:00:05,520 --> 00:00:07,280
那这抽取公共代码呀

5
00:00:07,280 --> 00:00:08,900
肯定是在多个页面中

6
00:00:08,900 --> 00:00:09,800
完了需要有怎么样

7
00:00:09,800 --> 00:00:10,460
公用的部分

8
00:00:10,460 --> 00:00:11,780
我才需要抽取出来吧

9
00:00:11,780 --> 00:00:12,660
那这时候呢

10
00:00:12,660 --> 00:00:14,580
肯定我们又需要配置一个所谓的多入口

11
00:00:14,580 --> 00:00:15,900
就比如两个入口

12
00:00:15,900 --> 00:00:17,000
这两个入口呢

13
00:00:17,000 --> 00:00:18,220
里面公用了某个文件

14
00:00:18,220 --> 00:00:19,080
那么这样的话

15
00:00:19,080 --> 00:00:20,980
我需要把两个文件怎么样抽取出来

16
00:00:20,980 --> 00:00:21,900
那这时候呢

17
00:00:21,900 --> 00:00:22,380
我同样

18
00:00:22,380 --> 00:00:25,760
先把我们这样一个index拷贝一份吧

19
00:00:25,760 --> 00:00:26,700
保存一份啊

20
00:00:26,700 --> 00:00:27,060
在这里

21
00:00:27,060 --> 00:00:27,880
那里面呢

22
00:00:27,880 --> 00:00:28,780
我就把这个东西清空

23
00:00:28,780 --> 00:00:31,220
那为了方便我把这test呢

24
00:00:31,220 --> 00:00:33,200
也就先都保存到里面去吧

25
00:00:33,200 --> 00:00:35,220
这个test应该就是这个test

26
00:00:35,220 --> 00:00:36,120
然后改过去

27
00:00:36,120 --> 00:00:37,360
那又放个这里名字了

28
00:00:37,360 --> 00:00:38,340
来test1

29
00:00:38,340 --> 00:00:39,200
这里呢

30
00:00:39,200 --> 00:00:40,540
我把这个test就删掉了

31
00:00:40,540 --> 00:00:41,200
不要了

32
00:00:41,200 --> 00:00:42,840
这里呢

33
00:00:42,840 --> 00:00:44,800
我来个文件叫a.js

34
00:00:44,800 --> 00:00:46,740
a.js

35
00:00:46,740 --> 00:00:48,980
同样呢

36
00:00:48,980 --> 00:00:49,480
我在这里呢

37
00:00:49,480 --> 00:00:50,600
我再来个other.js

38
00:00:50,600 --> 00:00:54,740
那你看

39
00:00:54,740 --> 00:00:55,620
如果呀

40
00:00:55,620 --> 00:00:56,760
现在我们有这样一个需求

41
00:00:56,760 --> 00:00:58,160
比如说index里面需要的a

42
00:00:58,160 --> 00:00:59,520
可能还需要用到什么

43
00:00:59,520 --> 00:01:00,060
用到B

44
00:01:00,060 --> 00:01:01,200
两个文件

45
00:01:01,200 --> 00:01:03,480
那AZER可能也需要用到A和B

46
00:01:03,480 --> 00:01:05,600
那这时候我是不是可以把A和B怎么样

47
00:01:05,600 --> 00:01:06,480
是不是抽离出去

48
00:01:06,480 --> 00:01:07,560
完单独弄一个包

49
00:01:07,560 --> 00:01:08,560
完这样的话

50
00:01:08,560 --> 00:01:09,760
我们index引AB

51
00:01:09,760 --> 00:01:10,680
AZER引B

52
00:01:10,680 --> 00:01:12,160
是不是这样的话就可以缓存一下

53
00:01:12,160 --> 00:01:14,360
比如说我刚开始加载了首页

54
00:01:14,360 --> 00:01:15,920
那首页里面是不是就把AB

55
00:01:15,920 --> 00:01:16,880
这两个文件加载出来

56
00:01:16,880 --> 00:01:18,600
那同样我们再访问AZER

57
00:01:18,600 --> 00:01:19,800
因为AZER引的也是AB

58
00:01:19,800 --> 00:01:20,620
那这样的话

59
00:01:20,620 --> 00:01:22,120
是不是AB已经被缓存后了

60
00:01:22,120 --> 00:01:22,680
那这样的话

61
00:01:22,680 --> 00:01:24,060
是不是就不需要再重新下载了

62
00:01:24,060 --> 00:01:26,200
这就是提取公共代码的好处

63
00:01:26,200 --> 00:01:28,000
这里呢就来一句

64
00:01:28,000 --> 00:01:29,560
比如说为了能看出来效果

65
00:01:29,560 --> 00:01:30,360
console log

66
00:01:30,360 --> 00:01:31,500
比如说我来个a

67
00:01:31,500 --> 00:01:32,400
波浪线

68
00:01:32,400 --> 00:01:34,520
同样这是a

69
00:01:34,520 --> 00:01:35,540
这个是b

70
00:01:35,540 --> 00:01:37,260
我在这里面

71
00:01:37,260 --> 00:01:39,160
我在index里面加上一句话

72
00:01:39,160 --> 00:01:39,900
比如说import

73
00:01:39,900 --> 00:01:41,420
我需要导入a

74
00:01:41,420 --> 00:01:44,360
同样我还需要导入这样一个b

75
00:01:44,360 --> 00:01:47,680
完之后我们还需要console log

76
00:01:47,680 --> 00:01:50,300
打印一下index.js

77
00:01:50,300 --> 00:01:53,140
同样我把代码粘过来

78
00:01:53,140 --> 00:01:54,100
放到b这一份

79
00:01:54,100 --> 00:01:56,380
b里面不是放到b

80
00:01:56,380 --> 00:01:57,240
是放到other

81
00:01:57,240 --> 00:01:57,740
是吧

82
00:01:57,740 --> 00:01:59,040
那这里面打出来的就是阿子

83
00:01:59,040 --> 00:02:02,640
那现在我们就开始来写这个逻辑

84
00:02:02,640 --> 00:02:03,240
那现在呢

85
00:02:03,240 --> 00:02:04,840
我们是不是里面就要稍微改一下

86
00:02:04,840 --> 00:02:05,440
那入口呢

87
00:02:05,440 --> 00:02:06,440
就不再是一个了

88
00:02:06,440 --> 00:02:07,640
而是应该是个对象

89
00:02:07,640 --> 00:02:10,240
那对象里面是不是应该有一个安锤

90
00:02:10,240 --> 00:02:11,140
比如这个安锤呢

91
00:02:11,140 --> 00:02:12,540
我就叫他不少安锤

92
00:02:12,540 --> 00:02:13,640
叫这个应代斯吧

93
00:02:13,640 --> 00:02:14,240
还是一样啊

94
00:02:14,240 --> 00:02:15,140
in.js

95
00:02:15,140 --> 00:02:16,040
index

96
00:02:16,040 --> 00:02:18,040
里面呢

97
00:02:18,040 --> 00:02:20,440
他就是src下的index.js

98
00:02:20,440 --> 00:02:22,240
然后同样的还有个阿子

99
00:02:22,240 --> 00:02:23,040
阿子里面呢

100
00:02:23,040 --> 00:02:23,640
我也一样

101
00:02:23,640 --> 00:02:26,640
因这个src下的我们的阿子.js

102
00:02:27,240 --> 00:02:30,200
同样的我们最后打包出来的结果为了方便

103
00:02:30,200 --> 00:02:32,560
我就把这个模块整个都干掉

104
00:02:32,560 --> 00:02:34,120
看起来太多了

105
00:02:34,120 --> 00:02:35,960
这样的话我打包完以后

106
00:02:35,960 --> 00:02:37,480
是不是应该就会产生两个文件

107
00:02:37,480 --> 00:02:38,320
一个叫index

108
00:02:38,320 --> 00:02:39,040
一个叫other

109
00:02:39,040 --> 00:02:41,600
这里面我就不去考虑协天妙的问题了

110
00:02:41,600 --> 00:02:42,960
这里面直接运行

111
00:02:42,960 --> 00:02:45,120
npm run build

112
00:02:45,120 --> 00:02:46,440
ok

113
00:02:46,440 --> 00:02:48,480
看看效果

114
00:02:48,480 --> 00:02:51,160
他告诉我这报了个小错

115
00:02:51,160 --> 00:02:52,720
说没有这样一个文件

116
00:02:52,720 --> 00:02:53,960
minifest Jason

117
00:02:53,960 --> 00:02:56,520
是因为我动态连接库已用的问题

118
00:02:56,520 --> 00:02:57,820
我把这个东西先住掉

119
00:02:57,820 --> 00:02:59,420
完了我再来运行

120
00:02:59,420 --> 00:03:01,880
这样的话

121
00:03:01,880 --> 00:03:03,400
应该就打包出来两个文件

122
00:03:03,400 --> 00:03:04,920
现在相当于是不是

123
00:03:04,920 --> 00:03:06,120
这里面告诉我了

124
00:03:06,120 --> 00:03:08,200
说多个模块有同一个GS文件出口

125
00:03:08,200 --> 00:03:09,940
这里面我也没有改

126
00:03:09,940 --> 00:03:11,300
我把出口也改一下

127
00:03:11,300 --> 00:03:13,660
出口应该变成一个output

128
00:03:13,660 --> 00:03:15,100
这应该放一个放过号

129
00:03:15,100 --> 00:03:17,780
这里面需要改动也挺多的

130
00:03:17,780 --> 00:03:18,440
再来

131
00:03:18,440 --> 00:03:22,220
现在我们运行一下看看结果

132
00:03:22,220 --> 00:03:24,140
是不是出来的是一个index

133
00:03:24,140 --> 00:03:24,660
一个other

134
00:03:24,660 --> 00:03:25,640
你看看

135
00:03:25,640 --> 00:03:27,020
我们可以看到效果

136
00:03:27,020 --> 00:03:28,340
比如说看一下index

137
00:03:28,340 --> 00:03:31,180
index里面是不是他就会以a和b

138
00:03:31,180 --> 00:03:31,540
对吧

139
00:03:31,540 --> 00:03:32,580
打印出来结果

140
00:03:32,580 --> 00:03:34,520
other里面是不是也有了什么

141
00:03:34,520 --> 00:03:36,440
是不是a和b打印出来other

142
00:03:36,440 --> 00:03:38,400
现在我们是不是可以做一件事

143
00:03:38,400 --> 00:03:39,420
把ab怎么样

144
00:03:39,420 --> 00:03:40,460
单独抽离出来

145
00:03:40,460 --> 00:03:41,760
好

146
00:03:41,760 --> 00:03:43,680
这里面我们也用过这样一个优化项

147
00:03:43,680 --> 00:03:45,340
配置

148
00:03:45,340 --> 00:03:46,380
配置叫什么

149
00:03:46,380 --> 00:03:48,260
叫优化

150
00:03:48,260 --> 00:03:48,700
对吧

151
00:03:48,700 --> 00:03:50,060
optimization

152
00:03:50,060 --> 00:03:51,700
里面还记得

153
00:03:51,700 --> 00:03:53,240
当时咱们写过了一个叫mini

154
00:03:53,240 --> 00:03:55,180
现在咱不考虑它

155
00:03:55,180 --> 00:03:56,300
那是压缩的是吧

156
00:03:56,300 --> 00:03:57,020
那这回呢

157
00:03:57,020 --> 00:03:57,820
我们要干嘛呢

158
00:03:57,820 --> 00:03:58,940
是不是要抽离公共样式

159
00:03:58,940 --> 00:04:00,260
就要分割我们的代码块

160
00:04:00,260 --> 00:04:01,360
所以这里面呢

161
00:04:01,360 --> 00:04:02,420
我们需要加一个属性

162
00:04:02,420 --> 00:04:03,860
叫SPEED TRUNKS

163
00:04:03,860 --> 00:04:05,280
这条固定写法

164
00:04:05,280 --> 00:04:06,440
叫分割代码块

165
00:04:06,440 --> 00:04:09,180
当然了

166
00:04:09,180 --> 00:04:10,760
如果要是就一个入口的话

167
00:04:10,760 --> 00:04:12,260
那你肯定就不需要怎么样

168
00:04:12,260 --> 00:04:13,080
来抽离了

169
00:04:13,080 --> 00:04:14,240
因为就是单页应用吧

170
00:04:14,240 --> 00:04:14,960
这种多页的话

171
00:04:14,960 --> 00:04:16,880
才需要把公共的部分抽离出来

172
00:04:16,880 --> 00:04:18,820
那这里面我需要放什么呢

173
00:04:18,820 --> 00:04:19,360
跟他说

174
00:04:19,360 --> 00:04:20,420
我是不是要缓存

175
00:04:20,420 --> 00:04:21,760
我们这样一些某些代码呀

176
00:04:21,760 --> 00:04:22,700
所以这里面呢

177
00:04:22,700 --> 00:04:24,380
我要加上一个属性叫Catch

178
00:04:24,380 --> 00:04:28,360
就是缓存组对吧

179
00:04:28,360 --> 00:04:30,080
缓存组

180
00:04:30,080 --> 00:04:32,560
那怎么缓存呢

181
00:04:32,560 --> 00:04:34,600
我们希望是不是单独抽一个文件

182
00:04:34,600 --> 00:04:35,400
它叫commen

183
00:04:35,400 --> 00:04:36,560
那commen里面呢

184
00:04:36,560 --> 00:04:37,860
我可能需要告诉人家对吧

185
00:04:37,860 --> 00:04:39,660
那commen就是一个公共模块

186
00:04:39,660 --> 00:04:42,560
公共的模块

187
00:04:42,560 --> 00:04:44,080
那公共模块里面啊

188
00:04:44,080 --> 00:04:45,380
就需要放上一系列的配置

189
00:04:45,380 --> 00:04:47,320
比如说什么样的代码我抽离

190
00:04:47,320 --> 00:04:48,540
比如说这个代码啊

191
00:04:48,540 --> 00:04:48,680
哎

192
00:04:48,680 --> 00:04:50,460
他的大小的min size

193
00:04:50,460 --> 00:04:52,320
比如说他只要大于零个字节

194
00:04:52,320 --> 00:04:53,020
有共用的

195
00:04:53,020 --> 00:04:53,720
我就抽离出来

196
00:04:53,720 --> 00:04:54,700
那同样呢

197
00:04:54,700 --> 00:04:56,320
可能还有这个叫meantrunks

198
00:04:56,320 --> 00:04:57,160
就说

199
00:04:57,160 --> 00:04:58,600
当前这个东西怎么样

200
00:04:58,600 --> 00:04:59,580
引用多少次

201
00:04:59,580 --> 00:05:00,740
完了我才需要抽离

202
00:05:00,740 --> 00:05:02,300
比如说这也是写个零吧

203
00:05:02,300 --> 00:05:02,820
无所谓

204
00:05:02,820 --> 00:05:04,020
加不加都行啊

205
00:05:04,020 --> 00:05:04,660
那同样

206
00:05:04,660 --> 00:05:05,980
我们最后要跟人家说

207
00:05:05,980 --> 00:05:07,140
那应该从哪开始

208
00:05:07,140 --> 00:05:08,420
是不是从入口处开始

209
00:05:08,420 --> 00:05:09,600
就要提取代码了

210
00:05:09,600 --> 00:05:10,420
那入口的话呢

211
00:05:10,420 --> 00:05:11,340
可以在这来个trunks

212
00:05:11,340 --> 00:05:12,960
怎么找呢

213
00:05:12,960 --> 00:05:13,760
有个叫initial

214
00:05:13,760 --> 00:05:15,520
当然了后面可能会说到

215
00:05:15,520 --> 00:05:17,840
initial是吧

216
00:05:17,840 --> 00:05:18,680
initial

217
00:05:18,680 --> 00:05:20,880
当然了后面可能还有一部模块

218
00:05:20,880 --> 00:05:21,860
咱们就不考虑了

219
00:05:21,860 --> 00:05:22,840
咱们就考虑什么呢

220
00:05:22,840 --> 00:05:23,400
就是刚开始

221
00:05:23,400 --> 00:05:24,380
我就需要怎么样

222
00:05:24,380 --> 00:05:25,500
进行抽离common

223
00:05:25,500 --> 00:05:27,300
并且如果它的大小

224
00:05:27,300 --> 00:05:28,760
只要有零个字节被共用

225
00:05:28,760 --> 00:05:29,660
超过零个字节

226
00:05:29,660 --> 00:05:31,720
并且只要用了两次以上

227
00:05:31,720 --> 00:05:32,160
对吧

228
00:05:32,160 --> 00:05:33,780
这里面可以来个一

229
00:05:33,780 --> 00:05:35,120
只要用过一次以上

230
00:05:35,120 --> 00:05:35,500
好

231
00:05:35,500 --> 00:05:36,200
我就需要怎么样

232
00:05:36,200 --> 00:05:37,340
把它抽离出来

233
00:05:37,340 --> 00:05:38,060
好

234
00:05:38,060 --> 00:05:38,700
来看看效果

235
00:05:38,700 --> 00:05:40,400
这里我再执行一下

236
00:05:40,400 --> 00:05:42,140
乱build

237
00:05:42,140 --> 00:05:44,220
现在他就会帮我们去抽离

238
00:05:44,220 --> 00:05:45,600
index和other中等

239
00:05:45,600 --> 00:05:46,580
公众的部分

240
00:05:46,580 --> 00:05:46,860
你看

241
00:05:46,860 --> 00:05:48,360
这时候抽离两个

242
00:05:48,360 --> 00:05:50,180
一个叫common下的index

243
00:05:50,180 --> 00:05:52,240
还有个叫commonindex other

244
00:05:52,240 --> 00:05:54,340
这个好像有一点点问题

245
00:05:54,340 --> 00:05:56,400
现在抽出了三个文件

246
00:05:56,400 --> 00:05:57,620
这里面应该怎么样

247
00:05:57,620 --> 00:05:58,340
是一次不行

248
00:05:58,340 --> 00:05:58,960
得用两次

249
00:05:58,960 --> 00:06:00,800
要太多了

250
00:06:00,800 --> 00:06:02,040
这样的话

251
00:06:02,040 --> 00:06:03,280
我把它就都住掉

252
00:06:03,280 --> 00:06:05,160
至少引过两次以上

253
00:06:05,160 --> 00:06:06,140
我才应该怎么样

254
00:06:06,140 --> 00:06:07,080
把它倒包出来

255
00:06:07,080 --> 00:06:08,720
这里面也不能给太小

256
00:06:08,720 --> 00:06:10,760
否则引用一次就抽离出来了

257
00:06:10,760 --> 00:06:11,960
看这里面很多

258
00:06:11,960 --> 00:06:12,940
再来一次

259
00:06:12,940 --> 00:06:14,820
这样的话

260
00:06:14,820 --> 00:06:15,440
它就会怎么样

261
00:06:15,440 --> 00:06:16,280
是不是再去抽离

262
00:06:16,280 --> 00:06:17,040
抽离的话

263
00:06:17,040 --> 00:06:17,820
是不是一个comment

264
00:06:17,820 --> 00:06:19,620
comment里面放着indice other

265
00:06:19,620 --> 00:06:20,880
再来看一看

266
00:06:20,880 --> 00:06:22,460
这里面是不是就两个

267
00:06:22,460 --> 00:06:23,440
一个是A

268
00:06:23,440 --> 00:06:24,120
一个是B

269
00:06:24,120 --> 00:06:25,020
很方便

270
00:06:25,020 --> 00:06:27,180
这里面是不是index里面放的

271
00:06:27,180 --> 00:06:29,100
是不是会引文件

272
00:06:29,100 --> 00:06:30,300
里面中文放的是index

273
00:06:30,300 --> 00:06:31,760
这里可能放的就是什么

274
00:06:31,760 --> 00:06:32,520
就是other

275
00:06:32,520 --> 00:06:33,580
可能效果

276
00:06:33,580 --> 00:06:35,160
这样的话

277
00:06:35,160 --> 00:06:37,200
我们就实现了代码的抽离

278
00:06:37,200 --> 00:06:38,960
我们还要干什么事

279
00:06:38,960 --> 00:06:39,920
其实还要做

280
00:06:39,920 --> 00:06:41,320
比如说我这里面

281
00:06:41,320 --> 00:06:43,480
在文件里面

282
00:06:43,480 --> 00:06:44,740
比如再来一个

283
00:06:44,740 --> 00:06:45,780
叫c.js

284
00:06:45,780 --> 00:06:47,500
来个c.js

285
00:06:47,500 --> 00:06:48,800
c.js

286
00:06:48,800 --> 00:06:50,480
可能它里面用到了一个东西

287
00:06:50,480 --> 00:06:53,360
以前可能我记得好像安没安过

288
00:06:53,360 --> 00:06:53,740
我忘了

289
00:06:53,740 --> 00:06:54,580
我再安一次是吧

290
00:06:54,580 --> 00:06:56,540
那我是不是会这样做

291
00:06:56,540 --> 00:06:58,100
a import比如说到了服务

292
00:06:58,100 --> 00:06:59,640
完了服务我们的什么

293
00:06:59,640 --> 00:07:00,100
jquery

294
00:07:00,100 --> 00:07:03,160
完了

295
00:07:03,160 --> 00:07:05,500
在这里呢

296
00:07:05,500 --> 00:07:07,160
我们就打印一下这个道乐

297
00:07:07,160 --> 00:07:08,880
随便来感受一下

298
00:07:08,880 --> 00:07:10,480
那这里面我来个这代码

299
00:07:10,480 --> 00:07:11,220
那同样呢

300
00:07:11,220 --> 00:07:12,760
我在这个应该怎么也来了一句

301
00:07:12,760 --> 00:07:15,160
那现在的话应该怎么办

302
00:07:15,160 --> 00:07:17,040
是不是应该把这块怎么样

303
00:07:17,040 --> 00:07:18,260
可能他这里面还有

304
00:07:18,260 --> 00:07:20,420
那现在啊

305
00:07:20,420 --> 00:07:21,480
除了AB能抽离

306
00:07:21,480 --> 00:07:23,680
那我们是不是还需要把这纸块也怎么样

307
00:07:23,680 --> 00:07:25,260
也单独抽离出去吧

308
00:07:25,260 --> 00:07:27,240
比如说在需要的模块也怎么样去引用

309
00:07:27,240 --> 00:07:28,260
那这样的话

310
00:07:28,260 --> 00:07:31,580
我是不是应该把这个第三方模块也抽离成一个文件

311
00:07:31,580 --> 00:07:33,440
那这时候我要怎么做呢

312
00:07:33,440 --> 00:07:33,640
哎

313
00:07:33,640 --> 00:07:34,580
其实也可以啊

314
00:07:34,580 --> 00:07:35,740
就是我们需要找到

315
00:07:35,740 --> 00:07:35,900
哎

316
00:07:35,900 --> 00:07:37,740
所有这个我们的第三方模块

317
00:07:37,740 --> 00:07:38,640
我们把它怎么样

318
00:07:38,640 --> 00:07:39,420
进行单独打包

319
00:07:39,420 --> 00:07:40,480
在这里啊

320
00:07:40,480 --> 00:07:41,580
一般我们会这样写

321
00:07:41,580 --> 00:07:43,780
这里面再加一个什么东西呢

322
00:07:43,780 --> 00:07:44,200
叫wonder

323
00:07:44,200 --> 00:07:45,620
wonder孩子就是第三方

324
00:07:45,620 --> 00:07:47,260
第三方里面呢

325
00:07:47,260 --> 00:07:49,600
我可以配上一些所谓的叫什么叫正责

326
00:07:49,600 --> 00:07:50,860
哎我只找什么呢

327
00:07:50,860 --> 00:07:54,280
如果这里面你引了这个node的modus文件的话

328
00:07:54,280 --> 00:07:55,060
我就怎么样

329
00:07:55,060 --> 00:07:56,260
把你抽离出来对吧

330
00:07:56,260 --> 00:07:57,300
把你抽离出来

331
00:07:57,300 --> 00:08:00,700
那好上面东西我不用变

332
00:08:00,700 --> 00:08:01,540
依然放在这

333
00:08:01,540 --> 00:08:03,020
只要你引你多次那好

334
00:08:03,020 --> 00:08:04,840
我就把这文件怎么样给你抽离出来

335
00:08:04,840 --> 00:08:06,760
但这里面有个问题啊

336
00:08:06,760 --> 00:08:08,860
代码执行啊还是从上到下

337
00:08:08,860 --> 00:08:10,740
那这样一抽离的话那就毁了

338
00:08:10,740 --> 00:08:11,700
那抽离的还是什么

339
00:08:11,700 --> 00:08:14,600
是不是相当于你不能说把那ab都抽离出来

340
00:08:14,600 --> 00:08:15,320
看看效果吧

341
00:08:15,320 --> 00:08:16,720
那行啊执行一下

342
00:08:17,260 --> 00:08:19,560
这时候一走

343
00:08:19,560 --> 00:08:20,200
你就看懂了

344
00:08:20,200 --> 00:08:21,420
回来里面

345
00:08:21,420 --> 00:08:22,360
直行

346
00:08:22,360 --> 00:08:22,880
直行

347
00:08:22,880 --> 00:08:23,420
稍等

348
00:08:23,420 --> 00:08:25,340
是不是还是出来的

349
00:08:25,340 --> 00:08:26,560
common index other

350
00:08:26,560 --> 00:08:27,540
这个C没有用到

351
00:08:27,540 --> 00:08:28,120
C

352
00:08:28,120 --> 00:08:29,160
C没有用到

353
00:08:29,160 --> 00:08:29,520
无所谓

354
00:08:29,520 --> 00:08:31,000
反正index里面

355
00:08:31,000 --> 00:08:31,640
用到gquare了

356
00:08:31,640 --> 00:08:32,760
other里面用到gquare了

357
00:08:32,760 --> 00:08:33,280
你发现

358
00:08:33,280 --> 00:08:34,320
他是不是还是

359
00:08:34,320 --> 00:08:35,700
把gquare也放到这里了

360
00:08:35,700 --> 00:08:36,800
那大小是85k

361
00:08:36,800 --> 00:08:37,960
你不相信咱看看

362
00:08:37,960 --> 00:08:39,480
我这里找一下

363
00:08:39,480 --> 00:08:41,340
是不是gquare在这里面

364
00:08:41,340 --> 00:08:42,000
为什么

365
00:08:42,000 --> 00:08:42,940
因为我说了

366
00:08:42,940 --> 00:08:43,880
他抽离的话怎么样

367
00:08:43,880 --> 00:08:45,180
他会先走第一个

368
00:08:45,180 --> 00:08:46,820
在入口里面

369
00:08:46,820 --> 00:08:48,120
你看gquery也被共用了

370
00:08:48,120 --> 00:08:49,280
那就直接抽离完了

371
00:08:49,280 --> 00:08:51,520
就不会再抽离什么第二个了

372
00:08:51,520 --> 00:08:52,720
那我是不是应该说哎

373
00:08:52,720 --> 00:08:54,580
这个第三方这个插件怎么样

374
00:08:54,580 --> 00:08:55,620
要单独抽出来

375
00:08:55,620 --> 00:08:57,320
可能这个c也要用

376
00:08:57,320 --> 00:08:58,360
那这时候我怎么做呢

377
00:08:58,360 --> 00:09:01,060
我可以这样加上一个叫什么prealready

378
00:09:01,060 --> 00:09:02,420
什么prealready

379
00:09:02,420 --> 00:09:04,120
你可以认为它就是一个权重

380
00:09:04,120 --> 00:09:06,480
比如说我让它权重怎么样高一点

381
00:09:06,480 --> 00:09:08,920
就是先抽离我们的第三方模块

382
00:09:08,920 --> 00:09:10,160
抽离完之后怎么样

383
00:09:10,160 --> 00:09:11,560
再抽离我们公共的

384
00:09:11,560 --> 00:09:14,580
这样的话是不是相当于会先把index的gquery

385
00:09:14,580 --> 00:09:15,860
还有这个gquery怎么样

386
00:09:15,860 --> 00:09:16,820
抽成一个文件

387
00:09:16,820 --> 00:09:18,560
而之后A和B怎么样

388
00:09:18,560 --> 00:09:20,420
再抽离成一个什么叫common

389
00:09:20,420 --> 00:09:22,960
这样的话我们就实现了第三方

390
00:09:22,960 --> 00:09:24,720
还有我们公共代码怎么样的抽离

391
00:09:24,720 --> 00:09:26,420
run build

392
00:09:26,420 --> 00:09:28,920
当然C我们用到你就不管它了

393
00:09:28,920 --> 00:09:31,520
这里面看看效果

394
00:09:31,520 --> 00:09:34,220
你看这里面是不是有个vindor

395
00:09:34,220 --> 00:09:35,320
indice other

396
00:09:35,320 --> 00:09:37,520
那咱来看看是吧

397
00:09:37,520 --> 00:09:40,520
vindor里面放的什么东西是吧

398
00:09:40,520 --> 00:09:42,120
vindor里面放的就无一例外

399
00:09:42,120 --> 00:09:43,720
只有什么了只有这块位了

400
00:09:43,720 --> 00:09:46,120
现在我们就实现了什么是不是

401
00:09:46,120 --> 00:09:47,120
分辨抽离了

402
00:09:47,120 --> 00:09:48,400
一个是抽离我们的文件

403
00:09:48,400 --> 00:09:50,260
一个是抽离我们的gquery第三方

404
00:09:50,260 --> 00:09:53,180
这里面放的是我们自己的两个文件

405
00:09:53,180 --> 00:09:54,920
当然index里面可以去弄一下

406
00:09:54,920 --> 00:09:55,720
这边没有写

407
00:09:55,720 --> 00:09:57,800
其实在我们以前的版本

408
00:09:57,800 --> 00:09:58,940
应该有个插件叫什么

409
00:09:58,940 --> 00:10:00,220
就common trunk

410
00:10:00,220 --> 00:10:00,680
对吧

411
00:10:00,680 --> 00:10:01,460
好了plugin

412
00:10:01,460 --> 00:10:03,420
现在我们wipac4的配置

413
00:10:03,420 --> 00:10:05,320
就变成了这样一个spreed trunk

414
00:10:05,320 --> 00:10:07,220
这就是它的一个用法

415
00:10:07,220 --> 00:10:07,700
好

416
00:10:07,700 --> 00:10:08,940
我们就说了一下

417
00:10:08,940 --> 00:10:10,900
怎么去实现一个什么

418
00:10:10,900 --> 00:10:12,920
叫多页面打包的时候

419
00:10:12,920 --> 00:10:15,760
抽离我们的公共代码

