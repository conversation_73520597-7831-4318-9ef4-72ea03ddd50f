1
00:00:00,000 --> 00:00:05,340
这一节呢我们来说一下这个webpack比较重要的一个配置叫resolve

2
00:00:05,340 --> 00:00:07,840
哎我们经常会用到他这个resolve啊

3
00:00:07,840 --> 00:00:10,780
他一听名字叫什么呢叫解析解析

4
00:00:10,780 --> 00:00:12,340
说要默认情况下呀

5
00:00:12,340 --> 00:00:15,260
我们在我们的代码里呢可能会去引用一些模块

6
00:00:15,260 --> 00:00:17,340
这个模块呢可能都从哪来的呢

7
00:00:17,340 --> 00:00:19,520
都是我们这样一个第三方包对吧

8
00:00:19,520 --> 00:00:20,140
第三方包

9
00:00:20,140 --> 00:00:22,440
那我们都知道啊

10
00:00:22,440 --> 00:00:24,520
在这个commongs规范中啊

11
00:00:24,520 --> 00:00:25,880
他查找的是怎么查找的

12
00:00:25,880 --> 00:00:28,200
是不是先找当前目标下的none module

13
00:00:28,200 --> 00:00:29,400
如果找不到怎么样

14
00:00:29,400 --> 00:00:30,140
会往上找

15
00:00:30,140 --> 00:00:30,880
往上再往上找

16
00:00:30,880 --> 00:00:31,580
不停的去找

17
00:00:31,580 --> 00:00:34,060
这里面我可以单独配一个叫什么呢

18
00:00:34,060 --> 00:00:36,080
叫这个叫猫丢子

19
00:00:36,080 --> 00:00:37,240
找的时候

20
00:00:37,240 --> 00:00:38,740
我可以强制的跟他说

21
00:00:38,740 --> 00:00:40,540
就在当前目标查找就好了

22
00:00:40,540 --> 00:00:41,480
不要再去怎么样

23
00:00:41,480 --> 00:00:42,720
上一期目标查找

24
00:00:42,720 --> 00:00:45,660
这里面我们可以直接配一个叫

25
00:00:45,660 --> 00:00:46,680
怕三标

26
00:00:46,680 --> 00:00:48,500
你找的时候

27
00:00:48,500 --> 00:00:50,760
只去当前目标查找就好了

28
00:00:50,760 --> 00:00:52,080
就不会他怎么样

29
00:00:52,080 --> 00:00:53,160
再往上面去查找

30
00:00:53,160 --> 00:00:55,060
相当于缩小了一下这个范围

31
00:00:55,060 --> 00:00:56,100
当然还有其他模块

32
00:00:56,100 --> 00:00:56,740
那也可以

33
00:00:56,740 --> 00:00:58,220
比如说我当前目标

34
00:00:58,220 --> 00:00:59,280
有一个地层模块

35
00:00:59,280 --> 00:01:01,320
我可以怎么样也把它加进来

36
00:01:01,320 --> 00:01:02,100
比如叫dist

37
00:01:02,100 --> 00:01:03,380
举个例子

38
00:01:03,380 --> 00:01:04,660
这里我就不加了

39
00:01:04,660 --> 00:01:08,120
同样可能我们还会有怎么样的

40
00:01:08,120 --> 00:01:10,580
是不是会在我们的页面里面有一些样式

41
00:01:10,580 --> 00:01:12,740
样式可能有个特殊的

42
00:01:12,740 --> 00:01:14,420
我在这里面给你安装一下

43
00:01:14,420 --> 00:01:15,780
比如说我们用样式的话

44
00:01:15,780 --> 00:01:17,020
我们先安装两个文件

45
00:01:17,020 --> 00:01:18,360
一个叫csiloader

46
00:01:18,360 --> 00:01:20,620
还有个叫styleloader

47
00:01:20,620 --> 00:01:24,860
这里我就把csiloader和styleloader配上

48
00:01:24,860 --> 00:01:26,140
一样的道理

49
00:01:26,140 --> 00:01:27,340
我来个test对吧

50
00:01:27,340 --> 00:01:28,300
我可以用匹配

51
00:01:28,300 --> 00:01:30,200
比如说以css结尾的

52
00:01:30,200 --> 00:01:32,140
完了我们用一下这个loader

53
00:01:32,140 --> 00:01:33,940
比如说叫cssloader

54
00:01:33,940 --> 00:01:35,600
完了还有我们这样一个styleloader

55
00:01:35,600 --> 00:01:37,440
也是从右向左线

56
00:01:37,440 --> 00:01:39,660
styleloader

57
00:01:39,660 --> 00:01:40,680
那好了

58
00:01:40,680 --> 00:01:42,960
那我们配好了这样两个配置以后

59
00:01:42,960 --> 00:01:44,120
完了我们可以怎么样

60
00:01:44,120 --> 00:01:44,360
是不是

61
00:01:44,360 --> 00:01:45,640
我可能会用到样式

62
00:01:45,640 --> 00:01:46,320
样式的话

63
00:01:46,320 --> 00:01:47,880
我就直接用这个比较方便的了

64
00:01:47,880 --> 00:01:48,660
就boostrap

65
00:01:48,660 --> 00:01:49,900
用这个

66
00:01:49,900 --> 00:01:51,880
它是一个我们的框架

67
00:01:51,880 --> 00:01:52,100
对吧

68
00:01:52,100 --> 00:01:52,620
css框架

69
00:01:52,620 --> 00:01:54,680
那这里面我们用的时候非常方便

70
00:01:54,680 --> 00:01:55,760
我们可以怎么样

71
00:01:55,760 --> 00:01:56,940
是不是我想的就是

72
00:01:56,940 --> 00:01:57,920
在我的一面里

73
00:01:57,920 --> 00:01:59,640
可以直接去导入这个样式

74
00:01:59,640 --> 00:01:59,960
对吧

75
00:01:59,960 --> 00:02:00,520
比如说

76
00:02:00,520 --> 00:02:00,920
impot

77
00:02:00,920 --> 00:02:02,700
叫bootstrap

78
00:02:02,700 --> 00:02:04,480
这个预法是ES6的

79
00:02:04,480 --> 00:02:06,220
我们没有加这个点杠

80
00:02:06,220 --> 00:02:08,080
说明它是一个安装后的模块

81
00:02:08,080 --> 00:02:09,360
那它会去哪找呢

82
00:02:09,360 --> 00:02:10,320
刚才也跟他说了

83
00:02:10,320 --> 00:02:12,440
去当前这个弄的猫就下找

84
00:02:12,440 --> 00:02:13,600
那找的时候

85
00:02:13,600 --> 00:02:14,680
找的就是B开头的

86
00:02:14,680 --> 00:02:14,920
对吧

87
00:02:14,920 --> 00:02:16,280
就是这个bootstrap

88
00:02:16,280 --> 00:02:17,400
刷新一下

89
00:02:17,400 --> 00:02:18,280
更新过来

90
00:02:18,280 --> 00:02:19,700
这里呢

91
00:02:19,700 --> 00:02:20,280
把它拉上去

92
00:02:20,280 --> 00:02:21,440
bootstrap

93
00:02:21,440 --> 00:02:23,360
应该在这个位置上

94
00:02:23,360 --> 00:02:23,740
打字

95
00:02:23,740 --> 00:02:24,600
bo

96
00:02:24,600 --> 00:02:25,240
看一下

97
00:02:25,240 --> 00:02:26,780
bootstrap

98
00:02:26,780 --> 00:02:28,080
这的是吧

99
00:02:28,080 --> 00:02:29,520
不算我这里面呢

100
00:02:29,520 --> 00:02:30,880
你看我们说查找的时候

101
00:02:30,880 --> 00:02:31,660
他要先查找什么

102
00:02:31,660 --> 00:02:34,040
是查找当前的这个叫慢文件

103
00:02:34,040 --> 00:02:35,680
那慢文件查找完以后

104
00:02:35,680 --> 00:02:36,720
是不是就找到的是什么

105
00:02:36,720 --> 00:02:38,300
第一次下的GS的bootstrap

106
00:02:38,300 --> 00:02:40,380
你说他其实是什么拿过来的

107
00:02:40,380 --> 00:02:42,200
他把这个GS拿过来了

108
00:02:42,200 --> 00:02:42,940
那好了

109
00:02:42,940 --> 00:02:44,240
那咱来试一试这个事

110
00:02:44,240 --> 00:02:45,380
那我在这里呢

111
00:02:45,380 --> 00:02:47,140
我就来一个按钮吧

112
00:02:47,140 --> 00:02:48,060
为了看着方便

113
00:02:48,060 --> 00:02:48,740
我来个button

114
00:02:48,740 --> 00:02:50,880
比如说给这按钮的来个样式

115
00:02:50,880 --> 00:02:52,100
叫这个button

116
00:02:52,100 --> 00:02:52,620
对吧

117
00:02:52,620 --> 00:02:54,000
不是btn-danger

118
00:02:54,000 --> 00:02:55,220
是一个红色按钮

119
00:02:55,220 --> 00:02:56,160
他这个样式

120
00:02:56,160 --> 00:02:56,960
它会不会生效

121
00:02:56,960 --> 00:02:58,440
这里面一样

122
00:02:58,440 --> 00:02:59,360
我们可以在这

123
00:02:59,360 --> 00:03:00,680
npm run div

124
00:03:00,680 --> 00:03:02,520
大家看看

125
00:03:02,520 --> 00:03:03,560
它能不能跑通

126
00:03:03,560 --> 00:03:05,880
这里面它报了个小小的错误

127
00:03:05,880 --> 00:03:06,520
说18号

128
00:03:06,520 --> 00:03:08,920
这里肯定是没有丢丢号了

129
00:03:08,920 --> 00:03:10,240
对对丢丢号了

130
00:03:10,240 --> 00:03:12,220
这里面我再来执行一下

131
00:03:12,220 --> 00:03:12,780
sony

132
00:03:12,780 --> 00:03:16,300
它运行这个代码的时候

133
00:03:16,300 --> 00:03:18,360
是不是应该是匹配所有css文件

134
00:03:18,360 --> 00:03:19,960
但是我这里面好像

135
00:03:19,960 --> 00:03:21,960
感觉不像css

136
00:03:21,960 --> 00:03:22,860
像gns

137
00:03:22,860 --> 00:03:23,900
咱来看看吧

138
00:03:23,900 --> 00:03:25,100
我说样式有没有效果

139
00:03:25,100 --> 00:03:55,080
嗯,刷新一下,有点慢,是吧,这时候报错了,说,呃,没有找到jquery,是吧,那好,那咱就,你看,明显啊,我引的明明是css,他给我找jquery去了,你说我引的确实是什么,是你看,写的呢,找的是dist下的,借的bootstrap点,借的,那我写的时候,我就要怎么办了,哎呀,我说这个很麻烦,是吧,那我会怎么写呢,会这样写,叫bootstrap,下的dist,下的css,下的什么,bootstrap点css,这就可以了,这相当于引的就是什么,就是引的就是那个,

140
00:03:55,080 --> 00:03:56,120
样式文件

141
00:03:56,120 --> 00:03:56,800
哎那好啊

142
00:03:56,800 --> 00:03:58,080
我再来试试是吧

143
00:03:58,080 --> 00:03:59,880
不过有点长写的是吧

144
00:03:59,880 --> 00:04:01,080
哎但是可以这样用

145
00:04:01,080 --> 00:04:03,400
来运行是吧

146
00:04:03,400 --> 00:04:04,200
看看我这回呢

147
00:04:04,200 --> 00:04:05,440
我的样式我不ok

148
00:04:05,440 --> 00:04:08,040
有点慢啊

149
00:04:08,040 --> 00:04:10,560
看着好像没包错

150
00:04:10,560 --> 00:04:10,960
但是呢

151
00:04:10,960 --> 00:04:12,440
他说这玩文件比较大啊

152
00:04:12,440 --> 00:04:13,320
人家不理他

153
00:04:13,320 --> 00:04:14,640
我给你刷新一下是吧

154
00:04:14,640 --> 00:04:17,520
有点慢啊

155
00:04:17,520 --> 00:04:18,960
端口应该是8080

156
00:04:18,960 --> 00:04:20,480
送你

157
00:04:20,480 --> 00:04:22,760
你看这个按钮确实变红了

158
00:04:22,760 --> 00:04:24,520
那说明我这样式怎么样

159
00:04:24,520 --> 00:04:25,540
确实生效了

160
00:04:25,540 --> 00:04:27,760
但是还是那个问题是吧

161
00:04:27,760 --> 00:04:28,920
他说我把这东西引进来

162
00:04:28,920 --> 00:04:31,560
要写这么长的名字是不是不太优雅呀

163
00:04:31,560 --> 00:04:32,920
那这时候可以怎么办呢

164
00:04:32,920 --> 00:04:34,060
哎我们经常啊

165
00:04:34,060 --> 00:04:35,980
会在我们这样一个属性里面呢

166
00:04:35,980 --> 00:04:37,580
配上一个东西叫lize

167
00:04:37,580 --> 00:04:39,080
这个东西什么意思

168
00:04:39,080 --> 00:04:40,780
就是别名的意思是吧

169
00:04:40,780 --> 00:04:41,480
别名

170
00:04:41,480 --> 00:04:44,020
其实我们都看过啊

171
00:04:44,020 --> 00:04:44,720
像view

172
00:04:44,720 --> 00:04:46,180
我们真正去引view的时候

173
00:04:46,180 --> 00:04:48,680
其实引的是什么什么view的什么run time

174
00:04:48,680 --> 00:04:49,060
是吧

175
00:04:49,060 --> 00:04:49,380
哎

176
00:04:49,380 --> 00:04:50,580
这个东西是吧

177
00:04:50,580 --> 00:04:52,180
view点什么run time.js

178
00:04:52,180 --> 00:04:53,260
一个道理

179
00:04:53,260 --> 00:04:56,160
就说我们可以把这个名字怎么样写的很短

180
00:04:56,160 --> 00:04:58,000
我就想在这去 bootstrap

181
00:04:58,000 --> 00:04:59,760
不写这么长的名字

182
00:04:59,760 --> 00:05:00,980
但是我这样引呢

183
00:05:00,980 --> 00:05:02,220
他默认引的就是gs

184
00:05:02,220 --> 00:05:03,420
那我可以怎么做呢

185
00:05:03,420 --> 00:05:03,720
可以这样

186
00:05:03,720 --> 00:05:04,780
来个什么呢

187
00:05:04,780 --> 00:05:06,720
叫这个写个别名是吧

188
00:05:06,720 --> 00:05:08,100
我说bootstrap

189
00:05:08,100 --> 00:05:10,800
他其实啊写的是这么长的

190
00:05:10,800 --> 00:05:11,720
s加一屁

191
00:05:11,720 --> 00:05:13,720
这样就好了

192
00:05:13,720 --> 00:05:14,540
bootstrap

193
00:05:14,540 --> 00:05:15,540
那这样的话

194
00:05:15,540 --> 00:05:17,900
是不是相当于我在页面中用的地方怎么样

195
00:05:17,900 --> 00:05:18,620
就短了

196
00:05:18,620 --> 00:05:19,440
别地方还要用

197
00:05:19,440 --> 00:05:19,780
那好

198
00:05:19,780 --> 00:05:20,340
这样也可以

199
00:05:20,340 --> 00:05:21,900
那好了啊

200
00:05:21,900 --> 00:05:23,240
那这样写完以后呢

201
00:05:23,240 --> 00:05:24,500
我们再来试试是吧

202
00:05:24,500 --> 00:05:25,100
打包

203
00:05:25,100 --> 00:05:27,100
其实你发现效果还是一样的

204
00:05:27,100 --> 00:05:28,080
稍等

205
00:05:28,080 --> 00:05:30,740
因为刚才我引错了是吧

206
00:05:30,740 --> 00:05:31,940
你看又又报警错了

207
00:05:31,940 --> 00:05:32,700
那这时候呢

208
00:05:32,700 --> 00:05:34,240
我们刷新一下比较慢

209
00:05:34,240 --> 00:05:35,640
完了刷新

210
00:05:35,640 --> 00:05:37,180
是不是依旧可以啊

211
00:05:37,180 --> 00:05:38,800
这就是别名的特点

212
00:05:38,800 --> 00:05:39,680
那同样啊

213
00:05:39,680 --> 00:05:42,440
那这时候写的时候感觉说还是很麻烦是吧

214
00:05:42,440 --> 00:05:43,880
那能不能有点方便的

215
00:05:43,880 --> 00:05:46,040
比如说我找这个文件的时候啊

216
00:05:46,040 --> 00:05:47,740
他能不能先去找这个东西

217
00:05:47,740 --> 00:05:48,780
你看有个style

218
00:05:48,780 --> 00:05:51,240
你style指的是他的第一次是吧

219
00:05:51,240 --> 00:05:52,900
完了慢的指的是他的什么

220
00:05:52,900 --> 00:05:58,460
ds 那能不能先去让他找style 再去找man 当然了 这其实也可以啊 都没问题

221
00:05:58,460 --> 00:06:05,320
这里面他还有属性叫什么叫man feels 是吧 就是出入口的什么字段对吧

222
00:06:05,320 --> 00:06:06,300
 f i e l d s

223
00:06:06,300 --> 00:06:08,100
f i e l d s

224
00:06:08,100 --> 00:06:10,600
e l d s 是吧

225
00:06:10,600 --> 00:06:16,300
这里我可以给上 比如说你先找style style找不到的话怎么样 你再去找man

226
00:06:16,300 --> 00:06:21,300
这也是可以的啊 他会怎么找呢 先找找找到以后怎么样 用他来进行误口

227
00:06:21,700 --> 00:06:22,700
那再试试是吧

228
00:06:22,700 --> 00:06:23,300
这里

229
00:06:23,300 --> 00:06:25,800
那我演的时候是不是这里面还是不虽然拍

230
00:06:25,800 --> 00:06:27,700
看一下效果

231
00:06:27,700 --> 00:06:29,400
没打包

232
00:06:29,400 --> 00:06:31,400
稍等是吧

233
00:06:31,400 --> 00:06:32,400
比较慢

234
00:06:32,400 --> 00:06:34,800
哦好了是吧

235
00:06:34,800 --> 00:06:35,700
我再刷新一下

236
00:06:35,700 --> 00:06:36,700
因为发现啊

237
00:06:36,700 --> 00:06:37,800
是不是也是可以的

238
00:06:37,800 --> 00:06:38,400
没问题

239
00:06:38,400 --> 00:06:39,700
这就是我们这个man

240
00:06:39,700 --> 00:06:42,000
这叫什么manfiles这个字段

241
00:06:42,000 --> 00:06:43,400
那同样除了这个manfiles

242
00:06:43,400 --> 00:06:44,300
其实还有个叫什么呢

243
00:06:44,300 --> 00:06:45,600
叫manfiles

244
00:06:45,600 --> 00:06:46,900
这是指定什么呢

245
00:06:46,900 --> 00:06:48,500
叫入口文件的名字对吧

246
00:06:48,500 --> 00:06:49,500
就是入口

247
00:06:49,500 --> 00:06:51,400
入口对吧

248
00:06:51,600 --> 00:06:53,240
文件的名字

249
00:06:53,240 --> 00:06:53,400
哎

250
00:06:53,400 --> 00:06:54,240
如果你没有指定

251
00:06:54,240 --> 00:06:55,640
其实他默认找的是谁呀

252
00:06:55,640 --> 00:06:57,160
是不是index.js

253
00:06:57,160 --> 00:06:58,920
那在这里面你可以怎么样去改

254
00:06:58,920 --> 00:07:00,480
但是我没有用场景啊

255
00:07:00,480 --> 00:07:01,360
这就就提一嘴

256
00:07:01,360 --> 00:07:02,480
你不多说了

257
00:07:02,480 --> 00:07:04,360
那除了我们现在这几种方式

258
00:07:04,360 --> 00:07:05,120
有猫迪奥斯

259
00:07:05,120 --> 00:07:06,360
还有曼菲尔斯

260
00:07:06,360 --> 00:07:07,560
还有曼菲尔斯

261
00:07:07,560 --> 00:07:08,440
赖子对吧

262
00:07:08,440 --> 00:07:10,200
其实还有个东西比较重要

263
00:07:10,200 --> 00:07:10,960
就是经常啊

264
00:07:10,960 --> 00:07:12,880
我们写代码的时候会这样写

265
00:07:12,880 --> 00:07:14,760
比如说我来个样式对吧

266
00:07:14,760 --> 00:07:17,360
叫这个index.s

267
00:07:17,360 --> 00:07:18,480
完了我把这个样式呢

268
00:07:18,480 --> 00:07:19,520
给你改一下啊

269
00:07:19,520 --> 00:07:20,200
保底

270
00:07:20,200 --> 00:07:21,480
比如说来个background

271
00:07:21,480 --> 00:07:21,940
Yellow

272
00:07:21,940 --> 00:07:24,720
我这此时我会怎么办呢

273
00:07:24,720 --> 00:07:26,240
我会把它引进来是吧

274
00:07:26,240 --> 00:07:28,200
比如说硬泡它去引什么呢

275
00:07:28,200 --> 00:07:29,420
引这个第二杠

276
00:07:29,420 --> 00:07:30,540
index

277
00:07:30,540 --> 00:07:33,120
你看这时候我不想写后坠对吧

278
00:07:33,120 --> 00:07:34,540
那我今后这种需求啊

279
00:07:34,540 --> 00:07:35,940
问了刷新

280
00:07:35,940 --> 00:07:38,520
好像人家不知道高不高兴是吧

281
00:07:38,520 --> 00:07:39,840
好像有点卡

282
00:07:39,840 --> 00:07:41,880
但这时候好像是过了是吧

283
00:07:41,880 --> 00:07:42,820
再来试试啊

284
00:07:42,820 --> 00:07:43,980
看看有没有生效

285
00:07:43,980 --> 00:07:45,420
要是没有拿进来

286
00:07:45,420 --> 00:07:47,720
好像是反正要是没生效啊

287
00:07:47,720 --> 00:07:49,580
我把这不算把先住掉

288
00:07:49,580 --> 00:07:51,380
看看这回呀会不会有问题啊

289
00:07:51,380 --> 00:07:51,980
刷新

290
00:07:51,980 --> 00:07:54,780
还是比较慢

291
00:07:54,780 --> 00:07:55,380
是吧

292
00:07:55,380 --> 00:07:55,940
OK

293
00:07:55,940 --> 00:07:57,380
是成功了是吧

294
00:07:57,380 --> 00:07:59,540
但是好像这个样式怎么样

295
00:07:59,540 --> 00:08:00,220
是不是没找到

296
00:08:00,220 --> 00:08:02,340
你看我引的是index.css是吧

297
00:08:02,340 --> 00:08:03,740
我写全看看行不行啊

298
00:08:03,740 --> 00:08:04,740
2.css

299
00:08:04,740 --> 00:08:06,940
再保存是吧

300
00:08:06,940 --> 00:08:07,980
我在这刷新一下

301
00:08:07,980 --> 00:08:10,820
好像怪怪的啊

302
00:08:10,820 --> 00:08:11,540
我在这里呢

303
00:08:11,540 --> 00:08:12,540
我再重启一下啊

304
00:08:12,540 --> 00:08:16,020
可能啊他引的时候有问题啊

305
00:08:16,020 --> 00:08:16,540
稍等

306
00:08:16,540 --> 00:08:20,220
现在成了是吧

307
00:08:20,220 --> 00:08:21,340
我看看变变红

308
00:08:21,380 --> 00:08:22,380
变黄了是吧

309
00:08:22,380 --> 00:08:23,380
那黄色以后呢

310
00:08:23,380 --> 00:08:25,060
我把这点CSI的删掉是吧

311
00:08:25,060 --> 00:08:25,580
删掉

312
00:08:25,580 --> 00:08:26,380
我再刷新

313
00:08:26,380 --> 00:08:29,380
没发现是不是样式根本没有引到啊

314
00:08:29,380 --> 00:08:30,940
那他引的可能会认为什么

315
00:08:30,940 --> 00:08:33,260
他会去引这个点借子文件是吧

316
00:08:33,260 --> 00:08:35,660
所以说他没有没有触发这个我们的样式

317
00:08:35,660 --> 00:08:37,460
那这时候我们希望怎么样

318
00:08:37,460 --> 00:08:39,220
是不是这可以怎么样省略一下

319
00:08:39,220 --> 00:08:40,380
比如说把这名字改了吧

320
00:08:40,380 --> 00:08:42,460
可能名字冲突的问题啊

321
00:08:42,460 --> 00:08:43,060
就这样

322
00:08:43,060 --> 00:08:44,620
这里面就改成style

323
00:08:44,620 --> 00:08:46,100
这样明显一点啊

324
00:08:46,100 --> 00:08:46,820
我保存

325
00:08:46,820 --> 00:08:47,980
我再在这儿

326
00:08:47,980 --> 00:08:49,820
编译完以后看看效果啊

327
00:08:50,820 --> 00:08:52,860
是告诉我找不到这个style文件

328
00:08:52,860 --> 00:08:54,220
因为这里面怎么样

329
00:08:54,220 --> 00:08:55,380
他没有写后准

330
00:08:55,380 --> 00:08:57,260
那这时候呢我们就可以怎么样

331
00:08:57,260 --> 00:08:58,380
强制写了后准

332
00:08:58,380 --> 00:08:59,780
比如说呀我们用view

333
00:08:59,780 --> 00:09:00,660
那view也是对吧

334
00:09:00,660 --> 00:09:02,500
比如说我们写的时候可能一样

335
00:09:02,500 --> 00:09:03,540
以这个组件对吧

336
00:09:03,540 --> 00:09:04,780
可能都是什么import

337
00:09:04,780 --> 00:09:05,780
或者XXX

338
00:09:05,780 --> 00:09:07,100
完了from对吧

339
00:09:07,100 --> 00:09:09,540
完了一个什么内部吧

340
00:09:09,540 --> 00:09:11,300
对吧内部吧

341
00:09:11,300 --> 00:09:12,660
内部吧

342
00:09:12,660 --> 00:09:14,660
完了我们后面一般也不会写点view吧

343
00:09:14,660 --> 00:09:16,060
那这时候我怎么配呢

344
00:09:16,060 --> 00:09:17,180
其实这里面也一样

345
00:09:17,180 --> 00:09:18,740
我们可以加上一个东西叫什么

346
00:09:18,740 --> 00:09:19,980
叫扩展名

347
00:09:19,980 --> 00:09:21,780
is tensions

348
00:09:21,780 --> 00:09:22,900
网络我可以在这标上

349
00:09:22,900 --> 00:09:23,580
比如说啊

350
00:09:23,580 --> 00:09:25,580
如果你找不到这个css

351
00:09:25,580 --> 00:09:26,340
那你可以怎么样

352
00:09:26,340 --> 00:09:27,580
再去说你可以怎么样

353
00:09:27,580 --> 00:09:28,300
填护载名

354
00:09:28,300 --> 00:09:29,260
先找什么呢

355
00:09:29,260 --> 00:09:31,500
先找gsgs找不到怎么办呢

356
00:09:31,500 --> 00:09:32,900
你再去找这个css

357
00:09:32,900 --> 00:09:34,060
我css找不到怎么办

358
00:09:34,060 --> 00:09:35,340
你可以再写个.jss

359
00:09:35,340 --> 00:09:36,860
哎你可以这样来配

360
00:09:36,860 --> 00:09:38,060
这样配完以后啊

361
00:09:38,060 --> 00:09:38,820
他就会怎么样

362
00:09:38,820 --> 00:09:39,660
一看哦

363
00:09:39,660 --> 00:09:41,220
这个style.js没有

364
00:09:41,220 --> 00:09:42,060
你就找什么

365
00:09:42,060 --> 00:09:43,540
找css后面的

366
00:09:43,540 --> 00:09:44,580
你可以自己去配啊

367
00:09:44,580 --> 00:09:45,700
比如文件名的同样

368
00:09:45,700 --> 00:09:46,220
有view的话

369
00:09:46,220 --> 00:09:47,540
可以再加个什么叫.view

370
00:09:47,540 --> 00:09:48,300
都可以

371
00:09:48,300 --> 00:09:50,600
顺序的话就是依次解析

372
00:09:50,600 --> 00:09:52,840
那这里呢我们再来跑一下

373
00:09:52,840 --> 00:09:54,140
sony

374
00:09:54,140 --> 00:09:57,960
看看这个效果啊马上就ok了

375
00:09:57,960 --> 00:09:59,060
8080

376
00:09:59,060 --> 00:10:02,340
往上这里呢我就把这个路径啊打开是吧

377
00:10:02,340 --> 00:10:03,640
刷新

378
00:10:03,640 --> 00:10:07,560
哎你看这时候是不是也变黄了发现怎么样是不是ok了

379
00:10:07,560 --> 00:10:10,640
那这时候我们这样一个resolve参数啊就讲完了

380
00:10:10,640 --> 00:10:13,580
这都是我们一些基本的应用啊

