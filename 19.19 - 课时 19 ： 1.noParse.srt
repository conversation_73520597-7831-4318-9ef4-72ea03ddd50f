1
00:00:00,000 --> 00:00:04,600
本章呢,主要来讲一下这个webpack的优化

2
00:00:04,600 --> 00:00:08,200
那我们呢,就把webpack这些优化手段啊,以此来使用一下

3
00:00:08,200 --> 00:00:11,800
那这里呢,我们一样,还是要先搭建一个webpack环境

4
00:00:11,800 --> 00:00:14,600
那这里呢,我们可以直接进入这个目录下

5
00:00:14,600 --> 00:00:17,800
完了,先初始化一下我们的这样一个patterjson

6
00:00:17,800 --> 00:00:21,800
完了呢,并且呢,我们需要安装webpack一系列的包

7
00:00:21,800 --> 00:00:25,800
enr的,我们的webpack,还有webpack-cli

8
00:00:25,800 --> 00:00:28,140
包括我们的atml对吧

9
00:00:28,140 --> 00:00:30,740
wipag plugin-plugin

10
00:00:30,740 --> 00:00:32,900
还有我们这个gs相关的

11
00:00:32,900 --> 00:00:35,540
那么可能需要这个babel的核心模块

12
00:00:35,540 --> 00:00:37,800
还有我们的这个babel-loader

13
00:00:37,800 --> 00:00:43,340
这就是我们这样一个babel的预设babel preset-es2015

14
00:00:43,340 --> 00:00:45,140
应该用我们的env了

15
00:00:45,140 --> 00:00:46,340
现在变成新的名字了

16
00:00:46,340 --> 00:00:48,960
并且呢其实我们还需要解析react

17
00:00:48,960 --> 00:00:50,600
这里呢我们可以再装一个包啊

18
00:00:50,600 --> 00:00:53,340
叫babel-preset-react

19
00:00:53,340 --> 00:00:55,240
完了这里呢我们就先安装

20
00:00:55,240 --> 00:01:00,240
那这个包呢主要就是帮我们解析我们react的gss预法先把它配好吧

21
00:01:00,240 --> 00:01:04,240
那这里呢我们就先写上这个配置啊就不等他安装了

22
00:01:04,240 --> 00:01:08,240
往来里面的依旧我们需要一个webpack.config.js

23
00:01:08,240 --> 00:01:13,240
往来这里还是一样我们呢需要默认的导出一个模块module.dispos

24
00:01:13,240 --> 00:01:17,240
往来里面呢我们需要加一个入口

25
00:01:17,240 --> 00:01:20,240
入口呢我们就可以在这里呢加一个src文件

26
00:01:20,240 --> 00:01:22,240
哎这个就快速搭建了啊

27
00:01:22,240 --> 00:01:24,240
这里面我就放上一个index.js

28
00:01:24,240 --> 00:01:27,220
并且呢我们应该再来一个目录吧

29
00:01:27,220 --> 00:01:28,780
我就叫它public好吧

30
00:01:28,780 --> 00:01:31,780
public呢里面专门存放我们的atml文件

31
00:01:31,780 --> 00:01:34,140
这里呢我们就来个indexatml

32
00:01:34,140 --> 00:01:35,820
我也快速生成一下

33
00:01:35,820 --> 00:01:41,180
然后这里呢我们就可以直接这里呢加上一个bavidroot

34
00:01:41,180 --> 00:01:42,440
我们有rata的话

35
00:01:42,440 --> 00:01:44,700
到时候肯定会把它选到这样一个根元素里面

36
00:01:44,700 --> 00:01:46,300
但是现在咱们先不考虑啊

37
00:01:46,300 --> 00:01:47,380
那这里呢一样

38
00:01:47,380 --> 00:01:52,940
那我们是不是有当前引用的目录就是src下的这个index.js

39
00:01:52,940 --> 00:01:57,000
并且呢我们可以配一个输出入境output

40
00:01:57,000 --> 00:02:00,240
output我把这个字稍微放大一点啊

41
00:02:00,240 --> 00:02:03,400
数选项我这边有设置把它改成24号

42
00:02:03,400 --> 00:02:04,480
啊24号

43
00:02:04,480 --> 00:02:06,520
那这里一样啊

44
00:02:06,520 --> 00:02:08,240
我们可以给他一个叫file name

45
00:02:08,240 --> 00:02:09,840
打包出来的文件名

46
00:02:09,840 --> 00:02:10,860
往往这里呢

47
00:02:10,860 --> 00:02:12,560
我就可以给一个比如叫点杠

48
00:02:12,560 --> 00:02:15,400
啊就叫bundle.js吧

49
00:02:15,400 --> 00:02:16,540
打包出来的文件

50
00:02:16,540 --> 00:02:19,300
并且呢我们还需要配一个路径pass

51
00:02:19,300 --> 00:02:21,820
那pass的话呢我们可以给个绝对路径

52
00:02:21,820 --> 00:02:24,760
那这里呢我们需要把这个pass模块呢引进来

53
00:02:24,760 --> 00:02:28,360
等于require我们可以把这个pass拿下来

54
00:02:28,360 --> 00:02:33,600
并且呢我们可以用这个pass.result来个杠杠第二例

55
00:02:33,600 --> 00:02:36,020
后面呢我们给一个第四目录

56
00:02:36,020 --> 00:02:39,200
那这样的话就可以帮我们打包到这个bundle.js中

57
00:02:39,200 --> 00:02:42,640
并且呢我们把那个atmlplugin也配好啊

58
00:02:42,640 --> 00:02:44,400
它是一个类atml

59
00:02:44,400 --> 00:02:48,400
webpackplugin

60
00:02:48,400 --> 00:02:50,440
然后把它呢业力进来

61
00:02:50,440 --> 00:02:53,100
require hml webpack plugin

62
00:02:53,100 --> 00:02:54,520
而我们需要呢

63
00:02:54,520 --> 00:02:56,400
在这个plugins参数里面呢

64
00:02:56,400 --> 00:02:57,700
去注册一下这个插件

65
00:02:57,700 --> 00:03:00,080
new hml webpack plugin

66
00:03:00,080 --> 00:03:01,240
这里呢

67
00:03:01,240 --> 00:03:02,600
我们需要指定这个模板

68
00:03:02,600 --> 00:03:03,540
那模板呢

69
00:03:03,540 --> 00:03:05,740
我们就可以选用当前这样一个点杠

70
00:03:05,740 --> 00:03:06,860
叫public

71
00:03:06,860 --> 00:03:09,180
下的index.atml

72
00:03:09,180 --> 00:03:11,900
index.atml

73
00:03:11,900 --> 00:03:13,380
那现在我们来打包一下

74
00:03:13,380 --> 00:03:14,260
看看行不行

75
00:03:14,260 --> 00:03:15,080
那这里呢

76
00:03:15,080 --> 00:03:15,700
非常简单

77
00:03:15,700 --> 00:03:16,840
我们可以在这里呢

78
00:03:16,840 --> 00:03:18,200
再去npx

79
00:03:18,200 --> 00:03:21,100
npxwipac

80
00:03:21,100 --> 00:03:23,600
现在执行一下

81
00:03:23,600 --> 00:03:26,200
看看能不能打包出我们相对的结果

82
00:03:26,200 --> 00:03:27,400
我看是可以的

83
00:03:27,400 --> 00:03:28,900
并且这里面也有了

84
00:03:28,900 --> 00:03:30,200
我们再给他一个模式

85
00:03:30,200 --> 00:03:32,000
mode比如说就是开发模式

86
00:03:32,000 --> 00:03:33,700
bvelopnmd

87
00:03:33,700 --> 00:03:35,700
完了我们再来试一下

88
00:03:35,700 --> 00:03:38,400
这就是一个简单的wipac环境

89
00:03:38,400 --> 00:03:42,100
并且我们还需要把GS也匹配上对吧

90
00:03:42,100 --> 00:03:44,200
GS的话上次我们已经说了怎么配

91
00:03:44,200 --> 00:03:45,300
我们可以加个module

92
00:03:45,300 --> 00:03:47,100
网上里面配上一些规则

93
00:03:47,100 --> 00:03:48,820
规则的默认是个数组

94
00:03:48,820 --> 00:03:49,620
完了里面呢

95
00:03:49,620 --> 00:03:51,640
我们就要匹配所有的gs对吧

96
00:03:51,640 --> 00:03:52,160
test

97
00:03:52,160 --> 00:03:55,660
比如说以这个gs结尾的

98
00:03:55,660 --> 00:03:57,460
完了我们呢

99
00:03:57,460 --> 00:03:58,740
可以去用对吧

100
00:03:58,740 --> 00:04:00,120
用我们的这个对象

101
00:04:00,120 --> 00:04:01,580
我们可以放上一个loader

102
00:04:01,580 --> 00:04:04,680
我们要用的就是这个babel-loader

103
00:04:04,680 --> 00:04:05,920
后面呢

104
00:04:05,920 --> 00:04:08,160
我们还可能配上一些我们所谓的options

105
00:04:08,160 --> 00:04:10,080
options就是它的一些选项

106
00:04:10,080 --> 00:04:11,160
那选项呢

107
00:04:11,160 --> 00:04:12,560
我们可以配上一些预设

108
00:04:12,560 --> 00:04:14,420
这个预设其实就有多个了

109
00:04:14,420 --> 00:04:16,060
我们需要一个叫babel

110
00:04:16,060 --> 00:04:17,560
@对吧

111
00:04:17,560 --> 00:04:21,060
@bibble-preset-env

112
00:04:21,060 --> 00:04:22,560
完了还有我们这样一个对吧

113
00:04:22,560 --> 00:04:26,560
叫@bibble-preset-react

114
00:04:26,560 --> 00:04:28,460
这就用来解析我们的对吧

115
00:04:28,460 --> 00:04:31,060
esu还有我们的react语法的

116
00:04:31,060 --> 00:04:31,660
这样的话

117
00:04:31,660 --> 00:04:32,960
我们再来运行一下

118
00:04:32,960 --> 00:04:34,260
看看应该是还可以的

119
00:04:34,260 --> 00:04:35,060
有问题

120
00:04:35,060 --> 00:04:38,260
这里面我们就写了个错的

121
00:04:38,260 --> 00:04:40,760
bibble loader说他没有装多了个空格

122
00:04:40,760 --> 00:04:42,360
这里面一定要注意名字呢

123
00:04:42,360 --> 00:04:43,360
不能写错

124
00:04:43,360 --> 00:04:44,360
这里再运行去

125
00:04:45,760 --> 00:04:47,560
这样应该就安好了

126
00:04:47,560 --> 00:04:48,820
你看没有问题

127
00:04:48,820 --> 00:04:51,200
但是现在我们需要先学会第一个优化项

128
00:04:51,200 --> 00:04:52,220
就是有些酷

129
00:04:52,220 --> 00:04:54,980
可能我们在页面中可能会安装

130
00:04:54,980 --> 00:04:56,000
gquery是吧

131
00:04:56,000 --> 00:04:58,660
一二的gq一二外安装上

132
00:04:58,660 --> 00:05:01,420
那我们可能就会在我们这个打包里面怎么样

133
00:05:01,420 --> 00:05:03,120
是不是使用我们这样一个gquery

134
00:05:03,120 --> 00:05:04,120
那几块的用法呢

135
00:05:04,120 --> 00:05:05,000
可能是in part

136
00:05:05,000 --> 00:05:06,560
我们会gquery

137
00:05:06,560 --> 00:05:07,820
gq要压拜

138
00:05:07,820 --> 00:05:09,560
或者from我们的gquery

139
00:05:09,560 --> 00:05:11,520
我们都知道webpack有特点

140
00:05:11,520 --> 00:05:13,360
他会呢先找到这个入口

141
00:05:13,360 --> 00:05:14,660
通过入口呢来解析

142
00:05:14,660 --> 00:05:16,520
一看哎这里面用的requery

143
00:05:16,520 --> 00:05:18,360
那好他就会去加的requery

144
00:05:18,360 --> 00:05:21,320
往来去分析一下这个requery里面的依然没有依赖

145
00:05:21,320 --> 00:05:23,320
对吧这些依赖怎么样再去打包

146
00:05:23,320 --> 00:05:24,760
但是我们都很明确

147
00:05:24,760 --> 00:05:28,100
这个requery中一般不会再使用其他的模块了

148
00:05:28,100 --> 00:05:28,720
对吧

149
00:05:28,720 --> 00:05:30,000
它是一个独立的模块

150
00:05:30,000 --> 00:05:32,820
那这时候呢我并不需要这个webpack来解析

151
00:05:32,820 --> 00:05:34,560
比如说呢我们可以来试试

152
00:05:34,560 --> 00:05:35,760
但是不知道没有效果

153
00:05:35,760 --> 00:05:37,460
这样的话我们可以去npx

154
00:05:37,460 --> 00:05:38,560
比如说webpack

155
00:05:38,560 --> 00:05:41,500
看看这打包的速度多久是吧

156
00:05:41,500 --> 00:05:44,000
比如说有点慢

157
00:05:44,000 --> 00:05:46,760
那打包的速度呢大约是在两秒左右

158
00:05:46,760 --> 00:05:49,760
因为他也会去检测我们这块里面没有依赖

159
00:05:49,760 --> 00:05:51,640
那我们可以在这个配置中啊

160
00:05:51,640 --> 00:05:53,800
加上一个东西叫什么呢叫nopass

161
00:05:53,800 --> 00:05:55,960
nopass什么意思就是我可以啊

162
00:05:55,960 --> 00:05:57,400
不去解析某些包

163
00:05:57,400 --> 00:05:59,240
比如说我不想去解析这块

164
00:05:59,240 --> 00:06:00,840
只要看到这块以后怎么样

165
00:06:00,840 --> 00:06:01,760
我就把它忽略掉

166
00:06:01,760 --> 00:06:02,840
相当于这个包啊

167
00:06:02,840 --> 00:06:04,240
他并不是要去解析

168
00:06:04,240 --> 00:06:05,960
并没有去引用其他的模块

169
00:06:05,960 --> 00:06:07,600
那这时候呢我们来看一下

170
00:06:07,600 --> 00:06:09,360
这个速度呢会不会加快是吧

171
00:06:09,360 --> 00:06:10,720
哦我按错了

172
00:06:10,720 --> 00:06:11,680
重新按了一遍

173
00:06:11,680 --> 00:06:13,200
这里呢安完

174
00:06:13,200 --> 00:06:15,420
我再去执行npx webpack

175
00:06:15,420 --> 00:06:18,120
那这时候好像是看不太出来是吧

176
00:06:18,120 --> 00:06:19,240
这是2405

177
00:06:19,240 --> 00:06:22,300
这个nopass的意思就是不去对吧

178
00:06:22,300 --> 00:06:24,140
就是不去解析

179
00:06:24,140 --> 00:06:26,000
不去解析对吧

180
00:06:26,000 --> 00:06:28,660
解析这块位中的依赖关系

181
00:06:28,660 --> 00:06:32,480
比如说这个酷很大的情况下会有帮助

182
00:06:32,480 --> 00:06:34,080
听效率很高

183
00:06:34,080 --> 00:06:35,280
所以说一般情况下

184
00:06:35,280 --> 00:06:37,860
我们如果知道这个帮并没有一些依赖项

185
00:06:37,860 --> 00:06:40,400
那我就可以使用这个nopass把它忽略掉

186
00:06:40,400 --> 00:06:41,440
这样一个用法

187
00:06:41,440 --> 00:06:43,500
这就是我们第一个的优害点

