1
00:00:00,000 --> 00:00:03,840
我们呢再来写一个叫内联的webpack插件

2
00:00:03,840 --> 00:00:05,380
这个插件有什么作用呢

3
00:00:05,380 --> 00:00:07,680
就是目前现在我们打包出来的结果呢

4
00:00:07,680 --> 00:00:08,960
其实都是分开来的

5
00:00:08,960 --> 00:00:10,500
比如说要我们再来加一个吧

6
00:00:10,500 --> 00:00:11,520
加个css

7
00:00:11,520 --> 00:00:12,720
别人能看到效果

8
00:00:12,720 --> 00:00:15,360
我们在这里面再新建一个叫index.css

9
00:00:15,360 --> 00:00:18,440
那这里面呢我们可能会写一个body

10
00:00:18,440 --> 00:00:19,960
比如说background对吧

11
00:00:19,960 --> 00:00:21,240
来个yellow黄色

12
00:00:21,240 --> 00:00:25,080
我们在gs中的把它引进来import对吧

13
00:00:25,080 --> 00:00:27,400
比如.gob index.css

14
00:00:27,400 --> 00:00:29,860
那引完以后我们需要干嘛呢

15
00:00:29,860 --> 00:00:31,720
需要再去加一些东西

16
00:00:31,720 --> 00:00:33,920
需要加上这个 css loader

17
00:00:33,920 --> 00:00:36,320
完了还有我们这样一个叫对吧

18
00:00:36,320 --> 00:00:38,600
我为了不就不把它放到4条标签里了

19
00:00:38,600 --> 00:00:40,820
我就直接用这个mini css对吧

20
00:00:40,820 --> 00:00:42,100
extract plugin

21
00:00:42,100 --> 00:00:44,360
把我们这个文件怎么样提取出来

22
00:00:44,360 --> 00:00:44,920
-d

23
00:00:44,920 --> 00:00:46,820
ok

24
00:00:46,820 --> 00:00:47,460
那这里面呢

25
00:00:47,460 --> 00:00:48,460
我们配完以后呢

26
00:00:48,460 --> 00:00:50,560
需要怎么样找到webpack配置文件

27
00:00:50,560 --> 00:00:53,160
需要再怎么样配上一个这样的loader是吧

28
00:00:53,160 --> 00:00:53,760
module

29
00:00:53,760 --> 00:00:55,860
module是吧

30
00:00:55,860 --> 00:00:56,500
完了这里面呢

31
00:00:56,500 --> 00:00:57,620
我来个对象

32
00:00:57,620 --> 00:00:58,600
我们需要呢

33
00:00:58,600 --> 00:00:59,700
配置一些规则

34
00:00:59,860 --> 00:01:01,120
完了去匹配什么呢

35
00:01:01,120 --> 00:01:01,860
test

36
00:01:01,860 --> 00:01:03,800
比如说用这个gs文件

37
00:01:03,800 --> 00:01:05,860
用这个gs文件

38
00:01:05,860 --> 00:01:11,560
第二gs完了结尾的完了这里面放到里面去啊

39
00:01:11,560 --> 00:01:13,200
完了我们用字一下对吧

40
00:01:13,200 --> 00:01:14,400
loadloader的话呢

41
00:01:14,400 --> 00:01:15,960
我们需要把那个mini

42
00:01:15,960 --> 00:01:16,560
对吧

43
00:01:16,560 --> 00:01:17,760
light mini

44
00:01:17,760 --> 00:01:21,020
的css的s track

45
00:01:21,020 --> 00:01:23,960
一 track的plugin拿进来

46
00:01:23,960 --> 00:01:25,160
等于require我说了

47
00:01:25,160 --> 00:01:26,660
他既有我们的loader的用法

48
00:01:26,660 --> 00:01:27,660
又有插件的用法

49
00:01:27,660 --> 00:01:28,000
所以呢

50
00:01:28,000 --> 00:01:29,360
e 的时候要把它

51
00:01:29,360 --> 00:01:34,600
mini css-css-as tract对吧

52
00:01:34,600 --> 00:01:35,300
plugin

53
00:01:35,300 --> 00:01:39,600
这里呢把这插件用上是吧叫 new 这个插件

54
00:01:39,600 --> 00:01:41,760
先把这个loader放上去吧

55
00:01:41,760 --> 00:01:43,300
他点什么loader

56
00:01:43,300 --> 00:01:44,000
完了之后呢

57
00:01:44,000 --> 00:01:46,100
他还需要一个叫 css loader

58
00:01:46,100 --> 00:01:47,000
完了之后呢

59
00:01:47,000 --> 00:01:49,700
我们这里面匹配的应该是 css 刚写错了

60
00:01:49,700 --> 00:01:50,900
完了在这插件里面呢

61
00:01:50,900 --> 00:01:53,700
也需要加一个 new 这个插件完了呢

62
00:01:53,700 --> 00:01:54,900
去怎么样执行

63
00:01:54,900 --> 00:01:56,800
里面你需要给个 fail name

64
00:01:56,900 --> 00:02:01,800
比如后面呢我就给他一个叫比如说叫慢点css的啊慢点css

65
00:02:01,800 --> 00:02:04,700
完了之后呢把它拿过来

66
00:02:04,700 --> 00:02:07,100
我们把第四目录啊先整个干掉啊

67
00:02:07,100 --> 00:02:09,500
完了咱去重新生成一下看看这效果

68
00:02:09,500 --> 00:02:13,100
我在这里呢npm对吧npxvpeg

69
00:02:13,100 --> 00:02:15,400
你发现现在这个文件应该出来几个了

70
00:02:15,400 --> 00:02:17,900
是不是应该至少有三四个是吧三四个

71
00:02:17,900 --> 00:02:19,700
大家看看啊

72
00:02:19,700 --> 00:02:22,800
那这里面是不是有帮到index list main 四个

73
00:02:22,800 --> 00:02:25,400
但是有的时候我们上线的时候并不希望怎么样

74
00:02:25,400 --> 00:02:26,900
是不是把这个文件都部署到

75
00:02:26,900 --> 00:02:28,800
有的可能有需要是分开处理

76
00:02:28,800 --> 00:02:30,500
但是有的时候我们可能希望

77
00:02:30,500 --> 00:02:32,000
直接把这文件放到一个服务器上

78
00:02:32,000 --> 00:02:33,300
这些都跑了是吧

79
00:02:33,300 --> 00:02:34,900
我们只想把itml放进去

80
00:02:34,900 --> 00:02:37,000
但是我希望把这个css

81
00:02:37,000 --> 00:02:38,100
还有gs怎么样

82
00:02:38,100 --> 00:02:39,100
内连到我们里面

83
00:02:39,100 --> 00:02:40,300
相当于这里面改造什么

84
00:02:40,300 --> 00:02:41,300
应该变成style

85
00:02:41,300 --> 00:02:43,000
哎这个里面呢

86
00:02:43,000 --> 00:02:44,400
我们需要把这个东西呢

87
00:02:44,400 --> 00:02:45,400
变成我们这样一个

88
00:02:45,400 --> 00:02:46,800
比如就是一个gs文本

89
00:02:46,800 --> 00:02:48,400
那这样的放到上面去可能怎么样

90
00:02:48,400 --> 00:02:49,100
更加方便

91
00:02:49,100 --> 00:02:50,400
哎不想发rt请求

92
00:02:50,400 --> 00:02:51,700
因为可能这个逻辑怎么样

93
00:02:51,700 --> 00:02:53,200
非常小非常容易

94
00:02:53,200 --> 00:02:54,900
那这时候呢我们需要怎么做呢

95
00:02:54,900 --> 00:02:57,240
我们还是需要用一个插件来转化

96
00:02:57,240 --> 00:02:58,560
那这个插件刚才说了

97
00:02:58,560 --> 00:02:59,400
就叫内联

98
00:02:59,400 --> 00:03:01,040
就是把另一个标签变成style

99
00:03:01,040 --> 00:03:04,140
script 变成一个什么脚本插件来直接

100
00:03:04,140 --> 00:03:06,340
来这里呢我们就写这样个插件啊

101
00:03:06,340 --> 00:03:07,160
在这新建一个

102
00:03:07,160 --> 00:03:08,900
叫一烂对吧

103
00:03:08,900 --> 00:03:09,540
sauce

104
00:03:09,540 --> 00:03:10,460
plugin

105
00:03:10,460 --> 00:03:11,200
叫什么意思呢

106
00:03:11,200 --> 00:03:13,840
就是我们内联资源的一个插件

107
00:03:13,840 --> 00:03:15,440
那好了一样class呢

108
00:03:15,440 --> 00:03:16,600
我们就叫他一烂

109
00:03:16,600 --> 00:03:18,560
一烂对吧

110
00:03:18,560 --> 00:03:19,200
sauce

111
00:03:19,200 --> 00:03:19,960
plugin

112
00:03:19,960 --> 00:03:20,960
完了这里呢

113
00:03:20,960 --> 00:03:22,960
我需要把这样一个方法导出去

114
00:03:22,960 --> 00:03:23,500
对吧

115
00:03:23,500 --> 00:03:25,600
等于inline source plugin

116
00:03:25,600 --> 00:03:27,300
哎他需要怎么样

117
00:03:27,300 --> 00:03:28,060
就是说白了

118
00:03:28,060 --> 00:03:29,500
就是把我们的对吧

119
00:03:29,500 --> 00:03:32,140
把标签把外链的标签对吧

120
00:03:32,140 --> 00:03:33,360
把外链的

121
00:03:33,360 --> 00:03:35,400
链接的链是吧

122
00:03:35,400 --> 00:03:37,040
把外链的标签对吧

123
00:03:37,040 --> 00:03:38,700
变成什么变成内链对吧

124
00:03:38,700 --> 00:03:39,500
标签

125
00:03:39,500 --> 00:03:41,040
变成

126
00:03:41,040 --> 00:03:42,760
内链的是吧

127
00:03:42,760 --> 00:03:43,760
内链的标签

128
00:03:43,760 --> 00:03:47,100
内链的标签

129
00:03:47,100 --> 00:03:48,600
那好了

130
00:03:48,600 --> 00:03:50,700
那这里面我们就很容易了是吧

131
00:03:50,700 --> 00:03:51,560
那怎么去做呢

132
00:03:51,560 --> 00:03:52,460
首先还是一样

133
00:03:52,460 --> 00:03:54,100
他里面你肯定要跟人家说

134
00:03:54,100 --> 00:03:55,420
哎什么样的标签那点

135
00:03:55,420 --> 00:03:57,700
你不能说我把这个什么list.md

136
00:03:57,700 --> 00:03:59,260
万一我虽然引了个别的呢对吧

137
00:03:59,260 --> 00:04:00,100
引了个接子呢

138
00:04:00,100 --> 00:04:01,360
你不能练在一起去是吧

139
00:04:01,360 --> 00:04:03,960
所以这里面我也稍微就要配置一下吧

140
00:04:03,960 --> 00:04:05,500
比如说先用这个插件

141
00:04:05,500 --> 00:04:07,860
我需要先把这插件引进来

142
00:04:07,860 --> 00:04:09,800
import我就叫他inlan

143
00:04:09,800 --> 00:04:11,520
当然了这里面我还是应该用

144
00:04:11,520 --> 00:04:12,760
common gsu 语法

145
00:04:12,760 --> 00:04:13,860
let inlan

146
00:04:13,860 --> 00:04:15,720
inlan对吧

147
00:04:15,720 --> 00:04:17,300
sauce plug-in

148
00:04:17,300 --> 00:04:18,420
等于require

149
00:04:18,420 --> 00:04:19,920
我们就这样写inlan

150
00:04:19,920 --> 00:04:20,200
对吧

151
00:04:20,200 --> 00:04:22,160
当然了是plug-in下的是吧

152
00:04:22,460 --> 00:04:24,560
扑拉个印象的一类挿扫拉给

153
00:04:24,560 --> 00:04:25,620
往来这里面呢

154
00:04:25,620 --> 00:04:27,920
我们可以直接去牛一下这个茶届啊

155
00:04:27,920 --> 00:04:29,760
在这里来给牛他

156
00:04:29,760 --> 00:04:33,360
牛我们的一类挿扫拉给你

157
00:04:33,360 --> 00:04:35,120
完了并且呢里面哎呦

158
00:04:35,120 --> 00:04:36,700
这里面还需要来个对象是吧

159
00:04:36,700 --> 00:04:37,820
来个对象

160
00:04:37,820 --> 00:04:39,020
是行的时候

161
00:04:39,020 --> 00:04:41,460
成个参数里面呢也一样应该有什么呢

162
00:04:41,460 --> 00:04:42,760
有这样一个配置对吧

163
00:04:42,760 --> 00:04:44,220
比如说我跟他说一声

164
00:04:44,220 --> 00:04:46,600
哎里面你要匹配到了所有以什么呢

165
00:04:46,600 --> 00:04:47,360
以这个

166
00:04:47,360 --> 00:04:48,260
gs对吧

167
00:04:48,260 --> 00:04:51,200
或者是ccss的才需要怎么去改

168
00:04:51,200 --> 00:04:52,700
比如这里来着 gs

169
00:04:52,700 --> 00:04:54,700
或者 css

170
00:04:54,700 --> 00:04:55,640
哎这样的

171
00:04:55,640 --> 00:04:56,540
因为有可能啊

172
00:04:56,540 --> 00:04:57,600
我们写的这个link

173
00:04:57,600 --> 00:04:58,740
有可能他里面会一些

174
00:04:58,740 --> 00:04:59,540
比如接生啊

175
00:04:59,540 --> 00:05:00,540
这也是有可能的是吧

176
00:05:00,540 --> 00:05:02,100
那这东西你肯定不要内联了

177
00:05:02,100 --> 00:05:03,400
这也是有特点的啊

178
00:05:03,400 --> 00:05:04,100
那现在好了

179
00:05:04,100 --> 00:05:05,200
我们有这样个插件

180
00:05:05,200 --> 00:05:07,440
那这插件怎么去写呢

181
00:05:07,440 --> 00:05:08,140
回到这里来

182
00:05:08,140 --> 00:05:11,100
他里面就应该有一个match对吧

183
00:05:11,100 --> 00:05:11,800
那这个match呢

184
00:05:11,800 --> 00:05:13,140
我就不去判断了

185
00:05:13,140 --> 00:05:15,200
我就让他this.match

186
00:05:15,200 --> 00:05:17,060
等于一个match是吧

187
00:05:17,060 --> 00:05:17,900
等于match

188
00:05:17,900 --> 00:05:19,400
他其实就是一个说白了

189
00:05:19,400 --> 00:05:20,460
就是个正责是吧

190
00:05:20,460 --> 00:05:21,100
正责

191
00:05:21,200 --> 00:05:23,720
那现在我们还是一样

192
00:05:23,720 --> 00:05:25,120
插件都需要一个方法

193
00:05:25,120 --> 00:05:25,840
叫apply

194
00:05:25,840 --> 00:05:26,780
它里面呢

195
00:05:26,780 --> 00:05:28,460
需要一个compiler属性

196
00:05:28,460 --> 00:05:28,900
对吧

197
00:05:28,900 --> 00:05:30,200
这compiler上肯定有些钩子

198
00:05:30,200 --> 00:05:30,640
哎

199
00:05:30,640 --> 00:05:31,760
那这钩子怎么用呢

200
00:05:31,760 --> 00:05:32,020
是吧

201
00:05:32,020 --> 00:05:33,220
这时候我们要看了

202
00:05:33,220 --> 00:05:34,740
是不是最后这个文件

203
00:05:34,740 --> 00:05:36,540
会被插到这个atml里面

204
00:05:36,540 --> 00:05:37,300
所以说呀

205
00:05:37,300 --> 00:05:38,020
这个配置啊

206
00:05:38,020 --> 00:05:39,100
肯定是谁来提供的呀

207
00:05:39,100 --> 00:05:40,100
肯定是我们知道

208
00:05:40,100 --> 00:05:41,300
插到atml里面

209
00:05:41,300 --> 00:05:42,140
完了之后怎么样

210
00:05:42,140 --> 00:05:42,900
有一些钩子吧

211
00:05:42,900 --> 00:05:44,540
那这时候我需要怎么做呢

212
00:05:44,540 --> 00:05:46,520
需要看一下我们这个atml

213
00:05:46,520 --> 00:05:47,960
webpack plug-in的用法

214
00:05:47,960 --> 00:05:49,380
这里面有明确啊

215
00:05:49,380 --> 00:05:50,060
就是要

216
00:05:50,060 --> 00:05:52,180
通过对吧

217
00:05:52,180 --> 00:05:54,260
这个web pack plug in

218
00:05:54,260 --> 00:05:56,560
对来实现这个功能

219
00:05:56,560 --> 00:05:58,200
实现这个功能

220
00:05:58,200 --> 00:06:02,580
来实现这个功能

221
00:06:02,580 --> 00:06:05,400
实现实现这个功能

222
00:06:05,400 --> 00:06:06,480
比如说这个功能呢

223
00:06:06,480 --> 00:06:07,120
是因为怎么样

224
00:06:07,120 --> 00:06:09,340
是因为当我们把它插到标签里面

225
00:06:09,340 --> 00:06:10,140
插到一天庙里去了

226
00:06:10,140 --> 00:06:11,260
我才能去干某些事

227
00:06:11,260 --> 00:06:11,540
是吧

228
00:06:11,540 --> 00:06:12,840
那这时候咱看看吧

229
00:06:12,840 --> 00:06:14,940
他也会给我们这样一个提供的写法

230
00:06:14,940 --> 00:06:16,420
比如说我可以访问

231
00:06:16,420 --> 00:06:17,980
叫npm jsoig

232
00:06:17,980 --> 00:06:18,840
是吧

233
00:06:18,840 --> 00:06:20,220
再发问一下

234
00:06:20,220 --> 00:06:22,680
这里面我们就要搜叫什么

235
00:06:22,680 --> 00:06:25,080
叫atml-webpack-plugin

236
00:06:25,080 --> 00:06:26,020
ok

237
00:06:26,020 --> 00:06:27,880
所以他会告诉我们这个插件

238
00:06:27,880 --> 00:06:29,000
其实刚刚更新了

239
00:06:29,000 --> 00:06:30,940
我们需要安装这样一个测试版本

240
00:06:30,940 --> 00:06:31,560
你看这写着

241
00:06:31,560 --> 00:06:32,860
我安装的时候在这

242
00:06:32,860 --> 00:06:34,660
这个不是最新的

243
00:06:34,660 --> 00:06:36,180
点过来

244
00:06:36,180 --> 00:06:38,040
稍等

245
00:06:38,040 --> 00:06:40,340
他现在说了这个版本的刚刚更新

246
00:06:40,340 --> 00:06:42,180
你可以通过at next来安装

247
00:06:42,180 --> 00:06:43,740
这里面为了统一一下

248
00:06:43,740 --> 00:06:45,600
我就直接再这样加一下

249
00:06:45,600 --> 00:06:47,140
防止这个api对不上

250
00:06:47,140 --> 00:06:49,620
因为它现在的这个文档都更新到最新了

251
00:06:49,620 --> 00:06:53,520
所以这里面我就直接ITNEX这里面直接安装

252
00:06:53,520 --> 00:06:55,640
往来用法呢

253
00:06:55,640 --> 00:06:56,260
它这是什么样呢

254
00:06:56,260 --> 00:06:57,260
其实下面有啊

255
00:06:57,260 --> 00:06:58,240
我们要干嘛呢

256
00:06:58,240 --> 00:07:00,160
是不是要实现修改标签啊

257
00:07:00,160 --> 00:07:03,120
那这里面它就给你提供了一些它自身的构子啊

258
00:07:03,120 --> 00:07:05,980
就是说它在我们这个原有的基础上又封装了一些构子

259
00:07:05,980 --> 00:07:07,100
你看它在地面有

260
00:07:07,100 --> 00:07:08,160
这是它一个流程图

261
00:07:08,160 --> 00:07:10,040
下面呢就有一个叫什么

262
00:07:10,040 --> 00:07:11,320
就是它自己的构子

263
00:07:11,320 --> 00:07:12,000
这什么意思叫

264
00:07:12,000 --> 00:07:15,660
在什么资源标签生成之前

265
00:07:15,660 --> 00:07:16,960
可以干这事是吧

266
00:07:16,960 --> 00:07:17,720
还有什么呢

267
00:07:17,720 --> 00:07:20,280
在这个什么什么修改资源标签

268
00:07:20,280 --> 00:07:22,900
这个呢是修改资源标签组

269
00:07:22,900 --> 00:07:23,740
这这表示什么

270
00:07:23,740 --> 00:07:25,140
是不是资源有很多呀

271
00:07:25,140 --> 00:07:26,840
那我们肯定有GS有CSS

272
00:07:26,840 --> 00:07:27,820
那肯定应该就是他

273
00:07:27,820 --> 00:07:30,100
那这里面怎么去写呢

274
00:07:30,100 --> 00:07:31,140
就说了哎你要写插件

275
00:07:31,140 --> 00:07:31,940
你怎么做呢

276
00:07:31,940 --> 00:07:32,720
说你可以啊

277
00:07:32,720 --> 00:07:36,020
先把我们这样一个atmlwebpackpluggy拿进来

278
00:07:36,020 --> 00:07:37,900
好我就往这写了啊

279
00:07:37,900 --> 00:07:38,960
这是人家提供的

280
00:07:38,960 --> 00:07:41,380
拿起来以后还没完事

281
00:07:41,380 --> 00:07:42,240
你需要干嘛呢

282
00:07:42,240 --> 00:07:44,680
是不是可以去通过他上面怎么样

283
00:07:44,680 --> 00:07:47,220
先用complication这个对象去绑一个插件

284
00:07:47,220 --> 00:07:47,720
一个名字

285
00:07:47,720 --> 00:07:48,580
需要拿到什么对象

286
00:07:48,580 --> 00:07:50,180
需要拿到这个complication

287
00:07:50,180 --> 00:07:51,500
明确吧

288
00:07:51,500 --> 00:07:53,480
这complication就是当前编义的资源

289
00:07:53,480 --> 00:07:55,200
所以这里面我就需要怎么样

290
00:07:55,200 --> 00:07:56,580
是不是把它粘过来

291
00:07:56,580 --> 00:07:57,640
干掉了

292
00:07:57,640 --> 00:07:59,080
这不要对吧

293
00:07:59,080 --> 00:07:59,360
OK

294
00:07:59,360 --> 00:08:01,060
就compile上绑这样一个实践

295
00:08:01,060 --> 00:08:03,060
同样这里面来个回调

296
00:08:03,060 --> 00:08:06,040
插件的名字我就改成它了

297
00:08:06,040 --> 00:08:07,000
看这方面一点

298
00:08:07,000 --> 00:08:09,840
有了complication干嘛

299
00:08:09,840 --> 00:08:10,660
人说了

300
00:08:10,660 --> 00:08:12,380
他需要通过这个插件

301
00:08:12,380 --> 00:08:14,660
去拿到complication的一些钩子

302
00:08:14,660 --> 00:08:16,780
你说我们atmlwipy plug-in

303
00:08:16,780 --> 00:08:19,420
它内置了一些构子是基于complication的

304
00:08:19,420 --> 00:08:20,940
好了这里面一样

305
00:08:20,940 --> 00:08:22,000
拿过来

306
00:08:22,000 --> 00:08:25,340
但是这时候我们要干什么事

307
00:08:25,340 --> 00:08:27,020
是不是要修不是发射之前

308
00:08:27,020 --> 00:08:28,180
而是应该干嘛

309
00:08:28,180 --> 00:08:31,460
是不是应该叫我们的alter assess target groups

310
00:08:31,460 --> 00:08:34,800
就是在我们修改我们资源的标签组

311
00:08:34,800 --> 00:08:36,180
这里面拿过来

312
00:08:36,180 --> 00:08:38,080
这样一个方法

313
00:08:38,080 --> 00:08:39,060
它也是个实践

314
00:08:39,060 --> 00:08:40,380
这实践应该是什么

315
00:08:40,380 --> 00:08:41,500
你看它是异补的

316
00:08:41,500 --> 00:08:42,180
明确了

317
00:08:42,180 --> 00:08:42,980
它可以怎么办

318
00:08:42,980 --> 00:08:44,560
是不是在这里叫type sync

319
00:08:44,560 --> 00:08:46,360
OK就一样吧

320
00:08:46,360 --> 00:08:47,360
来个第二个type

321
00:08:47,360 --> 00:08:48,580
有点长是吧

322
00:08:48,580 --> 00:08:50,940
在这里面呢又是一个回调

323
00:08:50,940 --> 00:08:52,560
那参数呢我就随便写了

324
00:08:52,560 --> 00:08:53,380
比如就叫outer

325
00:08:53,380 --> 00:08:54,480
plugin

326
00:08:54,480 --> 00:08:56,660
后面呢也是个回调

327
00:08:56,660 --> 00:08:58,220
各式化一下是吧

328
00:08:58,220 --> 00:08:59,280
往这这里面呢

329
00:08:59,280 --> 00:09:00,220
我们放到参数呢

330
00:09:00,220 --> 00:09:00,920
第一个是数据

331
00:09:00,920 --> 00:09:02,360
第二个就是我们所谓的cb

332
00:09:02,360 --> 00:09:03,800
你看这是他这么写的

333
00:09:03,800 --> 00:09:04,460
data cb

334
00:09:04,460 --> 00:09:05,880
那咱来看一下吧

335
00:09:05,880 --> 00:09:06,980
如果成功以后呢

336
00:09:06,980 --> 00:09:08,680
你还需要把这个东西怎么样翻回去

337
00:09:08,680 --> 00:09:09,060
对吧

338
00:09:09,060 --> 00:09:10,360
这里一样

339
00:09:10,360 --> 00:09:11,380
这里

340
00:09:11,380 --> 00:09:12,740
那再来看看吧

341
00:09:12,740 --> 00:09:13,640
那现在好了

342
00:09:13,640 --> 00:09:14,600
我们就已经怎么样

343
00:09:14,600 --> 00:09:15,820
知道怎么去修改标签了

344
00:09:15,820 --> 00:09:17,100
那修改标签的时候

345
00:09:17,100 --> 00:09:18,060
我们要先拿到什么

346
00:09:18,060 --> 00:09:19,940
是不是拿到当前我们所有的资源

347
00:09:19,940 --> 00:09:21,340
你看里面怎么做的

348
00:09:21,340 --> 00:09:22,640
它里面有什么data.atml

349
00:09:22,640 --> 00:09:23,600
咱就不公管了

350
00:09:23,600 --> 00:09:24,560
咱去怎么做

351
00:09:24,560 --> 00:09:26,100
在这里打印一下

352
00:09:26,100 --> 00:09:26,300
是吧

353
00:09:26,300 --> 00:09:26,640
Kontolog

354
00:09:26,640 --> 00:09:27,780
我们看一下这个data

355
00:09:27,780 --> 00:09:29,420
这个data就是它当前

356
00:09:29,420 --> 00:09:31,180
我们要插入的标签的数据

357
00:09:31,180 --> 00:09:32,640
这个数据可以怎么样

358
00:09:32,640 --> 00:09:33,260
进行改写

359
00:09:33,260 --> 00:09:34,660
比如把它的script

360
00:09:34,660 --> 00:09:36,080
变成我们的内置的script

361
00:09:36,080 --> 00:09:37,940
完了hati这个style

362
00:09:37,940 --> 00:09:38,740
我们把它变成

363
00:09:38,740 --> 00:09:39,820
不是link变成style

364
00:09:39,820 --> 00:09:40,680
这样来做

365
00:09:40,680 --> 00:09:42,060
这时候一样

366
00:09:42,060 --> 00:09:43,600
我把它执行一下

367
00:09:43,600 --> 00:09:44,680
npx webpack

368
00:09:44,680 --> 00:09:47,440
网络里面就生成了

369
00:09:47,440 --> 00:09:49,300
你看看它是不是就是这样一个所谓的对象

370
00:09:49,300 --> 00:09:50,700
对象里面有什么

371
00:09:50,700 --> 00:09:51,960
有一个head有个body

372
00:09:51,960 --> 00:09:53,540
这个head也指的就是你看

373
00:09:53,540 --> 00:09:55,620
是不是就是我们那个tugname link

374
00:09:55,620 --> 00:09:56,320
就link标签

375
00:09:56,320 --> 00:09:59,080
body呢指的就是我们这样一个body tags

376
00:09:59,080 --> 00:09:59,440
是吧

377
00:09:59,440 --> 00:10:00,700
那我们就应该怎么样

378
00:10:00,700 --> 00:10:02,040
把它进行处理

379
00:10:02,040 --> 00:10:04,000
处理成我们一个所谓的style标签

380
00:10:04,000 --> 00:10:05,340
这个处理还是script

381
00:10:05,340 --> 00:10:07,400
但是变成什么script内容标签

382
00:10:07,400 --> 00:10:08,980
那好了

383
00:10:08,980 --> 00:10:10,200
那我们现在怎么样呢

384
00:10:10,200 --> 00:10:11,420
是不是就要处理这个数据

385
00:10:11,420 --> 00:10:12,120
那好了

386
00:10:12,120 --> 00:10:13,420
我就写个方法吧

387
00:10:13,420 --> 00:10:15,360
叫process叫tags

388
00:10:15,360 --> 00:10:17,040
处理所有的标签

389
00:10:17,040 --> 00:10:18,440
这里呢叫like this

390
00:10:18,440 --> 00:10:19,920
完了里面呢

391
00:10:19,920 --> 00:10:21,380
我就把这数据往里一丢

392
00:10:21,380 --> 00:10:23,600
那返回的就应该是一个新的什么

393
00:10:23,600 --> 00:10:24,480
数据

394
00:10:24,480 --> 00:10:25,500
新的数据里面

395
00:10:25,500 --> 00:10:26,540
是不是就能运运可以认为

396
00:10:26,540 --> 00:10:27,720
就变成了一个这样的对象

397
00:10:27,720 --> 00:10:28,380
把它改好了

398
00:10:28,380 --> 00:10:30,060
那这里面用的时候呢

399
00:10:30,060 --> 00:10:31,000
可能还需要什么呢

400
00:10:31,000 --> 00:10:33,000
用到我们当前这样一个文件的资源

401
00:10:33,000 --> 00:10:34,340
因为我要拿到它什么

402
00:10:34,340 --> 00:10:35,840
是不是当前比如atml

403
00:10:35,840 --> 00:10:37,000
里面对应的这个内容

404
00:10:37,000 --> 00:10:38,180
完了当前这个

405
00:10:38,180 --> 00:10:39,840
引的这个link标签里面丢的内容

406
00:10:39,840 --> 00:10:41,880
所以这肯定需要一个complication

407
00:10:41,880 --> 00:10:42,220
对吧

408
00:10:42,220 --> 00:10:43,360
也传进去吧

409
00:10:43,360 --> 00:10:44,820
因为我们都知道

410
00:10:44,820 --> 00:10:47,480
那个资源是不是都放到这个对象的什么属性上了

411
00:10:47,480 --> 00:10:48,180
assess上

412
00:10:48,180 --> 00:10:49,220
这样的

413
00:10:49,220 --> 00:10:51,200
那现在我们就写这样一个方法

414
00:10:51,200 --> 00:10:51,440
对吧

415
00:10:51,440 --> 00:10:52,460
这个方法刚才说了

416
00:10:52,460 --> 00:10:53,180
只是干嘛呢

417
00:10:53,180 --> 00:10:53,860
叫处理

418
00:10:53,860 --> 00:10:55,100
在这描述一下

419
00:10:55,100 --> 00:10:55,360
对吧

420
00:10:55,360 --> 00:10:56,100
叫处理

421
00:10:56,100 --> 00:10:56,840
对吧

422
00:10:56,840 --> 00:10:57,400
处理

423
00:10:57,400 --> 00:10:59,400
引入标签的数据

424
00:10:59,400 --> 00:11:00,100
对吧

425
00:11:00,100 --> 00:11:01,800
标签的数据

426
00:11:01,800 --> 00:11:02,680
那好了

427
00:11:02,680 --> 00:11:03,820
我们在这拿到

428
00:11:03,820 --> 00:11:04,640
第一个呢是date

429
00:11:04,640 --> 00:11:05,440
第二个呢

430
00:11:05,440 --> 00:11:06,680
就是我们的complication

431
00:11:06,680 --> 00:11:07,000
是吧

432
00:11:07,000 --> 00:11:08,080
complication

433
00:11:08,080 --> 00:11:09,140
完了此时

434
00:11:09,140 --> 00:11:10,480
我们可以打印一下这个date

435
00:11:10,480 --> 00:11:11,000
看看这date

436
00:11:11,000 --> 00:11:11,700
刚才看到了

437
00:11:11,700 --> 00:11:12,480
我就不去打印了

438
00:11:12,480 --> 00:11:13,260
完了里面呢

439
00:11:13,260 --> 00:11:14,220
我们可以怎么做呢

440
00:11:14,220 --> 00:11:15,700
是不是要一下这个height tags

441
00:11:15,700 --> 00:11:16,600
还有这个body tags

442
00:11:16,600 --> 00:11:19,360
这里面放的就是一个个的什么标签是吧

443
00:11:19,360 --> 00:11:19,820
那好

444
00:11:19,820 --> 00:11:20,720
那就跟他一样

445
00:11:20,720 --> 00:11:22,240
我在这里呢

446
00:11:22,240 --> 00:11:22,760
我就let

447
00:11:22,760 --> 00:11:24,640
比如说我需要一个head tags

448
00:11:24,640 --> 00:11:26,080
完了它是空数组

449
00:11:26,080 --> 00:11:26,840
因为干嘛呀

450
00:11:26,840 --> 00:11:28,280
我是不是要把它进行替换呀

451
00:11:28,280 --> 00:11:29,860
那同样那body tags呢

452
00:11:29,860 --> 00:11:31,320
我也需要这样做啊

453
00:11:31,320 --> 00:11:32,780
let一个body tags

454
00:11:32,780 --> 00:11:35,500
tags当一个空数组

455
00:11:35,500 --> 00:11:36,460
完了此时呢

456
00:11:36,460 --> 00:11:38,080
我就循环数组里的tags

457
00:11:38,080 --> 00:11:39,260
因为tags可能有多个

458
00:11:39,260 --> 00:11:40,040
那这里呢

459
00:11:40,040 --> 00:11:41,080
我们就来个for each

460
00:11:41,080 --> 00:11:42,960
那这里面拿到的就是什么

461
00:11:42,960 --> 00:11:46,160
就是我们每一个的这个叫嗨的什么他

462
00:11:46,160 --> 00:11:48,840
他他我这时候呢

463
00:11:48,840 --> 00:11:51,800
我们把每个标签再去传一个个的单独处理

464
00:11:51,800 --> 00:11:55,420
也说我们的这嗨他个和这个玻璃他个其实功能是一样的是吧

465
00:11:55,420 --> 00:11:57,260
那这里面我再封装一个方法吧

466
00:11:57,260 --> 00:12:01,820
为了方便我就写了个process叫他个处理某一个标签对吧

467
00:12:01,820 --> 00:12:03,620
这时候就不用复述了单数

468
00:12:03,620 --> 00:12:04,760
我把它呢传进去

469
00:12:04,760 --> 00:12:07,320
那同样的我把这completion呀就接转一下传

470
00:12:07,320 --> 00:12:08,660
你甭管我怎么写是吧

471
00:12:08,660 --> 00:12:09,760
反正我这样一做

472
00:12:09,760 --> 00:12:11,520
那做完以后我处理完以后

473
00:12:11,520 --> 00:12:12,820
我说把处理完后的

474
00:12:12,820 --> 00:12:14,620
再放到嗨的tags就可以了

475
00:12:14,620 --> 00:12:15,320
那好

476
00:12:15,320 --> 00:12:17,420
是他第二扑式

477
00:12:17,420 --> 00:12:19,520
放进去

478
00:12:19,520 --> 00:12:20,620
那同样呢

479
00:12:20,620 --> 00:12:21,320
最后还有什么呢

480
00:12:21,320 --> 00:12:22,420
是不是包的也要处理啊

481
00:12:22,420 --> 00:12:22,920
那好

482
00:12:22,920 --> 00:12:24,820
那把包的怎么样也拷贝一份是吧

483
00:12:24,820 --> 00:12:26,520
哎这里拿过来啊

484
00:12:26,520 --> 00:12:29,120
这是包的tags

485
00:12:29,120 --> 00:12:31,520
那这个呢是包的tags

486
00:12:31,520 --> 00:12:33,620
我要for each完了每一个对吧

487
00:12:33,620 --> 00:12:34,620
我拿到以后呢

488
00:12:34,620 --> 00:12:37,020
是不是这里面应该放的就是包的tags

489
00:12:37,020 --> 00:12:37,520
对吧

490
00:12:37,520 --> 00:12:39,620
我要把包的tags呢一个个的放进去

491
00:12:39,620 --> 00:12:40,720
那此时啊

492
00:12:40,720 --> 00:12:41,380
我是不是就产

493
00:12:41,380 --> 00:12:42,060
你可以认为对吧

494
00:12:42,060 --> 00:12:43,920
处理完以后是不是就是两个新的了

495
00:12:43,920 --> 00:12:45,320
但这个方法现在没有啊

496
00:12:45,320 --> 00:12:46,420
那处理完新的以后

497
00:12:46,420 --> 00:12:47,460
我是不是要return

498
00:12:47,460 --> 00:12:48,320
完了怎么办呢

499
00:12:48,320 --> 00:12:50,680
是不是拿新的这个包的他跟害的他个

500
00:12:50,680 --> 00:12:52,760
把原来的data中的覆盖掉啊

501
00:12:52,760 --> 00:12:53,080
那好

502
00:12:53,080 --> 00:12:54,620
我可以怎么做是不是点点点

503
00:12:54,620 --> 00:12:55,320
data

504
00:12:55,320 --> 00:12:57,620
完了把这个嗨的他个是数组放在这

505
00:12:57,620 --> 00:12:59,920
包的他是放在这

506
00:12:59,920 --> 00:13:02,320
完最后他返回的是不是就是一个新的data

507
00:13:02,320 --> 00:13:04,220
那新的data是不是就把它放到了哪去啊

508
00:13:04,220 --> 00:13:05,780
是最终完成了

509
00:13:05,780 --> 00:13:07,920
那现在我们要唯一写的一个功能就是什么了

510
00:13:07,920 --> 00:13:10,220
是不是就这个process他改啊

511
00:13:10,220 --> 00:13:11,080
他是干嘛的呢

512
00:13:11,080 --> 00:13:13,580
就是处理某一个标签的对吧

513
00:13:13,580 --> 00:13:18,120
处理某一个标签的

514
00:13:18,120 --> 00:13:19,940
完了他的参数是什么呢

515
00:13:19,940 --> 00:13:21,360
第一个呢就是标签就是他个

516
00:13:21,360 --> 00:13:22,540
完了第二个呢

517
00:13:22,540 --> 00:13:24,140
其实放的就是我们的这个compleation

518
00:13:24,140 --> 00:13:26,440
哎那咱看看这个他个长啥样是不是

519
00:13:26,440 --> 00:13:28,480
再来明确一下是吧他个

520
00:13:28,480 --> 00:13:29,740
好色了他

521
00:13:29,740 --> 00:13:33,860
那这时候他个应该就是刚才对象里的这一坨是吧

522
00:13:33,860 --> 00:13:35,360
那好再来试一下

523
00:13:35,360 --> 00:13:36,520
外派

524
00:13:36,520 --> 00:13:39,460
完了里面拿到的呢

525
00:13:39,460 --> 00:13:40,140
果然包错了

526
00:13:40,140 --> 00:13:40,700
咱就不理他

527
00:13:40,700 --> 00:13:41,700
因为我没有进行处理

528
00:13:41,700 --> 00:13:43,120
这时候是不是我就

529
00:13:43,120 --> 00:13:44,220
你看他告诉我

530
00:13:44,220 --> 00:13:45,120
这个是个另一个标签

531
00:13:45,120 --> 00:13:45,960
它是个空标签

532
00:13:45,960 --> 00:13:47,000
属性的叫啥

533
00:13:47,000 --> 00:13:49,280
是不是HIF是慢点的CSS

534
00:13:49,280 --> 00:13:51,040
那我要来看一看

535
00:13:51,040 --> 00:13:52,320
它的属性的HIF

536
00:13:52,320 --> 00:13:53,240
这个东西

537
00:13:53,240 --> 00:13:54,840
它能不能匹配到我们的政责

538
00:13:54,840 --> 00:13:56,160
能匹配到在处理

539
00:13:56,160 --> 00:13:58,280
所以这里面我会这样做

540
00:13:58,280 --> 00:14:00,640
如果当前的什么Tag

541
00:14:00,640 --> 00:14:01,760
其实你看

542
00:14:01,760 --> 00:14:03,600
这个东西非常像我们的虚拟道

543
00:14:03,600 --> 00:14:05,240
这就是用它这样一个对象

544
00:14:05,240 --> 00:14:07,520
来描述我们这个道路的节点

545
00:14:07,520 --> 00:14:08,780
那我可以怎么做

546
00:14:08,780 --> 00:14:10,120
判断Tag的什么

547
00:14:10,120 --> 00:14:10,920
Tag name

548
00:14:10,920 --> 00:14:13,260
Tag的name

549
00:14:13,260 --> 00:14:14,740
Tag name

550
00:14:14,740 --> 00:14:18,180
看看它是不是什么东西

551
00:14:18,180 --> 00:14:19,260
是不是我们的link

552
00:14:19,260 --> 00:14:19,540
是吧

553
00:14:19,540 --> 00:14:19,840
好

554
00:14:19,840 --> 00:14:20,600
我再放在这

555
00:14:20,600 --> 00:14:21,200
link

556
00:14:21,200 --> 00:14:22,500
而如果是

557
00:14:22,500 --> 00:14:23,780
完了并且我再来

558
00:14:23,780 --> 00:14:25,620
并且当前我们说了

559
00:14:25,620 --> 00:14:27,740
这正则还得匹配到我们的它的属性

560
00:14:27,740 --> 00:14:28,280
对吧

561
00:14:28,280 --> 00:14:29,480
正则DR什么

562
00:14:29,480 --> 00:14:30,280
match

563
00:14:30,280 --> 00:14:31,980
这里面有点写的不太好

564
00:14:31,980 --> 00:14:33,420
叫叫r1G

565
00:14:33,420 --> 00:14:34,480
更好听一点

566
00:14:34,480 --> 00:14:35,240
r1G

567
00:14:35,240 --> 00:14:36,200
match

568
00:14:36,200 --> 00:14:37,180
匹配谁

569
00:14:37,180 --> 00:14:38,340
正则的话

570
00:14:38,340 --> 00:14:39,700
我用tess最好不用match

571
00:14:39,700 --> 00:14:40,700
是吧不用match

572
00:14:40,700 --> 00:14:42,700
那这里面我们一test的谁呢

573
00:14:42,700 --> 00:14:44,900
是不是当前的标签的属性啊

574
00:14:44,900 --> 00:14:46,900
是不是tug的attribute是吧

575
00:14:46,900 --> 00:14:49,440
tug的attribute

576
00:14:49,440 --> 00:14:51,060
往这里呢我再去点谁啊

577
00:14:51,060 --> 00:14:53,500
是不是点我们当前的这个hif是吧

578
00:14:53,500 --> 00:14:54,960
好来个hif

579
00:14:54,960 --> 00:14:57,200
那如果都匹配到了我要干什么事

580
00:14:57,200 --> 00:14:58,040
是不是该处理了

581
00:14:58,040 --> 00:14:59,260
那否则就不理他是吧

582
00:14:59,260 --> 00:14:59,960
那不理他的话呢

583
00:14:59,960 --> 00:15:01,940
就委呈一个原来叫啥啥样

584
00:15:01,940 --> 00:15:02,960
现在长啥样是吧

585
00:15:02,960 --> 00:15:04,200
那同样除了这个呢

586
00:15:04,200 --> 00:15:05,700
可能还有我们所谓的叫啥样

587
00:15:05,700 --> 00:15:06,900
那gs是吧

588
00:15:07,200 --> 00:15:09,840
gs 这里面就取的不是 hrf 了

589
00:15:09,840 --> 00:15:10,540
是吧

590
00:15:10,540 --> 00:15:11,440
你可以看一下

591
00:15:11,440 --> 00:15:14,460
这里面取的应该就是我们当前的 src

592
00:15:14,460 --> 00:15:15,640
把这个也改一下

593
00:15:15,640 --> 00:15:18,840
现在我们把 link 和 gs

594
00:15:18,840 --> 00:15:21,040
那不应该叫 gs 应该叫 script 是吧

595
00:15:21,040 --> 00:15:23,140
是不是两个标签就都处理好了

596
00:15:23,140 --> 00:15:24,960
那处理好以后我需要干嘛呢

597
00:15:24,960 --> 00:15:25,860
是不是来替换

598
00:15:25,860 --> 00:15:27,740
比如 tag name 要改名字 是吧

599
00:15:27,740 --> 00:15:28,140
那好了

600
00:15:28,140 --> 00:15:29,660
我在这里为了方便

601
00:15:29,660 --> 00:15:31,040
我再生成一个新的标签

602
00:15:31,040 --> 00:15:31,760
是吧

603
00:15:31,760 --> 00:15:32,540
新的标签

604
00:15:32,540 --> 00:15:33,160
tug

605
00:15:33,160 --> 00:15:34,140
名字别重复

606
00:15:34,140 --> 00:15:34,800
new tag

607
00:15:34,800 --> 00:15:35,300
是吧

608
00:15:35,300 --> 00:15:36,500
新的标签

609
00:15:36,500 --> 00:15:40,740
默认的没有完了我需要把这个new tag等于一个什么对象

610
00:15:40,740 --> 00:15:42,960
它里面应该也有一个叫tag内容属性

611
00:15:42,960 --> 00:15:44,360
但是他现在就不再是link了

612
00:15:44,360 --> 00:15:45,900
应该叫什么叫style

613
00:15:45,900 --> 00:15:49,260
同样这个东西还是一个script

614
00:15:49,260 --> 00:15:50,940
这倒不用管是吧这无所谓

615
00:15:50,940 --> 00:15:53,400
但是这里面我要把这个稍微改一改了

616
00:15:53,400 --> 00:15:54,560
我还需要干嘛呢

617
00:15:54,560 --> 00:15:56,060
是不是需要把这个慢点

618
00:15:56,060 --> 00:15:58,460
css对应的那个文件内容拿出来

619
00:15:58,460 --> 00:15:59,960
还有它的对应的内容拿出来

620
00:15:59,960 --> 00:16:01,160
那就是怎么拿呢

621
00:16:01,160 --> 00:16:02,740
我们需要拿到这个url是吧

622
00:16:02,740 --> 00:16:04,760
那好我就再声明一个叫url

623
00:16:04,760 --> 00:16:06,040
那这url长什么样呢

624
00:16:06,040 --> 00:16:10,140
是不是就应该等于我们当前的Tag的ertribute的HIF

625
00:16:10,140 --> 00:16:14,140
这个底下就应该是等于它的什么叫SRC

626
00:16:14,140 --> 00:16:15,840
路径是不是就有了

627
00:16:15,840 --> 00:16:17,940
那好了我在这里面来个分号是吧

628
00:16:17,940 --> 00:16:19,540
如果呀有路径

629
00:16:19,540 --> 00:16:22,240
有路径的话是不是说明这个文件匹配到了

630
00:16:22,240 --> 00:16:23,340
那匹配到以后

631
00:16:23,340 --> 00:16:26,840
我是不是要把这个new tag里面再给它是不是c点内容了

632
00:16:26,840 --> 00:16:30,440
那atml里面c内容是不是inneratmlc内容

633
00:16:30,440 --> 00:16:31,740
那内容长什么样呢

634
00:16:31,740 --> 00:16:34,040
是不是就是当前我们这个对吧

635
00:16:34,040 --> 00:16:35,640
文件的这个路径对吧

636
00:16:35,640 --> 00:16:38,640
路径呢是不是就是这个什么慢点cssbundle

637
00:16:38,640 --> 00:16:41,840
那我是不是可以通过我们的路径取到对应的资源

638
00:16:41,840 --> 00:16:45,840
那好是不是apply叫什么叫叫complayation对吧

639
00:16:45,840 --> 00:16:47,440
往里面取什么呢

640
00:16:47,440 --> 00:16:49,640
是不是取当前我们这样一个属性叫啥

641
00:16:49,640 --> 00:16:51,240
来这我找一下

642
00:16:51,240 --> 00:16:52,840
这里面就是ur

643
00:16:52,840 --> 00:16:56,440
那取到的时候是不是应该叫drassess下的是吧

644
00:16:56,440 --> 00:16:58,040
这个assess是所有的资源

645
00:16:58,040 --> 00:17:00,140
哎通过assess呢取url

646
00:17:00,140 --> 00:17:02,540
取到的是不是就是那个他对应的资源对象

647
00:17:02,540 --> 00:17:05,040
那对象里面是不是有个东西叫sauce

648
00:17:05,140 --> 00:17:06,100
取到就是他的什么

649
00:17:06,100 --> 00:17:08,140
是不是就是稳件的内容

650
00:17:08,140 --> 00:17:09,900
哎我来放到哪去呢

651
00:17:09,900 --> 00:17:10,900
放到对吧

652
00:17:10,900 --> 00:17:12,540
这个应的一天便要属性上对吧

653
00:17:12,540 --> 00:17:15,100
inner html 属性上

654
00:17:15,100 --> 00:17:17,400
属性上是吧

655
00:17:17,400 --> 00:17:18,100
属性上

656
00:17:18,100 --> 00:17:18,900
那现在好了

657
00:17:18,900 --> 00:17:20,600
那你说这个标签里面又多了个属性

658
00:17:20,600 --> 00:17:21,640
就是他的内容

659
00:17:21,640 --> 00:17:23,480
那现在我把这标签一返回

660
00:17:23,480 --> 00:17:24,900
那这个标签是不是就

661
00:17:24,900 --> 00:17:26,940
放到我们这个最终的结果里了

662
00:17:26,940 --> 00:17:28,100
那结果一看 ok

663
00:17:28,100 --> 00:17:29,640
他里面并没有什么外链

664
00:17:29,640 --> 00:17:30,440
只是一个标签

665
00:17:30,440 --> 00:17:31,640
标签里面也装了什么

666
00:17:31,640 --> 00:17:33,340
当然这里你可以写的全一点啊

667
00:17:33,340 --> 00:17:35,140
比如说当前也可以加些属性

668
00:17:35,140 --> 00:17:36,440
那你不加也行啊

669
00:17:36,440 --> 00:17:37,880
出规定了是可以的

670
00:17:37,880 --> 00:17:39,240
这里面要给他来个什么呢

671
00:17:39,240 --> 00:17:40,540
阿吹bos 有什么呢

672
00:17:40,540 --> 00:17:42,540
有这个比如说类型吧

673
00:17:42,540 --> 00:17:44,240
对吧叫type

674
00:17:44,240 --> 00:17:45,640
等于什么呢是吧

675
00:17:45,640 --> 00:17:47,040
啊 style 应该是他

676
00:17:47,040 --> 00:17:48,740
泰斯告什么 css 是吧

677
00:17:48,740 --> 00:17:50,440
那你不加也行啊还是那句话

678
00:17:50,440 --> 00:17:51,640
我区分一下吧

679
00:17:51,640 --> 00:17:53,640
那这里呢我也可以加一个是吧

680
00:17:53,640 --> 00:17:54,640
但没有分号啊

681
00:17:54,640 --> 00:17:55,940
我这里呢我也加上

682
00:17:55,940 --> 00:17:57,140
那他就应该是一个什么

683
00:17:57,140 --> 00:17:58,640
是不是额普来

684
00:17:58,640 --> 00:18:00,040
开始告什么呢

685
00:18:00,040 --> 00:18:00,840
javascript

686
00:18:00,840 --> 00:18:02,940
那这样就可以了

687
00:18:02,940 --> 00:18:04,340
现在我们是不是就实现了哎

688
00:18:04,340 --> 00:18:05,780
两个标签都可以处理

689
00:18:05,780 --> 00:18:06,340
完最后呢

690
00:18:06,340 --> 00:18:07,180
我把这东西怎么样

691
00:18:07,180 --> 00:18:08,540
是放到我们的atml里

692
00:18:08,540 --> 00:18:09,500
最后把它返回

693
00:18:09,500 --> 00:18:10,460
那返回以后呢

694
00:18:10,460 --> 00:18:11,260
是不是最终炫的

695
00:18:11,260 --> 00:18:13,380
就是我们新生成的这个标签了

696
00:18:13,380 --> 00:18:14,500
那咱来看一看吧

697
00:18:14,500 --> 00:18:16,340
是不是能不能实现我这个需求啊

698
00:18:16,340 --> 00:18:17,020
那在这里

699
00:18:17,020 --> 00:18:17,580
出列

700
00:18:17,580 --> 00:18:19,220
我来这

701
00:18:19,220 --> 00:18:19,980
npx

702
00:18:19,980 --> 00:18:20,500
wipike

703
00:18:20,500 --> 00:18:21,100
是吧

704
00:18:21,100 --> 00:18:23,060
现在我们应该就可以怎么样

705
00:18:23,060 --> 00:18:24,460
大包出来就是一个文件了

706
00:18:24,460 --> 00:18:24,900
你看

707
00:18:24,900 --> 00:18:26,580
是哦还是四个是吧

708
00:18:26,580 --> 00:18:28,460
那说明这个插件是没有用呢

709
00:18:28,460 --> 00:18:29,540
我来看一看啊

710
00:18:29,540 --> 00:18:30,260
new

711
00:18:30,260 --> 00:18:31,620
这个插件开始走了啊

712
00:18:31,620 --> 00:18:32,460
那走完以后呢

713
00:18:32,460 --> 00:18:33,740
我在这看一下效果

714
00:18:33,740 --> 00:18:35,180
这个data等于他是吧

715
00:18:35,180 --> 00:18:36,700
这里面我看看有没有问题

716
00:18:36,700 --> 00:18:39,980
在这儿coslog我们打印一下是吧

717
00:18:39,980 --> 00:18:41,980
coslog我打下这个对象

718
00:18:41,980 --> 00:18:43,780
看看是不是对象没有输出处理啊

719
00:18:43,780 --> 00:18:45,340
这里重新打印一下

720
00:18:45,340 --> 00:18:46,940
运行

721
00:18:46,940 --> 00:18:49,440
嗯稍等是吧

722
00:18:49,440 --> 00:18:51,180
我这里面呢出来这样一个对象了

723
00:18:51,180 --> 00:18:53,580
对象里面你看是不是哦是link是吧

724
00:18:53,580 --> 00:18:54,640
完了wire的处

725
00:18:54,640 --> 00:18:56,000
完这还是script的是吧

726
00:18:56,000 --> 00:18:58,780
好像没有生命成功是吧

727
00:18:58,780 --> 00:19:01,480
那咱来看看是不是这个正泽洗的有问题

728
00:19:01,480 --> 00:19:02,440
这里是吧

729
00:19:02,440 --> 00:19:03,580
说这里面没有走

730
00:19:03,580 --> 00:19:05,880
他的tugname等于link

731
00:19:05,880 --> 00:19:08,020
我在这里呢我匹配一下是吧

732
00:19:08,020 --> 00:19:10,720
这个叫rg.test是吧

733
00:19:10,720 --> 00:19:12,240
这个是没有成功啊

734
00:19:12,240 --> 00:19:13,740
哎稍微处理一下

735
00:19:13,740 --> 00:19:16,180
这里一样在运行

736
00:19:16,180 --> 00:19:19,080
看看哪的问题

737
00:19:19,080 --> 00:19:20,580
他告诉我第一个是true

738
00:19:20,580 --> 00:19:21,180
第二是false

739
00:19:21,180 --> 00:19:22,780
那就是有一个没进去是吧

740
00:19:22,780 --> 00:19:24,640
嗯那好了我来看看啊

741
00:19:24,640 --> 00:19:26,020
这里面呢他是哪个没进去呢

742
00:19:26,020 --> 00:19:27,620
我把这个hif放进来

743
00:19:27,620 --> 00:19:29,640
放在这来是吧

744
00:19:29,640 --> 00:19:31,180
我来看看他tugname是什么是吧

745
00:19:31,180 --> 00:19:32,440
我就多打印几个啊

746
00:19:32,440 --> 00:19:33,980
防止他要出点什么问题

747
00:19:33,980 --> 00:19:34,540
哎

748
00:19:34,540 --> 00:19:35,640
这里是他跟你

749
00:19:35,640 --> 00:19:37,780
我这个呢我就不要了

750
00:19:37,780 --> 00:19:39,540
再运行一下是吧

751
00:19:39,540 --> 00:19:40,600
看个效果

752
00:19:40,600 --> 00:19:44,580
嗯找一下啊

753
00:19:44,580 --> 00:19:47,940
这里面第一告诉我link是man.css

754
00:19:47,940 --> 00:19:49,700
完了第二个script的是啥呀

755
00:19:49,700 --> 00:19:52,040
是安定范哦有问题是吧

756
00:19:52,040 --> 00:19:52,780
这里面呢

757
00:19:52,780 --> 00:19:54,780
我需要把这个东西稍微改造一下

758
00:19:54,780 --> 00:19:56,800
这里面反复的应该是new tag是吧

759
00:19:56,800 --> 00:19:57,940
这是写的不太对了

760
00:19:57,940 --> 00:19:58,740
如果他对吧

761
00:19:58,740 --> 00:20:01,000
那我就return new tag是吧

762
00:20:01,000 --> 00:20:03,100
这少了一个 new tag

763
00:20:03,100 --> 00:20:07,000
不过这 css 确实没皮上是吧

764
00:20:07,000 --> 00:20:08,900
css gs 咱看一下

765
00:20:08,900 --> 00:20:10,600
gss 里面放的是什么呢

766
00:20:10,600 --> 00:20:13,800
是不是就是当前的 script 没错吧

767
00:20:13,800 --> 00:20:15,500
s script 没错

768
00:20:15,500 --> 00:20:17,800
这里面呢应该放的是属性

769
00:20:17,800 --> 00:20:21,300
属性应该放的是 atributes 下的 src 是吧

770
00:20:21,300 --> 00:20:24,700
看看 atributes 下的 src

771
00:20:24,700 --> 00:20:26,500
这位看着挺对的

772
00:20:26,500 --> 00:20:28,600
我再来试一次再打包

773
00:20:29,800 --> 00:20:31,900
啊刚才就是可能没有返回这个

774
00:20:31,900 --> 00:20:37,800
他跟内因为我们要匹配到了是不是要返回这个我新生成的这个标签啊哎这就对了啊

775
00:20:37,800 --> 00:20:39,200
这里面要再看

776
00:20:39,200 --> 00:20:43,800
呃好像还是没成功帮到音带斯哦好像

777
00:20:43,800 --> 00:20:48,100
还是没行是吧我们再来看看效果啊这里面的什么原因

778
00:20:48,100 --> 00:20:49,700
我把这个他跟内

779
00:20:49,700 --> 00:20:54,000
这个东西拿过来我再把这正泽那也粘过来吧啊防止他出问题

780
00:20:54,000 --> 00:20:57,600
href应该是可以的进来以后死带应该是可以的

781
00:20:57,600 --> 00:20:59,360
这是JavaScript的也是可以的

782
00:20:59,360 --> 00:20:59,600
哦

783
00:20:59,600 --> 00:21:01,620
这个现在其实已经生成功了

784
00:21:01,620 --> 00:21:02,420
我给你看一下

785
00:21:02,420 --> 00:21:03,560
这里面是有的

786
00:21:03,560 --> 00:21:03,760
你看

787
00:21:03,760 --> 00:21:04,900
是CSL有了

788
00:21:04,900 --> 00:21:06,360
这里面是不是也OK啊

789
00:21:06,360 --> 00:21:08,660
为什么我们当前这资源还产生了呢

790
00:21:08,660 --> 00:21:11,520
因为我们这个Assass里面是不是还有这个资源呀

791
00:21:11,520 --> 00:21:12,760
那我应该干嘛呢

792
00:21:12,760 --> 00:21:13,260
是不是在这里

793
00:21:13,260 --> 00:21:13,940
对吧

794
00:21:13,940 --> 00:21:14,700
复完以后

795
00:21:14,700 --> 00:21:16,620
我是不是还要在这里面把这资源干嘛呀

796
00:21:16,620 --> 00:21:17,340
删除掉

797
00:21:17,340 --> 00:21:20,360
少了这补是吧

798
00:21:20,360 --> 00:21:21,440
删除掉

799
00:21:21,440 --> 00:21:23,040
删除掉对吧

800
00:21:23,040 --> 00:21:26,600
原有应该生成的资源

801
00:21:26,600 --> 00:21:27,420
那这样的话

802
00:21:27,420 --> 00:21:28,960
是不是把它放到ATML里面

803
00:21:28,960 --> 00:21:29,600
是不是把它怎么样

804
00:21:29,600 --> 00:21:30,320
还要删除掉

805
00:21:30,320 --> 00:21:31,720
那这样的话

806
00:21:31,720 --> 00:21:32,580
是不是我再打包

807
00:21:32,580 --> 00:21:33,380
它就理睿所当然

808
00:21:33,380 --> 00:21:34,320
应该是没有了吧

809
00:21:34,320 --> 00:21:35,640
那为了开效果

810
00:21:35,640 --> 00:21:37,060
我把这东西给你干掉

811
00:21:37,060 --> 00:21:39,240
这个逻辑

812
00:21:39,240 --> 00:21:40,400
大家也有明白了

813
00:21:40,400 --> 00:21:41,680
基本上就是匹配什么

814
00:21:41,680 --> 00:21:42,320
另个标签

815
00:21:42,320 --> 00:21:43,560
匹配到了以后怎么办

816
00:21:43,560 --> 00:21:44,160
这就不要了

817
00:21:44,160 --> 00:21:45,320
是匹配到以后

818
00:21:45,320 --> 00:21:46,080
我就把这东西

819
00:21:46,080 --> 00:21:47,300
替换成新的标签

820
00:21:47,300 --> 00:21:48,540
把路径放上去

821
00:21:48,540 --> 00:21:49,740
如果sql的也一样

822
00:21:49,740 --> 00:21:50,940
替换成一个新的sql标签

823
00:21:50,940 --> 00:21:52,140
并且把内容放进去

824
00:21:52,140 --> 00:21:54,300
最后把这放进去的内容怎么样

825
00:21:54,300 --> 00:21:55,500
把这个资源再删掉

826
00:21:55,500 --> 00:21:56,900
这样打包出来的时候怎么样

827
00:21:56,900 --> 00:21:57,720
就没有了

828
00:21:57,720 --> 00:21:58,500
试试是吧

829
00:21:58,500 --> 00:21:59,780
npxwipac

830
00:21:59,780 --> 00:22:01,780
OK

831
00:22:01,780 --> 00:22:03,460
你看这时候是不是

832
00:22:03,460 --> 00:22:04,560
我虽然打了很多

833
00:22:04,560 --> 00:22:05,460
我先不理他

834
00:22:05,460 --> 00:22:07,580
看看是不是只有一个ATML

835
00:22:07,580 --> 00:22:09,540
是不是里面有GS啊

836
00:22:09,540 --> 00:22:10,280
CSS是吧

837
00:22:10,280 --> 00:22:11,360
我把它跑一下

838
00:22:11,360 --> 00:22:12,300
但是这样的话

839
00:22:12,300 --> 00:22:13,720
我们这个功能就实现了

840
00:22:13,720 --> 00:22:15,240
就是个内联的功能

841
00:22:15,240 --> 00:22:16,180
里面是一样的

842
00:22:16,180 --> 00:22:16,460
看看

843
00:22:16,460 --> 00:22:17,860
因为可能GS什么都没写

844
00:22:17,860 --> 00:22:18,900
看不到效果了

845
00:22:18,900 --> 00:22:19,460
好

846
00:22:19,460 --> 00:22:22,280
现在我们就完成了这样一个插件

