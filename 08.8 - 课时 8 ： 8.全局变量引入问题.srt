1
00:00:00,000 --> 00:00:04,400
接下来呢,我们再来说一下我们的webpack中的一些低参方模块的使用

2
00:00:04,400 --> 00:00:08,200
比如说这里呢,我们可以去eon add,我们可以安装这个jquery

3
00:00:08,200 --> 00:00:11,120
我们一想到jquery啊,都会想到一些低参方模块

4
00:00:11,120 --> 00:00:13,720
那这些模块呢,可能会依赖于这个jquery

5
00:00:13,720 --> 00:00:15,900
而且呢,是依赖于window上的jquery

6
00:00:15,900 --> 00:00:17,840
那这时候我们要怎么处理呢,对吧

7
00:00:17,840 --> 00:00:21,300
我们来看一看啊,比如说我在我的indexgs里面

8
00:00:21,300 --> 00:00:23,400
我先把这东西啊,稍微注视一调啊

9
00:00:23,400 --> 00:00:27,360
那这里面越来越多,那同样呢,这里面我可以用我们的es6语法也可以啊

10
00:00:27,360 --> 00:00:29,220
如果硬泡他我们的到达服务

11
00:00:29,220 --> 00:00:30,660
from我们的gquery

12
00:00:30,660 --> 00:00:32,920
gqrv

13
00:00:32,920 --> 00:00:35,020
那这里呢我们可以直接去用啊

14
00:00:35,020 --> 00:00:36,860
比如说我council.log一下这个doller

15
00:00:36,860 --> 00:00:38,720
哎这是毫无质疑的肯定可以用

16
00:00:38,720 --> 00:00:41,500
相当于我们通过require去引入这样一个模块

17
00:00:41,500 --> 00:00:42,560
用法是一样的

18
00:00:42,560 --> 00:00:45,620
他相当于是引诱了一个什么默认导出哎

19
00:00:45,620 --> 00:00:47,300
那这里呢我们就跑一下啊

20
00:00:47,300 --> 00:00:49,360
npm run dv

21
00:00:49,360 --> 00:00:51,120
那就直接跑开发了啊

22
00:00:51,120 --> 00:00:52,520
那这里呢来试一下

23
00:00:52,520 --> 00:00:56,120
同样这里面的localhouse8080

24
00:00:56,120 --> 00:00:57,280
直接跑一下

25
00:00:57,280 --> 00:00:59,680
看看能不能拿到我们这样一个Dollar服务

26
00:00:59,680 --> 00:01:02,000
这样的话肯定是可以的

27
00:01:02,000 --> 00:01:02,960
log一下

28
00:01:02,960 --> 00:01:05,560
看到了没

29
00:01:05,560 --> 00:01:07,560
是不是可以拿到我们这样一个FM函数

30
00:01:07,560 --> 00:01:10,440
同样这里面我们还可以怎么做

31
00:01:10,440 --> 00:01:11,160
再问一下

32
00:01:11,160 --> 00:01:13,020
能不能拿到Windows点到了

33
00:01:13,020 --> 00:01:15,080
我们都知道我们YPAC

34
00:01:15,080 --> 00:01:16,300
其实或者像node

35
00:01:16,300 --> 00:01:18,840
外面都会默认去封装这样一个B包函数

36
00:01:18,840 --> 00:01:20,360
再可以看打包后的结果

37
00:01:20,360 --> 00:01:21,840
我们每一个结果

38
00:01:21,840 --> 00:01:24,160
是不是都封到这样一个B包里面去了

39
00:01:24,160 --> 00:01:25,720
你看这个是都有个函数

40
00:01:25,720 --> 00:01:26,700
那这时候呢

41
00:01:26,700 --> 00:01:27,780
这个结果

42
00:01:27,780 --> 00:01:28,220
这个Dollar

43
00:01:28,220 --> 00:01:30,220
它并不会挂到我们的这个Windows上

44
00:01:30,220 --> 00:01:31,440
那可以看看效果

45
00:01:31,440 --> 00:01:31,960
刷新

46
00:01:31,960 --> 00:01:32,820
是不是Andify

47
00:01:32,820 --> 00:01:34,760
那这时候我要怎么处理呢

48
00:01:34,760 --> 00:01:35,680
我们希望啊

49
00:01:35,680 --> 00:01:36,480
这个变量呢

50
00:01:36,480 --> 00:01:37,840
可以暴露给这个Windows

51
00:01:37,840 --> 00:01:39,340
那我们有什么方案呢

52
00:01:39,340 --> 00:01:40,460
那最方便的呢

53
00:01:40,460 --> 00:01:41,760
是我们可以用这样一个模块

54
00:01:41,760 --> 00:01:42,740
这样一个模块叫什么呢

55
00:01:42,740 --> 00:01:43,640
叫有个东西叫

56
00:01:43,640 --> 00:01:45,300
Exposeloader

57
00:01:45,300 --> 00:01:46,020
叫什么叫

58
00:01:46,020 --> 00:01:47,620
暴露全局的loader

59
00:01:47,620 --> 00:01:48,080
对吧

60
00:01:48,080 --> 00:01:48,540
暴露

61
00:01:48,540 --> 00:01:50,280
暴露

62
00:01:50,280 --> 00:01:50,760
对吧

63
00:01:50,760 --> 00:01:52,540
全局的loader

64
00:01:52,540 --> 00:01:54,180
那它怎么使用

65
00:01:54,180 --> 00:01:55,200
来看一下啊

66
00:01:55,200 --> 00:01:56,900
说这里面我们可以直接呢

67
00:01:56,900 --> 00:01:58,560
去在我们的代码里来用

68
00:01:58,560 --> 00:02:01,200
这种方式呢叫什么叫内联的loader

69
00:02:01,200 --> 00:02:02,400
内联的loader

70
00:02:02,400 --> 00:02:04,400
哎现在我们都知道啊loader有几种了

71
00:02:04,400 --> 00:02:05,600
第一种呢叫pray

72
00:02:05,600 --> 00:02:07,800
就是我们的叫什么上一个loader是吧

73
00:02:07,800 --> 00:02:09,300
就在前面执行的啊

74
00:02:09,300 --> 00:02:11,700
前面的执行的loader

75
00:02:11,700 --> 00:02:14,900
完了还有我们所谓的叫什么叫normalloader

76
00:02:14,900 --> 00:02:16,700
对就是普通loader

77
00:02:16,700 --> 00:02:19,400
完了现在又说了个什么叫内联loader

78
00:02:19,400 --> 00:02:20,500
内联loader

79
00:02:20,500 --> 00:02:21,100
完了之后呢

80
00:02:21,100 --> 00:02:22,700
刚才又说了个叫什么叫后置

81
00:02:22,700 --> 00:02:23,100
对吧

82
00:02:23,100 --> 00:02:25,000
后置就是我们那个postloader

83
00:02:25,200 --> 00:02:26,300
post loader

84
00:02:26,300 --> 00:02:28,000
这有这么多几种类型

85
00:02:28,000 --> 00:02:29,620
后面咱们去详细讲

86
00:02:29,620 --> 00:02:31,080
这里我们先来说一下

87
00:02:31,080 --> 00:02:32,200
什么叫内联loader

88
00:02:32,200 --> 00:02:34,420
有时候我们可以直接在代码里去用

89
00:02:34,420 --> 00:02:35,020
比如在这里

90
00:02:35,020 --> 00:02:36,200
我们可以去injquery

91
00:02:36,200 --> 00:02:37,820
完了把这个东西来个什么

92
00:02:37,820 --> 00:02:38,560
来个探号

93
00:02:38,560 --> 00:02:40,160
完了后面可以来个什么

94
00:02:40,160 --> 00:02:42,560
比如说来个injpossloader

95
00:02:42,560 --> 00:02:43,840
来个什么问号

96
00:02:43,840 --> 00:02:45,960
就是我们希望怎么样

97
00:02:45,960 --> 00:02:47,940
把这个jquery暴露出去

98
00:02:47,940 --> 00:02:49,020
完了暴露出一个什么

99
00:02:49,020 --> 00:02:51,020
暴露出一个dollar符来

100
00:02:51,020 --> 00:02:51,900
这是传餐的意思

101
00:02:51,900 --> 00:02:53,580
就相当于我们写的options

102
00:02:53,580 --> 00:02:54,320
完了问号

103
00:02:54,320 --> 00:02:54,740
对吧

104
00:02:54,740 --> 00:02:55,500
我后面写个什么

105
00:02:55,500 --> 00:02:57,800
就是把这query暴露成道乐福

106
00:02:57,800 --> 00:02:58,880
暴露给谁啊

107
00:02:58,880 --> 00:02:59,380
全局上

108
00:02:59,380 --> 00:02:59,960
那好了

109
00:02:59,960 --> 00:03:01,020
我们来试试啊

110
00:03:01,020 --> 00:03:01,920
同样的效果

111
00:03:01,920 --> 00:03:02,740
那这里面呢

112
00:03:02,740 --> 00:03:03,480
人家告诉我了

113
00:03:03,480 --> 00:03:05,240
你不安肯定是不行的啊

114
00:03:05,240 --> 00:03:06,040
is post load

115
00:03:06,040 --> 00:03:08,880
那这里面代码中要用

116
00:03:08,880 --> 00:03:09,180
是吧

117
00:03:09,180 --> 00:03:10,960
这里面就应该加港帝啊

118
00:03:10,960 --> 00:03:11,580
就不加了

119
00:03:11,580 --> 00:03:13,460
那这里面呢

120
00:03:13,460 --> 00:03:15,040
我们就安装好了

121
00:03:15,040 --> 00:03:15,620
马上

122
00:03:15,620 --> 00:03:16,380
那这里呢

123
00:03:16,380 --> 00:03:16,940
我再来刷新

124
00:03:16,940 --> 00:03:17,260
是吧

125
00:03:17,260 --> 00:03:18,400
还是一样啊

126
00:03:18,400 --> 00:03:19,000
npx

127
00:03:19,000 --> 00:03:20,660
应该叫npx

128
00:03:20,660 --> 00:03:22,220
哪个比较方便呢

129
00:03:22,220 --> 00:03:23,700
我们就npm run dv吧

130
00:03:23,700 --> 00:03:26,080
起个服务还是一样

131
00:03:26,080 --> 00:03:27,060
方便一些

132
00:03:27,060 --> 00:03:30,340
这里应该很快就安好了

133
00:03:30,340 --> 00:03:31,500
刷新一下

134
00:03:31,500 --> 00:03:34,420
你看这时候是不是就挂到我们的window上了

135
00:03:34,420 --> 00:03:35,400
就这样一个功能

136
00:03:35,400 --> 00:03:38,420
同样有人说这样的写法很恶心

137
00:03:38,420 --> 00:03:38,940
很变态

138
00:03:38,940 --> 00:03:40,760
同样你也可以怎么样

139
00:03:40,760 --> 00:03:41,960
不用内联楼的

140
00:03:41,960 --> 00:03:43,600
这里面我又放了个断碟

141
00:03:43,600 --> 00:03:44,240
关掉

142
00:03:44,240 --> 00:03:45,160
也可以怎么样

143
00:03:45,160 --> 00:03:47,920
可以直接配到我们的webpack文件里去

144
00:03:47,920 --> 00:03:49,120
这样配的话也可以

145
00:03:49,120 --> 00:03:50,540
比如在这里面来个啥

146
00:03:50,540 --> 00:03:52,640
来一个在下面接着加

147
00:03:52,640 --> 00:03:53,840
就是来一个

148
00:03:53,840 --> 00:03:56,440
我这家是吧

149
00:03:56,440 --> 00:03:57,940
比如说在这我要匹配

150
00:03:57,940 --> 00:03:59,440
这里面我们可以这样写对吧

151
00:03:59,440 --> 00:04:00,540
叫当我呀

152
00:04:00,540 --> 00:04:02,840
你的代码里面去引用了对吧

153
00:04:02,840 --> 00:04:04,240
叫require.result

154
00:04:04,240 --> 00:04:05,040
只要你引用了

155
00:04:05,040 --> 00:04:05,940
他就可以怎么样

156
00:04:05,940 --> 00:04:07,940
你用了gquery我就匹配到

157
00:04:07,940 --> 00:04:10,140
这时候我们可以怎么样

158
00:04:10,140 --> 00:04:11,040
在这加个多号

159
00:04:11,040 --> 00:04:13,940
完了我可以怎么样去用我们的loader

160
00:04:13,940 --> 00:04:16,540
这个loader叫as post对吧loader

161
00:04:16,540 --> 00:04:18,240
同样可以来个什么问号

162
00:04:18,240 --> 00:04:19,140
$服务

163
00:04:19,140 --> 00:04:20,340
碳号什么

164
00:04:20,340 --> 00:04:21,540
gquery

165
00:04:21,540 --> 00:04:23,040
这也是一样的写法

166
00:04:23,040 --> 00:04:24,740
现在我们用的时候也是一样的

167
00:04:24,740 --> 00:04:26,640
那就相当于不需要怎么做了

168
00:04:26,640 --> 00:04:29,040
就不需要把这个东西写到这了

169
00:04:29,040 --> 00:04:31,440
才能写到里面去了是吧写到这来

170
00:04:31,440 --> 00:04:33,040
这样一个效果是吧

171
00:04:33,040 --> 00:04:33,940
就是引的时候哎

172
00:04:33,940 --> 00:04:35,740
我直接把它怎么样生成过来

173
00:04:35,740 --> 00:04:37,540
那好了我再去执行一下

174
00:04:37,540 --> 00:04:38,640
npx

175
00:04:38,640 --> 00:04:42,040
对吧叫npm对吧

176
00:04:42,040 --> 00:04:42,940
软dv

177
00:04:42,940 --> 00:04:46,640
哎这个效果应该是一样的

178
00:04:46,640 --> 00:04:48,140
就是用我们的gquery对吧

179
00:04:48,140 --> 00:04:48,840
完了怎么样

180
00:04:48,840 --> 00:04:50,240
把它暴露成一个dollar

181
00:04:50,240 --> 00:04:51,940
我这个碳号可以不用

182
00:04:51,940 --> 00:04:53,140
无所谓多了一个

183
00:04:53,140 --> 00:04:55,540
这里面我们来刷新一下看看效果

184
00:04:55,540 --> 00:04:57,040
是不是也是依旧的看看

185
00:04:57,040 --> 00:04:57,640
console

186
00:04:57,640 --> 00:05:00,000
哦有点暗地犯是吧

187
00:05:00,000 --> 00:05:02,040
这里面应该还是得写全

188
00:05:02,040 --> 00:05:03,540
那这里面还是加上碳号

189
00:05:03,540 --> 00:05:05,540
后面给个这块位是吧

190
00:05:05,540 --> 00:05:07,940
那这里面再来试试走你

191
00:05:07,940 --> 00:05:10,540
刷新

192
00:05:10,540 --> 00:05:12,240
稍等一下

193
00:05:12,240 --> 00:05:13,940
8080

194
00:05:13,940 --> 00:05:17,140
哦还是暗地犯是吧

195
00:05:17,140 --> 00:05:18,940
那这里面我看看是不是这个地方

196
00:05:18,940 --> 00:05:20,340
连这个东西又不能写

197
00:05:20,340 --> 00:05:21,580
我再试试啊

198
00:05:21,580 --> 00:05:23,640
就是把它变成一个Dollar服

199
00:05:23,640 --> 00:05:27,140
可能多写也不行

200
00:05:27,140 --> 00:05:28,180
烧写也不行是吧

201
00:05:28,180 --> 00:05:29,780
这里面我再刷新一下是吧

202
00:05:29,780 --> 00:05:30,980
你看是不是就OK了

203
00:05:30,980 --> 00:05:32,380
也说我们希望怎么样

204
00:05:32,380 --> 00:05:34,280
是不是把GQuery一用的时候

205
00:05:34,280 --> 00:05:36,800
把它变成一个什么Dollar服的球技变量

206
00:05:36,800 --> 00:05:38,400
那除了这种方法发现

207
00:05:38,400 --> 00:05:40,300
哎他可能并不是很友好对吧

208
00:05:40,300 --> 00:05:41,740
我希望我用的时候呢

209
00:05:41,740 --> 00:05:43,740
我不希望怎么样在这里面去GQuery了

210
00:05:43,740 --> 00:05:44,700
我就希望默认

211
00:05:44,700 --> 00:05:47,140
我就可以拿到这样一个Dollar服

212
00:05:47,140 --> 00:05:48,540
哎那这时候怎么做呢

213
00:05:48,540 --> 00:05:49,240
我们可以怎么样

214
00:05:49,240 --> 00:05:51,440
就是在每个对吧

215
00:05:51,440 --> 00:05:55,040
模块中对吧注入注入对吧

216
00:05:55,040 --> 00:05:56,740
这个叫道路对象

217
00:05:56,740 --> 00:06:00,440
来试试怎么注入呢

218
00:06:00,440 --> 00:06:02,540
这时候要靠webpack的一个插件了

219
00:06:02,540 --> 00:06:04,040
哎回到我们的这里面来

220
00:06:04,040 --> 00:06:06,040
那webpack的插件肯定需要怎么样

221
00:06:06,040 --> 00:06:07,640
就需要随便改一下了是吧

222
00:06:07,640 --> 00:06:09,540
需要先引入这个webpack

223
00:06:09,540 --> 00:06:12,140
webpack也是个模块啊

224
00:06:12,140 --> 00:06:12,940
可以引入

225
00:06:12,940 --> 00:06:14,040
那引入的时候呢

226
00:06:14,040 --> 00:06:15,040
我说它是个插件

227
00:06:15,040 --> 00:06:16,440
那好了走走走走

228
00:06:16,440 --> 00:06:17,840
那这个就没有用了

229
00:06:17,840 --> 00:06:19,440
因为我没有没有去引这块

230
00:06:19,440 --> 00:06:20,640
那好把它住掉吧

231
00:06:20,640 --> 00:06:22,240
哎往下找插件的话

232
00:06:22,240 --> 00:06:24,640
肯定是在这个plugin里面是吧

233
00:06:24,640 --> 00:06:26,140
你在这是吧

234
00:06:26,140 --> 00:06:26,840
应该需要怎么样

235
00:06:26,840 --> 00:06:28,240
再去new这样的插件

236
00:06:28,240 --> 00:06:29,240
他有个调什么呢

237
00:06:29,240 --> 00:06:32,240
叫provide的plugin一听零的啥意思

238
00:06:32,240 --> 00:06:34,040
叫提供插件是吧

239
00:06:34,040 --> 00:06:35,040
provide的plugin

240
00:06:35,040 --> 00:06:36,440
那我可以提供一个什么呢

241
00:06:36,440 --> 00:06:38,040
比如说我要提供一个对吧

242
00:06:38,040 --> 00:06:40,840
把这query提供成一个doll服务

243
00:06:40,840 --> 00:06:42,040
哎这样就可以了

244
00:06:42,040 --> 00:06:43,040
叫provide的plugin

245
00:06:43,040 --> 00:06:44,240
就是提供插件是吧

246
00:06:44,240 --> 00:06:45,640
那这是个括号啊

247
00:06:45,640 --> 00:06:46,440
括号

248
00:06:46,840 --> 00:06:47,840
提示功能很弱

249
00:06:47,840 --> 00:06:50,240
这里锁进一下

250
00:06:50,240 --> 00:06:51,840
这也是一个用法

251
00:06:51,840 --> 00:06:56,040
就相当于在在每个模块中对吧

252
00:06:56,040 --> 00:06:59,140
我块中都注入道车辅

253
00:06:59,140 --> 00:07:00,540
那好了

254
00:07:00,540 --> 00:07:02,440
我在这里面一样运行一下

255
00:07:02,440 --> 00:07:05,140
在这起控npm run dv

256
00:07:05,140 --> 00:07:08,040
我们来看看效果能不能 ok

257
00:07:08,040 --> 00:07:12,240
这里呢我们就访问8080来试一下

258
00:07:12,240 --> 00:07:12,940
是吧刷新

259
00:07:12,940 --> 00:07:15,440
发现刀车没定义是吧

260
00:07:15,440 --> 00:07:16,740
那是不是写法的呢

261
00:07:16,740 --> 00:07:17,700
我说很容易写反

262
00:07:17,700 --> 00:07:19,100
就是把刀的完了

263
00:07:19,100 --> 00:07:20,640
用这块来暴露出来是吧

264
00:07:20,640 --> 00:07:21,680
应该是这样写啊

265
00:07:21,680 --> 00:07:22,740
就是默认情况下

266
00:07:22,740 --> 00:07:24,180
我就把它暴露出来了啊

267
00:07:24,180 --> 00:07:26,980
那现在提供到每一个模块里面

268
00:07:26,980 --> 00:07:27,840
那就可以怎么样

269
00:07:27,840 --> 00:07:30,460
直接来获取这样一个道路幅变量了

270
00:07:30,460 --> 00:07:31,960
嗯

271
00:07:31,960 --> 00:07:32,820
马上出来啊

272
00:07:32,820 --> 00:07:33,320
刷新

273
00:07:33,320 --> 00:07:35,160
看见是不是就拿到了

274
00:07:35,160 --> 00:07:36,260
那同样啊

275
00:07:36,260 --> 00:07:38,880
比如说我能不能在这里面通过window去拿呀

276
00:07:38,880 --> 00:07:39,120
是不是

277
00:07:39,120 --> 00:07:39,820
比如一样

278
00:07:39,820 --> 00:07:41,900
我还是希望能不能拿到window点道了

279
00:07:41,900 --> 00:07:43,060
但是你猜呢

280
00:07:43,060 --> 00:07:43,300
是不是

281
00:07:43,300 --> 00:07:44,380
刷新

282
00:07:44,380 --> 00:07:44,960
哦

283
00:07:44,960 --> 00:07:45,920
有点慢是吧

284
00:07:45,920 --> 00:07:48,280
好像有点卡重新编译了

285
00:07:48,280 --> 00:07:49,320
是不是拿不到

286
00:07:49,320 --> 00:07:50,260
因为我说了

287
00:07:50,260 --> 00:07:53,640
他只是在每个模块中都注入了一个Dollar而已

288
00:07:53,640 --> 00:07:56,120
同样其实还有一种可能

289
00:07:56,120 --> 00:07:57,360
就是我们这个jquery

290
00:07:57,360 --> 00:07:59,540
他并不需要通过计算网模块注入

291
00:07:59,540 --> 00:08:00,580
可能他怎么样

292
00:08:00,580 --> 00:08:03,680
他就在页面中引入了一个cdn的路径

293
00:08:03,680 --> 00:08:05,000
我们都知道cdn比较快

294
00:08:05,000 --> 00:08:06,880
我们可以在这直接script

295
00:08:06,880 --> 00:08:08,120
我来来个src

296
00:08:08,120 --> 00:08:09,420
我来引个jquery

297
00:08:09,420 --> 00:08:10,780
这肯定不犯法

298
00:08:10,780 --> 00:08:12,380
我在这里面找一下

299
00:08:12,380 --> 00:08:14,900
百度

300
00:08:14,900 --> 00:08:15,260
是吧

301
00:08:15,260 --> 00:08:18,160
比如说我就在这找这query的什么cdn

302
00:08:18,160 --> 00:08:19,160
完了

303
00:08:19,160 --> 00:08:20,140
把它粘过来

304
00:08:20,140 --> 00:08:20,780
在这里

305
00:08:20,780 --> 00:08:21,600
应该这个就有

306
00:08:21,600 --> 00:08:24,180
找一下

307
00:08:24,180 --> 00:08:24,820
你出来

308
00:08:24,820 --> 00:08:25,380
你出来

309
00:08:25,380 --> 00:08:25,920
是吧

310
00:08:25,920 --> 00:08:27,180
就是不出来

311
00:08:27,180 --> 00:08:28,160
那咱就换一个

312
00:08:28,160 --> 00:08:28,740
是吧

313
00:08:28,740 --> 00:08:29,260
这个呢

314
00:08:29,260 --> 00:08:32,180
这个不是

315
00:08:32,180 --> 00:08:32,580
是吧

316
00:08:32,580 --> 00:08:33,320
这个吧

317
00:08:33,320 --> 00:08:34,260
这querycdn

318
00:08:34,260 --> 00:08:35,960
这个看着像

319
00:08:35,960 --> 00:08:36,680
是吧

320
00:08:36,680 --> 00:08:39,040
有点慢

321
00:08:39,040 --> 00:08:40,080
我把这个也点开

322
00:08:40,080 --> 00:08:41,840
这个先出来的快

323
00:08:41,840 --> 00:08:42,440
那就用它

324
00:08:42,440 --> 00:08:43,940
这里面告诉我有个

325
00:08:43,940 --> 00:08:45,540
jquery的js是吧

326
00:08:45,540 --> 00:08:46,340
我把它印进来

327
00:08:46,340 --> 00:08:48,140
我在我的文件中

328
00:08:48,140 --> 00:08:49,140
其实理所当然的

329
00:08:49,140 --> 00:08:51,740
就应该能拿到这个window.dollar服务是吧

330
00:08:51,740 --> 00:08:52,840
拿来试试是吧

331
00:08:52,840 --> 00:08:53,540
乱dv

332
00:08:53,540 --> 00:08:55,940
看看效果

333
00:08:55,940 --> 00:08:57,740
在这里

334
00:08:57,740 --> 00:08:59,340
刷新一下是吧

335
00:08:59,340 --> 00:09:00,340
走你

336
00:09:00,340 --> 00:09:02,040
看看是不是可以拿到

337
00:09:02,040 --> 00:09:04,340
但是我这人就有洁癖对吧

338
00:09:04,340 --> 00:09:05,940
我就希望我用的时候

339
00:09:05,940 --> 00:09:07,140
我还是直接拿dollar行不行

340
00:09:07,140 --> 00:09:08,340
当然肯定也行是吧

341
00:09:08,340 --> 00:09:10,840
但是我就希望在前面多来一句什么呢

342
00:09:10,840 --> 00:09:12,940
我就想在这里面import

343
00:09:13,140 --> 00:09:14,520
道着服务from对吧

344
00:09:14,520 --> 00:09:15,040
gquery

345
00:09:15,040 --> 00:09:16,920
这可就坏了

346
00:09:16,920 --> 00:09:18,480
你要这样一写他怎么样

347
00:09:18,480 --> 00:09:20,420
他就会把这个gquery给你打包

348
00:09:20,420 --> 00:09:22,620
是不是相当于又多引了一个模块

349
00:09:22,620 --> 00:09:24,900
但是其实我这里面明明都已经怎么样

350
00:09:24,900 --> 00:09:25,500
引好了

351
00:09:25,500 --> 00:09:27,060
但是我就心里有障碍是吧

352
00:09:27,060 --> 00:09:28,860
我就想把它怎么样引进来

353
00:09:28,860 --> 00:09:29,460
那这时候呢

354
00:09:29,460 --> 00:09:30,200
你看啊

355
00:09:30,200 --> 00:09:32,100
我们打包一下npm run build

356
00:09:32,100 --> 00:09:33,580
run build

357
00:09:33,580 --> 00:09:36,140
完了这时候呢

358
00:09:36,140 --> 00:09:37,220
我运行一下是吧

359
00:09:37,220 --> 00:09:37,900
看看结果

360
00:09:37,900 --> 00:09:39,540
嗯

361
00:09:39,540 --> 00:09:40,340
有点慢啊

362
00:09:40,340 --> 00:09:41,700
这时候他告诉我帮动呢

363
00:09:41,700 --> 00:09:42,740
有305k

364
00:09:42,900 --> 00:09:44,300
这时候我希望的是什么

365
00:09:44,300 --> 00:09:46,060
是不是他不应该被打包进去

366
00:09:46,060 --> 00:09:47,180
这时候怎么做

367
00:09:47,180 --> 00:09:49,240
我们可以再配一个属性叫什么

368
00:09:49,240 --> 00:09:50,440
叫Eternals

369
00:09:50,440 --> 00:09:51,640
这样一个属性

370
00:09:51,640 --> 00:09:53,920
就说我们如果用的话

371
00:09:53,920 --> 00:09:55,840
如果他引了一个这块的话

372
00:09:55,840 --> 00:09:57,180
你就把它忽略掉就好了

373
00:09:57,180 --> 00:09:58,120
来看看是吧

374
00:09:58,120 --> 00:09:59,820
这个用法我突然忘了

375
00:09:59,820 --> 00:10:00,980
搜一下

376
00:10:00,980 --> 00:10:02,380
Eternals

377
00:10:02,380 --> 00:10:04,000
他可以告诉人家怎么样

378
00:10:04,000 --> 00:10:05,880
这个模块是外部引入的

379
00:10:05,880 --> 00:10:07,560
他并不需要打包

380
00:10:07,560 --> 00:10:08,960
明确一下

381
00:10:08,960 --> 00:10:10,120
主是未来怎么样

382
00:10:10,120 --> 00:10:11,580
起到心理安慰作用

383
00:10:11,580 --> 00:10:12,680
我把它拿过来

384
00:10:13,340 --> 00:10:14,080
这样是吧

385
00:10:14,080 --> 00:10:14,680
ok

386
00:10:14,680 --> 00:10:16,940
完了你这样配置好了

387
00:10:16,940 --> 00:10:17,740
你再试试是吧

388
00:10:17,740 --> 00:10:18,940
比如说引了这块位

389
00:10:18,940 --> 00:10:19,280
那好

390
00:10:19,280 --> 00:10:20,640
他应该改成到了服务是吧

391
00:10:20,640 --> 00:10:22,140
在这里啊运行一下

392
00:10:22,140 --> 00:10:23,740
软表的

393
00:10:23,740 --> 00:10:26,940
看看大小没有变啊

394
00:10:26,940 --> 00:10:29,840
现在是变成了4.88k了

395
00:10:29,840 --> 00:10:31,940
那也相当于我们的这块怎么样

396
00:10:31,940 --> 00:10:34,120
是并没有把它进行打包啊

397
00:10:34,120 --> 00:10:34,820
这因为是什么

398
00:10:34,820 --> 00:10:36,620
是不是因为他是一个第三方模块

399
00:10:36,620 --> 00:10:37,080
对吧

400
00:10:37,080 --> 00:10:38,840
并且呢他在外面引入了

401
00:10:38,840 --> 00:10:40,120
这里面引入指是怎么样

402
00:10:40,120 --> 00:10:41,880
是不是给我来个新年安慰对吧

403
00:10:41,880 --> 00:10:43,680
只是能看到我们当前的Dollar

404
00:10:43,680 --> 00:10:45,380
是通过GQuery拿到的

405
00:10:45,380 --> 00:10:47,540
好了这里面我们来试试

406
00:10:47,540 --> 00:10:49,580
看看真正情况下能不能跑

407
00:10:49,580 --> 00:10:50,540
肯定也没问题

408
00:10:50,540 --> 00:10:53,180
因为我这里面毕竟它引了GQuery是吧

409
00:10:53,180 --> 00:10:54,580
好看完效果

410
00:10:54,580 --> 00:10:55,680
走你

411
00:10:55,680 --> 00:10:57,880
哇这里面我们看一下结果

412
00:10:57,880 --> 00:10:59,680
console是不是依旧可以

413
00:10:59,680 --> 00:11:02,680
也就是说我们现在有几种方式来引入一个

414
00:11:02,680 --> 00:11:04,180
比如说第三个模块对吧

415
00:11:04,180 --> 00:11:06,180
就是第一种我们可以使用什么

416
00:11:06,180 --> 00:11:07,880
可以使用eSports Loader

417
00:11:07,880 --> 00:11:09,980
叫暴露到什么

418
00:11:09,980 --> 00:11:11,880
暴露到windows上

419
00:11:11,880 --> 00:11:12,320
对吧

420
00:11:12,320 --> 00:11:13,040
windows

421
00:11:13,040 --> 00:11:14,740
还有第二种就是什么

422
00:11:14,740 --> 00:11:17,180
就是给每个人怎么样都加一个对吧

423
00:11:17,180 --> 00:11:19,780
这个东西叫什么叫provide对吧

424
00:11:19,780 --> 00:11:20,640
plugin

425
00:11:20,640 --> 00:11:22,680
对给每个人

426
00:11:22,680 --> 00:11:26,240
提供一个对吧

427
00:11:26,240 --> 00:11:28,320
叫导乐服务可以这样做是吧

428
00:11:28,320 --> 00:11:30,940
同样第三个就是什么就是引入怎么样

429
00:11:30,940 --> 00:11:33,680
引入不打包对不打包的方式

430
00:11:33,680 --> 00:11:35,280
哎这开你的选择了

431
00:11:35,280 --> 00:11:37,880
看你用哪种方式更适合你好

