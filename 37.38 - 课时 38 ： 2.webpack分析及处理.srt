1
00:00:00,300 --> 00:00:01,740
那我们呢接着往下写

2
00:00:01,740 --> 00:00:03,440
现在呢我们需要干嘛呢

3
00:00:03,440 --> 00:00:06,260
是不是需要通过我们这样一个YPAC打包工具

4
00:00:06,260 --> 00:00:10,400
往来引用我们当前的这样一个模块里的这个YPAC CONFIR GS

5
00:00:10,400 --> 00:00:13,460
往来拿到当前这样一个配置呢来进行变译

6
00:00:13,460 --> 00:00:14,980
比如说找到我们的入口

7
00:00:14,980 --> 00:00:16,420
找到我们一些东西是吧

8
00:00:16,420 --> 00:00:17,060
那好

9
00:00:17,060 --> 00:00:18,800
那这里呢我们就可以这样去做

10
00:00:18,800 --> 00:00:19,060
对吧

11
00:00:19,060 --> 00:00:19,620
就是第一步

12
00:00:19,620 --> 00:00:20,560
我们需要

13
00:00:20,560 --> 00:00:26,180
需要找到当前执行命令的

14
00:00:26,180 --> 00:00:28,220
命令的路径

15
00:00:29,220 --> 00:00:29,960
执行什么

16
00:00:29,960 --> 00:00:31,620
执行我们拿到

17
00:00:31,620 --> 00:00:36,300
拿到我们的webpack.config.js

18
00:00:36,300 --> 00:00:40,460
你看我们当前执行的路径

19
00:00:40,460 --> 00:00:41,980
就是在webpack第一位下

20
00:00:41,980 --> 00:00:43,760
这时候我们执行的时候

21
00:00:43,760 --> 00:00:45,280
是不是就是找当前目录下的

22
00:00:45,280 --> 00:00:47,240
webpack.js就OK了

23
00:00:47,240 --> 00:00:49,560
所以说这里面我们可以直接去写

24
00:00:49,560 --> 00:00:50,580
需要拿到什么

25
00:00:50,580 --> 00:00:52,180
我们肯定需要拿到路径

26
00:00:52,180 --> 00:00:54,220
肯定需要一张一个路径模块

27
00:00:54,220 --> 00:00:56,860
我们可以在这里直接通过pass

28
00:00:56,860 --> 00:00:59,280
等于require这样一个pass

29
00:00:59,280 --> 00:01:00,360
完了呢

30
00:01:00,360 --> 00:01:01,920
我们可以在这里去做是吧

31
00:01:01,920 --> 00:01:03,440
拿到我们这样一个配置文件

32
00:01:03,440 --> 00:01:04,880
这个配置文件呢

33
00:01:04,880 --> 00:01:07,640
可以直接通过我们的require去引用

34
00:01:07,640 --> 00:01:08,640
那引用的时候

35
00:01:08,640 --> 00:01:10,400
我们需要引用就是当前目录下

36
00:01:10,400 --> 00:01:10,740
对吧

37
00:01:10,740 --> 00:01:12,300
那就需要pass.resolve

38
00:01:12,300 --> 00:01:13,380
来个杠杠

39
00:01:13,380 --> 00:01:13,880
压力

40
00:01:13,880 --> 00:01:16,460
完了它的意思就是

41
00:01:16,460 --> 00:01:18,820
引用我们当前这个当前这个

42
00:01:18,820 --> 00:01:24,100
当前当前

43
00:01:24,100 --> 00:01:26,940
当前这个运行的目录是吧

44
00:01:26,940 --> 00:01:28,860
那这个目录呢指的就是我们当前

45
00:01:28,860 --> 00:01:31,640
这里放的webpack.confilergs这个目录

46
00:01:31,640 --> 00:01:33,160
那我们就可以拿到这样一个配置了

47
00:01:33,160 --> 00:01:36,100
那这config就是我们所谓的这个配置文件

48
00:01:36,100 --> 00:01:39,460
那有了这个配置文件以后啊

49
00:01:39,460 --> 00:01:40,820
我们就可以做我们的事了

50
00:01:40,820 --> 00:01:43,740
比如说我们可以在这里去写一个专门用来编译的类

51
00:01:43,740 --> 00:01:44,880
我们叫它compiler

52
00:01:44,880 --> 00:01:48,000
完了我们在这里呢去new上这样一个compiler

53
00:01:48,000 --> 00:01:50,420
当然它是个类啊

54
00:01:50,420 --> 00:01:52,060
那这个类呢我们需要从哪来呢

55
00:01:52,060 --> 00:01:53,160
我们肯定需要怎么样

56
00:01:53,160 --> 00:01:54,180
去导入这样一个类

57
00:01:54,180 --> 00:01:55,340
专门有个这样的文件

58
00:01:55,340 --> 00:01:56,260
那我们呢

59
00:01:56,260 --> 00:01:57,000
就可以在这里呢

60
00:01:57,000 --> 00:01:58,580
去写上这样一个compiler

61
00:01:58,580 --> 00:01:59,620
等于require

62
00:01:59,620 --> 00:02:00,660
完了去引用

63
00:02:00,660 --> 00:02:01,600
当前我们

64
00:02:01,600 --> 00:02:03,600
比如说lab下的这个compiler

65
00:02:03,600 --> 00:02:04,040
是吧

66
00:02:04,040 --> 00:02:05,820
把这个文件引进来

67
00:02:05,820 --> 00:02:06,580
那这时候呢

68
00:02:06,580 --> 00:02:07,380
我们需要怎么样

69
00:02:07,380 --> 00:02:08,680
compiler评错了

70
00:02:08,680 --> 00:02:10,040
我们需要怎么样

71
00:02:10,040 --> 00:02:11,860
在当前我们这个目录上面

72
00:02:11,860 --> 00:02:13,040
去建一个lab

73
00:02:13,040 --> 00:02:14,560
专门来存放我们的圆满

74
00:02:14,560 --> 00:02:15,360
那这里呢

75
00:02:15,360 --> 00:02:16,460
我就建上这样一个文件

76
00:02:16,460 --> 00:02:19,000
就叫它compiler.js

77
00:02:19,000 --> 00:02:21,160
OK

78
00:02:21,160 --> 00:02:22,460
那一用的时候呢

79
00:02:22,460 --> 00:02:23,360
这里面我也加上吧

80
00:02:23,360 --> 00:02:23,980
.jsn

81
00:02:23,980 --> 00:02:25,800
那引到这个文件以后啊

82
00:02:25,800 --> 00:02:26,560
我们就可以编译

83
00:02:26,560 --> 00:02:27,720
那编译的时候呢

84
00:02:27,720 --> 00:02:29,420
我们可以把这个配置传进去

85
00:02:29,420 --> 00:02:30,480
完了并且呢一样

86
00:02:30,480 --> 00:02:32,740
我们来这样一个compiler怎么样

87
00:02:32,740 --> 00:02:33,420
去运行

88
00:02:33,420 --> 00:02:34,400
运行的时候呢

89
00:02:34,400 --> 00:02:35,440
我就起个方法吧

90
00:02:35,440 --> 00:02:35,940
乱方法

91
00:02:35,940 --> 00:02:36,480
可以跑

92
00:02:36,480 --> 00:02:37,720
标标识对吧

93
00:02:37,720 --> 00:02:39,400
标识运行代码

94
00:02:39,400 --> 00:02:41,660
标识对吧

95
00:02:41,660 --> 00:02:43,000
运行

96
00:02:43,000 --> 00:02:44,620
编译

97
00:02:44,620 --> 00:02:45,580
那好了

98
00:02:45,580 --> 00:02:46,900
那我们理所当然的啊

99
00:02:46,900 --> 00:02:48,660
就要在对应这个文件上啊

100
00:02:48,660 --> 00:02:50,200
去建上这样一个compiler

101
00:02:50,200 --> 00:02:52,080
完了把这个类导进来

102
00:02:52,080 --> 00:02:54,280
有个乱方法一调用就可以跑了

103
00:02:54,280 --> 00:02:56,140
这时候我们一样分个屏

104
00:02:56,140 --> 00:02:59,160
完了我们在这里写成这样一个compiler

105
00:02:59,160 --> 00:03:01,020
classcompiler

106
00:03:01,020 --> 00:03:05,160
完了并且我们可以把这个类导出去

107
00:03:05,160 --> 00:03:08,540
毛丢件ispos等于一个compiler

108
00:03:08,540 --> 00:03:10,640
compiler

109
00:03:10,640 --> 00:03:13,200
把它放进来

110
00:03:13,200 --> 00:03:14,520
compiler

111
00:03:14,520 --> 00:03:17,020
这时候我们说了

112
00:03:17,020 --> 00:03:18,560
它内部其实应该有一个

113
00:03:18,560 --> 00:03:19,360
肯定有个勾动函数

114
00:03:19,360 --> 00:03:20,680
那勾动函数里面呢

115
00:03:20,680 --> 00:03:22,180
其实我们就传了个配置文件

116
00:03:22,180 --> 00:03:22,700
叫config

117
00:03:22,700 --> 00:03:23,560
那这里呢

118
00:03:23,560 --> 00:03:24,640
我就把这config放在这

119
00:03:24,640 --> 00:03:26,460
那为了所有的地方通常拿到

120
00:03:26,460 --> 00:03:27,480
那我们可以呢

121
00:03:27,480 --> 00:03:28,560
把这个config呢

122
00:03:28,560 --> 00:03:30,840
挂在我们当前的这个实例上

123
00:03:30,840 --> 00:03:31,560
config

124
00:03:31,560 --> 00:03:33,100
往里面有包含着

125
00:03:33,100 --> 00:03:34,480
我们所谓的这个叫entry

126
00:03:34,480 --> 00:03:35,340
对吧

127
00:03:35,340 --> 00:03:37,080
还有刚才我们看到那个output

128
00:03:37,080 --> 00:03:38,700
往来同样啊

129
00:03:38,700 --> 00:03:39,620
我们现在呢

130
00:03:39,620 --> 00:03:41,300
去new这样一个compiler

131
00:03:41,300 --> 00:03:42,040
往来并且呢

132
00:03:42,040 --> 00:03:42,800
它有个方法叫run

133
00:03:42,800 --> 00:03:43,600
run呢

134
00:03:43,600 --> 00:03:45,280
就表示当前代码的执行

135
00:03:45,280 --> 00:03:46,600
我在这里呢

136
00:03:46,600 --> 00:03:47,540
可以去调run方法

137
00:03:47,540 --> 00:03:48,460
表示就是执行

138
00:03:48,460 --> 00:03:50,320
那这执行的操作是什么呢

139
00:03:50,320 --> 00:03:51,560
其实他要干很多事

140
00:03:51,560 --> 00:03:54,960
第一件事就是要解析我们当前文件的依赖

141
00:03:54,960 --> 00:03:58,240
完了把这个依赖变成刚才我们看到的模样

142
00:03:58,240 --> 00:04:01,140
变到这里是不是有这样个对象

143
00:04:01,140 --> 00:04:02,020
那好了

144
00:04:02,020 --> 00:04:04,240
那我们还是不是需要把它编译出来以后

145
00:04:04,240 --> 00:04:06,280
把这个主模块的路径放在这里

146
00:04:06,280 --> 00:04:09,900
所以说我们现在需要实现这个webpack

147
00:04:09,900 --> 00:04:10,860
主要靠两步

148
00:04:10,860 --> 00:04:12,260
第一步需要怎么样

149
00:04:12,260 --> 00:04:16,780
需要保存入口文件的路径

150
00:04:16,780 --> 00:04:18,440
完了第二步呢

151
00:04:18,440 --> 00:04:19,540
就是需要怎么样

152
00:04:19,540 --> 00:04:21,960
需要保存所有什么

153
00:04:21,960 --> 00:04:23,120
所有的模块依赖

154
00:04:23,120 --> 00:04:26,180
所有的模块依赖

155
00:04:26,180 --> 00:04:27,880
那好了

156
00:04:27,880 --> 00:04:28,660
那非常简单

157
00:04:28,660 --> 00:04:29,560
我在这里呢

158
00:04:29,560 --> 00:04:30,780
就可以写这样个变量

159
00:04:30,780 --> 00:04:31,960
叫Entry ID

160
00:04:31,960 --> 00:04:33,440
这个东西啊

161
00:04:33,440 --> 00:04:34,600
我就认为它是一个主模块

162
00:04:34,600 --> 00:04:34,940
路径

163
00:04:34,940 --> 00:04:35,780
完了它呢

164
00:04:35,780 --> 00:04:36,660
应该就是比如说

165
00:04:36,660 --> 00:04:37,520
刚才我们看到了

166
00:04:37,520 --> 00:04:37,960
应该是什么

167
00:04:37,960 --> 00:04:39,620
第二杠SRC下的这个

168
00:04:39,620 --> 00:04:40,440
IndexJS

169
00:04:40,440 --> 00:04:41,740
那同样啊

170
00:04:41,740 --> 00:04:42,660
现在不知道对吧

171
00:04:42,660 --> 00:04:43,760
那肯定我执行的时候

172
00:04:43,760 --> 00:04:44,300
编译的时候

173
00:04:44,300 --> 00:04:45,820
才知道这个入口是哪个

174
00:04:45,820 --> 00:04:46,720
路径是哪一个

175
00:04:46,720 --> 00:04:50,400
同样我们还可以在这里写上这样一个属性

176
00:04:50,400 --> 00:04:50,960
叫modules

177
00:04:50,960 --> 00:04:53,760
你可以认为这个对象就是存放着

178
00:04:53,760 --> 00:04:55,460
所有的依赖关系

179
00:04:55,460 --> 00:04:56,200
比如说路径

180
00:04:56,200 --> 00:04:57,400
对它代码是什么

181
00:04:57,400 --> 00:04:59,460
OK现在是空的

182
00:04:59,460 --> 00:05:01,920
我们run的时候主要就干这样两件事

183
00:05:01,920 --> 00:05:04,620
第一个就叫我们的buildmodule

184
00:05:04,620 --> 00:05:06,620
一听名字就是创建模块

185
00:05:06,620 --> 00:05:08,160
就是执行

186
00:05:08,160 --> 00:05:09,500
并且怎么样

187
00:05:09,500 --> 00:05:12,900
并且创建模块的依赖关系

188
00:05:12,900 --> 00:05:13,420
对吧

189
00:05:13,420 --> 00:05:15,020
模块的依赖关系

190
00:05:15,020 --> 00:05:16,000
那你想啊

191
00:05:16,000 --> 00:05:17,540
那刚开始执行的时候

192
00:05:17,540 --> 00:05:19,220
肯定我们可以拿到什么呢

193
00:05:19,220 --> 00:05:21,700
是不是当前文件的这样一个入口文件

194
00:05:21,700 --> 00:05:23,220
那入口文件怎么拿

195
00:05:23,220 --> 00:05:24,480
那同样我们可以这样写

196
00:05:24,480 --> 00:05:24,760
对吧

197
00:05:24,760 --> 00:05:26,260
我先把入口保存下来

198
00:05:26,260 --> 00:05:26,780
Entry

199
00:05:26,780 --> 00:05:29,480
等于我们当前的这样一个Options

200
00:05:29,480 --> 00:05:30,980
叫Config吧

201
00:05:30,980 --> 00:05:31,740
Config

202
00:05:31,740 --> 00:05:33,300
第二这个Entry

203
00:05:33,300 --> 00:05:35,080
Entry

204
00:05:35,080 --> 00:05:36,080
也就是说它呢

205
00:05:36,080 --> 00:05:37,640
是我们的入口路径

206
00:05:37,640 --> 00:05:39,320
那同样啊

207
00:05:39,320 --> 00:05:40,380
除了这个入口

208
00:05:40,380 --> 00:05:41,760
那我们可能还有什么呢

209
00:05:41,760 --> 00:05:43,880
还有我们当前这样一个工作目录

210
00:05:43,880 --> 00:05:44,900
比如说我在这里呢

211
00:05:44,900 --> 00:05:45,720
也写上一个吧

212
00:05:45,720 --> 00:05:46,920
叫工作目录

213
00:05:46,920 --> 00:05:48,060
我就叫它root吧

214
00:05:48,060 --> 00:05:49,820
因为我们要知道这个文件

215
00:05:49,820 --> 00:05:50,820
它是个相对路径

216
00:05:50,820 --> 00:05:51,860
那我们需要怎么样

217
00:05:51,860 --> 00:05:53,420
找到当前文件的绝路径

218
00:05:53,420 --> 00:05:54,300
来这样处理

219
00:05:54,300 --> 00:05:55,160
所以这里呢

220
00:05:55,160 --> 00:05:57,700
我们可以直接写叫process.cwd

221
00:05:57,700 --> 00:05:59,000
它代表的就是

222
00:05:59,000 --> 00:06:00,780
当前我们运行这个

223
00:06:00,780 --> 00:06:03,520
NPSX中文pack的那个路径

224
00:06:03,520 --> 00:06:04,980
也是工作路径是吧

225
00:06:04,980 --> 00:06:05,860
是工作路径

226
00:06:05,860 --> 00:06:09,520
那我们呀就需要怎么样

227
00:06:09,520 --> 00:06:10,580
是不是根据这样一个

228
00:06:10,580 --> 00:06:11,760
哎有当前路径

229
00:06:11,760 --> 00:06:13,140
还有当前文件的名字

230
00:06:13,140 --> 00:06:15,160
我是不是就可以读到这个文件中的内容

231
00:06:15,160 --> 00:06:16,040
完了并且呢

232
00:06:16,040 --> 00:06:17,540
根据这个文件中的内容呢

233
00:06:17,540 --> 00:06:18,200
进行怎么样

234
00:06:18,200 --> 00:06:19,400
编译这个依赖关系

235
00:06:19,400 --> 00:06:20,140
那好了

236
00:06:20,140 --> 00:06:21,000
那这里面呢

237
00:06:21,000 --> 00:06:22,000
我就可以先写上

238
00:06:22,000 --> 00:06:23,180
比如说写个什么呢

239
00:06:23,180 --> 00:06:23,620
第一

240
00:06:23,620 --> 00:06:25,120
我们需要这个pass.j

241
00:06:25,120 --> 00:06:28,320
完了我要找到这个绝对路径

242
00:06:28,320 --> 00:06:28,760
那好

243
00:06:28,760 --> 00:06:29,660
我用resolve更好

244
00:06:29,660 --> 00:06:31,020
resolve绝对路径

245
00:06:31,020 --> 00:06:32,080
完了第一呢

246
00:06:32,080 --> 00:06:34,540
我把这个this.root呢放进去

247
00:06:34,540 --> 00:06:36,200
这是我们的根路径

248
00:06:36,200 --> 00:06:36,880
完了之后呢

249
00:06:36,880 --> 00:06:38,000
我们还要找到什么呢

250
00:06:38,000 --> 00:06:39,660
当前路径对应的这个entry

251
00:06:39,660 --> 00:06:41,040
也就是说这是一个

252
00:06:41,040 --> 00:06:42,240
当前我们那个

253
00:06:42,240 --> 00:06:43,740
index.js的一个决定路径

254
00:06:43,740 --> 00:06:45,260
并且告诉他

255
00:06:45,260 --> 00:06:45,800
当前呢

256
00:06:45,800 --> 00:06:46,680
他是一个什么呀

257
00:06:46,680 --> 00:06:47,440
是个主模块

258
00:06:47,440 --> 00:06:48,300
OK

259
00:06:48,300 --> 00:06:49,440
那到时候呢

260
00:06:49,440 --> 00:06:50,640
我们是不是把它解析完以后

261
00:06:50,640 --> 00:06:51,660
就可以放到这个对象里

262
00:06:51,660 --> 00:06:52,060
完了

263
00:06:52,060 --> 00:06:53,100
如果是主模块的话

264
00:06:53,100 --> 00:06:53,400
好

265
00:06:53,400 --> 00:06:55,120
我把这个当前的路径呢

266
00:06:55,120 --> 00:06:55,480
取出来

267
00:06:55,480 --> 00:06:56,900
放到这个untry.id上

268
00:06:56,900 --> 00:06:57,900
完了

269
00:06:57,900 --> 00:06:58,420
同样啊

270
00:06:58,420 --> 00:06:59,660
我们解析完以后呢

271
00:06:59,660 --> 00:07:00,680
还需要干一件事

272
00:07:00,680 --> 00:07:01,440
就是我们要干嘛

273
00:07:01,440 --> 00:07:01,860
是不是

274
00:07:01,860 --> 00:07:03,020
发射一个文件

275
00:07:03,020 --> 00:07:03,340
对吧

276
00:07:03,340 --> 00:07:04,380
发射一个文件

277
00:07:04,380 --> 00:07:06,100
那这个文件的作用就是什么

278
00:07:06,100 --> 00:07:06,320
就是

279
00:07:06,320 --> 00:07:07,240
你可以认为就是

280
00:07:07,240 --> 00:07:08,320
打包后的文件

281
00:07:08,320 --> 00:07:10,740
打包后的

282
00:07:10,740 --> 00:07:11,340
文件

283
00:07:11,340 --> 00:07:13,320
那现在好了

284
00:07:13,320 --> 00:07:14,320
我们再写这样一个方法

285
00:07:14,320 --> 00:07:15,600
叫this.email file

286
00:07:15,600 --> 00:07:17,160
email file

287
00:07:17,160 --> 00:07:18,840
那这两个方法现在都没有

288
00:07:18,840 --> 00:07:20,120
那我可以在上面怎么样

289
00:07:20,120 --> 00:07:21,560
分别建设这样一个方法

290
00:07:21,560 --> 00:07:22,640
一个叫build module

291
00:07:22,640 --> 00:07:23,840
完了另一个呢

292
00:07:23,840 --> 00:07:25,060
我们就叫它email file

293
00:07:25,060 --> 00:07:28,240
完了我们打包的时候

294
00:07:28,240 --> 00:07:29,980
第一步我们需要传一个去路径

295
00:07:29,980 --> 00:07:31,920
第二步呢传一个是否是入口

296
00:07:31,920 --> 00:07:33,840
那这里呢我们可以来一个

297
00:07:33,840 --> 00:07:35,300
比如就叫module pass

298
00:07:35,300 --> 00:07:36,620
完了后面呢

299
00:07:36,620 --> 00:07:38,400
这个叫我们的age entry

300
00:07:38,400 --> 00:07:39,600
是否是入口

301
00:07:39,600 --> 00:07:41,920
那这里面就是我们的发射文件的逻辑

302
00:07:41,920 --> 00:07:42,240
对吧

303
00:07:42,240 --> 00:07:43,100
就是发射文件

304
00:07:43,100 --> 00:07:44,440
那现在我们用不到它

305
00:07:44,440 --> 00:07:45,260
我们先不写它

306
00:07:45,260 --> 00:07:47,060
那主要来写这个关系

307
00:07:47,060 --> 00:07:52,400
那总结一下吧

308
00:07:52,400 --> 00:07:53,960
我们要干的事就两点

309
00:07:53,960 --> 00:07:54,500
第一呢

310
00:07:54,500 --> 00:07:56,500
我们需要解析我们的这样一个modus

311
00:07:56,500 --> 00:07:57,100
第二呢

312
00:07:57,100 --> 00:07:59,020
就是我们当前这样一个入口ID

313
00:07:59,020 --> 00:08:00,380
拿到这两个值呢

314
00:08:00,380 --> 00:08:02,600
我们可以通过刚才这样一个东西

315
00:08:02,600 --> 00:08:03,300
就这个模板

316
00:08:03,300 --> 00:08:04,560
完了把这个地方怎么样

317
00:08:04,560 --> 00:08:05,220
替换掉

318
00:08:05,220 --> 00:08:08,200
就可以实现我们自己的wapack了

