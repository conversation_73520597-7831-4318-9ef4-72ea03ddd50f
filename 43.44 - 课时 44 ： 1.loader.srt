1
00:00:00,000 --> 00:00:04,940
本章节我们就来实现webpack中的常用loader

2
00:00:04,940 --> 00:00:08,080
在以前的徐义中我们都知道什么是loader

3
00:00:08,080 --> 00:00:11,840
我们说webpack默认只能处理这种java square的模块

4
00:00:11,840 --> 00:00:15,620
这时候我们希望处理一些比如说css还有图片

5
00:00:15,620 --> 00:00:19,000
这时候我们就需要一些loader来进行处理

6
00:00:19,000 --> 00:00:21,800
来进行转化我们的这样一个代码

7
00:00:21,800 --> 00:00:22,100
那好

8
00:00:22,100 --> 00:00:25,160
loader其实是webpack中一个非常重要的概念

9
00:00:25,160 --> 00:00:26,580
除了loader还有我们的插件

10
00:00:26,580 --> 00:00:28,080
我们后面会讲这个插件

11
00:00:28,080 --> 00:00:29,740
那咱们先来说一说吧

12
00:00:29,740 --> 00:00:31,700
这个loader我们去怎么配置

13
00:00:31,700 --> 00:00:32,800
我们以前也配过

14
00:00:32,800 --> 00:00:33,800
我们先来配一下

15
00:00:33,800 --> 00:00:35,600
完了再通过这个配置

16
00:00:35,600 --> 00:00:37,380
来自己手写一个loader

17
00:00:37,380 --> 00:00:39,420
通过这个loader来知道一下

18
00:00:39,420 --> 00:00:40,880
我们的loader是怎么实现的

19
00:00:40,880 --> 00:00:42,400
之后我们来写一下

20
00:00:42,400 --> 00:00:43,640
比如常见的那些loader

21
00:00:43,640 --> 00:00:44,440
像fileloader

22
00:00:44,440 --> 00:00:45,200
cssloader

23
00:00:45,200 --> 00:00:46,220
还有我们的urloader

24
00:00:46,220 --> 00:00:46,660
好

25
00:00:46,660 --> 00:00:48,720
这里我们就来写下代码

26
00:00:48,720 --> 00:00:50,180
新建一个src目录

27
00:00:50,180 --> 00:00:51,100
完了里面

28
00:00:51,100 --> 00:00:53,200
我就直接建上一个index.js

29
00:00:53,200 --> 00:00:54,880
完了我写一段脚本

30
00:00:54,880 --> 00:00:56,120
我就写个coslog

31
00:00:56,120 --> 00:00:56,780
来个hello

32
00:00:56,780 --> 00:00:59,660
完了并且我们还需要一个

33
00:00:59,660 --> 00:01:01,400
配置文件还记得吧

34
00:01:01,400 --> 00:01:03,120
webpack.config.js

35
00:01:03,120 --> 00:01:04,720
那这里呢我们就把它打开

36
00:01:04,720 --> 00:01:06,620
哎在这里呢去生成一下

37
00:01:06,620 --> 00:01:09,560
压iit-y

38
00:01:09,560 --> 00:01:12,620
好了并且呢我们可以去安装

39
00:01:12,620 --> 00:01:13,800
压add

40
00:01:13,800 --> 00:01:16,700
好了我们的webpack

41
00:01:16,700 --> 00:01:19,520
还有我们的webpack-cly-d

42
00:01:19,520 --> 00:01:24,460
好了先让他按照我这边呢先去写我们的代码

43
00:01:24,460 --> 00:01:25,760
但是新建一个文件

44
00:01:25,760 --> 00:01:29,620
我们的webpack.config.js

45
00:01:29,660 --> 00:01:32,360
Ok 我们说了webpack的默认呢

46
00:01:32,360 --> 00:01:34,060
需要导出这样一个东西是吧

47
00:01:34,060 --> 00:01:34,760
一个对象

48
00:01:34,760 --> 00:01:35,660
完了里面呢

49
00:01:35,660 --> 00:01:37,260
我们需要配上一个叫

50
00:01:37,260 --> 00:01:39,360
啊配上一个entry入口

51
00:01:39,360 --> 00:01:40,860
entry入口

52
00:01:40,860 --> 00:01:42,060
完了我们就直接呢

53
00:01:42,060 --> 00:01:44,860
去找到我们的src下的index.js

54
00:01:44,860 --> 00:01:46,960
那这里呢

55
00:01:46,960 --> 00:01:48,660
我们也可以给一个开发模式

56
00:01:48,660 --> 00:01:49,160
到时候呢

57
00:01:49,160 --> 00:01:51,160
我们可以看着看他编译后的结果

58
00:01:51,160 --> 00:01:52,260
development

59
00:01:52,260 --> 00:01:53,360
哎 ok

60
00:01:53,360 --> 00:01:53,760
我们呢

61
00:01:53,760 --> 00:01:54,060
我们呢

62
00:01:54,060 --> 00:01:56,160
再给他配上一个出口output

63
00:01:56,160 --> 00:01:56,960
那这里呢

64
00:01:56,960 --> 00:01:59,160
我们可以直接给上一个叫fail name

65
00:01:59,360 --> 00:02:00,500
就是告诉他当前呢

66
00:02:00,500 --> 00:02:02,060
我们的文件的名字写到呢

67
00:02:02,060 --> 00:02:03,760
我们叫他build.js

68
00:02:03,760 --> 00:02:08,000
OK完了我们呢还需要给上这样一个pass

69
00:02:08,000 --> 00:02:09,960
那我们呢需要把pass模块引进来

70
00:02:09,960 --> 00:02:13,120
letpass等于requirepass

71
00:02:13,120 --> 00:02:16,620
完了在底下呢

72
00:02:16,620 --> 00:02:18,600
我们需要去解析我们的路径

73
00:02:18,600 --> 00:02:21,200
来pass.result来一个杠杠

74
00:02:21,200 --> 00:02:21,960
第2name

75
00:02:21,960 --> 00:02:24,500
完了并且呢我们给上一个这样的第4目录

76
00:02:24,500 --> 00:02:25,360
那好了

77
00:02:25,360 --> 00:02:28,000
那这样的一个我们的入口出口能配好了

78
00:02:28,000 --> 00:02:28,620
那这时候呢

79
00:02:28,620 --> 00:02:29,280
我们希望呢

80
00:02:29,280 --> 00:02:30,980
把我们这样一个代码进行转化

81
00:02:30,980 --> 00:02:32,180
那这时候呢

82
00:02:32,180 --> 00:02:33,780
我们可能就需要我们的这样一个loader了

83
00:02:33,780 --> 00:02:34,860
来把一段代码呢

84
00:02:34,860 --> 00:02:36,020
转化成另一段代码

85
00:02:36,020 --> 00:02:36,500
那好

86
00:02:36,500 --> 00:02:37,020
这里呢

87
00:02:37,020 --> 00:02:38,080
我们可以配上一个module

88
00:02:38,080 --> 00:02:39,080
这里呢

89
00:02:39,080 --> 00:02:39,620
来个ruth

90
00:02:39,620 --> 00:02:42,060
那现在我们要匹配的肯定是所有的gs

91
00:02:42,060 --> 00:02:42,460
是吧

92
00:02:42,460 --> 00:02:43,000
那好

93
00:02:43,000 --> 00:02:44,780
我就可以来一个正泽test

94
00:02:44,780 --> 00:02:45,980
完了去匹配

95
00:02:45,980 --> 00:02:47,520
以这个gs结尾的

96
00:02:47,520 --> 00:02:48,060
完了

97
00:02:48,060 --> 00:02:48,500
我们呢

98
00:02:48,500 --> 00:02:49,280
可以在这里呢

99
00:02:49,280 --> 00:02:50,680
使用我们这样一个babel

100
00:02:50,680 --> 00:02:51,660
就叫loader吧

101
00:02:51,660 --> 00:02:52,520
就叫loader1

102
00:02:52,520 --> 00:02:53,280
OK

103
00:02:53,280 --> 00:02:54,200
那这时候呢

104
00:02:54,200 --> 00:02:55,060
我们loader1在哪呢

105
00:02:55,060 --> 00:02:56,200
现在并没有这样一个模块

106
00:02:56,200 --> 00:02:56,760
那好了

107
00:02:56,760 --> 00:02:57,680
我们希望怎么样呢

108
00:02:57,680 --> 00:02:58,640
是不是建上这样一个东西

109
00:02:58,640 --> 00:03:00,080
比如叫loader

110
00:03:00,080 --> 00:03:00,940
这里呢

111
00:03:00,940 --> 00:03:01,940
装着我们所有的模块

112
00:03:01,940 --> 00:03:02,960
完了里面呢

113
00:03:02,960 --> 00:03:05,360
我们就建上这样一个loader1.js

114
00:03:05,360 --> 00:03:06,360
OK

115
00:03:06,360 --> 00:03:07,760
那建好以后啊

116
00:03:07,760 --> 00:03:08,780
我们希望怎么做呢

117
00:03:08,780 --> 00:03:11,300
是不是他就应该可以通过这样一个配置文件

118
00:03:11,300 --> 00:03:12,580
找到这个loader1啊

119
00:03:12,580 --> 00:03:14,660
但默认情况下肯定不会去找的

120
00:03:14,660 --> 00:03:15,940
那再来试一下啊

121
00:03:15,940 --> 00:03:18,020
我们可以在这里直接通过mpx

122
00:03:18,020 --> 00:03:20,900
来执行我们这样一个webpack

123
00:03:20,900 --> 00:03:23,460
因为在执行的过程中啊

124
00:03:23,460 --> 00:03:24,220
他肯定会告诉我

125
00:03:24,220 --> 00:03:25,400
是找不到这个loader1

126
00:03:25,400 --> 00:03:26,800
那我们来看看吧

127
00:03:26,800 --> 00:03:28,600
那找到这个loader的方式呢

128
00:03:28,600 --> 00:03:29,280
有几种呢

129
00:03:29,280 --> 00:03:29,520
是吧

130
00:03:29,520 --> 00:03:31,120
我们最常用的可以怎么样

131
00:03:31,120 --> 00:03:32,580
直接写一个局的路径

132
00:03:32,580 --> 00:03:33,420
比如说在这里

133
00:03:33,420 --> 00:03:34,900
来个pass.result

134
00:03:34,900 --> 00:03:36,500
比如说我给他一个杠杠

135
00:03:36,500 --> 00:03:36,980
dannin

136
00:03:36,980 --> 00:03:37,960
完了里面呢

137
00:03:37,960 --> 00:03:38,960
我给上一个loader

138
00:03:38,960 --> 00:03:40,380
完了写死

139
00:03:40,380 --> 00:03:40,720
对吧

140
00:03:40,720 --> 00:03:41,840
就找这个loader1

141
00:03:41,840 --> 00:03:43,120
这是没有问题的

142
00:03:43,120 --> 00:03:43,740
就告诉人家

143
00:03:43,740 --> 00:03:44,900
这个文件就在这

144
00:03:44,900 --> 00:03:45,800
你就用它就好了

145
00:03:45,800 --> 00:03:46,420
那好

146
00:03:46,420 --> 00:03:47,280
那咱来试试是吧

147
00:03:47,280 --> 00:03:47,840
OK

148
00:03:47,840 --> 00:03:51,520
这里也很明显是找到了

149
00:03:51,520 --> 00:03:52,520
但是说会有点包错

150
00:03:52,520 --> 00:03:53,420
因为我们的loader呢

151
00:03:53,420 --> 00:03:54,000
写的不太对

152
00:03:54,000 --> 00:03:54,320
是吧

153
00:03:54,320 --> 00:03:55,540
那咱可以看一下

154
00:03:55,540 --> 00:03:59,600
其实我们把它补上loader其实它就是一个什么普通函数

155
00:03:59,600 --> 00:04:02,400
它的名字随便起就是个函数是吧

156
00:04:02,400 --> 00:04:04,200
我把函数左右怎么样导出去

157
00:04:04,200 --> 00:04:06,020
is pause当于一个loader

158
00:04:06,020 --> 00:04:08,060
loader的参数是什么

159
00:04:08,060 --> 00:04:10,660
我写上loader的参数

160
00:04:10,660 --> 00:04:11,660
其实就是什么

161
00:04:11,660 --> 00:04:14,560
就是我们需要是不是把我们这个代码进行转化

162
00:04:14,560 --> 00:04:17,240
这个参数肯定就是我们这条代码

163
00:04:17,240 --> 00:04:18,380
我们就是它的原码

164
00:04:18,380 --> 00:04:19,720
sauce ok

165
00:04:19,720 --> 00:04:23,680
我这loader的参数就是原代码

166
00:04:25,540 --> 00:04:28,480
我们可以把这个猜数的再默认返回

167
00:04:28,480 --> 00:04:29,260
这相当于怎么样

168
00:04:29,260 --> 00:04:30,120
就是什么都没有做

169
00:04:30,120 --> 00:04:31,620
当然你要返回个别的也可以

170
00:04:31,620 --> 00:04:32,980
返回的结果怎么样

171
00:04:32,980 --> 00:04:34,460
就会作为最终的结果

172
00:04:34,460 --> 00:04:34,760
OK

173
00:04:34,760 --> 00:04:36,320
这是一个loader的情况下

174
00:04:36,320 --> 00:04:38,620
这里我们就把它再运行一下

175
00:04:38,620 --> 00:04:39,400
看看效果

176
00:04:39,400 --> 00:04:40,540
这位肯定是找到了

177
00:04:40,540 --> 00:04:42,700
你看是打包成功了

178
00:04:42,700 --> 00:04:43,980
但是我们再来看看

179
00:04:43,980 --> 00:04:44,680
这种写法

180
00:04:44,680 --> 00:04:46,120
你不能都写成绝对路径

181
00:04:46,120 --> 00:04:47,540
有没有更方便的方法

182
00:04:47,540 --> 00:04:47,920
有

183
00:04:47,920 --> 00:04:49,660
比如说我们可以这样做

184
00:04:49,660 --> 00:04:52,000
在所谓的叫Resultloader

185
00:04:52,000 --> 00:04:53,020
在这个事是吧

186
00:04:53,020 --> 00:04:55,100
完了里面我可以配上一个别名

187
00:04:55,100 --> 00:04:57,560
因为底下名字太长了

188
00:04:57,560 --> 00:04:57,940
对吧

189
00:04:57,940 --> 00:04:59,040
这个resolve loader

190
00:04:59,040 --> 00:05:01,140
和那resolve的关系是不一样的

191
00:05:01,140 --> 00:05:02,720
resolve是解析模块的

192
00:05:02,720 --> 00:05:04,220
这是专门解析loader的

193
00:05:04,220 --> 00:05:05,700
我可以在这里去配上

194
00:05:05,700 --> 00:05:06,780
比如说来个loader1

195
00:05:06,780 --> 00:05:08,180
loader1

196
00:05:08,180 --> 00:05:09,820
完了对应的路径就是它

197
00:05:09,820 --> 00:05:11,560
我可以把它拿过来

198
00:05:11,560 --> 00:05:14,580
完这里我可以放上一个loader1

199
00:05:14,580 --> 00:05:16,240
咱来试试看看能不能找到

200
00:05:16,240 --> 00:05:17,360
其实是一样的

201
00:05:17,360 --> 00:05:19,600
刚才写法就是把它写到了上面去

202
00:05:19,600 --> 00:05:21,400
看是不是也可以

203
00:05:21,400 --> 00:05:23,800
同样的这个写法还是比较麻烦

204
00:05:23,800 --> 00:05:25,220
他能不能自己去找

205
00:05:25,220 --> 00:05:26,180
当然也可以

206
00:05:26,180 --> 00:05:27,580
我们不配别名

207
00:05:27,580 --> 00:05:27,860
对吧

208
00:05:27,860 --> 00:05:28,380
我点写上

209
00:05:28,380 --> 00:05:29,320
这是别名的意思

210
00:05:29,320 --> 00:05:30,120
别名

211
00:05:30,120 --> 00:05:32,140
和我们那个result是一样的

212
00:05:32,140 --> 00:05:33,940
这里我们还可以配上一个叫modus

213
00:05:33,940 --> 00:05:36,100
modus

214
00:05:36,100 --> 00:05:36,880
完了里面

215
00:05:36,880 --> 00:05:38,260
我们可以直接写个数组

216
00:05:38,260 --> 00:05:39,540
他默认我们说了

217
00:05:39,540 --> 00:05:41,240
他找loader肯定去哪找

218
00:05:41,240 --> 00:05:42,440
是不是nodemodus下找

219
00:05:42,440 --> 00:05:43,040
那好

220
00:05:43,040 --> 00:05:44,180
那我还要把它贴上

221
00:05:44,180 --> 00:05:45,240
nodemodus

222
00:05:45,240 --> 00:05:46,800
那如果他找不到的话

223
00:05:46,800 --> 00:05:47,420
可以怎么样呢

224
00:05:47,420 --> 00:05:47,920
告诉他

225
00:05:47,920 --> 00:05:48,760
去哪找

226
00:05:48,760 --> 00:05:50,900
是不是去当前loader文件夹下找

227
00:05:50,900 --> 00:05:51,660
那好

228
00:05:51,660 --> 00:05:52,980
这里我们可以来个第二类

229
00:05:52,980 --> 00:05:54,380
后面给他一个loader

230
00:05:54,380 --> 00:05:56,520
这样他找不到node module

231
00:05:56,520 --> 00:05:58,480
就会去loader目标才去查找

232
00:05:58,480 --> 00:06:00,180
好了我们再来试一下

233
00:06:00,180 --> 00:06:01,980
再来执行

234
00:06:01,980 --> 00:06:05,080
OK是不是也可以

235
00:06:05,080 --> 00:06:06,200
你看我边有批评名

236
00:06:06,200 --> 00:06:07,040
是不是还是找到了

237
00:06:07,040 --> 00:06:08,740
因为这名字和这个名字批评到了

238
00:06:08,740 --> 00:06:10,520
好我们之后就来看一下

239
00:06:10,520 --> 00:06:12,240
loader怎么能配置多个

240
00:06:12,240 --> 00:06:14,820
包括loader有哪几种类型

