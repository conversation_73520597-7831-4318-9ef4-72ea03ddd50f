1
00:00:00,000 --> 00:00:03,000
我们接着呢往下处理我们的这个css

2
00:00:03,000 --> 00:00:05,200
那好同样我们先把这个配置文件啊

3
00:00:05,200 --> 00:00:06,000
备份一份

4
00:00:06,000 --> 00:00:09,000
那这里呢我们把config呢再放里一份是吧

5
00:00:09,000 --> 00:00:12,000
完了同样呢我把这个注视呢删一删是吧

6
00:00:12,000 --> 00:00:13,000
有点多啊抽着

7
00:00:13,000 --> 00:00:15,000
呃这里一样删掉

8
00:00:15,000 --> 00:00:16,000
嗯这个就不要了啊

9
00:00:16,000 --> 00:00:18,000
同样这个不要了

10
00:00:18,000 --> 00:00:21,000
ok 这个呢我也删掉

11
00:00:21,000 --> 00:00:24,000
完了下面呢我们就接着来配啊

12
00:00:24,000 --> 00:00:27,000
刚才我们只是简单的处理了一下我们的这个css

13
00:00:27,000 --> 00:00:29,000
那现在呢我们再接着往下处理

14
00:00:29,000 --> 00:00:30,740
再往下处理这个山调

15
00:00:30,740 --> 00:00:32,600
刚才我们发现啊

16
00:00:32,600 --> 00:00:34,060
他只能插到哪去呢

17
00:00:34,060 --> 00:00:36,760
是不是插入到我们当前这样一个模板的什么

18
00:00:36,760 --> 00:00:37,940
是不是style标签那样

19
00:00:37,940 --> 00:00:39,500
我们最后打包后已经看到了

20
00:00:39,500 --> 00:00:41,340
丢在这来一个打包吧

21
00:00:41,340 --> 00:00:42,760
看效果run build

22
00:00:42,760 --> 00:00:45,160
我们可以发现啊

23
00:00:45,160 --> 00:00:46,540
当前这样一个东西呢

24
00:00:46,540 --> 00:00:48,600
默认会插到我们的atml里是吧

25
00:00:48,600 --> 00:00:49,640
当然会变成一行啊

26
00:00:49,640 --> 00:00:51,200
你看是都在style里去

27
00:00:51,200 --> 00:00:53,460
那我能不能做到把这个文件呀

28
00:00:53,460 --> 00:00:54,500
单独抽理一下

29
00:00:54,500 --> 00:00:56,460
比如说抽成一个link标签的形式

30
00:00:56,460 --> 00:00:57,900
哎那这样的话可能怎么样

31
00:00:57,900 --> 00:00:59,100
都放在这style标签里

32
00:00:59,100 --> 00:00:59,860
要是很多的话

33
00:00:59,860 --> 00:01:01,060
可能会阻塞是吧

34
00:01:01,060 --> 00:01:01,900
这时候怎么办

35
00:01:01,900 --> 00:01:03,740
我们同样可以配一下什么

36
00:01:03,740 --> 00:01:06,300
叫抽离csi样式的一个插件

37
00:01:06,300 --> 00:01:08,900
当然他也要弄到他的loader

38
00:01:08,900 --> 00:01:10,660
这里把这个就删掉了

39
00:01:10,660 --> 00:01:13,140
完了为了方便我这压缩就先不压了

40
00:01:13,140 --> 00:01:13,660
是吧

41
00:01:13,660 --> 00:01:14,420
先不压了

42
00:01:14,420 --> 00:01:15,260
其他都关掉

43
00:01:15,260 --> 00:01:16,140
关闭其他

44
00:01:16,140 --> 00:01:17,820
往后里面我把它折一下

45
00:01:17,820 --> 00:01:18,300
是吧

46
00:01:18,300 --> 00:01:19,060
把它打开

47
00:01:19,060 --> 00:01:21,780
这里我把minify什么的

48
00:01:21,780 --> 00:01:22,700
为了看的清楚一点

49
00:01:22,700 --> 00:01:23,500
我就不要了

50
00:01:23,500 --> 00:01:25,420
现在我们这个插件叫什么

51
00:01:25,420 --> 00:01:25,780
对吧

52
00:01:25,780 --> 00:01:26,740
挺长的名字

53
00:01:26,740 --> 00:01:27,660
叫yard

54
00:01:27,660 --> 00:01:34,240
叫mini对吧,mini的css叫extract,对吧,extract plugin

55
00:01:34,240 --> 00:01:38,180
它是一个专门干嘛的呢,叫抽离css插件的

56
00:01:38,180 --> 00:01:41,640
当然了以前那个叫,有一个叫什么testetpm plugin

57
00:01:41,640 --> 00:01:43,240
那东西怎么样已经废弃了

58
00:01:43,240 --> 00:01:45,140
vpeg4用的就是这样一个插件

59
00:01:45,140 --> 00:01:47,720
那同样我们用的时候非常方便

60
00:01:47,720 --> 00:01:50,040
第一步我们需要拿进来,对吧

61
00:01:50,040 --> 00:01:52,720
它是一个类对吧,我们说的插件都是类

62
00:01:52,720 --> 00:01:56,920
mini css extract plugin

63
00:01:57,660 --> 00:01:59,160
这里我可以引进来

64
00:01:59,160 --> 00:02:00,160
放进来

65
00:02:00,160 --> 00:02:01,760
现在我们就可以怎么做了

66
00:02:01,760 --> 00:02:03,660
插件的用法都非常像对吧

67
00:02:03,660 --> 00:02:05,860
我们可以在这里再找到插件

68
00:02:05,860 --> 00:02:08,660
我说插件的使用顺序其实是没有先后的

69
00:02:08,660 --> 00:02:10,660
你在这里就一样放就好了

70
00:02:10,660 --> 00:02:12,960
叫mini css extract plugin

71
00:02:12,960 --> 00:02:16,960
网络里面你要跟人家说我抽离出来的样式叫什么名是吧

72
00:02:16,960 --> 00:02:18,460
好了肯定有个什么东西

73
00:02:18,460 --> 00:02:19,860
feeling要跟人家说对吧

74
00:02:19,860 --> 00:02:22,660
比如说我抽离出来的名字就要慢点css

75
00:02:22,660 --> 00:02:23,860
好他就会怎么样

76
00:02:23,860 --> 00:02:27,360
把这个文件放到我们当前的这样一个css拿出去

77
00:02:27,360 --> 00:02:28,360
但是同样啊

78
00:02:28,360 --> 00:02:33,120
你要跟人家说是css要被这个抽离出慢点css还是这个赖死对吧

79
00:02:33,120 --> 00:02:37,360
那这时候呢我们就需要这样一个他上面有个属性叫loader

80
00:02:37,360 --> 00:02:39,900
那我们现在啊比如说就拿它为例吧

81
00:02:39,900 --> 00:02:44,860
我不希望当前这个css里面抽出来的东西再放到这个style标签里

82
00:02:44,860 --> 00:02:46,300
那现在可以怎么做的可以这样

83
00:02:46,300 --> 00:02:47,460
把它删掉

84
00:02:47,460 --> 00:02:48,760
看删掉了啊

85
00:02:48,760 --> 00:02:52,160
我在上面呢用一下这个mini css hrplugin

86
00:02:52,160 --> 00:02:53,360
我来去点一下loader

87
00:02:53,360 --> 00:02:54,560
这什么意思呢

88
00:02:54,560 --> 00:02:56,960
就相当于我们把css弄完以后啊

89
00:02:56,960 --> 00:02:58,460
再弄一个这样的插件

90
00:02:58,460 --> 00:02:58,740
对吧

91
00:02:58,740 --> 00:02:59,400
这样一个loader

92
00:02:59,400 --> 00:03:00,200
把它怎么样

93
00:03:00,200 --> 00:03:02,020
创建个link标签

94
00:03:02,020 --> 00:03:03,200
把内容怎么样

95
00:03:03,200 --> 00:03:04,420
塞到link标签里面去

96
00:03:04,420 --> 00:03:05,760
再扔到页面上去

97
00:03:05,760 --> 00:03:06,780
这唯一的区别

98
00:03:06,780 --> 00:03:07,400
好了

99
00:03:07,400 --> 00:03:08,360
如果他也要抽离的

100
00:03:08,360 --> 00:03:08,680
那好

101
00:03:08,680 --> 00:03:09,380
他也一样

102
00:03:09,380 --> 00:03:10,340
这东西我就不要了

103
00:03:10,340 --> 00:03:11,380
直接把它怎么样放在这

104
00:03:11,380 --> 00:03:12,180
就可以了

105
00:03:12,180 --> 00:03:13,020
相当于怎么样

106
00:03:13,020 --> 00:03:14,580
抽离成一个文件

107
00:03:14,580 --> 00:03:15,880
都叫慢点css

108
00:03:15,880 --> 00:03:16,880
说我想多个

109
00:03:16,880 --> 00:03:17,280
那好

110
00:03:17,280 --> 00:03:17,800
你怎么样

111
00:03:17,800 --> 00:03:18,920
你再拷贝一份

112
00:03:18,920 --> 00:03:20,320
我就不去拷贝了

113
00:03:20,320 --> 00:03:21,400
拷贝的时候一样

114
00:03:21,400 --> 00:03:22,360
我就用两次

115
00:03:22,360 --> 00:03:23,740
这里我也一样

116
00:03:23,740 --> 00:03:25,500
名字就用两个loader就好了

117
00:03:25,500 --> 00:03:25,700
是不是

118
00:03:25,700 --> 00:03:28,940
一个叫什么抽离类似的抽离css的吧

119
00:03:28,940 --> 00:03:30,140
现在我就用一个了

120
00:03:30,140 --> 00:03:31,940
这里面我们抽离好了

121
00:03:31,940 --> 00:03:33,460
完了再来乱一下对吧

122
00:03:33,460 --> 00:03:34,100
转bill的

123
00:03:34,100 --> 00:03:37,860
看看他会不会多打包出来一个这样的css文件

124
00:03:37,860 --> 00:03:38,740
是吧

125
00:03:38,740 --> 00:03:39,940
来看一看

126
00:03:39,940 --> 00:03:41,940
这里面会没有慢点css

127
00:03:41,940 --> 00:03:43,460
你看是不是可以

128
00:03:43,460 --> 00:03:44,260
已经抽离出来了

129
00:03:44,260 --> 00:03:45,860
一个body一个body一个body

130
00:03:45,860 --> 00:03:46,460
好了

131
00:03:46,460 --> 00:03:48,340
现在我们抽离好了

132
00:03:48,340 --> 00:03:51,260
但是发现这个样式我们在写的稍微复杂一点

133
00:03:51,260 --> 00:03:52,940
看看现在抽离好

134
00:03:52,940 --> 00:03:54,460
我们不满足是吧

135
00:03:54,460 --> 00:03:55,340
我们希望怎么样

136
00:03:55,340 --> 00:03:56,020
再来一个

137
00:03:56,020 --> 00:03:59,860
比如说我希望当前 body让它旋转对吧

138
00:03:59,860 --> 00:04:00,620
我穿了缝

139
00:04:00,620 --> 00:04:02,100
四十五度低计

140
00:04:02,100 --> 00:04:03,540
这肯定没问题是吧

141
00:04:03,540 --> 00:04:05,340
看能不能转跑一下

142
00:04:05,340 --> 00:04:09,340
看当前我们的页面会不会旋转的是吧

143
00:04:09,340 --> 00:04:11,060
往来这里未能看到效果

144
00:04:11,060 --> 00:04:13,340
我还是npm run dv是吧

145
00:04:13,340 --> 00:04:15,980
看看稍等

146
00:04:15,980 --> 00:04:21,100
出来来8080这里改个名字叫8080

147
00:04:21,100 --> 00:04:23,140
刷新是吧

148
00:04:23,140 --> 00:04:27,140
但是好像Body Transform45DG

149
00:04:27,140 --> 00:04:28,340
好像没生效是吧

150
00:04:28,340 --> 00:04:30,040
我少了个Rotate

151
00:04:30,040 --> 00:04:31,640
这么愚蠢的事都能干出来

152
00:04:31,640 --> 00:04:33,940
这里面来一个叫Rotate

153
00:04:33,940 --> 00:04:36,240
我来个45DG

154
00:04:36,240 --> 00:04:37,240
我刷新一下

155
00:04:37,240 --> 00:04:38,640
看是不是在转

156
00:04:38,640 --> 00:04:39,840
但是我希望怎么样

157
00:04:39,840 --> 00:04:41,140
你看他虽然转了

158
00:04:41,140 --> 00:04:43,640
但是并没有加我们所谓的乱七前坠

159
00:04:43,640 --> 00:04:46,240
这时候我们希望他能加上前坠怎么办

160
00:04:46,240 --> 00:04:48,640
同样我们也可以这样去做

161
00:04:48,640 --> 00:04:50,140
这个插件一样叫什么

162
00:04:50,140 --> 00:04:51,940
叫抽离我们这什么

163
00:04:51,940 --> 00:04:54,400
是不是相当于我们需要给他加一个前缀

164
00:04:54,400 --> 00:04:55,500
不叫抽离前缀

165
00:04:55,500 --> 00:04:58,040
怎么加前缀叫自动填前缀

166
00:04:58,040 --> 00:05:02,220
所以这里面我们有个包叫auto什么prefixer

167
00:05:02,220 --> 00:05:03,740
用这个包的前提示

168
00:05:03,740 --> 00:05:05,560
也需要用个loader来处理一下

169
00:05:05,560 --> 00:05:05,860
对吧

170
00:05:05,860 --> 00:05:06,720
因为我们都知道了

171
00:05:06,720 --> 00:05:08,720
都是用loader来添加一些前缀

172
00:05:08,720 --> 00:05:10,520
什么改变样式改变lice

173
00:05:10,520 --> 00:05:11,900
这个文件叫什么

174
00:05:11,900 --> 00:05:14,380
叫postcssloader

175
00:05:14,380 --> 00:05:15,400
这样一个东西

176
00:05:15,400 --> 00:05:17,720
同样这里面我加个杠地

177
00:05:17,720 --> 00:05:19,560
这个插件怎么用

178
00:05:19,560 --> 00:05:21,240
loader的用法都是一样的

179
00:05:21,240 --> 00:05:23,340
就是找到我们要匹配的文件

180
00:05:23,340 --> 00:05:24,240
哎在这里

181
00:05:24,240 --> 00:05:25,540
我们是不是在css

182
00:05:25,540 --> 00:05:27,540
解析css之前

183
00:05:27,540 --> 00:05:28,640
我就需要加上前准了

184
00:05:28,640 --> 00:05:29,240
那好

185
00:05:29,240 --> 00:05:30,340
加上就ok了

186
00:05:30,340 --> 00:05:33,040
那同样他里面也一样把它加上是吧

187
00:05:33,040 --> 00:05:33,740
ok

188
00:05:33,740 --> 00:05:35,940
哎光加不行人家说了

189
00:05:35,940 --> 00:05:38,040
你这加完以后他怎么知不知道

190
00:05:38,040 --> 00:05:40,040
使用这个out to prefacer是吧

191
00:05:40,040 --> 00:05:41,040
那他会提示我什么

192
00:05:41,040 --> 00:05:42,440
比如说我要是运行

193
00:05:42,440 --> 00:05:44,240
哎他会很友善的提示我啊

194
00:05:44,240 --> 00:05:46,240
说你需要啊给我来个配置文件

195
00:05:46,240 --> 00:05:48,440
我去用一下这个out to prefacer

196
00:05:48,440 --> 00:05:49,440
你看是告诉我了

197
00:05:49,440 --> 00:05:50,540
说你用这样一个东西

198
00:05:50,540 --> 00:05:53,100
但是写着呢是把说error了

199
00:05:53,100 --> 00:05:53,520
对吧

200
00:05:53,520 --> 00:05:54,720
你需要怎么样呢

201
00:05:54,720 --> 00:05:55,340
找一下

202
00:05:55,340 --> 00:05:56,420
这写着呢

203
00:05:56,420 --> 00:05:57,440
说没有css

204
00:05:57,440 --> 00:05:59,160
那叫postcssconfig

205
00:05:59,160 --> 00:05:59,500
对吧

206
00:05:59,500 --> 00:06:00,320
没有找到

207
00:06:00,320 --> 00:06:00,780
那好了

208
00:06:00,780 --> 00:06:01,980
那你就听人家的吧

209
00:06:01,980 --> 00:06:02,940
那我来一个吧

210
00:06:02,940 --> 00:06:04,060
这个文件叫什么呢

211
00:06:04,060 --> 00:06:04,740
叫post

212
00:06:04,740 --> 00:06:05,480
对吧

213
00:06:05,480 --> 00:06:08,000
postcss.config

214
00:06:08,000 --> 00:06:11,140
config.js

215
00:06:11,140 --> 00:06:12,640
o.config

216
00:06:12,640 --> 00:06:13,100
对吧

217
00:06:13,100 --> 00:06:14,420
config.js

218
00:06:14,420 --> 00:06:15,520
你看它变颜色了

219
00:06:15,520 --> 00:06:16,460
全部拼对了

220
00:06:16,460 --> 00:06:17,720
这也是一个配置文件

221
00:06:17,720 --> 00:06:18,880
就是它默认怎么样

222
00:06:18,880 --> 00:06:19,880
用这个loader

223
00:06:19,880 --> 00:06:21,380
会电用这样一个文件

224
00:06:21,380 --> 00:06:22,820
那他需要干嘛呢

225
00:06:22,820 --> 00:06:24,560
需要导出一个模块

226
00:06:24,560 --> 00:06:25,960
这个模块里面呢

227
00:06:25,960 --> 00:06:27,660
他需要放一些所谓的插件

228
00:06:27,660 --> 00:06:28,620
他告诉人家哎

229
00:06:28,620 --> 00:06:29,520
用哪个插件呀

230
00:06:29,520 --> 00:06:29,960
那好

231
00:06:29,960 --> 00:06:32,780
你就把这个凹凸prefix拿进来

232
00:06:32,780 --> 00:06:33,400
他知道OK

233
00:06:33,400 --> 00:06:35,100
你是需要用到这个插件呢

234
00:06:35,100 --> 00:06:36,700
去给他加一个前坠

235
00:06:36,700 --> 00:06:37,380
那好

236
00:06:37,380 --> 00:06:37,980
看看行不行

237
00:06:37,980 --> 00:06:39,380
这里一样

238
00:06:39,380 --> 00:06:41,180
run build

239
00:06:41,180 --> 00:06:43,900
没包错呢

240
00:06:43,900 --> 00:06:45,300
这个90%就OK了

241
00:06:45,300 --> 00:06:45,700
哦

242
00:06:45,700 --> 00:06:46,440
有包错是不是

243
00:06:46,440 --> 00:06:48,600
说这里面写的有问题啊

244
00:06:48,600 --> 00:06:48,840
看看

245
00:06:48,840 --> 00:06:49,760
我们看一下

246
00:06:49,760 --> 00:06:51,060
哦原来这样说

247
00:06:51,060 --> 00:06:52,760
他说css有问题是吧

248
00:06:52,760 --> 00:06:54,120
那咱找一下吧

249
00:06:54,120 --> 00:06:55,700
看看css这里面写什么了

250
00:06:55,700 --> 00:06:57,300
哦这里面应该先怎么样

251
00:06:57,300 --> 00:06:58,020
刚才刚说完

252
00:06:58,020 --> 00:07:00,560
先处理我们posscss

253
00:07:00,560 --> 00:07:01,840
再处理我们的css

254
00:07:01,840 --> 00:07:03,740
这样的话就应该没有问题了

255
00:07:03,740 --> 00:07:05,360
我们再来试一下是吧

256
00:07:05,360 --> 00:07:06,340
软build

257
00:07:06,340 --> 00:07:10,060
那现在应该就可以帮我们加上前缀了

258
00:07:10,060 --> 00:07:10,420
看看

259
00:07:10,420 --> 00:07:11,200
OK

260
00:07:11,200 --> 00:07:12,980
那我们来看看效果

261
00:07:12,980 --> 00:07:14,340
我们打包出来的结果

262
00:07:14,340 --> 00:07:15,420
看看应该挺妙

263
00:07:15,420 --> 00:07:18,140
这里面link我们的慢点css

264
00:07:18,140 --> 00:07:18,420
是吧

265
00:07:18,420 --> 00:07:18,880
看看慢

266
00:07:18,880 --> 00:07:21,520
是不是可以帮我们加上YK的前准了

267
00:07:21,520 --> 00:07:23,220
现在我们就知道了

268
00:07:23,220 --> 00:07:26,200
原来是可以这样来去使用我们这样一个POSE的CSS

269
00:07:26,200 --> 00:07:29,860
同样我们可能最后我们处理完的文件也需要打包

270
00:07:29,860 --> 00:07:31,500
这时候我们会想到什么

271
00:07:31,500 --> 00:07:33,500
是不是把我们这样一个东西改成我们的

272
00:07:33,500 --> 00:07:34,560
现在就是生产环境

273
00:07:34,560 --> 00:07:37,580
但你发现GS肯定是可以打包的

274
00:07:37,580 --> 00:07:38,700
看看GS叫什么

275
00:07:38,700 --> 00:07:40,440
叫FEBB

276
00:07:40,440 --> 00:07:42,680
应该就打包了

277
00:07:42,680 --> 00:07:45,220
但是我们的CSS还是这样的

278
00:07:45,220 --> 00:07:47,000
我们希望去把CSS压缩

279
00:07:47,000 --> 00:07:48,340
这时候应该怎么办

280
00:07:48,340 --> 00:07:51,400
这时候我们就只能去优化我们的css资源了

281
00:07:51,400 --> 00:07:53,520
这里面其实我们在mini

282
00:07:53,520 --> 00:07:54,480
有点卡是吧

283
00:07:54,480 --> 00:07:55,160
有没有打包

284
00:07:55,160 --> 00:07:58,120
这里面可以直接打印去npm搜一下给大家

285
00:07:58,120 --> 00:08:01,240
这个mini css extract plugin

286
00:08:01,240 --> 00:08:02,560
它里面其实跟你说了

287
00:08:02,560 --> 00:08:03,920
需要这样一个东西

288
00:08:03,920 --> 00:08:06,260
叫css extract 对吧

289
00:08:06,260 --> 00:08:07,020
完了 plugin

290
00:08:07,020 --> 00:08:08,980
出来来回车

291
00:08:08,980 --> 00:08:10,680
这里是吧

292
00:08:10,680 --> 00:08:11,940
完了你看看下面

293
00:08:11,940 --> 00:08:12,940
其实有在介绍

294
00:08:12,940 --> 00:08:15,380
说你要想用mini css extract plugin

295
00:08:15,380 --> 00:08:16,680
那你就需要干嘛

296
00:08:16,680 --> 00:08:18,500
需要自己去压缩文件

297
00:08:18,500 --> 00:08:18,900
对吧

298
00:08:18,900 --> 00:08:19,840
比如这里有个叫什么

299
00:08:19,840 --> 00:08:20,380
叫优化项

300
00:08:20,380 --> 00:08:22,520
这是我们的YPAC4的吸气功能

301
00:08:22,520 --> 00:08:23,660
比如说你需要加个优化

302
00:08:23,660 --> 00:08:25,200
优化就是我们的叫什么

303
00:08:25,200 --> 00:08:25,960
叫压缩体积

304
00:08:25,960 --> 00:08:27,560
往来默认情况下

305
00:08:27,560 --> 00:08:28,880
我们的YPAC

306
00:08:28,880 --> 00:08:31,180
调的就是这个Arglify GS Plug E

307
00:08:31,180 --> 00:08:32,640
它可以去加缩我们GS

308
00:08:32,640 --> 00:08:33,480
同样

309
00:08:33,480 --> 00:08:35,280
如果你添上这样一个配置

310
00:08:35,280 --> 00:08:36,860
就必须要加上这样一个压缩GS

311
00:08:36,860 --> 00:08:38,540
否则GS就不会压缩了

312
00:08:38,540 --> 00:08:39,560
再来一个个看

313
00:08:39,560 --> 00:08:41,620
这里我们就需要这样一个插件

314
00:08:41,620 --> 00:08:43,700
叫Automize CSS Assess

315
00:08:43,700 --> 00:08:44,680
YPAC Plug E

316
00:08:44,680 --> 00:08:45,840
这名字好长

317
00:08:45,840 --> 00:08:46,380
是吧

318
00:08:46,380 --> 00:08:48,500
那这里呢我们就一按二的哎

319
00:08:48,500 --> 00:08:49,180
安装一下

320
00:08:49,180 --> 00:08:49,800
当地

321
00:08:49,800 --> 00:08:52,000
完了用法呢一样啊

322
00:08:52,000 --> 00:08:53,880
只要是插件你都懂得是吧

323
00:08:53,880 --> 00:08:55,580
那我们需要把它拿进来

324
00:08:55,580 --> 00:08:55,900
对吧

325
00:08:55,900 --> 00:08:57,080
叫哦

326
00:08:57,080 --> 00:08:57,900
这有点长是吧

327
00:08:57,900 --> 00:08:59,400
先写requare吧

328
00:08:59,400 --> 00:09:01,200
这里完了把这个名字呢

329
00:09:01,200 --> 00:09:02,340
沾过来是吧

330
00:09:02,340 --> 00:09:04,680
叫optimize对吧

331
00:09:04,680 --> 00:09:05,440
optimize

332
00:09:05,440 --> 00:09:07,240
css我就不写全称了啊

333
00:09:07,240 --> 00:09:08,440
为了方便点

334
00:09:08,440 --> 00:09:09,800
css

335
00:09:09,800 --> 00:09:11,480
requare

336
00:09:11,480 --> 00:09:12,180
哇这里呢

337
00:09:12,180 --> 00:09:13,640
我需要加一个优化项

338
00:09:13,640 --> 00:09:15,400
optimization是吧

339
00:09:15,400 --> 00:09:18,300
完了里面呢加上一个属性叫什么叫minimizer是吧

340
00:09:18,300 --> 00:09:19,200
这叫优化像

341
00:09:19,200 --> 00:09:21,400
优化像

342
00:09:21,400 --> 00:09:25,060
优化像像像这个像是吧

343
00:09:25,060 --> 00:09:29,000
完了里面呢我可以加上一个minimizer是吧

344
00:09:29,000 --> 00:09:29,800
完了里面呢

345
00:09:29,800 --> 00:09:34,200
我就可以去用这样一个插件叫new optimize css是吧

346
00:09:34,200 --> 00:09:36,000
优化css

347
00:09:36,000 --> 00:09:39,100
new的

348
00:09:39,100 --> 00:09:41,500
那这里面用完以后啊

349
00:09:41,500 --> 00:09:43,300
当然了优化像肯定是个数组啊

350
00:09:43,300 --> 00:09:44,900
因为你还有优化gs是吧

351
00:09:45,200 --> 00:09:46,700
ok 再来试试吧

352
00:09:46,700 --> 00:09:48,400
看能不能帮我们去优化

353
00:09:48,400 --> 00:09:49,900
来一个让软表的

354
00:09:49,900 --> 00:09:51,700
那这时候你会发现啊

355
00:09:51,700 --> 00:09:53,200
css可以压缩了

356
00:09:53,200 --> 00:09:54,100
但是gs怎么样

357
00:09:54,100 --> 00:09:55,000
我没有压缩

358
00:09:55,000 --> 00:09:56,100
你看是不是这样啊

359
00:09:56,100 --> 00:09:57,100
我找到man

360
00:09:57,100 --> 00:09:58,900
看是可以的是吧

361
00:09:58,900 --> 00:09:59,700
是优化了是吧

362
00:09:59,700 --> 00:10:00,200
一行

363
00:10:00,200 --> 00:10:01,600
但是我们的gs呢

364
00:10:01,600 --> 00:10:02,500
gs叫什么

365
00:10:02,500 --> 00:10:03,000
看一眼

366
00:10:03,000 --> 00:10:03,700
三六

367
00:10:03,700 --> 00:10:06,400
这里呢一样有三六是吧

368
00:10:06,400 --> 00:10:07,600
看是不是又变过去了

369
00:10:07,600 --> 00:10:09,800
所以这里面如果用了这个插件以后啊

370
00:10:09,800 --> 00:10:11,400
就必须用一下我们的这个

371
00:10:11,400 --> 00:10:12,500
aglify gs

372
00:10:12,500 --> 00:10:13,700
人家也明确表明了

373
00:10:13,700 --> 00:10:14,500
这个aglify呢

374
00:10:14,500 --> 00:10:15,600
应该也是个插件

375
00:10:15,600 --> 00:10:18,000
叫aglify gs vp plug in

376
00:10:18,000 --> 00:10:20,500
这里面我就安装一下

377
00:10:20,500 --> 00:10:21,000
是吧

378
00:10:21,000 --> 00:10:22,800
名字都是人家给好的

379
00:10:22,800 --> 00:10:24,000
插件就有这样的作用

380
00:10:24,000 --> 00:10:24,900
ok

381
00:10:24,900 --> 00:10:27,900
我在这里就去引一下这个插件

382
00:10:27,900 --> 00:10:30,900
叫aglify gs

383
00:10:30,900 --> 00:10:31,400
对吧

384
00:10:31,400 --> 00:10:33,100
plug in等于require

385
00:10:33,100 --> 00:10:34,000
它是吧

386
00:10:34,000 --> 00:10:35,500
用的时候可以怎么样去new

387
00:10:35,500 --> 00:10:36,000
是吧

388
00:10:36,000 --> 00:10:36,700
new它

389
00:10:36,700 --> 00:10:38,000
其实默认new就可以了

390
00:10:38,000 --> 00:10:39,400
但这里面还有些参数

391
00:10:39,400 --> 00:10:40,400
给大家看看吧

392
00:10:40,400 --> 00:10:43,600
第一个叫比如说是否用缓存是吧

393
00:10:43,600 --> 00:10:44,500
然后加上是吧

394
00:10:44,500 --> 00:10:45,200
这是矿存处

395
00:10:45,200 --> 00:10:47,220
并且是否是并发大包

396
00:10:47,220 --> 00:10:47,520
对吧

397
00:10:47,520 --> 00:10:48,280
叫perial处

398
00:10:48,280 --> 00:10:50,480
就是我们可以一起压缩多个

399
00:10:50,480 --> 00:10:50,740
是吧

400
00:10:50,740 --> 00:10:52,620
同样这里面还有就是sauce map

401
00:10:52,620 --> 00:10:54,320
如果你设置了sauce map

402
00:10:54,320 --> 00:10:55,380
后面我会讲

403
00:10:55,380 --> 00:10:57,060
就是我们压缩完GS

404
00:10:57,060 --> 00:10:58,300
它可能会有一些

405
00:10:58,300 --> 00:11:00,620
比如说我们把S5变成S5了

406
00:11:00,620 --> 00:11:01,960
我需要一个圆码映射

407
00:11:01,960 --> 00:11:03,020
来更好的调试

408
00:11:03,020 --> 00:11:05,420
所以这里我也要加上这样一个sauce map

409
00:11:05,420 --> 00:11:06,420
默认就好了

410
00:11:06,420 --> 00:11:07,960
这里面沾过来

411
00:11:07,960 --> 00:11:08,980
我再去跑一下

412
00:11:08,980 --> 00:11:10,300
点错了

413
00:11:10,300 --> 00:11:10,960
run build

414
00:11:10,960 --> 00:11:13,900
看看效果是吧

415
00:11:13,900 --> 00:11:15,760
这里面出来出来

416
00:11:15,760 --> 00:11:17,860
就可以实现压缩了

417
00:11:17,860 --> 00:11:19,540
他告诉我这东西有点问题

418
00:11:19,540 --> 00:11:20,800
说bundle

419
00:11:20,800 --> 00:11:21,860
说token name

420
00:11:21,860 --> 00:11:22,140
是吧

421
00:11:22,140 --> 00:11:22,800
看了一下

422
00:11:22,800 --> 00:11:25,840
好像这个东西是因为没有处理我们的GS导致的

423
00:11:25,840 --> 00:11:29,140
这里面我们现在还没有对GS作用处理

424
00:11:29,140 --> 00:11:31,240
这里面我们就先不管错误了

425
00:11:31,240 --> 00:11:32,260
或者你可以这样

426
00:11:32,260 --> 00:11:35,140
先把当前我们这两个关于GS文件的先删掉

427
00:11:35,140 --> 00:11:35,660
是吧

428
00:11:35,660 --> 00:11:38,140
好了我再跑一下应该就可以了

429
00:11:38,140 --> 00:11:39,160
再来看看效果

430
00:11:39,160 --> 00:11:41,440
运行一下

431
00:11:42,840 --> 00:11:45,140
嗯看看这时候是不是就没有你的异常了

432
00:11:45,140 --> 00:11:48,140
那等会我们讲这个babel的时候配置一下应该就好了

433
00:11:48,140 --> 00:11:50,840
哎那好那这里呢我们可以看看是不是也压缩了

434
00:11:50,840 --> 00:11:52,740
同样css的也进行处理了

435
00:11:52,740 --> 00:11:55,740
ok那现在呢我们就接着来说一下

436
00:11:55,740 --> 00:11:57,860
gs呢是怎么进行配置的

