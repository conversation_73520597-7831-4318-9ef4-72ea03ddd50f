1
00:00:00,000 --> 00:00:01,560
本节呢

2
00:00:01,560 --> 00:00:04,180
我们来讲一下这个webpack中比较重要的一个概念

3
00:00:04,180 --> 00:00:05,120
叫动态连接库

4
00:00:05,120 --> 00:00:06,660
那我们要掌握呢

5
00:00:06,660 --> 00:00:07,740
什么是动态连接库

6
00:00:07,740 --> 00:00:09,240
而且使用动态连接库呢

7
00:00:09,240 --> 00:00:10,260
它能解决什么问题

8
00:00:10,260 --> 00:00:12,380
完了怎么去使用这样一个动态连接库

9
00:00:12,380 --> 00:00:13,220
那好了

10
00:00:13,220 --> 00:00:14,400
那我们这里呢

11
00:00:14,400 --> 00:00:15,920
就先把这个代码改造一下

12
00:00:15,920 --> 00:00:16,600
我们呢

13
00:00:16,600 --> 00:00:17,560
把我们的read呢

14
00:00:17,560 --> 00:00:18,760
引入进来

15
00:00:18,760 --> 00:00:19,420
那这里呢

16
00:00:19,420 --> 00:00:20,720
我们需要先安装一下

17
00:00:20,720 --> 00:00:21,540
en add

18
00:00:21,540 --> 00:00:22,740
我们要安装read

19
00:00:22,740 --> 00:00:24,020
还有我们的read all

20
00:00:24,020 --> 00:00:25,460
这里呢

21
00:00:25,460 --> 00:00:26,240
我就先默认认为

22
00:00:26,240 --> 00:00:26,740
大家呢

23
00:00:26,740 --> 00:00:28,300
已经掌握了read和read all

24
00:00:28,300 --> 00:00:31,120
我们如果你还是没有掌握的话

25
00:00:31,120 --> 00:00:33,000
你可以参加我们的珠峰架构课

26
00:00:33,000 --> 00:00:35,940
这里面我们也包含了所有的read的课程

27
00:00:35,940 --> 00:00:37,560
我们就直接来应用一下

28
00:00:37,560 --> 00:00:40,240
第一步我们需要导入我们的read

29
00:00:40,240 --> 00:00:42,340
react

30
00:00:42,340 --> 00:00:43,700
完了from我们的read

31
00:00:43,700 --> 00:00:46,340
同样我们还需要我们这样一个read on

32
00:00:46,340 --> 00:00:49,120
当然了我就直接把它的render方法拿出来了

33
00:00:49,120 --> 00:00:50,580
这里就不多说了

34
00:00:50,580 --> 00:00:52,320
from我们的这样一个read on

35
00:00:52,320 --> 00:00:55,080
完了同样我们可以怎么样

36
00:00:55,080 --> 00:00:56,720
是不是通过read down

37
00:00:56,720 --> 00:00:58,400
我们可以在这来个叫叫runder

38
00:00:58,400 --> 00:00:59,760
那渲染的时候

39
00:00:59,760 --> 00:01:01,240
我们可以直接渲染一个标签

40
00:01:01,240 --> 00:01:02,160
标签呢

41
00:01:02,160 --> 00:01:03,340
我可以写上一个gss

42
00:01:03,340 --> 00:01:04,400
同样呢

43
00:01:04,400 --> 00:01:04,860
那这里呢

44
00:01:04,860 --> 00:01:07,460
我们一样可以渲染到我们这样一个节点里

45
00:01:07,460 --> 00:01:08,440
叫windowroot

46
00:01:08,440 --> 00:01:09,380
这个root呢

47
00:01:09,380 --> 00:01:11,080
指的就是我们atml这样一个文件

48
00:01:11,080 --> 00:01:12,440
在这里面配的啊

49
00:01:12,440 --> 00:01:12,720
root

50
00:01:12,720 --> 00:01:15,740
那同样我在这个配置文件里面也简单配置一下吧

51
00:01:15,740 --> 00:01:16,960
那默认情况下呢

52
00:01:16,960 --> 00:01:18,240
我可以配一个叫dv server

53
00:01:18,240 --> 00:01:19,020
还记得吧

54
00:01:19,020 --> 00:01:19,720
短过号呢

55
00:01:19,720 --> 00:01:20,680
我可以自己配一下

56
00:01:20,680 --> 00:01:21,940
比如说pot呢是3000

57
00:01:21,940 --> 00:01:23,100
同样呢

58
00:01:23,100 --> 00:01:24,100
我们写完代码以后

59
00:01:24,100 --> 00:01:24,800
写完来个open

60
00:01:24,800 --> 00:01:25,720
open has

61
00:01:25,720 --> 00:01:27,200
就是自动打开软件

62
00:01:27,200 --> 00:01:27,820
哎

63
00:01:27,820 --> 00:01:28,400
完了呢

64
00:01:28,400 --> 00:01:28,940
并且呢

65
00:01:28,940 --> 00:01:29,840
我们希望他哎

66
00:01:29,840 --> 00:01:31,080
打包后的结果呢

67
00:01:31,080 --> 00:01:31,900
可以放到这个

68
00:01:31,900 --> 00:01:33,000
找到这个content

69
00:01:33,000 --> 00:01:34,080
贝斯目录下

70
00:01:34,080 --> 00:01:34,620
还记得吧

71
00:01:34,620 --> 00:01:35,380
那好

72
00:01:35,380 --> 00:01:36,720
我可以指定到第四目录

73
00:01:36,720 --> 00:01:38,000
那我把这第四目录啊

74
00:01:38,000 --> 00:01:38,580
就清空了

75
00:01:38,580 --> 00:01:40,100
当然了

76
00:01:40,100 --> 00:01:40,820
找不到也没关系

77
00:01:40,820 --> 00:01:41,280
找不到的话

78
00:01:41,280 --> 00:01:42,220
他会找内存中的

79
00:01:42,220 --> 00:01:43,160
这样的话

80
00:01:43,160 --> 00:01:43,940
为了保险

81
00:01:43,940 --> 00:01:44,920
那我在这里呢

82
00:01:44,920 --> 00:01:46,360
就直接来启用一下

83
00:01:46,360 --> 00:01:47,320
叫npm

84
00:01:47,320 --> 00:01:48,580
软dv

85
00:01:48,580 --> 00:01:50,660
那这时候呢

86
00:01:50,660 --> 00:01:51,940
他应该会启动这个服务

87
00:01:51,940 --> 00:01:52,480
三天灯口

88
00:01:52,480 --> 00:01:53,240
打开软件

89
00:01:53,240 --> 00:01:54,000
并且呢

90
00:01:54,000 --> 00:01:54,740
给我们去怎么样

91
00:01:54,740 --> 00:01:55,840
找到第四目路

92
00:01:55,840 --> 00:01:56,940
我来刷新一下

93
00:01:56,940 --> 00:01:58,480
发现没有东西

94
00:01:58,480 --> 00:01:59,680
来看看结果

95
00:01:59,680 --> 00:02:00,980
会有什么效果

96
00:02:00,980 --> 00:02:02,340
这里面他告诉我的说

97
00:02:02,340 --> 00:02:03,540
Redome没有找到

98
00:02:03,540 --> 00:02:07,140
这里面我可以把我的Redome的引进来

99
00:02:07,140 --> 00:02:08,260
Redome就不用了

100
00:02:08,260 --> 00:02:10,360
刷新一下

101
00:02:10,360 --> 00:02:13,140
这里面已经出现了我们的gsx

102
00:02:13,140 --> 00:02:14,220
说明怎么样

103
00:02:14,220 --> 00:02:15,600
是不是这个效果已经OK了

104
00:02:15,600 --> 00:02:17,160
我大妈就可以跑了

105
00:02:17,160 --> 00:02:18,780
这里面他解析Redome语法

106
00:02:18,780 --> 00:02:21,200
当然说了主要靠的就是这样一个插件

107
00:02:21,200 --> 00:02:23,140
叫at babel Preset React

108
00:02:23,140 --> 00:02:25,340
但是我们可以看到

109
00:02:25,340 --> 00:02:27,100
这个打包的过程中

110
00:02:27,100 --> 00:02:28,480
他打包出来的结果

111
00:02:28,480 --> 00:02:29,340
其实是非常大的

112
00:02:29,340 --> 00:02:30,060
我们可以在这

113
00:02:30,060 --> 00:02:31,320
为了能看到效果

114
00:02:31,320 --> 00:02:32,500
我把它干掉

115
00:02:32,500 --> 00:02:33,400
我再重新

116
00:02:33,400 --> 00:02:34,600
软build一下

117
00:02:34,600 --> 00:02:35,480
软build

118
00:02:35,480 --> 00:02:37,940
看到效果

119
00:02:37,940 --> 00:02:39,180
你看到

120
00:02:39,180 --> 00:02:40,620
他这个结果大致有

121
00:02:40,620 --> 00:02:42,380
858k

122
00:02:42,380 --> 00:02:44,160
说明这个文件非常大

123
00:02:44,160 --> 00:02:45,500
这时候我要怎么做

124
00:02:45,500 --> 00:02:46,460
我是不是希望

125
00:02:46,460 --> 00:02:47,900
能不能先把

126
00:02:47,900 --> 00:02:48,900
Royant和Royant

127
00:02:48,900 --> 00:02:49,260
怎么样

128
00:02:49,260 --> 00:02:50,240
先抽离出去

129
00:02:50,240 --> 00:02:51,060
到时候

130
00:02:51,060 --> 00:02:51,840
我打包的时候

131
00:02:51,840 --> 00:02:52,120
怎么样

132
00:02:52,120 --> 00:02:52,860
就不打包

133
00:02:52,860 --> 00:02:53,540
这两个文件

134
00:02:53,540 --> 00:02:55,560
因为这两个文件属于第三方库

135
00:02:55,560 --> 00:02:57,400
而且他也不需要怎么样

136
00:02:57,400 --> 00:02:58,580
是不是也不会去更改

137
00:02:58,580 --> 00:02:59,380
一更改他

138
00:02:59,380 --> 00:03:00,240
也没有效果

139
00:03:00,240 --> 00:03:01,380
应该不用重新打包

140
00:03:01,380 --> 00:03:03,560
这时候我们可以先独立的

141
00:03:03,560 --> 00:03:05,120
把Write和WriteDOM怎么样

142
00:03:05,120 --> 00:03:05,740
进行打包

143
00:03:05,740 --> 00:03:07,640
这时候我们就会想到怎么样

144
00:03:07,640 --> 00:03:09,280
是不是再见这样一个配置文件

145
00:03:09,280 --> 00:03:12,760
Wipegconfig.write.js

146
00:03:12,760 --> 00:03:14,840
这里我就认为他可以怎么样

147
00:03:14,840 --> 00:03:16,600
单独去打包WriteDOM

148
00:03:16,600 --> 00:03:17,940
打包完以后

149
00:03:17,940 --> 00:03:19,180
在我们开发的时候

150
00:03:19,180 --> 00:03:20,980
引用我们打包好的文件

151
00:03:20,980 --> 00:03:21,740
这样的话

152
00:03:21,740 --> 00:03:23,940
RETE和RETEDOM就不会重新打包了

153
00:03:23,940 --> 00:03:25,120
相当于一个优化

154
00:03:25,120 --> 00:03:27,180
那也同样这里面一样

155
00:03:27,180 --> 00:03:28,800
我们先建一个测试文件

156
00:03:28,800 --> 00:03:30,120
为了先打包它

157
00:03:30,120 --> 00:03:32,440
默认情况下我们在这里

158
00:03:32,440 --> 00:03:33,840
module is pause

159
00:03:33,840 --> 00:03:34,460
中培训

160
00:03:34,460 --> 00:03:37,720
你会发现你导出这样一个中培训

161
00:03:37,720 --> 00:03:39,620
如果直接打包这样一个模块

162
00:03:39,620 --> 00:03:41,700
这个结果是无法拿到的

163
00:03:41,700 --> 00:03:42,560
咱来试一下

164
00:03:42,560 --> 00:03:44,780
我同样打包的话

165
00:03:44,780 --> 00:03:47,080
需要以我们这样一个pass模块

166
00:03:47,080 --> 00:03:50,380
快速的把基本结构写出来

167
00:03:50,380 --> 00:03:55,020
需要导出一个叫安锤安锤呢

168
00:03:55,020 --> 00:03:56,920
我这里面可以放个对象还记得吧

169
00:03:56,920 --> 00:04:01,000
完了前面来个泰斯找到的就是src下的泰斯的杰斯

170
00:04:01,000 --> 00:04:02,220
那同样呢

171
00:04:02,220 --> 00:04:03,760
我们这里面还需要一个出口

172
00:04:03,760 --> 00:04:04,800
那出口的话呢

173
00:04:04,800 --> 00:04:05,460
我就写到下面

174
00:04:05,460 --> 00:04:06,540
output

175
00:04:06,540 --> 00:04:08,860
完了里面一样

176
00:04:08,860 --> 00:04:10,180
我们需要来个feel内

177
00:04:10,180 --> 00:04:12,060
我这里面是不是可以来个方过号

178
00:04:12,060 --> 00:04:13,240
这里面呢

179
00:04:13,240 --> 00:04:14,280
我可以取他的名字

180
00:04:14,280 --> 00:04:15,800
相当于打包出来的结果呀

181
00:04:15,800 --> 00:04:17,440
也叫泰斯的杰斯

182
00:04:17,440 --> 00:04:18,580
那这里呢

183
00:04:18,580 --> 00:04:20,080
我们最后还要放一个什么来着

184
00:04:20,080 --> 00:04:22,880
是不是放一个叫passpass是个绝对路径

185
00:04:22,880 --> 00:04:26,280
围揍完了可以在这里面直接写上刚刚第2 name

186
00:04:26,280 --> 00:04:27,880
还有我们当前的这样一个模块

187
00:04:27,880 --> 00:04:30,080
那模块应该指向的就是我们的第四目录

188
00:04:30,080 --> 00:04:31,280
那好了

189
00:04:31,280 --> 00:04:33,380
那这里面我们最好还配一个mode

190
00:04:33,380 --> 00:04:35,280
否则会报以报一场是吧

191
00:04:35,280 --> 00:04:36,880
bvlopmnt

192
00:04:36,880 --> 00:04:39,380
那咱们来看一看吧

193
00:04:39,380 --> 00:04:41,980
那我执行的时候就执行npx

194
00:04:41,980 --> 00:04:43,880
比如说webpack

195
00:04:43,880 --> 00:04:44,680
当然webpack的话

196
00:04:44,680 --> 00:04:46,180
你还要传个配置文件对吧

197
00:04:46,180 --> 00:04:48,780
这个配置文件叫webpack.config

198
00:04:49,480 --> 00:04:52,580
完了第二react.js哎呦好长啊

199
00:04:52,580 --> 00:04:53,320
写短点好了

200
00:04:53,320 --> 00:04:54,880
把这个config去掉好了

201
00:04:54,880 --> 00:04:56,180
那现在我们可以看到啊

202
00:04:56,180 --> 00:04:58,720
它是不是打包出来个泰斯点杰子

203
00:04:58,720 --> 00:04:59,020
那好

204
00:04:59,020 --> 00:05:01,880
我们来看一下泰斯点杰子的这个目录

205
00:05:01,880 --> 00:05:02,580
那这个文件啊

206
00:05:02,580 --> 00:05:03,580
为了看着清晰一点

207
00:05:03,580 --> 00:05:04,180
还记得吧

208
00:05:04,180 --> 00:05:06,380
我们其实以前也研究过这代码啊

209
00:05:06,380 --> 00:05:07,480
这里把它删定

210
00:05:07,480 --> 00:05:09,580
那同样的这个require function放在这

211
00:05:09,580 --> 00:05:10,980
完了底下这一坨代码呀

212
00:05:10,980 --> 00:05:12,680
我就把它先删掉啊

213
00:05:12,680 --> 00:05:13,380
呃到哪呢

214
00:05:13,380 --> 00:05:14,080
到这是吧

215
00:05:14,080 --> 00:05:14,780
还记得吧

216
00:05:14,780 --> 00:05:16,680
这是没用代码啊

217
00:05:16,680 --> 00:05:17,680
其实你可以看到啊

218
00:05:17,680 --> 00:05:19,980
他呢会引用我们的这个tethergs

219
00:05:19,980 --> 00:05:21,880
完了并且呢把中培训呢

220
00:05:21,880 --> 00:05:23,580
孵到这个module is pause 上

221
00:05:23,580 --> 00:05:24,480
是这样吧

222
00:05:24,480 --> 00:05:26,180
因为这里面呢拿到这来的

223
00:05:26,180 --> 00:05:27,180
但是拿完以后呢

224
00:05:27,180 --> 00:05:29,480
他默认这个函数返回的结果呀

225
00:05:29,480 --> 00:05:31,880
并没有被外面这个函数所接收

226
00:05:31,880 --> 00:05:33,180
应该可以看得懂

227
00:05:33,180 --> 00:05:35,180
那就说我们调用完这个方法以后

228
00:05:35,180 --> 00:05:37,180
我这个结果呢也默认给返回了

229
00:05:37,180 --> 00:05:38,080
但是返回的话呀

230
00:05:38,080 --> 00:05:39,180
这个方法怎么样

231
00:05:39,180 --> 00:05:40,880
他的返回值我并没有要

232
00:05:40,880 --> 00:05:44,480
你看最后返回的是不是也是这个require方法的返回结果呀

233
00:05:44,480 --> 00:05:45,680
就是module is pause

234
00:05:45,780 --> 00:05:48,480
相当于这个函数返回的其实是什么

235
00:05:48,480 --> 00:05:51,080
是不是整个就是我们这样一个中备训

236
00:05:51,080 --> 00:05:53,080
但是呢我并没有去拿到

237
00:05:53,080 --> 00:05:54,180
那这时候我怎么办呢

238
00:05:54,180 --> 00:05:54,780
我可以这样做

239
00:05:54,780 --> 00:05:55,380
你看啊

240
00:05:55,380 --> 00:05:57,080
赖特A等于他

241
00:05:57,080 --> 00:05:58,980
那这时候我可以在最下面怎么样

242
00:05:58,980 --> 00:06:01,180
打印这样一个A

243
00:06:01,180 --> 00:06:03,180
说相当于等这个函数执行完

244
00:06:03,180 --> 00:06:05,380
我把执行完这个结果怎么样

245
00:06:05,380 --> 00:06:07,780
是不是返还给了这个A的变量

246
00:06:07,780 --> 00:06:09,680
那这时候我说就可以拿到这样一个结果了

247
00:06:09,680 --> 00:06:10,580
你看啊

248
00:06:10,580 --> 00:06:11,780
这里面

249
00:06:11,780 --> 00:06:12,680
是中备训

250
00:06:12,680 --> 00:06:13,180
那好了

251
00:06:13,180 --> 00:06:16,220
那我们不能每次都这样来写也很麻烦

252
00:06:16,220 --> 00:06:17,620
那这时候可以怎么办呢

253
00:06:17,620 --> 00:06:17,940
哎

254
00:06:17,940 --> 00:06:20,540
我是不是可以给当起这样输出的结果

255
00:06:20,540 --> 00:06:21,940
加上这样个名字是吧

256
00:06:21,940 --> 00:06:22,980
那怎么加呢

257
00:06:22,980 --> 00:06:25,480
这里面我就可以给他加一个叫library

258
00:06:25,480 --> 00:06:26,640
哎

259
00:06:26,640 --> 00:06:27,680
library

260
00:06:27,680 --> 00:06:29,780
完了里面呢你给他一个东西叫哎

261
00:06:29,780 --> 00:06:30,940
就默认啊

262
00:06:30,940 --> 00:06:34,280
会把这样一个结果完了用个比如换个名字吧

263
00:06:34,280 --> 00:06:34,840
名字一样了

264
00:06:34,840 --> 00:06:35,440
ab

265
00:06:35,440 --> 00:06:36,680
那再来看看效果啊

266
00:06:36,680 --> 00:06:37,280
懂你

267
00:06:37,280 --> 00:06:39,280
这时候呢

268
00:06:39,280 --> 00:06:40,620
你可以看看结果啊

269
00:06:40,620 --> 00:06:41,240
那现在啊

270
00:06:41,240 --> 00:06:42,940
我们这个产生的这个结果

271
00:06:42,940 --> 00:06:45,040
他是不是变成ab了

272
00:06:45,040 --> 00:06:47,240
所以说我们可以自己去指定这个名字

273
00:06:47,240 --> 00:06:48,840
那当然了他还有其他参数

274
00:06:48,840 --> 00:06:49,840
我就简单介绍一下

275
00:06:49,840 --> 00:06:52,400
还有这个library什么呢叫target

276
00:06:52,400 --> 00:06:54,440
比如说呀我并不希望用wad的方式

277
00:06:54,440 --> 00:06:55,540
那好我还可以用什么呢

278
00:06:55,540 --> 00:06:58,540
比如说可以用这个commonjs指的就是什么

279
00:06:58,540 --> 00:07:01,040
我可以把我们当前这样一个结果呀

280
00:07:01,040 --> 00:07:03,440
放到s pos属性上

281
00:07:03,440 --> 00:07:05,140
试试是吧

282
00:07:05,140 --> 00:07:07,040
相当于是在node中使用

283
00:07:07,040 --> 00:07:09,440
那这里面我可以看一下是不是变成了哦

284
00:07:09,440 --> 00:07:10,780
泰斯在这是吧

285
00:07:10,780 --> 00:07:12,240
是变成s pos ab

286
00:07:12,240 --> 00:07:13,540
那同样除了这个呢

287
00:07:13,540 --> 00:07:15,240
可能还有我们一些常见的啊

288
00:07:15,240 --> 00:07:16,000
我给你去配

289
00:07:16,000 --> 00:07:18,600
比如说umd哎统一资源模块是吧

290
00:07:18,600 --> 00:07:20,000
那好一样

291
00:07:20,000 --> 00:07:21,400
那这里呢可以看一看吧

292
00:07:21,400 --> 00:07:22,580
是不是都都可以啊

293
00:07:22,580 --> 00:07:24,440
是不是变成了我们的umd模式

294
00:07:24,440 --> 00:07:26,080
所以这里面支持很多啊

295
00:07:26,080 --> 00:07:28,500
比如说像刚才我们看到的那个commongs

296
00:07:28,500 --> 00:07:31,080
哇什么zs还有什么好多好多啊

297
00:07:31,080 --> 00:07:32,500
我就不予介绍了哎

298
00:07:32,500 --> 00:07:34,280
那这里面我们主要用到的还是哇

299
00:07:34,280 --> 00:07:37,640
那我们现在是不是就把这样一个结果

300
00:07:37,640 --> 00:07:39,000
放到了我们的这个ab上

301
00:07:39,000 --> 00:07:40,440
那好了

302
00:07:40,440 --> 00:07:41,340
那同样啊

303
00:07:41,340 --> 00:07:42,540
我们现在要干什么事

304
00:07:42,540 --> 00:07:44,220
打包的并不是test

305
00:07:44,220 --> 00:07:46,020
而是我们的rect

306
00:07:46,020 --> 00:07:46,620
那好

307
00:07:46,620 --> 00:07:48,660
那这里面的rect我需要打包谁呢

308
00:07:48,660 --> 00:07:49,620
是不是两个包

309
00:07:49,620 --> 00:07:51,060
一个叫rect

310
00:07:51,060 --> 00:07:53,700
还有一个叫什么叫rect-到

311
00:07:53,700 --> 00:07:56,420
那此时这个名字就会跑到这一来

312
00:07:56,420 --> 00:07:56,980
是不是

313
00:07:56,980 --> 00:07:58,500
那这个library的名字

314
00:07:58,500 --> 00:07:59,340
我改个名吧

315
00:07:59,340 --> 00:08:00,180
为了好看

316
00:08:00,180 --> 00:08:02,260
这里面我就来个叫dll

317
00:08:02,260 --> 00:08:02,900
这样一个文件

318
00:08:02,900 --> 00:08:03,460
这是什么呢

319
00:08:03,460 --> 00:08:05,140
就是产生的文件名

320
00:08:05,140 --> 00:08:06,340
这个应该没问题啊

321
00:08:06,340 --> 00:08:07,300
产生的

322
00:08:07,300 --> 00:08:10,980
产生的文件名

323
00:08:10,980 --> 00:08:11,840
完了这里面呢

324
00:08:11,840 --> 00:08:13,240
我们也不叫这个他改他

325
00:08:13,240 --> 00:08:14,480
我说你不给他就是哇

326
00:08:14,480 --> 00:08:15,620
那这里面呢

327
00:08:15,620 --> 00:08:16,460
我也改个名字

328
00:08:16,460 --> 00:08:18,800
这个名字指的是不是就是这个产生文件

329
00:08:18,800 --> 00:08:20,780
它导出的那个变量叫什么名字啊

330
00:08:20,780 --> 00:08:21,020
是吧

331
00:08:21,020 --> 00:08:21,580
那好

332
00:08:21,580 --> 00:08:22,860
那这个变量叫什么呢

333
00:08:22,860 --> 00:08:25,540
就叫他-dl应该叫什么rect

334
00:08:25,540 --> 00:08:28,520
那这时候啊

335
00:08:28,520 --> 00:08:31,080
我们就把我们的这个插件拿进来plugins

336
00:08:31,080 --> 00:08:32,040
我说了

337
00:08:32,040 --> 00:08:34,300
我需要把当前这样一个文件呀

338
00:08:34,300 --> 00:08:35,960
定义成一个动态连接库

339
00:08:35,960 --> 00:08:36,760
那好

340
00:08:36,760 --> 00:08:37,720
那也相当于怎么样

341
00:08:37,720 --> 00:08:38,560
是不是有一个清单

342
00:08:38,560 --> 00:08:40,860
可以找到这个文件中的所有模块

343
00:08:40,860 --> 00:08:41,980
比如 write write on

344
00:08:41,980 --> 00:08:43,620
这里面我需要怎么做

345
00:08:43,620 --> 00:08:45,400
肯定需要这样一个webpack

346
00:08:45,400 --> 00:08:47,440
我说了是webpack内置的

347
00:08:47,440 --> 00:08:49,120
而且是个插件

348
00:08:49,120 --> 00:08:51,280
所以说我们用的时候就需要new

349
00:08:51,280 --> 00:08:51,920
对吧

350
00:08:51,920 --> 00:08:53,600
这个叫webpack

351
00:08:53,600 --> 00:08:54,680
dr什么呢

352
00:08:54,680 --> 00:08:57,520
drdllplugin

353
00:08:57,520 --> 00:08:59,600
完了里面他说了可以放个参数

354
00:08:59,600 --> 00:08:59,840
对吧

355
00:08:59,840 --> 00:09:00,640
那好听人家的

356
00:09:00,640 --> 00:09:02,080
这里面要求什么

357
00:09:02,080 --> 00:09:04,440
就是我们要产生这样一个名字

358
00:09:04,440 --> 00:09:05,160
名字叫什么

359
00:09:05,160 --> 00:09:07,900
这个名字要和我们这样一个名字怎么样

360
00:09:07,900 --> 00:09:08,820
同名

361
00:09:08,820 --> 00:09:09,780
就是这个里面的name

362
00:09:09,780 --> 00:09:12,660
name要等等我们这样一个library

363
00:09:12,660 --> 00:09:14,260
这是人家规定好的

364
00:09:14,260 --> 00:09:17,480
要不到会怎么去找到这个文件的丁关系呢

365
00:09:17,480 --> 00:09:17,720
是吧

366
00:09:17,720 --> 00:09:18,160
好

367
00:09:18,160 --> 00:09:19,180
我就把这个东西放在这

368
00:09:19,180 --> 00:09:21,900
当然这里面加个字不串

369
00:09:21,900 --> 00:09:23,340
交完以后呢

370
00:09:23,340 --> 00:09:24,120
同样我说了

371
00:09:24,120 --> 00:09:25,420
你要给人家一个东西叫什么

372
00:09:25,420 --> 00:09:26,460
叫入境

373
00:09:26,460 --> 00:09:26,900
对吧

374
00:09:26,900 --> 00:09:28,240
你要产生这样一个清单

375
00:09:28,240 --> 00:09:29,340
这个清单呢

376
00:09:29,340 --> 00:09:30,560
可以找到这个文件

377
00:09:30,560 --> 00:09:31,920
那这清单怎么给呢

378
00:09:31,920 --> 00:09:32,640
非常简单啊

379
00:09:32,640 --> 00:09:33,480
它就是一个jason

380
00:09:33,480 --> 00:09:34,320
pass

381
00:09:34,320 --> 00:09:34,720
dr

382
00:09:34,720 --> 00:09:35,240
对吧

383
00:09:35,240 --> 00:09:35,680
vizal

384
00:09:35,680 --> 00:09:38,500
我就给它生成到drname下的一个叫

385
00:09:38,500 --> 00:09:42,200
dist下载什么叫manifest.json

386
00:09:42,200 --> 00:09:44,160
那好了

387
00:09:44,160 --> 00:09:45,640
我再给你打包一下

388
00:09:45,640 --> 00:09:47,240
你看看效果

389
00:09:47,240 --> 00:09:47,640
运行

390
00:09:47,640 --> 00:09:50,400
这是他的默认page

391
00:09:50,400 --> 00:09:54,800
那现在我们就可以打包出来一个这样的叫下滑线dl rect

392
00:09:54,800 --> 00:09:56,540
还有个manifest.json

393
00:09:56,540 --> 00:09:58,100
那你看看这个json吧

394
00:09:58,100 --> 00:09:59,200
这个json里面呢

395
00:09:59,200 --> 00:10:00,120
其实他就定义了

396
00:10:00,120 --> 00:10:01,720
当前这个变量里面呀

397
00:10:01,720 --> 00:10:04,040
应该会有这些这些这些这些都是模块

398
00:10:04,040 --> 00:10:05,680
那这个变量哪来的呢

399
00:10:05,680 --> 00:10:09,580
我说了是不是他是通过这个GS里面会声明这样一个变量

400
00:10:09,580 --> 00:10:10,700
有吧

401
00:10:10,700 --> 00:10:12,840
那同样我说了这里面是不是有路径

402
00:10:12,840 --> 00:10:14,460
那这个路径哪来的呢

403
00:10:14,460 --> 00:10:16,520
是不是可以去这个变量里面怎么样

404
00:10:16,520 --> 00:10:18,000
找到这样一个路径

405
00:10:18,000 --> 00:10:21,180
所以我们管这个minifest就叫什么呢

406
00:10:21,180 --> 00:10:22,180
叫一个任务清单

407
00:10:22,180 --> 00:10:24,800
它对应这样一个名字有很多任务

408
00:10:24,800 --> 00:10:25,940
那这些任务去哪找呢

409
00:10:25,940 --> 00:10:28,020
就去同名的这样一个变量上的怎么样

410
00:10:28,020 --> 00:10:29,560
去找这样一个结果

411
00:10:29,560 --> 00:10:30,760
因为它里面是不是就

412
00:10:30,760 --> 00:10:32,120
你看刚才看到了有点长

413
00:10:32,120 --> 00:10:33,260
我再拉上去

414
00:10:33,260 --> 00:10:36,020
是不是就是这样的一个结果

415
00:10:36,020 --> 00:10:37,360
哎呦好长啊

416
00:10:37,360 --> 00:10:38,380
是不是就是这样一个名字

417
00:10:38,380 --> 00:10:39,300
那好了

418
00:10:39,300 --> 00:10:41,040
那现在我们定义完之后

419
00:10:41,040 --> 00:10:42,140
还需要干什么事呢

420
00:10:42,140 --> 00:10:44,540
我们是不是需要在我们刚才说了

421
00:10:44,540 --> 00:10:45,860
打包完以后

422
00:10:45,860 --> 00:10:47,660
我是不是要在我们的atml里怎么样

423
00:10:47,660 --> 00:10:49,460
去用这样一个文件

424
00:10:49,460 --> 00:10:50,840
那这文件怎么引呢

425
00:10:50,840 --> 00:10:51,840
是不是可以通过script

426
00:10:51,840 --> 00:10:53,080
来个src

427
00:10:53,080 --> 00:10:54,720
引什么呢

428
00:10:54,720 --> 00:10:56,140
是不是应该引我们.杠

429
00:10:56,140 --> 00:10:57,760
或者叫直接就杠

430
00:10:57,760 --> 00:11:01,340
叫消耗线dl什么rect.js

431
00:11:01,340 --> 00:11:04,060
那这里面引用的就是他

432
00:11:04,060 --> 00:11:05,560
但是他默认情况下

433
00:11:05,560 --> 00:11:06,820
他并不知道怎么去找

434
00:11:06,820 --> 00:11:07,760
我是不是希望

435
00:11:07,760 --> 00:11:08,800
打包的时候

436
00:11:08,800 --> 00:11:10,160
我写这个代码

437
00:11:10,160 --> 00:11:12,000
inneract或者redom

438
00:11:12,000 --> 00:11:12,720
去哪里找

439
00:11:12,720 --> 00:11:14,800
是不是去我们当前这样一个

440
00:11:14,800 --> 00:11:15,880
这个文件里去找

441
00:11:15,880 --> 00:11:17,200
但是默认情况下

442
00:11:17,200 --> 00:11:17,820
他肯定会怎么样

443
00:11:17,820 --> 00:11:18,700
是不是会打包

444
00:11:18,700 --> 00:11:19,880
那你只能说

445
00:11:19,880 --> 00:11:21,480
先去我们这样一个

446
00:11:21,480 --> 00:11:22,900
DIO东台电阶库里面去插找

447
00:11:22,900 --> 00:11:24,080
找不到的话

448
00:11:24,080 --> 00:11:24,820
怎么样再打包

449
00:11:24,820 --> 00:11:26,320
那这时候我要怎么做呢

450
00:11:26,320 --> 00:11:28,180
回到我们正式的这个配列文件里

451
00:11:28,180 --> 00:11:29,640
同样啊

452
00:11:29,640 --> 00:11:30,440
那还是个插件

453
00:11:30,440 --> 00:11:32,640
我要引用这个动态连接库

454
00:11:32,640 --> 00:11:33,700
那这里面呢

455
00:11:33,700 --> 00:11:34,840
我需要用他

456
00:11:34,840 --> 00:11:35,800
第二什么呢

457
00:11:35,800 --> 00:11:36,580
叫dl

458
00:11:36,580 --> 00:11:37,980
你看有提示啊

459
00:11:37,980 --> 00:11:40,860
叫引用我们的dl插件

460
00:11:40,860 --> 00:11:43,540
那引用的名字啊

461
00:11:43,540 --> 00:11:44,480
叫minifest

462
00:11:44,480 --> 00:11:45,520
你要引谁呢

463
00:11:45,520 --> 00:11:46,300
你是不是说了

464
00:11:46,300 --> 00:11:47,600
我要引这样一个文件

465
00:11:47,600 --> 00:11:48,660
往这个文件呢

466
00:11:48,660 --> 00:11:49,640
会去找这个变量

467
00:11:49,640 --> 00:11:50,520
找这个文件

468
00:11:50,520 --> 00:11:51,460
这个变量里面

469
00:11:51,460 --> 00:11:52,840
是不是就定义了所有模块啊

470
00:11:52,840 --> 00:11:54,100
他会先去这里面去找

471
00:11:54,100 --> 00:11:55,260
看看有没有这些

472
00:11:55,260 --> 00:11:56,440
没有的话怎么样

473
00:11:56,440 --> 00:11:57,600
再去实现打包

474
00:11:57,600 --> 00:11:59,100
那这里面怎么找呢

475
00:11:59,100 --> 00:11:59,600
是不是一样

476
00:11:59,600 --> 00:12:02,360
我many fast的引的就是pass.redo

477
00:12:02,360 --> 00:12:04,260
我们找的就是dir name下的

478
00:12:04,260 --> 00:12:06,100
这个beast目录下的什么

479
00:12:06,100 --> 00:12:08,880
many fast.jes

480
00:12:08,880 --> 00:12:10,700
引的他

481
00:12:10,700 --> 00:12:11,900
那这样的话呀

482
00:12:11,900 --> 00:12:13,040
我们再写代码的时候

483
00:12:13,040 --> 00:12:15,320
他会先去查找我们这样一个清单

484
00:12:15,320 --> 00:12:17,000
清单找不到的话怎么样

485
00:12:17,000 --> 00:12:19,680
再去真正的打包我们的right和redo

486
00:12:19,680 --> 00:12:20,940
那好了啊

487
00:12:20,940 --> 00:12:21,620
你说了那为什么

488
00:12:21,620 --> 00:12:22,760
哎天堂里还要引这个文件

489
00:12:22,760 --> 00:12:24,800
你不引怎么能找到这个变量呢

490
00:12:24,800 --> 00:12:25,480
是吧

491
00:12:25,480 --> 00:12:27,000
所以说这一面你需要怎么样

492
00:12:27,000 --> 00:12:27,460
写死

493
00:12:27,460 --> 00:12:29,120
但是这样的好处是什么

494
00:12:29,120 --> 00:12:31,460
就说我再次打包的时候是吧

495
00:12:31,460 --> 00:12:34,280
你会发现哎打包出来的结果呀就小了

496
00:12:34,280 --> 00:12:35,060
有多小呢

497
00:12:35,060 --> 00:12:36,920
看看是不是是不是就6k了

498
00:12:36,920 --> 00:12:37,560
为什么

499
00:12:37,560 --> 00:12:39,620
因为我们已经把如爱的怎么样

500
00:12:39,620 --> 00:12:41,720
是不是打包好都放到这个文件里了

501
00:12:41,720 --> 00:12:43,880
到时候要用的话怎么样是不是就可以了

502
00:12:43,880 --> 00:12:45,060
那为了方便

503
00:12:45,060 --> 00:12:46,760
npm run dv

504
00:12:46,760 --> 00:12:49,280
先跑个运行环境是吧

505
00:12:49,280 --> 00:12:50,220
跑个运行环境

506
00:12:50,220 --> 00:12:52,420
这里面是不是就可以引到我们的第四

507
00:12:52,420 --> 00:12:54,620
你看是不是让照样子可以显示

508
00:12:54,620 --> 00:12:55,980
而且呢他引的是什么

509
00:12:55,980 --> 00:12:56,920
看看效果

510
00:12:56,920 --> 00:12:57,820
network

511
00:12:58,420 --> 00:13:02,380
刷新这里面是不是引到了我们的什么DL的文件

512
00:13:02,380 --> 00:13:03,720
你看是不是就是这个查件

513
00:13:03,720 --> 00:13:05,100
那引完以后怎么样

514
00:13:05,100 --> 00:13:06,820
是不是就实现了个动态连接库

515
00:13:06,820 --> 00:13:09,600
相当于就是一个我们先把打包后的结果怎么样

516
00:13:09,600 --> 00:13:10,200
存好

517
00:13:10,200 --> 00:13:11,780
完到时候我们用的时候怎么样

518
00:13:11,780 --> 00:13:12,920
就用打包后的结果

519
00:13:12,920 --> 00:13:15,380
而且我们改代码的时候是不是也很快

520
00:13:15,380 --> 00:13:16,320
我只需要怎么样

521
00:13:16,320 --> 00:13:17,920
重新编译我自己的代码

522
00:13:17,920 --> 00:13:18,920
而这个代码怎么样

523
00:13:18,920 --> 00:13:20,760
是不是人家已经找动态连接库里去了

524
00:13:20,760 --> 00:13:22,220
这就是它的好处

525
00:13:22,220 --> 00:13:22,940
你看效果

526
00:13:22,940 --> 00:13:23,580
是不是依旧

527
00:13:23,580 --> 00:13:25,460
而且打包的话是不是只打包了

528
00:13:25,460 --> 00:13:26,960
你看是不是344K

529
00:13:26,960 --> 00:13:27,920
这383字节

530
00:13:27,920 --> 00:13:29,120
很方便了

531
00:13:29,120 --> 00:13:32,600
现在我们就实现了这样一个DL plug-in的用法

