1
00:00:00,000 --> 00:00:04,320
这里呢我们还要给我们的今天资源的进行分类

2
00:00:04,320 --> 00:00:06,800
比如说我希望css创建一个目录下

3
00:00:06,800 --> 00:00:08,440
图片呢又放到一个目录下

4
00:00:08,440 --> 00:00:10,160
那这时候呢我们可以怎么做呢

5
00:00:10,160 --> 00:00:11,840
可以在我们的这个office里面

6
00:00:11,840 --> 00:00:14,340
加上这样一个属性叫什么叫output path

7
00:00:14,340 --> 00:00:17,740
就我们可以给他一个叫输出的路径

8
00:00:17,740 --> 00:00:19,920
哎你跟他说应该去哪呢

9
00:00:19,920 --> 00:00:21,780
去这个image目录下对吧

10
00:00:21,780 --> 00:00:22,600
那可以这样去写

11
00:00:22,600 --> 00:00:24,500
那这时候呢我们再来运行一下

12
00:00:24,500 --> 00:00:25,900
OK run build

13
00:00:25,900 --> 00:00:29,520
看看他会不会把我们这个文件呢

14
00:00:29,520 --> 00:00:30,760
生成到这个目录下

15
00:00:30,760 --> 00:00:32,340
你发现是不是可以啊

16
00:00:32,340 --> 00:00:34,460
是跑到了image下的这个3D目录下

17
00:00:34,460 --> 00:00:35,180
那好了

18
00:00:35,180 --> 00:00:37,140
我们这里面是不是应该也有个image

19
00:00:37,140 --> 00:00:38,340
你看没错出来了

20
00:00:38,340 --> 00:00:39,440
那同样啊

21
00:00:39,440 --> 00:00:40,960
我们图片会引用出错呢

22
00:00:40,960 --> 00:00:42,140
这里面让我们来看一下

23
00:00:42,140 --> 00:00:43,860
是不是都会自动变成什么

24
00:00:43,860 --> 00:00:45,480
是不是image下的这个图片

25
00:00:45,480 --> 00:00:46,360
哎很方便啊

26
00:00:46,360 --> 00:00:46,980
因为我们说了

27
00:00:46,980 --> 00:00:48,360
他们默认把这个路径拿出来

28
00:00:48,360 --> 00:00:49,300
把它放到我们这里来

29
00:00:49,300 --> 00:00:51,060
哎那同样像样式啊

30
00:00:51,060 --> 00:00:51,820
应该也是ok的

31
00:00:51,820 --> 00:00:53,700
慢点css是不是也是可以的

32
00:00:53,700 --> 00:00:54,100
image

33
00:00:54,100 --> 00:00:55,560
但是我们这样的话

34
00:00:55,560 --> 00:00:56,980
我们希望这个样式啊

35
00:00:56,980 --> 00:00:58,020
也可以进行分类

36
00:00:58,020 --> 00:00:58,300
对吧

37
00:00:58,300 --> 00:01:00,220
比如说样式出到哪个目录下

38
00:01:00,220 --> 00:01:01,680
这里面也非常方便

39
00:01:01,680 --> 00:01:03,560
我可以找到比如说CSS

40
00:01:03,560 --> 00:01:05,900
我们产生的文件是不是叫

41
00:01:05,900 --> 00:01:06,860
我搜一下是吧

42
00:01:06,860 --> 00:01:07,800
叫new mini

43
00:01:07,800 --> 00:01:08,680
这里是吧

44
00:01:08,680 --> 00:01:09,840
我也可以给它加一个

45
00:01:09,840 --> 00:01:11,640
比如说它应该是CSS下的

46
00:01:11,640 --> 00:01:12,640
慢点CSS

47
00:01:12,640 --> 00:01:13,980
再来看一下

48
00:01:13,980 --> 00:01:15,240
行

49
00:01:15,240 --> 00:01:17,360
这样的话要产生出来的文件

50
00:01:17,360 --> 00:01:20,460
就会跑到我们当前的CSS目录下

51
00:01:20,460 --> 00:01:20,740
是吧

52
00:01:20,740 --> 00:01:21,340
我刷新

53
00:01:21,340 --> 00:01:23,040
应该是不是又多了个CSS目录

54
00:01:23,040 --> 00:01:25,260
里面是不是也有我们当前的这样一个东西

55
00:01:25,260 --> 00:01:26,880
现在好了

56
00:01:26,880 --> 00:01:29,060
我们资源就应该是可以的

57
00:01:29,060 --> 00:01:30,600
现在我们还要再做一件事

58
00:01:30,600 --> 00:01:32,220
就是我们最后一运行

59
00:01:32,220 --> 00:01:35,580
可能我们文件或者CSS或者Image

60
00:01:35,580 --> 00:01:37,560
它都会跑到CDN服务器上

61
00:01:37,560 --> 00:01:41,120
肯定我们希望我们引用的时候应该怎么样

62
00:01:41,120 --> 00:01:42,420
是不是加上个运名

63
00:01:42,420 --> 00:01:45,160
什么我的运名下的Image下的这张图片

64
00:01:45,160 --> 00:01:46,420
这时候怎么做

65
00:01:46,420 --> 00:01:51,000
同样我们可以给我们当前这样一个路径的一个东西叫Public Path

66
00:01:51,000 --> 00:01:52,560
就是在我们输出的时候

67
00:01:52,560 --> 00:01:54,680
Output找一下上面

68
00:01:54,680 --> 00:01:57,040
output在这里

69
00:01:57,040 --> 00:01:58,720
我们可以在这里配一下

70
00:01:58,720 --> 00:02:00,220
叫out叫什么

71
00:02:00,220 --> 00:02:01,100
叫不叫output了

72
00:02:01,100 --> 00:02:02,480
叫public path

73
00:02:02,480 --> 00:02:04,780
一听明白的什么意思

74
00:02:04,780 --> 00:02:05,800
是公共的路径

75
00:02:05,800 --> 00:02:07,540
就是我们加了这个路径以后

76
00:02:07,540 --> 00:02:09,380
它会在给引用资源的时候

77
00:02:09,380 --> 00:02:11,500
统一加上这样一个值

78
00:02:11,500 --> 00:02:13,760
比如说给一个atp

79
00:02:13,760 --> 00:02:14,480
冒号刚刚

80
00:02:14,480 --> 00:02:15,800
比如说随便我就写了

81
00:02:15,800 --> 00:02:18,360
3w.珠峰培训.cn

82
00:02:18,360 --> 00:02:20,420
我这样写的话

83
00:02:20,420 --> 00:02:21,600
它就会去引什么

84
00:02:21,600 --> 00:02:22,600
引这样一个路径

85
00:02:22,600 --> 00:02:25,100
在我们ATML里面会加上这个路径

86
00:02:25,100 --> 00:02:26,260
在哪都会加上这个路径

87
00:02:26,260 --> 00:02:27,140
咱来试试

88
00:02:27,140 --> 00:02:28,540
我在这里运行

89
00:02:28,540 --> 00:02:31,840
看看能不能达到我预期效果

90
00:02:31,840 --> 00:02:33,120
OK

91
00:02:33,120 --> 00:02:34,740
出来了

92
00:02:34,740 --> 00:02:36,620
这里面我们看看ATML

93
00:02:36,620 --> 00:02:39,700
是不是这里面会自动的去帮我们去加上这样一个

94
00:02:39,700 --> 00:02:40,800
不是ATML

95
00:02:40,800 --> 00:02:41,220
是这个

96
00:02:41,220 --> 00:02:44,120
是在每个前面都加上这样一个

97
00:02:44,120 --> 00:02:45,660
但是你发现个问题少了个什么

98
00:02:45,660 --> 00:02:46,380
少了个杠

99
00:02:46,380 --> 00:02:48,420
这里面我写的时候最好怎么样

100
00:02:48,420 --> 00:02:49,560
是不是加个杠就好了

101
00:02:49,560 --> 00:02:51,720
我在我这里面可以再加一个什么

102
00:02:51,720 --> 00:02:54,200
在这里我们图片输出的时候

103
00:02:54,200 --> 00:02:55,120
加个杠最好了

104
00:02:55,120 --> 00:02:58,460
这里杠image杠

105
00:02:58,460 --> 00:03:03,860
这样的话就能统一给每个人都加上

106
00:03:03,860 --> 00:03:04,940
再运行

107
00:03:04,940 --> 00:03:07,180
这里面我们看一下产生的文件

108
00:03:07,180 --> 00:03:08,280
应该就靠谱了

109
00:03:08,280 --> 00:03:09,740
是不是就有了

110
00:03:09,740 --> 00:03:10,920
他下的css

111
00:03:10,920 --> 00:03:12,060
他下的image

112
00:03:12,060 --> 00:03:12,800
那时候说了

113
00:03:12,800 --> 00:03:14,140
我可能有这种需求

114
00:03:14,140 --> 00:03:15,520
我只需要怎么样

115
00:03:15,520 --> 00:03:18,140
给比如说图片加上这样一个路径

116
00:03:18,140 --> 00:03:19,080
其他都不加了

117
00:03:19,080 --> 00:03:19,820
有可能

118
00:03:19,820 --> 00:03:22,760
可能GS它并不会通过这种cdn的方式来引路

119
00:03:22,760 --> 00:03:24,680
这时候我们就可以怎么样

120
00:03:24,680 --> 00:03:26,240
可以这样写再把它住掉

121
00:03:26,240 --> 00:03:27,660
后面再会写这样的插件

122
00:03:27,660 --> 00:03:30,660
就是我们打包完以后自动发布到cdn上

123
00:03:30,660 --> 00:03:34,640
这里面我们就把public path先住掉

124
00:03:34,640 --> 00:03:35,580
OK怎么样

125
00:03:35,580 --> 00:03:37,940
你是不是只想处理我们图片

126
00:03:37,940 --> 00:03:38,460
好

127
00:03:38,460 --> 00:03:41,440
你就在图片里面给它单独加一个public path

128
00:03:41,440 --> 00:03:42,600
这也是可以的

129
00:03:42,600 --> 00:03:43,300
这里面一样

130
00:03:43,300 --> 00:03:43,840
ATP

131
00:03:43,840 --> 00:03:45,420
冒号

132
00:03:45,420 --> 00:03:46,100
刚刚

133
00:03:46,100 --> 00:03:48,680
3w.珠峰培训.cn

134
00:03:48,680 --> 00:03:50,280
这就可以了

135
00:03:50,280 --> 00:03:52,160
相当于只有图片怎么样

136
00:03:52,160 --> 00:03:53,280
进行加这个前准

137
00:03:53,280 --> 00:03:54,700
别人就不需要加了

138
00:03:54,700 --> 00:03:55,680
一样效果

139
00:03:55,680 --> 00:03:56,700
这里

140
00:03:56,700 --> 00:03:58,920
CD的好处我们就不多说了

141
00:03:58,920 --> 00:04:01,340
可以帮我们提升网络性能速度

142
00:04:01,340 --> 00:04:02,180
OK

143
00:04:02,180 --> 00:04:05,040
网站里面我们是不是就出来一个这样的东西

144
00:04:05,040 --> 00:04:06,580
看看结果

145
00:04:06,580 --> 00:04:07,860
是不是图片上

146
00:04:07,860 --> 00:04:08,900
在这个里面

147
00:04:08,900 --> 00:04:11,360
是不是图片上已经加了我们这样一个路径

148
00:04:11,360 --> 00:04:13,300
但是我们的GS是不是千万没有了

149
00:04:13,300 --> 00:04:15,160
是不是我们的样式里面也没有了

150
00:04:15,160 --> 00:04:15,920
好

151
00:04:15,920 --> 00:04:17,240
我们就可以实现什么

152
00:04:17,240 --> 00:04:19,340
给某一个样式加上CDN

153
00:04:19,340 --> 00:04:20,440
还可以实现什么

154
00:04:20,440 --> 00:04:21,840
对我们文件的划分

155
00:04:21,840 --> 00:04:23,540
比如说CSS image对吧

156
00:04:23,540 --> 00:04:24,980
放到对应的文件夹下

