1
00:00:00,000 --> 00:00:03,380
那我们再来说一下什么叫一步串行

2
00:00:03,380 --> 00:00:06,960
也就是说我们这个方法不再是一个个的去并发执行了

3
00:00:06,960 --> 00:00:09,540
而是第一个人怎么样执行完再走第二个

4
00:00:09,540 --> 00:00:12,300
那好了这里面我把代码了同样拷贝一份

5
00:00:12,300 --> 00:00:15,420
这里名字稍微改一下吧

6
00:00:15,420 --> 00:00:19,120
回到我们的start里面去把其他关掉防止错乱

7
00:00:19,120 --> 00:00:22,260
往里面我的名字叫async叫serioushook

8
00:00:22,260 --> 00:00:27,420
一听名字就是一步串行的钩子

9
00:00:27,420 --> 00:00:29,380
OK那这里我把这个都删掉

10
00:00:29,380 --> 00:00:30,840
那写法也差不多

11
00:00:30,840 --> 00:00:31,720
这里面呢

12
00:00:31,720 --> 00:00:33,300
我们就扭它了

13
00:00:33,300 --> 00:00:34,020
完了此时呢

14
00:00:34,020 --> 00:00:34,860
我们用的是一步

15
00:00:34,860 --> 00:00:36,160
那肯定叫type什么

16
00:00:36,160 --> 00:00:37,120
sync是吧

17
00:00:37,120 --> 00:00:38,060
type of sync

18
00:00:38,060 --> 00:00:38,840
那好了

19
00:00:38,840 --> 00:00:39,560
底下那也一样

20
00:00:39,560 --> 00:00:40,300
type of sync

21
00:00:40,300 --> 00:00:41,920
比起来呢

22
00:00:41,920 --> 00:00:42,960
这东西我就不要了

23
00:00:42,960 --> 00:00:44,020
我说了一步的话

24
00:00:44,020 --> 00:00:45,200
肯定需要有回调

25
00:00:45,200 --> 00:00:45,680
叫cb

26
00:00:45,680 --> 00:00:47,620
那这里面没有问题啊

27
00:00:47,620 --> 00:00:48,220
肯定也是一样

28
00:00:48,220 --> 00:00:49,100
加上cb

29
00:00:49,100 --> 00:00:50,760
完了每个执行完以后呢

30
00:00:50,760 --> 00:00:51,500
现在对吧

31
00:00:51,500 --> 00:00:52,880
那肯定这要改一下吧

32
00:00:52,880 --> 00:00:53,740
改一下

33
00:00:53,740 --> 00:00:54,680
同样啊

34
00:00:54,680 --> 00:00:55,520
我们把它呢

35
00:00:55,520 --> 00:00:56,460
也改造一下

36
00:00:56,460 --> 00:00:57,880
这里面不叫promise

37
00:00:57,880 --> 00:00:58,780
应该叫call

38
00:00:58,780 --> 00:01:00,840
和Sync

39
00:01:00,840 --> 00:01:04,080
和Sync

40
00:01:04,080 --> 00:01:06,220
并且这里面我有标字了

41
00:01:06,220 --> 00:01:07,520
直接写个回答栏数

42
00:01:07,520 --> 00:01:09,220
此时就这样一个结构

43
00:01:09,220 --> 00:01:11,580
第一个执行完再执行第二个

44
00:01:11,580 --> 00:01:13,240
第二执行完怎么样再执行最后一个

45
00:01:13,240 --> 00:01:15,720
这就是我们所谓的一步串行

46
00:01:15,720 --> 00:01:17,220
那好了看看效果是不是这样

47
00:01:17,220 --> 00:01:17,980
行

48
00:01:17,980 --> 00:01:21,320
没有选中

49
00:01:21,320 --> 00:01:21,880
再来一次

50
00:01:21,880 --> 00:01:23,960
第一个先走node对吧

51
00:01:23,960 --> 00:01:25,420
再去走我们的read

52
00:01:25,420 --> 00:01:27,620
两个人都走外以后怎么样走n的

53
00:01:27,620 --> 00:01:29,920
这效果呢和我们刚才看到的那个叫对吧

54
00:01:29,920 --> 00:01:31,520
叫同步执行其实很像啊

55
00:01:31,520 --> 00:01:33,320
也是一个个执行完再执行最后一个

56
00:01:33,320 --> 00:01:34,740
那咱来看看吧

57
00:01:34,740 --> 00:01:36,080
那这个东西怎么实现呢

58
00:01:36,080 --> 00:01:37,420
其实稍稍的有点复杂了

59
00:01:37,420 --> 00:01:38,580
如果我们要求第一个

60
00:01:38,580 --> 00:01:40,100
第一个呢再执行完要怎么样

61
00:01:40,100 --> 00:01:40,780
再执行第二个

62
00:01:40,780 --> 00:01:43,960
其实啊这个呢就很像我们ePress弄的圆码一样

63
00:01:43,960 --> 00:01:47,040
第一个执行一件执行完再执行第二个

64
00:01:47,040 --> 00:01:47,820
类比一下是吧

65
00:01:47,820 --> 00:01:49,980
那咱好了啊看看东西怎么实现

66
00:01:49,980 --> 00:01:53,140
叫A-Think Series是吧

67
00:01:53,140 --> 00:01:54,160
Series

68
00:01:54,160 --> 00:01:56,800
这里也一样A-Think Series

69
00:01:56,800 --> 00:01:57,800
完了呢

70
00:01:57,800 --> 00:01:58,240
并且呢

71
00:01:58,240 --> 00:01:59,000
把下面也改了吧

72
00:01:59,000 --> 00:02:00,440
这个叫type async

73
00:02:00,440 --> 00:02:02,300
往这里呢

74
00:02:02,300 --> 00:02:03,160
叫对吧

75
00:02:03,160 --> 00:02:04,140
call async

76
00:02:04,140 --> 00:02:06,340
往同时呢

77
00:02:06,340 --> 00:02:07,180
这个代码就不要了

78
00:02:07,180 --> 00:02:08,300
肯定是这会儿有区别

79
00:02:08,300 --> 00:02:08,600
是吧

80
00:02:08,600 --> 00:02:09,460
往并且呢

81
00:02:09,460 --> 00:02:10,120
这里面一样

82
00:02:10,120 --> 00:02:11,620
这应该叫type async

83
00:02:11,620 --> 00:02:12,700
都改掉了

84
00:02:12,700 --> 00:02:14,060
删掉

85
00:02:14,060 --> 00:02:14,900
new promise

86
00:02:14,900 --> 00:02:15,560
new promise

87
00:02:15,560 --> 00:02:16,360
new promise

88
00:02:16,360 --> 00:02:16,940
new promise

89
00:02:16,940 --> 00:02:17,700
都干掉

90
00:02:17,700 --> 00:02:19,660
这个东西

91
00:02:19,660 --> 00:02:20,520
好了

92
00:02:20,520 --> 00:02:21,860
并且每次执行完以后呢

93
00:02:21,860 --> 00:02:22,880
都需要有这样个CB

94
00:02:22,880 --> 00:02:24,300
CB

95
00:02:24,300 --> 00:02:26,460
往这里也一样呢

96
00:02:26,460 --> 00:02:28,020
来个CB来个CB

97
00:02:28,020 --> 00:02:29,380
完都执行完以后怎么样

98
00:02:29,380 --> 00:02:30,140
再去执行N的

99
00:02:30,140 --> 00:02:31,520
那这里面也不叫Promise了

100
00:02:31,520 --> 00:02:32,740
应该叫CoreSync

101
00:02:32,740 --> 00:02:35,640
12345

102
00:02:35,640 --> 00:02:37,360
CoreSync

103
00:02:37,360 --> 00:02:40,000
CoreSync

104
00:02:40,000 --> 00:02:41,980
完了我们来看看吧

105
00:02:41,980 --> 00:02:44,240
那现在我们调这个CoreSync的时候

106
00:02:44,240 --> 00:02:45,340
其实就要注意了

107
00:02:45,340 --> 00:02:46,580
我们需要干嘛呢

108
00:02:46,580 --> 00:02:47,940
先去执行第一个

109
00:02:47,940 --> 00:02:49,060
把第一个拿出来怎么样

110
00:02:49,060 --> 00:02:50,400
把第二个再乘以第一个

111
00:02:50,400 --> 00:02:52,200
那这时候我们的想法是什么

112
00:02:52,200 --> 00:02:54,580
是不是就要写上一个一步迭代

113
00:02:54,580 --> 00:02:55,560
肯定需要一个

114
00:02:55,560 --> 00:02:56,680
中间函数

115
00:02:56,680 --> 00:02:58,440
那我们就写这样一个方法

116
00:02:58,440 --> 00:02:59,140
叫nest

117
00:02:59,140 --> 00:03:02,020
因为我们没掉这个人的时候

118
00:03:02,020 --> 00:03:03,120
就会往下再执行一次

119
00:03:03,120 --> 00:03:05,760
所以这里面我就写上这样一个函数

120
00:03:05,760 --> 00:03:08,800
并且我们需要干嘛呢

121
00:03:08,800 --> 00:03:09,580
先取出第一个

122
00:03:09,580 --> 00:03:11,000
所以说这里面肯定需要一个

123
00:03:11,000 --> 00:03:11,800
所以你来维护了

124
00:03:11,800 --> 00:03:13,080
第一个

125
00:03:13,080 --> 00:03:14,560
怎么维护呢

126
00:03:14,560 --> 00:03:16,180
在this的tasks

127
00:03:16,180 --> 00:03:16,660
对吧

128
00:03:16,660 --> 00:03:17,820
完了去取什么呢

129
00:03:17,820 --> 00:03:18,620
叫index加价

130
00:03:18,620 --> 00:03:20,600
完了里面呢

131
00:03:20,600 --> 00:03:23,160
就可以拿到某一个的任务

132
00:03:23,160 --> 00:03:24,160
完了我需要干嘛呢

133
00:03:24,160 --> 00:03:25,180
让任务去执行

134
00:03:25,180 --> 00:03:28,000
那第一次执行不管哪次执行啊

135
00:03:28,000 --> 00:03:29,140
它的结果参数怎么样

136
00:03:29,140 --> 00:03:30,300
都是这个东西是吧

137
00:03:30,300 --> 00:03:31,720
那好了那我怎么办呢

138
00:03:31,720 --> 00:03:34,180
我就把当前这样一个参数给它是吧

139
00:03:34,180 --> 00:03:35,240
叫.args

140
00:03:35,240 --> 00:03:36,940
但是最后一个参数一样

141
00:03:36,940 --> 00:03:38,360
我们需要先留下是吧

142
00:03:38,360 --> 00:03:40,300
所以说这里面还是原理是一样的

143
00:03:40,300 --> 00:03:43,140
我们需要拿到这个最终的叫Final

144
00:03:43,140 --> 00:03:45,380
Callback

145
00:03:45,380 --> 00:03:48,980
完了从我们这样一个args中对吧

146
00:03:48,980 --> 00:03:50,140
去pop出来

147
00:03:50,140 --> 00:03:52,560
那现在好了

148
00:03:52,560 --> 00:03:54,060
那我们可以判断了

149
00:03:54,060 --> 00:03:55,300
比如说第一次执行

150
00:03:55,300 --> 00:03:56,920
我们执行的时候需要干嘛呢

151
00:03:56,920 --> 00:03:58,880
是不是需要把我们的参数传去

152
00:03:58,880 --> 00:04:02,140
参数就是刚才我们所谓的ARGS展开

153
00:04:02,140 --> 00:04:03,300
因为可能有多个

154
00:04:03,300 --> 00:04:04,400
没有考虑

155
00:04:04,400 --> 00:04:06,600
并且这个回调指的是谁

156
00:04:06,600 --> 00:04:08,060
这个回调肯定就是

157
00:04:08,060 --> 00:04:10,540
他一调CB是不是走到下一个这去

158
00:04:10,540 --> 00:04:12,080
他当于再下一步走

159
00:04:12,080 --> 00:04:13,540
所以说这时候调的肯定是什么

160
00:04:13,540 --> 00:04:14,380
是next

161
00:04:14,380 --> 00:04:17,620
看着代码很简洁是吧

162
00:04:17,620 --> 00:04:18,960
完了当执行完以后

163
00:04:18,960 --> 00:04:20,100
不停的去调

164
00:04:20,100 --> 00:04:21,920
当我们最后也调CB的时候怎么样

165
00:04:21,920 --> 00:04:23,260
是不是应该走到这个函数

166
00:04:23,260 --> 00:04:23,640
他执行

167
00:04:23,640 --> 00:04:25,040
你说它会溢出

168
00:04:25,040 --> 00:04:26,780
所以说我可以在这判断一下

169
00:04:26,780 --> 00:04:29,120
如果当前this tax

170
00:04:29,120 --> 00:04:30,620
第2Lens

171
00:04:30,620 --> 00:04:33,560
等等了我们当前的所以

172
00:04:33,560 --> 00:04:35,200
那好了

173
00:04:35,200 --> 00:04:36,260
那就说明怎么样

174
00:04:36,260 --> 00:04:37,180
就别往下走了

175
00:04:37,180 --> 00:04:37,820
到头了

176
00:04:37,820 --> 00:04:39,120
那到头的话怎么办

177
00:04:39,120 --> 00:04:41,520
是不是应该去执行我们当前这样一个

178
00:04:41,520 --> 00:04:42,920
最终的回调

179
00:04:42,920 --> 00:04:43,680
那好

180
00:04:43,680 --> 00:04:45,480
最终回调就是我们的final back

181
00:04:45,480 --> 00:04:47,360
执行

182
00:04:47,360 --> 00:04:48,560
那执行的时候

183
00:04:48,560 --> 00:04:50,220
并且你忘了这样肯定不行

184
00:04:50,220 --> 00:04:50,880
肯定需要干嘛

185
00:04:50,880 --> 00:04:51,960
你得先来一次

186
00:04:51,960 --> 00:04:52,540
是吧

187
00:04:52,540 --> 00:04:53,340
nest

188
00:04:53,340 --> 00:04:55,900
其实这个圆码呀就非常像e-price了

189
00:04:55,900 --> 00:04:57,140
第一个走完走第二个

190
00:04:57,140 --> 00:04:58,380
第二个走完走第三个

191
00:04:58,380 --> 00:05:00,620
靠的就是我们这样一个nest来写地归

192
00:05:00,620 --> 00:05:02,200
第一个走完掉第二个

193
00:05:02,200 --> 00:05:03,700
那来看看效果吧

194
00:05:03,700 --> 00:05:04,400
看看obo k

195
00:05:04,400 --> 00:05:05,300
刷新一下

196
00:05:05,300 --> 00:05:06,860
哦有点小包错是吧

197
00:05:06,860 --> 00:05:07,840
名字没有改

198
00:05:07,840 --> 00:05:09,480
sync series

199
00:05:09,480 --> 00:05:10,500
下面这个没有改

200
00:05:10,500 --> 00:05:12,880
ok运行一下

201
00:05:12,880 --> 00:05:15,660
出来了

202
00:05:15,660 --> 00:05:16,500
react node

203
00:05:16,500 --> 00:05:17,500
完了之后结束

204
00:05:17,500 --> 00:05:21,140
那现在呀这种方法一样也挺棒的是吧

205
00:05:21,140 --> 00:05:22,320
但是我们还不满足

206
00:05:22,320 --> 00:05:23,140
肯定希望怎么样

207
00:05:23,140 --> 00:05:24,420
把这样一个效果呢

208
00:05:24,420 --> 00:05:25,400
形成Promise版的

209
00:05:25,400 --> 00:05:26,840
那我们就不废话了

210
00:05:26,840 --> 00:05:27,280
直接呢

211
00:05:27,280 --> 00:05:27,980
把它拷贝一份

212
00:05:27,980 --> 00:05:30,480
感觉呢

213
00:05:30,480 --> 00:05:31,620
其实这些都是思想

214
00:05:31,620 --> 00:05:33,320
跟这个原理性的关系

215
00:05:33,320 --> 00:05:34,140
也挺那个的

216
00:05:34,140 --> 00:05:34,380
是吧

217
00:05:34,380 --> 00:05:35,020
好

218
00:05:35,020 --> 00:05:35,560
我在这里呢

219
00:05:35,560 --> 00:05:36,560
把它删掉

220
00:05:36,560 --> 00:05:37,460
这也不要了

221
00:05:37,460 --> 00:05:39,720
完了里面呢

222
00:05:39,720 --> 00:05:39,960
哦

223
00:05:39,960 --> 00:05:40,500
不应该删这个

224
00:05:40,500 --> 00:05:41,440
删错了

225
00:05:41,440 --> 00:05:42,360
差点关闭其他

226
00:05:42,360 --> 00:05:43,600
顶开

227
00:05:43,600 --> 00:05:44,920
完了里面呢

228
00:05:44,920 --> 00:05:46,100
除了这个肯定还有什么呢

229
00:05:46,100 --> 00:05:47,180
用法还是一样的

230
00:05:47,180 --> 00:05:48,000
这时候注册呀

231
00:05:48,000 --> 00:05:48,680
可以换成什么呢

232
00:05:48,680 --> 00:05:49,580
是不是Type啊

233
00:05:49,580 --> 00:05:50,320
Promise

234
00:05:50,320 --> 00:05:51,800
那我理所当然呢

235
00:05:51,800 --> 00:05:53,660
应该retain一个new promise

236
00:05:53,660 --> 00:05:55,040
完了有reload

237
00:05:55,040 --> 00:05:57,700
reject

238
00:05:57,700 --> 00:06:00,920
并且把这东西塞进去

239
00:06:00,920 --> 00:06:01,880
OK

240
00:06:01,880 --> 00:06:03,500
完了成功以后

241
00:06:03,500 --> 00:06:05,280
我这里面就不要再掉cb了

242
00:06:05,280 --> 00:06:06,260
已经没有了

243
00:06:06,260 --> 00:06:07,180
好 这是一个

244
00:06:07,180 --> 00:06:09,320
完了第二个就应该放上react

245
00:06:09,320 --> 00:06:11,760
react

246
00:06:11,760 --> 00:06:13,560
完了最终我们需要怎么样

247
00:06:13,560 --> 00:06:14,660
是不是这里面注册的话

248
00:06:14,660 --> 00:06:15,380
应该是promise

249
00:06:15,380 --> 00:06:16,720
完了最后来一个什么

250
00:06:16,720 --> 00:06:17,900
来个点then

251
00:06:17,900 --> 00:06:19,160
OK

252
00:06:19,160 --> 00:06:20,440
这样个效果

253
00:06:20,440 --> 00:06:21,800
那现在一样是吧

254
00:06:21,800 --> 00:06:23,080
每一个人都成功以后

255
00:06:23,080 --> 00:06:24,680
我需要调的不再是cb了

256
00:06:24,680 --> 00:06:25,880
而是我们所谓的reload

257
00:06:25,880 --> 00:06:27,320
好 看看效果吧

258
00:06:27,320 --> 00:06:27,980
运行

259
00:06:27,980 --> 00:06:30,640
node react

260
00:06:30,640 --> 00:06:31,560
完了结束了

261
00:06:31,560 --> 00:06:33,040
结束完以后发现又没有调是吧

262
00:06:33,040 --> 00:06:34,500
看看什么原因啊

263
00:06:34,500 --> 00:06:36,140
promise又没改名字是吧

264
00:06:36,140 --> 00:06:37,460
上次就干这事了啊

265
00:06:37,460 --> 00:06:38,320
改了好

266
00:06:38,320 --> 00:06:40,240
再运行一下

267
00:06:40,240 --> 00:06:43,080
一个两个是不是结束了

268
00:06:43,080 --> 00:06:44,760
那这时候我们来看看吧

269
00:06:44,760 --> 00:06:45,840
它的原理就是什么

270
00:06:45,840 --> 00:06:47,860
就是当我们第一个promise执行完

271
00:06:47,860 --> 00:06:48,580
再去怎么样

272
00:06:48,580 --> 00:06:49,140
之间第二个

273
00:06:49,140 --> 00:06:49,940
第二执行再怎么样

274
00:06:49,940 --> 00:06:50,700
那只有第三个

275
00:06:50,700 --> 00:06:52,700
那这时候我们怎么去来做呢

276
00:06:52,700 --> 00:06:54,020
那回到我们的代码里

277
00:06:54,020 --> 00:06:55,180
那咱来看看啊

278
00:06:55,180 --> 00:06:56,260
这里面就改一下吧

279
00:06:56,260 --> 00:06:58,380
叫type promise

280
00:06:58,380 --> 00:07:00,520
type promise

281
00:07:00,520 --> 00:07:01,940
这个叫对吧

282
00:07:01,940 --> 00:07:02,940
promise

283
00:07:02,940 --> 00:07:05,680
原理呢都是这儿

284
00:07:05,680 --> 00:07:06,580
不停的在改是吧

285
00:07:06,580 --> 00:07:08,860
当然你可以整合一个自己写的库是吧

286
00:07:08,860 --> 00:07:10,900
这里呢我就把它删掉啊

287
00:07:10,900 --> 00:07:11,740
不要了

288
00:07:11,740 --> 00:07:15,680
这点滑是吧

289
00:07:15,680 --> 00:07:16,260
把它删掉

290
00:07:16,260 --> 00:07:18,520
并且呢我们把这个地方呢也改一下吧

291
00:07:18,520 --> 00:07:18,920
对吧

292
00:07:18,920 --> 00:07:21,320
这里面就叫type promise

293
00:07:21,320 --> 00:07:24,240
叫type promise

294
00:07:24,240 --> 00:07:26,880
一样下面的也改了

295
00:07:26,880 --> 00:07:28,400
这我就叫做promise

296
00:07:28,400 --> 00:07:30,800
这里我就快速改一下了

297
00:07:30,800 --> 00:07:33,700
这个里面一样完了可以去第二次

298
00:07:33,700 --> 00:07:34,820
OK

299
00:07:34,820 --> 00:07:39,000
往这里一样return new promise

300
00:07:39,000 --> 00:07:42,300
return new promise

301
00:07:42,300 --> 00:07:46,120
完了result reject

302
00:07:48,920 --> 00:07:50,460
完了里面呢

303
00:07:50,460 --> 00:07:51,800
我们调的就应该是resolve了

304
00:07:51,800 --> 00:07:53,160
它并没有我们所谓的callback

305
00:07:53,160 --> 00:07:57,500
这一样来个resolve

306
00:07:57,500 --> 00:07:59,020
并且呢

307
00:07:59,020 --> 00:08:00,700
我们需要包上一层这样的promise

308
00:08:00,700 --> 00:08:02,900
代码改一下

309
00:08:02,900 --> 00:08:03,580
挺费时间的

310
00:08:03,580 --> 00:08:06,880
那好了

311
00:08:06,880 --> 00:08:08,100
那咱来看看吧

312
00:08:08,100 --> 00:08:09,420
这样一个功能怎么实现

313
00:08:09,420 --> 00:08:10,960
那我们想法还是一样的

314
00:08:10,960 --> 00:08:12,080
我需要干嘛呢

315
00:08:12,080 --> 00:08:13,240
是不是先执行

316
00:08:13,240 --> 00:08:14,280
第一个

317
00:08:14,280 --> 00:08:16,920
我们是不是先要将它去执行第一个呀

318
00:08:16,920 --> 00:08:17,720
第一个执行的时候

319
00:08:17,720 --> 00:08:18,640
我需要先传上参数

320
00:08:18,640 --> 00:08:19,640
完了之后怎么样

321
00:08:19,640 --> 00:08:20,680
第一个执行完再执行

322
00:08:20,680 --> 00:08:21,160
第二个

323
00:08:21,160 --> 00:08:22,600
那好了这里面有一样

324
00:08:22,600 --> 00:08:23,700
我需要干嘛呢

325
00:08:23,700 --> 00:08:25,740
是不是先取出第一个执行

326
00:08:25,740 --> 00:08:27,260
完第一个执行的结果怎么样

327
00:08:27,260 --> 00:08:28,600
没有它没有结果是吧

328
00:08:28,600 --> 00:08:29,940
就执行完以后怎么样再执行

329
00:08:29,940 --> 00:08:31,320
第二个第三个

330
00:08:31,320 --> 00:08:32,840
那这时候我们需要干嘛呢

331
00:08:32,840 --> 00:08:35,260
是不是需要把这promise怎么样串联起来

332
00:08:35,260 --> 00:08:37,040
那好了我们需要怎么做呢

333
00:08:37,040 --> 00:08:38,980
同样我们先拿到第一个promise

334
00:08:38,980 --> 00:08:40,200
那第一个怎么拿呢

335
00:08:40,200 --> 00:08:42,240
可以这样做来个first对吧

336
00:08:42,240 --> 00:08:42,740
first

337
00:08:42,740 --> 00:08:44,480
完了后面给他一个点点

338
00:08:44,480 --> 00:08:45,960
others很多个

339
00:08:45,960 --> 00:08:48,280
从我们this.task.s里

340
00:08:48,280 --> 00:08:48,800
拿到

341
00:08:48,800 --> 00:08:51,320
完了可以怎么做呢

342
00:08:51,320 --> 00:08:52,020
是不是结构出来

343
00:08:52,020 --> 00:08:53,680
我们先要第一个执行吧

344
00:08:53,680 --> 00:08:54,660
那第一个怎么执行呢

345
00:08:54,660 --> 00:08:55,380
非常简单

346
00:08:55,380 --> 00:08:56,060
那好了

347
00:08:56,060 --> 00:08:56,900
那我会这样写

348
00:08:56,900 --> 00:08:57,900
叫first对吧

349
00:08:57,900 --> 00:08:58,480
执行

350
00:08:58,480 --> 00:09:01,180
并且呢把这个args传去

351
00:09:01,180 --> 00:09:03,200
那这样写完以后啊

352
00:09:03,200 --> 00:09:03,960
我们希望呢

353
00:09:03,960 --> 00:09:05,320
第一个掉案会掉第二个

354
00:09:05,320 --> 00:09:07,100
那我们同样的还是想到了什么

355
00:09:07,100 --> 00:09:07,320
是不是

356
00:09:07,320 --> 00:09:08,600
把后面的怎么样

357
00:09:08,600 --> 00:09:09,340
进行收敛

358
00:09:09,340 --> 00:09:10,260
reduce

359
00:09:10,260 --> 00:09:12,420
完了里面呢

360
00:09:12,420 --> 00:09:13,980
应该放上某一个对吧

361
00:09:13,980 --> 00:09:15,200
这个某一个other

362
00:09:15,200 --> 00:09:16,880
或者叫某一个p吧

363
00:09:16,880 --> 00:09:17,440
promise

364
00:09:17,440 --> 00:09:18,700
当然了

365
00:09:18,700 --> 00:09:19,700
它的参数肯定是什么

366
00:09:19,700 --> 00:09:20,320
是不是第一次

367
00:09:20,320 --> 00:09:22,120
我需要把第一个人传给去

368
00:09:22,120 --> 00:09:22,620
传进去

369
00:09:22,620 --> 00:09:25,220
所以说这里我们就把它放到后面

370
00:09:25,220 --> 00:09:28,060
当前的P

371
00:09:28,060 --> 00:09:28,860
来个函数

372
00:09:28,860 --> 00:09:30,360
第一个就是我们的P

373
00:09:30,360 --> 00:09:32,320
P就是我们的第一个Promise

374
00:09:32,320 --> 00:09:34,500
后面就是我们的NAS的Promise

375
00:09:34,500 --> 00:09:35,680
那我可以怎么做

376
00:09:35,680 --> 00:09:37,560
是不是当第一个Promise执行完

377
00:09:37,560 --> 00:09:40,080
我应该让下一个函数怎么样

378
00:09:40,080 --> 00:09:40,740
执行

379
00:09:40,740 --> 00:09:41,840
我要去调它的什么

380
00:09:41,840 --> 00:09:42,380
认方法

381
00:09:42,380 --> 00:09:43,980
这时候我们会怎么写

382
00:09:43,980 --> 00:09:44,500
会这样写

383
00:09:44,500 --> 00:09:45,680
叫P

384
00:09:45,680 --> 00:09:46,400
调什么

385
00:09:46,400 --> 00:09:46,880
第2

386
00:09:46,880 --> 00:09:48,060
执行

387
00:09:48,060 --> 00:09:49,320
执行完以后呢

388
00:09:49,320 --> 00:09:50,460
会执行后面的回调

389
00:09:50,460 --> 00:09:51,880
那回调的时候干嘛呀

390
00:09:51,880 --> 00:09:53,520
我再让这个N执行

391
00:09:53,520 --> 00:09:54,200
N执行

392
00:09:54,200 --> 00:09:55,240
如果呀

393
00:09:55,240 --> 00:09:56,440
这个N执行以后

394
00:09:56,440 --> 00:09:57,920
它返回的还是promise

395
00:09:57,920 --> 00:09:59,980
那是不是它会等待这个promise执行完

396
00:09:59,980 --> 00:10:01,060
执行完以后怎么样

397
00:10:01,060 --> 00:10:01,700
再往下走

398
00:10:01,700 --> 00:10:02,760
那最后呢

399
00:10:02,760 --> 00:10:04,960
我们把最终返回的这个promise进行返回

400
00:10:04,960 --> 00:10:06,100
完了此时呢

401
00:10:06,100 --> 00:10:07,400
我们把它返回完以后

402
00:10:07,400 --> 00:10:09,440
那整个这样一个结果返回的就是一个什么了

403
00:10:09,440 --> 00:10:10,040
promise

404
00:10:10,040 --> 00:10:11,260
我在这里面再来个return

405
00:10:11,260 --> 00:10:12,720
这样的话呢

406
00:10:12,720 --> 00:10:14,280
就把我们所有的这东西怎么样

407
00:10:14,280 --> 00:10:15,240
就串联起来了

408
00:10:15,240 --> 00:10:16,780
其实这个圆码

409
00:10:16,780 --> 00:10:19,000
就是跟我们的redus圆码是一样的

410
00:10:19,000 --> 00:10:19,240
是吧

411
00:10:19,240 --> 00:10:20,080
redus圆码

412
00:10:20,080 --> 00:10:21,880
那来看看吧

413
00:10:21,880 --> 00:10:23,700
能不能实现我们想要的结果

414
00:10:23,700 --> 00:10:24,260
有件乱

415
00:10:24,260 --> 00:10:28,380
看看是不是先输出这个

416
00:10:28,380 --> 00:10:28,820
又输出这个

417
00:10:28,820 --> 00:10:30,480
但这里面发现缺了一个什么

418
00:10:30,480 --> 00:10:31,120
参数

419
00:10:31,120 --> 00:10:32,800
比如我们让n之行的时候

420
00:10:32,800 --> 00:10:33,660
是不是还需要干嘛

421
00:10:33,660 --> 00:10:35,480
是不是让这个函数之行的时候

422
00:10:35,480 --> 00:10:36,860
还需要揣入一个参数

423
00:10:36,860 --> 00:10:37,900
那这参数呢

424
00:10:37,900 --> 00:10:39,080
就是我们刚才看到那个

425
00:10:39,080 --> 00:10:40,240
加点

426
00:10:40,240 --> 00:10:41,320
对吧

427
00:10:41,320 --> 00:10:42,260
args

428
00:10:42,260 --> 00:10:45,060
加点args

429
00:10:45,060 --> 00:10:47,260
OK我们来看看效果运行

430
00:10:47,260 --> 00:10:50,580
这样的话呢应该就正确了

431
00:10:50,580 --> 00:10:51,220
可以了

432
00:10:51,220 --> 00:10:53,560
那这样的话呢我们就实现了一个叫

433
00:10:53,560 --> 00:10:56,960
异步promise版的踹行

