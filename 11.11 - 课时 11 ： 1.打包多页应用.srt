1
00:00:00,000 --> 00:00:02,780
上一章节中呢

2
00:00:02,780 --> 00:00:04,960
我们讲了一下这个webpack的基础配置

3
00:00:04,960 --> 00:00:06,180
那这一章节呢

4
00:00:06,180 --> 00:00:08,740
我们就来讲一下webpack的一些其他的配置

5
00:00:08,740 --> 00:00:09,720
那这里呢

6
00:00:09,720 --> 00:00:11,440
我们先说第一个最常用的配置

7
00:00:11,440 --> 00:00:12,400
叫我们的多页

8
00:00:12,400 --> 00:00:14,020
那这里呢

9
00:00:14,020 --> 00:00:14,800
我们就来写一下

10
00:00:14,800 --> 00:00:15,400
还是一样

11
00:00:15,400 --> 00:00:16,240
我们快速的呢

12
00:00:16,240 --> 00:00:16,980
把这个结构呢

13
00:00:16,980 --> 00:00:17,480
生成一下

14
00:00:17,480 --> 00:00:19,020
我们建一个src

15
00:00:19,020 --> 00:00:20,540
来一个index.js

16
00:00:20,540 --> 00:00:21,520
同样呢

17
00:00:21,520 --> 00:00:21,900
这里呢

18
00:00:21,900 --> 00:00:23,980
我就写上一个counterlog

19
00:00:23,980 --> 00:00:25,200
比如说写上一个home

20
00:00:25,200 --> 00:00:27,140
完了我们在下面呢

21
00:00:27,140 --> 00:00:28,200
我再来建一个页面

22
00:00:28,200 --> 00:00:29,240
多页嘛

23
00:00:29,240 --> 00:00:30,300
肯定需要怎么样

24
00:00:30,300 --> 00:00:32,440
每个页面都需要有自己的这个gs

25
00:00:32,440 --> 00:00:32,940
那这个呢

26
00:00:32,940 --> 00:00:33,840
我也来一下

27
00:00:33,840 --> 00:00:34,640
比如说COTLOG

28
00:00:34,640 --> 00:00:35,340
它叫other

29
00:00:35,340 --> 00:00:39,540
那此时我们就可以装一下我们的这个webpack相关的内容啊

30
00:00:39,540 --> 00:00:40,280
那这里呢

31
00:00:40,280 --> 00:00:41,340
我们先处置画

32
00:00:41,340 --> 00:00:42,840
ianit-y

33
00:00:42,840 --> 00:00:44,440
那这里呢

34
00:00:44,440 --> 00:00:46,040
我们就安装ian add

35
00:00:46,040 --> 00:00:47,440
比如说有我们的webpack

36
00:00:47,440 --> 00:00:49,440
还有我们的webpack-cy

37
00:00:49,440 --> 00:00:50,540
安装

38
00:00:50,540 --> 00:00:52,640
那这里呢

39
00:00:52,640 --> 00:00:54,240
我们就先写了这个配置文件啊

40
00:00:54,240 --> 00:00:57,140
叫webpack.config.js

41
00:00:57,440 --> 00:00:59,880
完了我说过了这个我就不再去详细了

42
00:00:59,880 --> 00:01:02,460
我们需要导出一个模块

43
00:01:02,460 --> 00:01:04,760
这个模块里那么需要呢有几个参数

44
00:01:04,760 --> 00:01:06,740
第一呢需要一个入口 entry

45
00:01:06,740 --> 00:01:08,000
我们呢可以去选

46
00:01:08,000 --> 00:01:10,620
但是以前我们写的时候是不是写了个字符串

47
00:01:10,620 --> 00:01:13,440
完了直接去用了这个什么index或者 other

48
00:01:13,440 --> 00:01:16,680
但现在我说了这两个文件的应该是两个入口

49
00:01:16,680 --> 00:01:19,720
所以我们给它称之为叫什么呢叫多入口

50
00:01:19,720 --> 00:01:20,640
多入口

51
00:01:20,640 --> 00:01:21,640
那多入口的时候

52
00:01:21,640 --> 00:01:24,240
我们就需要把这个东西呢写成个对象

53
00:01:24,240 --> 00:01:25,400
这个特点啊

54
00:01:25,400 --> 00:01:27,160
那这里面我们就应该有两个入口

55
00:01:27,160 --> 00:01:29,780
那分别是一个首页的还有一个other页的

56
00:01:29,780 --> 00:01:32,060
那这里呢我们就写个首页相关的

57
00:01:32,060 --> 00:01:35,760
那首页呢我们就用这个src下的这个index.js

58
00:01:35,760 --> 00:01:38,740
那同样还有我们other相关的

59
00:01:38,740 --> 00:01:41,120
other下呢就是我们的这样一个other.js

60
00:01:41,120 --> 00:01:44,480
ok那现在有了我们两个所谓的入口是吧

61
00:01:44,480 --> 00:01:46,640
那有两个入口啊我们肯定需要怎么样

62
00:01:46,640 --> 00:01:48,640
应该有两个出口是吧output

63
00:01:48,640 --> 00:01:52,320
那这时候我们以前配的时候是不是这样写的叫fail name

64
00:01:52,320 --> 00:01:56,500
里面来个什么叫bundle.js

65
00:01:56,500 --> 00:01:57,960
我来个什么pass

66
00:01:57,960 --> 00:02:00,720
这时候我们需要怎么做

67
00:02:00,720 --> 00:02:01,500
是不是这样

68
00:02:01,500 --> 00:02:02,640
light一个pass

69
00:02:02,640 --> 00:02:03,480
绝对路径

70
00:02:03,480 --> 00:02:05,480
这里我们把pass引进来

71
00:02:05,480 --> 00:02:07,400
这里我们就要改一下

72
00:02:07,400 --> 00:02:09,200
叫pass点什么result

73
00:02:09,200 --> 00:02:11,100
来个杠杠第二例

74
00:02:11,100 --> 00:02:13,200
后面我给它一个第四目录

75
00:02:13,200 --> 00:02:14,960
但这时候你可以明确

76
00:02:14,960 --> 00:02:16,800
我不可能把两个入口

77
00:02:16,800 --> 00:02:18,240
打包出来一个入口

78
00:02:18,240 --> 00:02:19,160
咱可以试试

79
00:02:19,160 --> 00:02:20,520
这里你们肯定会有提示

80
00:02:20,520 --> 00:02:23,420
npx我们就直接执行webpack了

81
00:02:23,420 --> 00:02:25,280
一行一下

82
00:02:25,280 --> 00:02:26,900
应该告诉我了说

83
00:02:26,900 --> 00:02:30,140
现在你有多个所谓的代码块被发射出来

84
00:02:30,140 --> 00:02:30,880
叫这样的资源

85
00:02:30,880 --> 00:02:34,100
但是他们输出的名字都是叫邦多尔GS

86
00:02:34,100 --> 00:02:35,760
这个他是不允许的

87
00:02:35,760 --> 00:02:37,180
这时候我怎么办

88
00:02:37,180 --> 00:02:38,280
我就需要怎么样

89
00:02:38,280 --> 00:02:39,800
是不是产生两个出口

90
00:02:39,800 --> 00:02:41,340
这时候你也不能写个数度

91
00:02:41,340 --> 00:02:42,100
你要怎么做呢

92
00:02:42,100 --> 00:02:43,280
这样来写方框

93
00:02:43,280 --> 00:02:45,320
完了把谁放下来

94
00:02:45,320 --> 00:02:47,320
把这个内容放进来

95
00:02:47,320 --> 00:02:49,620
这个方框内容代表什么

96
00:02:49,620 --> 00:02:52,320
代表的就是home或者other

97
00:02:52,320 --> 00:02:53,900
相当于他会怎么样

98
00:02:53,900 --> 00:02:54,800
把home产生完

99
00:02:54,800 --> 00:02:55,960
打包一次

100
00:02:55,960 --> 00:02:57,240
把阿瑟产上完怎么样

101
00:02:57,240 --> 00:02:58,080
再打包一次

102
00:02:58,080 --> 00:03:00,600
这样的话就实现了一个多出口

103
00:03:00,600 --> 00:03:02,120
同样里面我们还说过

104
00:03:02,120 --> 00:03:02,600
还有什么

105
00:03:02,600 --> 00:03:04,460
比如可以加给文件加一个哈希窗

106
00:03:04,460 --> 00:03:05,520
这也是可以的

107
00:03:05,520 --> 00:03:07,480
这里我就先不用它了

108
00:03:07,480 --> 00:03:10,880
这里我们再来试一下

109
00:03:10,880 --> 00:03:12,680
为了方便不报警告

110
00:03:12,680 --> 00:03:14,080
我把这个mode也加上

111
00:03:14,080 --> 00:03:15,740
省着它提示的很恶心

112
00:03:15,740 --> 00:03:18,000
DVROP

113
00:03:18,000 --> 00:03:18,580
MNT

114
00:03:18,580 --> 00:03:20,600
这里我们来试一下

115
00:03:20,600 --> 00:03:23,240
此时就应该实现了

116
00:03:23,240 --> 00:03:25,080
起码两个文件应该是有了

117
00:03:25,080 --> 00:03:25,800
告诉我

118
00:03:25,800 --> 00:03:26,700
这配置有点问题

119
00:03:26,700 --> 00:03:28,500
应该是两种

120
00:03:28,500 --> 00:03:29,240
少了个E

121
00:03:29,240 --> 00:03:30,680
DVROP

122
00:03:30,680 --> 00:03:33,160
完了我再次执行

123
00:03:33,160 --> 00:03:34,540
我们来看一下

124
00:03:34,540 --> 00:03:35,520
现在出来的文件

125
00:03:35,520 --> 00:03:36,280
是不是就是两个

126
00:03:36,280 --> 00:03:37,420
一个叫home

127
00:03:37,420 --> 00:03:38,040
一个叫other

128
00:03:38,040 --> 00:03:39,240
有这个方框name

129
00:03:39,240 --> 00:03:40,120
其实就是个变量

130
00:03:40,120 --> 00:03:41,800
这个变量代表的是我们的

131
00:03:41,800 --> 00:03:43,000
Entry的名字

132
00:03:43,000 --> 00:03:44,040
你看是不是两个

133
00:03:44,040 --> 00:03:46,440
但是我们这样写完还不够满足

134
00:03:46,440 --> 00:03:47,400
我们需要怎么样

135
00:03:47,400 --> 00:03:50,020
多页面肯定需要产生多个HTML

136
00:03:50,020 --> 00:03:51,780
这里我们就怎么样

137
00:03:51,780 --> 00:03:52,380
在这里

138
00:03:52,380 --> 00:03:54,680
再建上一个index.html

139
00:03:54,680 --> 00:03:56,040
完了呢

140
00:03:56,040 --> 00:03:57,260
我们需要那样一个插件

141
00:03:57,260 --> 00:03:57,840
还记得吧

142
00:03:57,840 --> 00:03:58,680
先生成一下

143
00:03:58,680 --> 00:03:59,420
这里呢

144
00:03:59,420 --> 00:04:00,420
我就随便写

145
00:04:00,420 --> 00:04:02,380
比如说不用写

146
00:04:02,380 --> 00:04:03,160
一路的gs

147
00:04:03,160 --> 00:04:05,380
应该默认可以以home或者以other

148
00:04:05,380 --> 00:04:07,140
那这时候呢

149
00:04:07,140 --> 00:04:08,260
我们还需要那个插件

150
00:04:08,260 --> 00:04:10,000
叫html对吧

151
00:04:10,000 --> 00:04:11,800
webpack-plugin

152
00:04:11,800 --> 00:04:12,920
那个-d

153
00:04:12,920 --> 00:04:15,160
这个插件的作用就是帮我们怎么样

154
00:04:15,160 --> 00:04:16,740
用模板来生成html

155
00:04:16,740 --> 00:04:17,780
完了并且呢

156
00:04:17,780 --> 00:04:19,420
自动把我们的gs怎么样

157
00:04:19,420 --> 00:04:20,680
移入进去

158
00:04:20,680 --> 00:04:21,400
那好了啊

159
00:04:21,400 --> 00:04:22,000
那这里呢

160
00:04:22,000 --> 00:04:24,260
我们就很容易的把这个插件的引进来

161
00:04:24,260 --> 00:04:28,640
叫htmlwebpackplugin等于requip

162
00:04:28,640 --> 00:04:30,600
我们就把它引进来

163
00:04:30,600 --> 00:04:31,800
完了我说这个插件的用法

164
00:04:31,800 --> 00:04:33,200
咱都不用再去说了

165
00:04:33,200 --> 00:04:35,460
直接就干一件事来个plugins

166
00:04:35,460 --> 00:04:37,640
完了去new这样一个东西是吧

167
00:04:37,640 --> 00:04:38,300
完了里面呢

168
00:04:38,300 --> 00:04:40,000
他需要选择一个模板

169
00:04:40,000 --> 00:04:40,200
哎

170
00:04:40,200 --> 00:04:40,660
模板呢

171
00:04:40,660 --> 00:04:41,660
我就叫他什么呢

172
00:04:41,660 --> 00:04:42,940
数据叫他点杠

173
00:04:42,940 --> 00:04:45,500
我就叫他sr不是src下的

174
00:04:45,500 --> 00:04:47,700
就直接就是index.tml

175
00:04:47,700 --> 00:04:49,000
但是默认我们说了

176
00:04:49,000 --> 00:04:50,760
这里面应该给个什么东西

177
00:04:50,760 --> 00:04:51,640
来个filling

178
00:04:52,000 --> 00:04:53,480
这个file name我说了默认呢

179
00:04:53,480 --> 00:04:54,940
他就叫indextml

180
00:04:54,940 --> 00:04:56,580
但是我们很明确啊

181
00:04:56,580 --> 00:04:58,740
我们是不是应该有两个文件

182
00:04:58,740 --> 00:04:59,600
一个叫home

183
00:04:59,600 --> 00:05:00,300
一个叫other

184
00:05:00,300 --> 00:05:01,380
这时候呢

185
00:05:01,380 --> 00:05:03,200
我也不希望他叫indextml了

186
00:05:03,200 --> 00:05:04,640
那这时候你也不能怎么样

187
00:05:04,640 --> 00:05:05,780
你说哎能不能这样写

188
00:05:05,780 --> 00:05:07,320
这样是不行的啊

189
00:05:07,320 --> 00:05:08,440
那我需要干嘛呢

190
00:05:08,440 --> 00:05:10,280
就需要new两次这个插件

191
00:05:10,280 --> 00:05:11,580
分别标示一下

192
00:05:11,580 --> 00:05:11,740
哎

193
00:05:11,740 --> 00:05:12,400
这个呢

194
00:05:12,400 --> 00:05:13,160
可能叫home

195
00:05:13,160 --> 00:05:14,420
完了这个底下呢

196
00:05:14,420 --> 00:05:15,400
我再来一个是吧

197
00:05:15,400 --> 00:05:16,320
再来一个

198
00:05:16,320 --> 00:05:17,740
这个叫什么呢

199
00:05:17,740 --> 00:05:18,160
叫other

200
00:05:18,160 --> 00:05:19,260
这样啊

201
00:05:19,260 --> 00:05:20,880
我们就可以区分出来什么

202
00:05:20,880 --> 00:05:21,940
这是两个页面

203
00:05:21,940 --> 00:05:22,860
那好了

204
00:05:22,860 --> 00:05:24,140
我们再在这试一下

205
00:05:24,140 --> 00:05:25,260
乱标的

206
00:05:25,260 --> 00:05:26,880
但是这样有个问题啊

207
00:05:26,880 --> 00:05:27,920
我们都知道啊

208
00:05:27,920 --> 00:05:29,220
你要这样执行的话

209
00:05:29,220 --> 00:05:31,400
是不是他们默认把home和other怎么样

210
00:05:31,400 --> 00:05:33,420
都移入到这个atml里去

211
00:05:33,420 --> 00:05:34,260
因为我说了

212
00:05:34,260 --> 00:05:37,120
他会把产生的文件资源都插到这个文件里

213
00:05:37,120 --> 00:05:37,800
那好了

214
00:05:37,800 --> 00:05:38,620
你看看啊

215
00:05:38,620 --> 00:05:40,640
是不是home里面有一个home有个other

216
00:05:40,640 --> 00:05:42,360
那同样我们other里面

217
00:05:42,360 --> 00:05:43,940
是不是也有这样一个home有个other

218
00:05:43,940 --> 00:05:45,040
那这样的话

219
00:05:45,040 --> 00:05:47,040
是不是就达不到我的预期的效果了

220
00:05:47,040 --> 00:05:48,480
那我期望的是什么

221
00:05:48,480 --> 00:05:50,580
是不是home应该以home

222
00:05:50,580 --> 00:05:51,380
other呢

223
00:05:51,380 --> 00:05:52,060
应该引other

224
00:05:52,060 --> 00:05:53,260
那同样啊

225
00:05:53,260 --> 00:05:54,300
这个插件里面呢

226
00:05:54,300 --> 00:05:55,540
也提供了一个属性

227
00:05:55,540 --> 00:05:56,460
叫trux

228
00:05:56,460 --> 00:05:57,680
trux的啥意思

229
00:05:57,680 --> 00:05:58,460
就是代码块

230
00:05:58,460 --> 00:06:00,320
它里面放哪些代码块呢

231
00:06:00,320 --> 00:06:00,940
这个呢

232
00:06:00,940 --> 00:06:01,500
放home

233
00:06:01,500 --> 00:06:01,920
对

234
00:06:01,920 --> 00:06:02,780
那下面呢

235
00:06:02,780 --> 00:06:03,580
我们就可以放什么

236
00:06:03,580 --> 00:06:04,340
再来一个啊

237
00:06:04,340 --> 00:06:04,660
多号

238
00:06:04,660 --> 00:06:06,140
这个呢

239
00:06:06,140 --> 00:06:07,360
放我们的这个other

240
00:06:07,360 --> 00:06:09,180
现在就比较好了啊

241
00:06:09,180 --> 00:06:10,420
就是home里面引的是home

242
00:06:10,420 --> 00:06:12,120
也就是引的是这个文件

243
00:06:12,120 --> 00:06:12,780
对吧

244
00:06:12,780 --> 00:06:13,660
other里面呢

245
00:06:13,660 --> 00:06:15,140
引的就是other这个文件

246
00:06:15,140 --> 00:06:16,300
那比如说other里面

247
00:06:16,300 --> 00:06:17,200
可能既需要home

248
00:06:17,200 --> 00:06:18,000
又需要other呢

249
00:06:18,000 --> 00:06:18,340
那好

250
00:06:18,340 --> 00:06:18,980
你可以这样

251
00:06:18,980 --> 00:06:19,340
对吧

252
00:06:19,340 --> 00:06:20,060
又other

253
00:06:20,060 --> 00:06:20,500
又home

254
00:06:20,500 --> 00:06:22,680
这时候我再试一下

255
00:06:22,680 --> 00:06:25,580
OK

256
00:06:25,580 --> 00:06:26,520
看个效果

257
00:06:26,520 --> 00:06:29,500
这时候我们看看home是不是就引了home

258
00:06:29,500 --> 00:06:30,900
other是不是也是一样

259
00:06:30,900 --> 00:06:31,740
先引了home

260
00:06:31,740 --> 00:06:33,140
再引了我们所谓的other

261
00:06:33,140 --> 00:06:34,300
这样的话

262
00:06:34,300 --> 00:06:36,520
我们是不是就实现了这样一个功能了

263
00:06:36,520 --> 00:06:37,820
就是我们一个多应用

264
00:06:37,820 --> 00:06:39,160
当然这个地方应该怎么样

265
00:06:39,160 --> 00:06:39,860
是不是写成other

266
00:06:39,860 --> 00:06:41,020
每个人怎么样

267
00:06:41,020 --> 00:06:41,900
你各自的

268
00:06:41,900 --> 00:06:44,740
这样的话我们的需求就实现了

269
00:06:44,740 --> 00:06:45,160
看看

270
00:06:45,160 --> 00:06:47,300
这里other

271
00:06:47,300 --> 00:06:48,280
你看是不是other

272
00:06:48,280 --> 00:06:49,440
home是不是home

273
00:06:49,440 --> 00:06:51,860
那我们就实现了一个多页营有

