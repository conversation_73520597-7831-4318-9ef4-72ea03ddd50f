1
00:00:00,000 --> 00:00:02,880
我们接着我们来往下讲

2
00:00:02,880 --> 00:00:06,020
现在我们虽然配了这样一个webpack配置

3
00:00:06,020 --> 00:00:07,700
并且也打包出来文件

4
00:00:07,700 --> 00:00:09,580
但是我们运行的时候很low

5
00:00:09,580 --> 00:00:12,560
我们需要直接双击indexed mail来运行

6
00:00:12,560 --> 00:00:15,080
这时候我们可以发现其实可以的

7
00:00:15,080 --> 00:00:16,940
但是我们一般开发的时候

8
00:00:16,940 --> 00:00:19,660
更希望我们可以通过这种local host的方式

9
00:00:19,660 --> 00:00:21,700
或者这种ip地址的方式来访问

10
00:00:21,700 --> 00:00:23,680
这时候我们需要怎么做

11
00:00:23,680 --> 00:00:25,440
我们就需要通过怎么样

12
00:00:25,440 --> 00:00:26,940
是不是起一个这样的服务

13
00:00:26,940 --> 00:00:29,580
在当前build的部分下来生成这样一个文件

14
00:00:29,580 --> 00:00:31,580
这时候我们可以来这样做

15
00:00:31,580 --> 00:00:35,020
比如说我们webpack也内置了一个叫开发服务

16
00:00:35,020 --> 00:00:39,060
它内部是通过e-press来实现这样一个静态服务

17
00:00:39,060 --> 00:00:39,380
那好了

18
00:00:39,380 --> 00:00:40,640
我在这里可以直接

19
00:00:40,640 --> 00:00:43,500
一按二的叫webpack-dv server

20
00:00:43,500 --> 00:00:46,360
它也是开发依赖

21
00:00:46,360 --> 00:00:48,360
它的用法非常简单

22
00:00:48,360 --> 00:00:50,520
就是说我们可以通过执行这样一个命令

23
00:00:50,520 --> 00:00:53,140
在我们这样一个输出的目录下的执行

24
00:00:53,140 --> 00:00:53,720
那好

25
00:00:53,720 --> 00:00:55,860
我们可以在这里再配个脚本

26
00:00:55,860 --> 00:00:59,080
当然你也可以通过npxwebpack

27
00:00:59,080 --> 00:00:59,420
对吧

28
00:00:59,420 --> 00:01:00,980
-dv-so

29
00:01:00,980 --> 00:01:02,300
它的好处

30
00:01:02,300 --> 00:01:04,400
它并不会真实的去打包文件

31
00:01:04,400 --> 00:01:05,540
它只是怎么样

32
00:01:05,540 --> 00:01:08,440
生成一个我们所谓的叫内存中的大包

33
00:01:08,440 --> 00:01:09,800
它可以怎么样

34
00:01:09,800 --> 00:01:11,800
是不是把这种文件写到内存中

35
00:01:11,800 --> 00:01:13,080
那来看看效果

36
00:01:13,080 --> 00:01:14,200
现在我们通过

37
00:01:14,200 --> 00:01:15,940
它会给我生成个地址

38
00:01:15,940 --> 00:01:17,300
叫localhouse8080

39
00:01:17,300 --> 00:01:18,820
我们来访问一下

40
00:01:18,820 --> 00:01:20,360
这里一样

41
00:01:20,360 --> 00:01:22,600
你发现它会默认

42
00:01:22,600 --> 00:01:24,060
以当前这个目录下

43
00:01:24,060 --> 00:01:25,120
作为一个精彩目录

44
00:01:25,120 --> 00:01:27,220
但是我希望并不是这样的

45
00:01:27,220 --> 00:01:28,460
我可以希望它怎么样

46
00:01:28,460 --> 00:01:30,520
是不是进到我们这样一个build的目录下

47
00:01:30,520 --> 00:01:32,800
这时候我们可以加个配置

48
00:01:32,800 --> 00:01:34,220
叫dv 什么呢

49
00:01:34,220 --> 00:01:37,180
so告诉他开发服务的配置

50
00:01:37,180 --> 00:01:40,520
在这里就是开发服务器的配置

51
00:01:40,520 --> 00:01:44,640
开发服务器的配置

52
00:01:44,640 --> 00:01:45,560
都在这里面

53
00:01:45,560 --> 00:01:46,700
我们刚才看到了

54
00:01:46,700 --> 00:01:48,560
默认我们起的时候通过npx

55
00:01:48,560 --> 00:01:49,400
这种写法

56
00:01:49,400 --> 00:01:51,560
当然我们可以直接在这里配一下

57
00:01:51,560 --> 00:01:52,720
在这个pire接生中

58
00:01:52,720 --> 00:01:54,000
我再加一个对吧

59
00:01:54,000 --> 00:01:55,040
开发的环境

60
00:01:55,040 --> 00:01:57,100
然后ok在这里来个dv

61
00:01:57,100 --> 00:01:58,560
那执行的时候呢

62
00:01:58,560 --> 00:01:59,340
就是webpack

63
00:01:59,340 --> 00:02:00,620
dv-sowa

64
00:02:00,620 --> 00:02:01,640
那好了

65
00:02:01,640 --> 00:02:02,960
那我们再回来看

66
00:02:02,960 --> 00:02:04,800
那现在我们可以怎么做呢

67
00:02:04,800 --> 00:02:06,860
是不是可以去启动我们的这个服务了

68
00:02:06,860 --> 00:02:08,020
那启动服务的时候呢

69
00:02:08,020 --> 00:02:08,440
我希望

70
00:02:08,440 --> 00:02:10,260
哎这多口号能不能改一改啊

71
00:02:10,260 --> 00:02:10,540
可以

72
00:02:10,540 --> 00:02:11,440
你可以怎么样

73
00:02:11,440 --> 00:02:12,100
写个pot

74
00:02:12,100 --> 00:02:14,320
比如来个3000

75
00:02:14,320 --> 00:02:15,320
同样呢

76
00:02:15,320 --> 00:02:16,040
我们可以怎么样

77
00:02:16,040 --> 00:02:17,360
在打包的时候啊

78
00:02:17,360 --> 00:02:18,080
内存中打包

79
00:02:18,080 --> 00:02:19,560
我希望他们看到一个进度条

80
00:02:19,560 --> 00:02:21,400
然后我可以加个progress

81
00:02:21,400 --> 00:02:22,340
gress

82
00:02:22,340 --> 00:02:23,460
我要给他一个处

83
00:02:23,460 --> 00:02:24,320
加个动物条

84
00:02:24,320 --> 00:02:26,100
但是啊

85
00:02:26,100 --> 00:02:27,660
这里面有个非常重要的事

86
00:02:27,660 --> 00:02:29,820
就是他怎么能指向build的目录

87
00:02:29,820 --> 00:02:30,860
那也非常方便

88
00:02:30,860 --> 00:02:33,620
这里面他也有个属性叫content的什么bass

89
00:02:33,620 --> 00:02:35,100
你可以跟他说

90
00:02:35,100 --> 00:02:39,420
我希望以第2杠build的目录作为我们的静态服务

91
00:02:39,420 --> 00:02:41,980
就能找到文件夹最好了是吧

92
00:02:41,980 --> 00:02:44,140
这里也一样我们可以再来一次对吧

93
00:02:44,140 --> 00:02:45,820
叫npx执行

94
00:02:45,820 --> 00:02:48,980
当然这里我们刚才配好了可以npm run dv

95
00:02:48,980 --> 00:02:54,780
运行的时候就会默认以当前这样我指定的这样个目录怎么样

96
00:02:54,780 --> 00:02:56,340
来运行我们精彩服务了

97
00:02:56,340 --> 00:02:58,240
这时候应该直接就可以打开我们的

98
00:02:58,240 --> 00:03:00,080
index 庙来试试

99
00:03:00,080 --> 00:03:02,140
然后同样也跑通了

100
00:03:02,140 --> 00:03:04,720
而且这有一个动个号3000

101
00:03:04,720 --> 00:03:05,980
来试试试吧

102
00:03:05,980 --> 00:03:08,580
说了我希望怎么样能自动打开罗软器

103
00:03:08,580 --> 00:03:11,140
当然了你也可以再加上一个叫什么叫 open 处

104
00:03:11,140 --> 00:03:12,440
这里我就不去加了

105
00:03:12,440 --> 00:03:14,680
我不是很喜欢那样一个明令运行

106
00:03:14,680 --> 00:03:17,380
你看这时候是不是依旧找到了目录

107
00:03:17,380 --> 00:03:20,620
这时候我们现在是不是就实现了一个这样的

108
00:03:20,620 --> 00:03:21,480
静态服务了

109
00:03:21,480 --> 00:03:23,580
同样比如说还可以有很多属性

110
00:03:23,580 --> 00:03:26,480
比如说可以启动我们的所谓的JZV压缩

111
00:03:26,480 --> 00:03:27,880
我们可以给个compress

112
00:03:27,880 --> 00:03:30,120
这里面我们再来往下看

113
00:03:30,120 --> 00:03:32,220
但是现在这ETML很有问题

114
00:03:32,220 --> 00:03:34,920
我不可能说我把打包后的文件打包出来了

115
00:03:34,920 --> 00:03:36,120
完了我再去怎么样

116
00:03:36,120 --> 00:03:37,120
建这个ETML

117
00:03:37,120 --> 00:03:39,280
因为我们有的时候可能这个文件怎么样

118
00:03:39,280 --> 00:03:40,120
它根本就没有

119
00:03:40,220 --> 00:03:44,480
我希望怎么样是不是npnp m对吧

120
00:03:44,480 --> 00:03:45,480
软dv

121
00:03:45,480 --> 00:03:48,380
他现在打包出来的我们都知道

122
00:03:48,380 --> 00:03:49,920
这个目录是不是没有

123
00:03:49,920 --> 00:03:51,480
没有的话肯定一刷新

124
00:03:51,480 --> 00:03:53,920
他会告诉我当前有点问题是吧

125
00:03:53,920 --> 00:03:56,760
可能是还是删不掉的问题还在是吧

126
00:03:56,760 --> 00:03:58,580
哎就我先把它这样

127
00:03:58,580 --> 00:03:59,780
把它清空一下

128
00:03:59,780 --> 00:04:02,760
m-f我就把目录的干掉

129
00:04:02,760 --> 00:04:04,720
把我们当前的这样一个build

130
00:04:04,720 --> 00:04:07,680
他告诉我目录正在忙着是吧

131
00:04:07,680 --> 00:04:09,260
我把它删掉再来一次

132
00:04:10,220 --> 00:04:13,300
我把这个项目也关掉

133
00:04:13,300 --> 00:04:15,500
它就是一个显示的问题

134
00:04:15,500 --> 00:04:16,400
这个也关掉

135
00:04:16,400 --> 00:04:17,620
我再来一次

136
00:04:17,620 --> 00:04:19,700
我把它再拖进去

137
00:04:19,700 --> 00:04:21,620
我们的webpack dv1

138
00:04:21,620 --> 00:04:23,360
再来试一下

139
00:04:23,360 --> 00:04:24,580
这回肯定没有了

140
00:04:24,580 --> 00:04:27,340
我再来一次npm run dv

141
00:04:27,340 --> 00:04:31,260
启动了

142
00:04:31,260 --> 00:04:34,060
这时候我们来看一看3000

143
00:04:34,060 --> 00:04:34,980
看有个精度调

144
00:04:34,980 --> 00:04:37,020
这里我们直接防问一下

145
00:04:37,020 --> 00:04:39,520
看看这时候

146
00:04:39,520 --> 00:04:41,520
我们是不是就找不到那样一个ATML了

147
00:04:41,520 --> 00:04:42,640
肯定是找不到的

148
00:04:42,640 --> 00:04:42,840
是不是

149
00:04:42,840 --> 00:04:45,280
最初boss我们这东西没有找到

150
00:04:45,280 --> 00:04:47,240
这时候我们希望怎么样

151
00:04:47,240 --> 00:04:47,720
是不是

152
00:04:47,720 --> 00:04:50,900
他应该能自动的去建这样一个ATML

153
00:04:50,900 --> 00:04:51,760
打不到内存种

154
00:04:51,760 --> 00:04:54,140
这时候我们就需要一个插件了

155
00:04:54,140 --> 00:04:56,140
插件其实就可以帮我们干件事

156
00:04:56,140 --> 00:04:57,980
比如说我们圆满可以放这里

157
00:04:57,980 --> 00:04:59,980
来个ATML

158
00:04:59,980 --> 00:05:02,080
也是能下是吧

159
00:05:02,080 --> 00:05:03,820
往这里我希望怎么样

160
00:05:03,820 --> 00:05:05,060
是不是打包后

161
00:05:05,060 --> 00:05:07,340
他可以帮我们把打包后的文件

162
00:05:07,340 --> 00:05:09,120
塞到ATML里面

163
00:05:09,120 --> 00:05:10,820
并且也把这个结果呀

164
00:05:10,820 --> 00:05:14,120
放到刚才我们想要输出的这个build目录下

165
00:05:14,120 --> 00:05:16,120
那看这时候我怎么做啊

166
00:05:16,120 --> 00:05:17,020
非常简单

167
00:05:17,020 --> 00:05:17,960
这时候呢我们就在这

168
00:05:17,960 --> 00:05:19,120
比如我就为了我说了

169
00:05:19,120 --> 00:05:19,920
他就是一个什么

170
00:05:19,920 --> 00:05:21,420
其实就是一个模板

171
00:05:21,420 --> 00:05:22,420
哎模板

172
00:05:22,420 --> 00:05:24,120
你说我们希望怎么样

173
00:05:24,120 --> 00:05:26,620
用这样一个模板插入这样一个脚本

174
00:05:26,620 --> 00:05:27,720
把这个atml呢

175
00:05:27,720 --> 00:05:30,420
再生成到我们所谓的这个叫build目录下

176
00:05:30,420 --> 00:05:31,520
那好了啊

177
00:05:31,520 --> 00:05:33,520
那这里呢我们就接着往下写

178
00:05:33,520 --> 00:05:34,320
那我们说了

179
00:05:34,320 --> 00:05:36,320
这时候肯定不能自己口过去对吧

180
00:05:36,320 --> 00:05:37,320
肯定需要一个插件

181
00:05:37,320 --> 00:05:38,820
那这个插件呢可以怎么样

182
00:05:38,820 --> 00:05:40,480
能帮我们去干这些事是吧

183
00:05:40,480 --> 00:05:41,780
比如打包后的名字叫这个

184
00:05:41,780 --> 00:05:42,920
他会帮我们自动引入

185
00:05:42,920 --> 00:05:45,120
这个插件我们把它引一下

186
00:05:45,120 --> 00:05:46,420
非常有名对吧

187
00:05:46,420 --> 00:05:48,920
它叫atmlwebpack

188
00:05:48,920 --> 00:05:50,920
pack plugin

189
00:05:50,920 --> 00:05:53,920
一听名字就是我们的atmlwebpack的一个插件

190
00:05:53,920 --> 00:05:56,320
哎那好了用法很像对吧

191
00:05:56,320 --> 00:05:57,620
直接第一步引进来

192
00:05:57,620 --> 00:05:58,520
但是还没有安安

193
00:05:58,520 --> 00:06:00,080
我先把它一写上对吧

194
00:06:00,080 --> 00:06:01,420
搞plug

195
00:06:01,420 --> 00:06:03,420
哎在这里我把它运行一下

196
00:06:03,420 --> 00:06:06,220
好了在这里我们通过

197
00:06:06,480 --> 00:06:10,580
Yan add 安装的-d

198
00:06:10,580 --> 00:06:14,380
我们说了插件的用法都非常一样

199
00:06:14,380 --> 00:06:14,620
对吧

200
00:06:14,620 --> 00:06:17,840
我们其实webpack本身就是用各种插件来对叠起来的

201
00:06:17,840 --> 00:06:21,780
这里我们可以直接来接上这样一个叫plugins

202
00:06:21,780 --> 00:06:24,120
就是我们的它是一个什么类型的是数组

203
00:06:24,120 --> 00:06:25,880
就是放着对吧

204
00:06:25,880 --> 00:06:29,040
放着所有的webpack插件

205
00:06:29,040 --> 00:06:31,020
都可以在这里配

206
00:06:31,020 --> 00:06:33,240
插件的用法我看我这大写了

207
00:06:33,240 --> 00:06:34,180
那肯定是个类

208
00:06:34,180 --> 00:06:36,120
类的用法基本都一样

209
00:06:36,120 --> 00:06:37,680
我们可以去new对吧

210
00:06:37,680 --> 00:06:40,120
这样一个atmlwebpack plugin

211
00:06:40,120 --> 00:06:42,120
完了并且里面你要跟人家说

212
00:06:42,120 --> 00:06:44,120
我希望以他作为模板

213
00:06:44,120 --> 00:06:46,760
这时候你需要跟他说你这玩意是个模板

214
00:06:46,760 --> 00:06:48,040
那模板在哪呢

215
00:06:48,040 --> 00:06:50,920
在src下的index.tml

216
00:06:50,920 --> 00:06:53,240
完了并且你还要跟人家说什么

217
00:06:53,240 --> 00:06:54,320
这个模板有了

218
00:06:54,320 --> 00:06:55,440
但是我希望怎么样

219
00:06:55,440 --> 00:06:58,200
是不是他打包出来的文件也叫index

220
00:06:58,200 --> 00:06:59,760
但你不给默认也是

221
00:06:59,760 --> 00:07:00,520
比如feeling

222
00:07:00,520 --> 00:07:01,800
你也给他一个叫

223
00:07:01,800 --> 00:07:04,320
比如说我这个名字就叫打包后

224
00:07:04,320 --> 00:07:06,220
他也是index.atml

225
00:07:06,220 --> 00:07:08,380
好了我们再来试试是吧

226
00:07:08,380 --> 00:07:09,880
我们先来启动一下

227
00:07:09,880 --> 00:07:12,820
这时候我说了他依然会产生两个文件

228
00:07:12,820 --> 00:07:14,420
这两个文件你看不到的

229
00:07:14,420 --> 00:07:16,880
这时候告诉我require拼错了是吧

230
00:07:16,880 --> 00:07:19,220
require再来一次

231
00:07:19,220 --> 00:07:20,660
这里跑一下

232
00:07:20,660 --> 00:07:23,320
我们来看一看

233
00:07:23,320 --> 00:07:24,620
看看是不是两个文件

234
00:07:24,620 --> 00:07:27,420
你看这里面告诉我了有个bundleatml是吧

235
00:07:27,420 --> 00:07:30,220
这时候我们能看到了这build里面就有这样一个atml了

236
00:07:30,220 --> 00:07:34,280
而且atml也确实引了一下我们这样一个bundle是吧

237
00:07:34,320 --> 00:07:35,680
帮抖啊

238
00:07:35,680 --> 00:07:37,260
中培讯

239
00:07:37,260 --> 00:07:38,160
那好了

240
00:07:38,160 --> 00:07:40,320
那我们知道了这样一个特点以后

241
00:07:40,320 --> 00:07:41,000
我们就知道了

242
00:07:41,000 --> 00:07:42,120
OK这样一个配置呢

243
00:07:42,120 --> 00:07:42,880
其实就配好了

244
00:07:42,880 --> 00:07:44,260
完了我们再往下看

245
00:07:44,260 --> 00:07:46,000
但是我们更希望呢

246
00:07:46,000 --> 00:07:47,200
他也可以去打包

247
00:07:47,200 --> 00:07:47,580
是吧

248
00:07:47,580 --> 00:07:48,260
那这里呢

249
00:07:48,260 --> 00:07:50,080
我们可能会运行npm run build

250
00:07:50,080 --> 00:07:51,940
那rebuild的时候啊

251
00:07:51,940 --> 00:07:52,840
我们肯定希望怎么样

252
00:07:52,840 --> 00:07:54,800
这里面我会很严格的

253
00:07:54,800 --> 00:07:55,000
对吧

254
00:07:55,000 --> 00:07:55,820
给他来个啥呢

255
00:07:55,820 --> 00:07:56,760
比如说我希望哎

256
00:07:56,760 --> 00:07:57,900
他现在是个生产环境

257
00:07:57,900 --> 00:07:59,180
到后面我们会区分

258
00:07:59,180 --> 00:08:00,880
这个开发环境和生产环境啊

259
00:08:00,880 --> 00:08:01,540
那这里呢

260
00:08:01,540 --> 00:08:02,280
我们先配上

261
00:08:02,280 --> 00:08:02,880
哎

262
00:08:02,880 --> 00:08:04,580
我配上一个production

263
00:08:04,580 --> 00:08:06,460
这里我再来run一下

264
00:08:06,460 --> 00:08:08,180
我希望肯定是怎么样

265
00:08:08,180 --> 00:08:10,260
上线的时候帮我们把代码压缩

266
00:08:10,260 --> 00:08:10,620
是不是

267
00:08:10,620 --> 00:08:11,720
来看看

268
00:08:11,720 --> 00:08:13,580
这个确实没问题

269
00:08:13,580 --> 00:08:14,640
压缩成了一个

270
00:08:14,640 --> 00:08:17,880
同样我们还有这样一个index tml

271
00:08:17,880 --> 00:08:20,360
这里面我们希望这样一个模板

272
00:08:20,360 --> 00:08:21,640
也可以被压缩

273
00:08:21,640 --> 00:08:23,640
好了这里我们可以怎么做

274
00:08:23,640 --> 00:08:25,460
非常简单这样做

275
00:08:25,460 --> 00:08:27,260
我们希望这样运行的时候

276
00:08:27,260 --> 00:08:28,540
它可以压缩这个tml

277
00:08:28,540 --> 00:08:31,600
我们就需要在这里再配一些参数

278
00:08:31,600 --> 00:08:32,620
压缩对吧

279
00:08:32,620 --> 00:08:34,420
那肯定是一个minify操作

280
00:08:34,420 --> 00:08:34,740
对吧

281
00:08:34,740 --> 00:08:35,960
叫最小化操作

282
00:08:35,960 --> 00:08:37,180
这里面我们都知道

283
00:08:37,180 --> 00:08:39,480
atml里面可能它有些双一号

284
00:08:39,480 --> 00:08:42,400
我们希望把双一号删掉

285
00:08:42,400 --> 00:08:43,260
怎么删

286
00:08:43,260 --> 00:08:44,160
非常方便

287
00:08:44,160 --> 00:08:46,920
我们可以来个叫remove attributes course

288
00:08:46,920 --> 00:08:47,580
啥意思

289
00:08:47,580 --> 00:08:49,620
就是删除我们属性的双一号

290
00:08:49,620 --> 00:08:51,620
这里面我们来看看

291
00:08:51,620 --> 00:08:53,000
就分分钟就搞定了

292
00:08:53,000 --> 00:08:54,300
非常简单

293
00:08:54,300 --> 00:08:56,380
一样

294
00:08:56,380 --> 00:08:57,280
看看结果

295
00:08:57,280 --> 00:08:58,820
是不是双一号就没了

296
00:08:58,820 --> 00:08:59,740
当然有的可能还有

297
00:08:59,740 --> 00:09:01,140
这因为他觉得这里面有多号

298
00:09:01,140 --> 00:09:01,700
删不掉

299
00:09:01,700 --> 00:09:03,620
同样我们再往下看

300
00:09:03,620 --> 00:09:05,080
比如说我希望它可以怎么样

301
00:09:05,080 --> 00:09:06,200
是不是变成一行

302
00:09:06,200 --> 00:09:07,420
这里面有个叫什么

303
00:09:07,420 --> 00:09:08,300
叫折叠控行

304
00:09:08,300 --> 00:09:08,740
OK

305
00:09:08,740 --> 00:09:10,300
然后它就变成一行了

306
00:09:10,300 --> 00:09:11,640
非常方便

307
00:09:11,640 --> 00:09:12,460
这东西

308
00:09:12,460 --> 00:09:14,320
稍等

309
00:09:14,320 --> 00:09:15,680
完了打包出来

310
00:09:15,680 --> 00:09:16,200
是吧

311
00:09:16,200 --> 00:09:17,180
我来看看结果

312
00:09:17,180 --> 00:09:18,060
这变成一行了

313
00:09:18,060 --> 00:09:20,360
同样我们希望引用的时候

314
00:09:20,360 --> 00:09:22,080
可以加一个所谓的哈希戳

315
00:09:22,080 --> 00:09:22,780
对吧

316
00:09:22,780 --> 00:09:24,200
比如缓存的问题

317
00:09:24,200 --> 00:09:25,680
这里面可以给个哈希

318
00:09:25,680 --> 00:09:26,260
来个处

319
00:09:26,260 --> 00:09:27,560
这时候怎么样

320
00:09:27,560 --> 00:09:28,820
是不是就可以怎么样

321
00:09:28,820 --> 00:09:30,740
帮我们添上这样一个所谓的哈希戳

322
00:09:30,740 --> 00:09:31,920
给大家看看效果

323
00:09:31,920 --> 00:09:34,220
这里面你看我们打包摔的结果

324
00:09:34,220 --> 00:09:36,460
是不是就有了一个这样的

325
00:09:36,460 --> 00:09:38,440
好像没成功是吧

326
00:09:38,440 --> 00:09:40,940
这个哈希应该是在minify的外面

327
00:09:40,940 --> 00:09:42,240
所以这里面要注意一点

328
00:09:42,240 --> 00:09:43,160
站出来

329
00:09:43,160 --> 00:09:44,620
我再来运行一下

330
00:09:44,620 --> 00:09:48,040
好

331
00:09:48,040 --> 00:09:49,700
有点慢是吧

332
00:09:49,700 --> 00:09:50,780
但让我们看看

333
00:09:50,780 --> 00:09:51,820
这回肯定是可以了

334
00:09:51,820 --> 00:09:52,340
你看问号

335
00:09:52,340 --> 00:09:53,180
哈希说

336
00:09:53,180 --> 00:09:54,260
有的人说了

337
00:09:54,260 --> 00:09:55,480
我希望这个文件

338
00:09:55,480 --> 00:09:57,040
它每次生成的都不一样

339
00:09:57,040 --> 00:09:58,880
因为它可能有缓存是吧

340
00:09:58,880 --> 00:10:00,060
这时候我们也可以

341
00:10:00,060 --> 00:10:02,740
比如说在我们输出的路径

342
00:10:02,740 --> 00:10:03,140
对吧

343
00:10:03,140 --> 00:10:05,000
这里面我们也可以加一个什么

344
00:10:05,000 --> 00:10:05,620
不是output

345
00:10:05,620 --> 00:10:07,500
这是fieldmame

346
00:10:07,500 --> 00:10:07,780
是吧

347
00:10:07,780 --> 00:10:08,660
我希望可以怎么样

348
00:10:08,660 --> 00:10:10,220
在这也可以再加个哈希

349
00:10:10,220 --> 00:10:10,940
这啥意思

350
00:10:10,940 --> 00:10:11,780
就是我们希望

351
00:10:11,780 --> 00:10:13,460
也帮到中间有个哈希

352
00:10:13,460 --> 00:10:15,000
就是我每次修改的时候

353
00:10:15,000 --> 00:10:16,580
你都给我产生个新的文件

354
00:10:16,580 --> 00:10:17,080
比如说你看

355
00:10:17,080 --> 00:10:18,440
我在这里运行

356
00:10:18,440 --> 00:10:21,160
这里面会产生一个文件

357
00:10:21,160 --> 00:10:21,500
是吧

358
00:10:21,500 --> 00:10:22,300
这是可以的

359
00:10:22,300 --> 00:10:24,120
你看是不是产生的是5C5C

360
00:10:24,120 --> 00:10:24,460
是吧

361
00:10:24,460 --> 00:10:26,780
比如说当我这文件有更改了

362
00:10:26,780 --> 00:10:27,080
OK

363
00:10:27,080 --> 00:10:28,020
改一下吧

364
00:10:28,020 --> 00:10:28,700
我加个1

365
00:10:28,700 --> 00:10:30,760
这时候我再来运行

366
00:10:30,760 --> 00:10:31,500
这时候怎么样

367
00:10:31,500 --> 00:10:33,040
其他生成的就应该是两个

368
00:10:33,040 --> 00:10:33,340
是吧

369
00:10:33,340 --> 00:10:34,320
有个新的文件

370
00:10:34,320 --> 00:10:35,460
你看是不是

371
00:10:35,460 --> 00:10:37,460
是不是叫ECFB0了

372
00:10:37,460 --> 00:10:39,740
这时候我们是不是就可以实现一个什么

373
00:10:39,740 --> 00:10:41,760
每次打包的时候怎么样

374
00:10:41,760 --> 00:10:43,260
都产生一个不同的文件

375
00:10:43,260 --> 00:10:44,500
这样防止覆盖

376
00:10:44,500 --> 00:10:46,680
也缓存一些出现缓存的问题

377
00:10:46,680 --> 00:10:48,760
这里面我们也非常方便了

378
00:10:48,760 --> 00:10:49,680
这里面再改改

379
00:10:49,680 --> 00:10:51,700
比如说我希望哈希说短一点

380
00:10:51,700 --> 00:10:52,600
你可以来个什么

381
00:10:52,600 --> 00:10:53,280
来个冒号8

382
00:10:53,280 --> 00:10:53,960
8啥意思

383
00:10:53,960 --> 00:10:55,560
就是只显示8位

384
00:10:55,560 --> 00:10:56,940
这里一样

385
00:10:56,940 --> 00:10:58,620
看看效果

386
00:10:58,620 --> 00:11:04,040
OK这里面你看是不是ECF还是一样的名字

387
00:11:04,040 --> 00:11:05,260
因为这个文件没有改

388
00:11:05,260 --> 00:11:05,940
好了

389
00:11:05,940 --> 00:11:10,060
现在我们就知道了这样一个HTML Webpack Plugin的用法

390
00:11:10,060 --> 00:11:12,520
接下来我们就来配一下什么

391
00:11:12,520 --> 00:11:14,800
比如说怎么让它支持我们的CSM模块

392
00:11:14,800 --> 00:11:16,700
怎么支持GS模块的转化

393
00:11:16,700 --> 00:11:17,940
还有我们的图片

394
00:11:17,940 --> 00:11:19,500
还有一些各项的优化

395
00:11:19,500 --> 00:11:20,440
还有配置等

